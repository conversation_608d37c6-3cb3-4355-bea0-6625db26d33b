<?php

include_once 'connection.php';

class Student extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    public function Student()
    {
        self::__construct();
    }

    private $_conn;

    public function getLogin($username)
    {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $username) . "' ";
        $query = "SELECT u.* FROM ggportal_tbl_student u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    // checkNic already exist
    public function checkNicAlreadyExists($nic, $student_id=0){
        $conn = $this->_conn;
        $cond = " and u.nic = '" . mysqli_real_escape_string($conn, $nic) . "' ";
        
        if($student_id>0){
            $cond = $cond . " and u.student_id not in( '" . mysqli_real_escape_string($conn, $student_id) . "') ";
        }
        
        $query = "SELECT u.* FROM ggportal_tbl_student u WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    //checkPassport number already exist
    public function checkPassportAlreadyExists($passport_number, $student_id=0){
        $conn = $this->_conn;
        $cond = " and p.passport_number = '" . mysqli_real_escape_string($conn, $passport_number) . "' "; 
        
        if($student_id>0){
            $cond = $cond . " and p.student_id not in( '" . mysqli_real_escape_string($conn, $student_id) . "') ";
        }

        $query = "SELECT p.* FROM ggportal_tbl_student_passport p WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getEnglishTestDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_english_test s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }
    public function checkEmail($email, $student_id)
    {
        // print_r($email);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $email) . "' ";
        $cond = $cond . " and u.student_id not in( '" . mysqli_real_escape_string($conn, $student_id) . "') ";
        $query = "SELECT u.* FROM ggportal_tbl_student u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    public function randomPassword()
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $password = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < 8; $i++) {
            $n = rand(0, $alphaLength);
            $password[] = $alphabet[$n];
        }
        return implode($password); //turn the array into a string
    }

    public function getStudentByID($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }
        // $query = "select s.* from ggportal_tbl_student s
        //             WHERE 1=1 $cond;"; //        echo $query;
        $query = "SELECT s.*, p.province_name, c.country_name 
        FROM ggportal_tbl_student s
        LEFT JOIN ggportal_tbl_country c ON s.country = c.country_id
        LEFT JOIN ggportal_tbl_province p ON s.province = p.province_id
        WHERE 1=1 $cond;"; //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    // get passport_details using student_id
    public function getPassportDetailsByStudentId($student_id){
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = " and p.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "select p.* from ggportal_tbl_student_passport p WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    public function getStudents($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $agent_id = mysqli_real_escape_string($conn, $criteria['agent_id']);

            if ($user_type == 'AG') {
                $cond .= " and s.assign_to_staff  in ( select staff_id from ggportal_tbl_staff where parent_user_id = $agent_id and parent_user_type = '$user_type') or s.assign_to_staff = $agent_id and s.assign_to_type = '$user_type' or s.assign_to_agent = '$agent_id' and s.assign_to_type = 'SF'  ";
            }
        }

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['staff_id']) && strlen($criteria['staff_id']) > 0) {
            $assign_to_staff = intval($criteria['staff_id']);
            $cond .= " and s.assign_to_staff  = $assign_to_staff ";
        }

        if (isset($criteria['country_id']) && strlen($criteria['country_id']) > 0) {
            $country_id = mysqli_real_escape_string($conn, $criteria['country_id']);
            $cond .= " and s.country = '$country_id' ";
        }

        $query = "SELECT s.*, st.first_name AS staff_name, c.country_name
        FROM ggportal_tbl_student s
        LEFT JOIN ggportal_tbl_staff st ON st.staff_id = s.assign_to_staff
        LEFT JOIN ggportal_tbl_country c ON s.country = c.country_id
        where 1=1  $cond
        ORDER BY s.student_id DESC";

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }


    public function registerStudent($_details)
    {
        $conn = $this->_conn;
        $student_id = intval($_details['student_id']);

        $order_no_check = "SELECT * FROM `ggportal_tbl_student` WHERE 1 ORDER BY student_no DESC LIMIT 1";
        $result = mysqli_query($conn, $order_no_check);
        $latest_order = db_to_array($result);
        if (count($latest_order) > 0) {
            //random number generator between 1-10
            $random_number = rand(2, 4);
            $student_no = $latest_order[0]['student_no'] + $random_number;
        } else {
            $student_no = '200001';
        }

        $query = "INSERT INTO ggportal_tbl_student SET  
                student_no = " . intval($student_no) . "     
                ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "' 
                        
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'
                ,emergency_contact_name = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_name']) . "'
                ,emergency_contact_relation = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_relation']) . "'
                ,emergency_contact_mobile = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_mobile']) . "'
                ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'
                ,real_time_status = '" . mysqli_real_escape_string($conn, $_details['real_time_status']) . "'
                ,created_by =  '" . intval($_details['user_id']) . "'
                ,created_at = now()      
                ,updated_by =  '" . intval($_details['user_id']) . "'
                ,updated_at = now()  
                ,nic = '" . mysqli_real_escape_string($conn, $_details['nic']) . "' 
                ,address = '" . mysqli_real_escape_string($conn, $_details['address']) . "'  
                ,province = '" . mysqli_real_escape_string($conn, $_details['province']) . "' 
                ,postal_code = '" . mysqli_real_escape_string($conn, $_details['postal-code']) . "'                                     
               ;";

        // echo $query;

        $res = mysqli_query($conn, $query);


        if ($res) {
            if ($student_id == 0) {
                $student_id = mysqli_insert_id($conn);
            }
            return $student_id;
        } else {
            return mysqli_error($conn);
        }
    }


    public function saveStudent($_details)
    {
        $conn = $this->_conn;
        $student_id = intval($_details['student_id']);



        $order_no_check = "SELECT * FROM `ggportal_tbl_student` WHERE 1 ORDER BY student_no DESC LIMIT 1";
        $result = mysqli_query($conn, $order_no_check);
        $latest_order = db_to_array($result);
        if (count($latest_order) > 0) {
            //random number generator between 1-10
            $random_number = rand(2, 4);
            $student_no = $latest_order[0]['student_no'] + $random_number;
        } else {
            $student_no = '200001';
        }
        //    return $_details;
        // die();

        //        print_R($driver_details);
        //        die();
        if ($student_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student SET  
                student_no = " . intval($student_no) . "     
                ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "' 
                                       
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'
                ,emergency_contact_name = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_name']) . "'
                ,emergency_contact_relation = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_relation']) . "'
                ,emergency_contact_mobile = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_mobile']) . "'
                ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'
                ,real_time_status = '" . mysqli_real_escape_string($conn, $_details['real_time_status']) . "'
                ,assign_to_staff= '" . mysqli_real_escape_string($conn, $_details['assign_to_staff']) . "'
                ,created_by =  '" . intval($_details['user_id']) . "'
                ,created_at = now()      
                ,updated_by =  '" . intval($_details['user_id']) . "'
                ,updated_at = now()    
                ,nic = '" . mysqli_real_escape_string($conn, $_details['nic']) . "'   
                ,address = '" . mysqli_real_escape_string($conn, $_details['address']) . "'   
                ,province = '" . mysqli_real_escape_string($conn, $_details['province']) . "' 
                ,postal_code = '" . mysqli_real_escape_string($conn, $_details['postal_code']) . "' 
                ,assign_to_type= '" . mysqli_real_escape_string($conn, $_details['assign_to_type']) . "'    
                ,assign_to_agent= '" . mysqli_real_escape_string($conn, $_details['assign_to_agent']) . "'                  
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student SET            
                first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "' 
                                
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'
                ,emergency_contact_name = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_name']) . "'
                ,emergency_contact_relation = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_relation']) . "'
                ,emergency_contact_mobile = '" . mysqli_real_escape_string($conn, $_details['emergency_contact_mobile']) . "'
                ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'
                ,real_time_status = '" . mysqli_real_escape_string($conn, $_details['real_time_status']) . "'
                ,updated_by =  '" . intval($_details['user_id']) . "'
                ,updated_at = now()  
                ,nic = '" . mysqli_real_escape_string($conn, $_details['nic']) . "'        
                ,address = '" . mysqli_real_escape_string($conn, $_details['address']) . "'  
                ,province = '" . mysqli_real_escape_string($conn, $_details['province']) . "' 
                ,postal_code = '" . mysqli_real_escape_string($conn, $_details['postal_code']) . "' 
                ,assign_to_type= '" . mysqli_real_escape_string($conn, $_details['assign_to_type']) . "'  
                ,assign_to_agent= '" . mysqli_real_escape_string($conn, $_details['assign_to_agent']) . "'                 
                WHERE student_id = $student_id";
        }
        // return $query;
        //    die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($student_id == 0) {
                $student_id = mysqli_insert_id($conn);
            }
            return $student_id;
        } else {
            return mysqli_error($conn);
        }
    }

    public function activateAccount($student_id, $token_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_student SET user_active_yn = 'Y', email_validate_yn = 'Y', updated_at = now() WHERE student_id = $student_id";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return true;
        } else {
            return mysqli_error($conn);
        }
    }

    public function updateStudentStaff($student_id, $staff_id, $staff_type)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_student SET assign_to_staff = $staff_id, assign_to_type = '$staff_type' WHERE student_id = $student_id";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $staff_id;
        } else {
            return mysqli_error($conn);
        }
    }

    // delete staff student asignment
    public function deleteStudentStaff($student_id)
    {
        $conn = $this->_conn;
        $query = "UPDATE ggportal_tbl_student SET assign_to_staff = 0, assign_to_type = '' WHERE student_id = $student_id";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $student_id;
        } else {
            return mysqli_error($conn);
        }
    }


    public function getStudentDocuments($student_id, $doc_type)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }


        if (isset($doc_type) && strlen($doc_type) > 0) {
            $doc_type = mysqli_real_escape_string($conn, $doc_type);
            $cond .= " and s.file_name = '$doc_type' ";
        }

        $query = "select s.* from ggportal_tbl_student_document s
                    WHERE 1=1 $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    public function getTravelHistoryDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_travel_history s
                    WHERE 1=1 $cond;"; //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    public function getVisaRefusalDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_visa_refusal s
                    WHERE 1=1 $cond;"; //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    public function getPassportDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_passport s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getEducationDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_education s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getWorkDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_work s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getDocumentDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_document s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getDependenceDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_dependence s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getDisabilityDetails($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);
        if ($student_id > 0) {
            $cond = "and s.student_id=$student_id ";
        } else {
            $cond = " and 1=2 ";
        }

        $query = "select s.* from ggportal_tbl_student_disability s
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function saveStudentVisaDetails($_details)

    {
        $conn = $this->_conn;
        $visa_refusal_id = intval($_details['visa_refusal_id']);

        if ($visa_refusal_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_visa_refusal SET  
                student_id = " . intval($_details['student_id']) . "     
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                         
                ,date = '" . mysqli_real_escape_string($conn, $_details['date']) . "'                     
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_visa_refusal SET            
            country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                         
            ,date = '" . mysqli_real_escape_string($conn, $_details['date']) . "'     
            WHERE visa_refusal_id = $visa_refusal_id ";
        }
        // return $query;
        // die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($visa_refusal_id == 0) {
                $visa_refusal_id = mysqli_insert_id($conn);
            }
            return $visa_refusal_id;
        } else {
            return 0;
        }
    }

    public function saveStudentPassportDetails($_details)
    {
        $conn = $this->_conn;
        $passport_id = intval($_details['passport_id']);

        if ($passport_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_passport SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,passport_number = '" . mysqli_real_escape_string($conn, $_details['passport_number']) . "'
                ,issued_country = '" . mysqli_real_escape_string($conn, $_details['issued_country']) . "'                          
                ,passport_issue_date = '" . mysqli_real_escape_string($conn, $_details['passport_issue_date']) . "' 
                ,passport_expiry_date = '" . mysqli_real_escape_string($conn, $_details['passport_expiry_date']) . "'
                ,nationality = '" . mysqli_real_escape_string($conn, $_details['nationality']) . "'
                ,birth_country = '" . mysqli_real_escape_string($conn, $_details['birth_country']) . "'                     
               ;";
        } else {
            //update
            $query = "UPDATE ggportal_tbl_student_passport SET
             student_id = " . intval($_details['student_id']) . "                
            ,passport_number = '" . mysqli_real_escape_string($conn, $_details['passport_number']) . "'  
            ,issued_country = '" . mysqli_real_escape_string($conn, $_details['issued_country']) . "'                       
            ,passport_issue_date = '" . mysqli_real_escape_string($conn, $_details['passport_issue_date']) . "' 
            ,passport_expiry_date = '" . mysqli_real_escape_string($conn, $_details['passport_expiry_date']) . "'
            ,nationality = '" . mysqli_real_escape_string($conn, $_details['nationality']) . "'
            ,birth_country = '" . mysqli_real_escape_string($conn, $_details['birth_country']) . "'    
            WHERE passport_id = $passport_id;";
        }
        // return $query;
        // die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($passport_id == 0) {
                $passport_id = mysqli_insert_id($conn);
            }
            return $passport_id;
        } else {
            return 0;
        }
    }

    public function saveStudentTravelDetails($_details)
    {
        $conn = $this->_conn;
        $travel_history_id = intval($_details['travel_history_id']);

        if ($travel_history_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_travel_history SET  
                student_id = " . intval($_details['student_id']) . "     
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                         
                ,travel_departure = '" . mysqli_real_escape_string($conn, $_details['travel_departure']) . "'
                ,travel_arrival = '" . mysqli_real_escape_string($conn, $_details['travel_arrival']) . "'   
                ,travel_reason = '" . mysqli_real_escape_string($conn, $_details['travel_reason']) . "'                         
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_travel_history SET            
                country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                         
                ,travel_departure = '" . mysqli_real_escape_string($conn, $_details['travel_departure']) . "'
                ,travel_arrival = '" . mysqli_real_escape_string($conn, $_details['travel_arrival']) . "'   
                ,travel_reason = '" . mysqli_real_escape_string($conn, $_details['travel_reason']) . "'       
                 WHERE travel_history_id = $travel_history_id;";
        }


        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($travel_history_id == 0) {
                $travel_history_id = mysqli_insert_id($conn);
            }
            return $travel_history_id;
        } else {
            return 0;
        }
    }

    function saveStudentEducationDetails($_details)
    {
        $conn = $this->_conn;
        $education_id = intval($_details['education_id']);

        if ($education_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_education SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,education_level = '" . mysqli_real_escape_string($conn, $_details['education_level']) . "'   
                ,education_institute = '" . mysqli_real_escape_string($conn, $_details['education_institute']) . "'
                ,education_program = '" . mysqli_real_escape_string($conn, $_details['education_program']) . "'
                ,study_language = '" . mysqli_real_escape_string($conn, $_details['study_language']) . "'
                ,degree_type= '" . mysqli_real_escape_string($conn, $_details['degree_type']) . "'
                ,starting_date = '" . mysqli_real_escape_string($conn, $_details['starting_date']) . "'
                ,end_date = '" . mysqli_real_escape_string($conn, $_details['end_date']) . "'
                ,education_country = '" . mysqli_real_escape_string($conn, $_details['education_country']) . "'             
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_education SET  
            student_id = " . intval($_details['student_id']) . "     
            ,education_level = '" . mysqli_real_escape_string($conn, $_details['education_level']) . "'   
            ,education_institute = '" . mysqli_real_escape_string($conn, $_details['education_institute']) . "'
            ,education_program = '" . mysqli_real_escape_string($conn, $_details['education_program']) . "'
            ,study_language = '" . mysqli_real_escape_string($conn, $_details['study_language']) . "'
            ,degree_type= '" . mysqli_real_escape_string($conn, $_details['degree_type']) . "'
            ,starting_date = '" . mysqli_real_escape_string($conn, $_details['starting_date']) . "'
            ,end_date = '" . mysqli_real_escape_string($conn, $_details['end_date']) . "'
            ,education_country = '" . mysqli_real_escape_string($conn, $_details['education_country']) . "'     
            WHERE education_id = $education_id;";
        }

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($education_id == 0) {
                $education_id = mysqli_insert_id($conn);
            }
            return $education_id;
        } else {
            return 0;
        }
    }

    function saveStudentWorkDetails($_details)
    {
        $conn = $this->_conn;
        $work_id = intval($_details['work_id']);

        if ($work_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_work SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,company_name = '" . mysqli_real_escape_string($conn, $_details['company_name']) . "'   
                ,position = '" . mysqli_real_escape_string($conn, $_details['position']) . "'
                ,start_date = '" . mysqli_real_escape_string($conn, $_details['work_starting_date']) . "'
                ,end_date = '" . mysqli_real_escape_string($conn, $_details['work_end_date']) . "'            
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_work SET  
            student_id = " . intval($_details['student_id']) . "     
                ,company_name = '" . mysqli_real_escape_string($conn, $_details['company_name']) . "'   
                ,position = '" . mysqli_real_escape_string($conn, $_details['position']) . "'
                ,start_date = '" . mysqli_real_escape_string($conn, $_details['work_starting_date']) . "'
                ,end_date = '" . mysqli_real_escape_string($conn, $_details['work_end_date']) . "'    
            WHERE work_id = $work_id;";
        }

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($work_id == 0) {
                $work_id = mysqli_insert_id($conn);
            }
            return $work_id;
        } else {
            return 0;
        }
    }

    function saveStudentEnglishTestDetails($_details)
    {
        $conn = $this->_conn;
        $english_test_id = intval($_details['english_test_id']);

        if ($english_test_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_english_test SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,test_name = '" . mysqli_real_escape_string($conn, $_details['test_name']) . "'   
                ,test_status = '" . mysqli_real_escape_string($conn, $_details['test_status']) . "'
                ,speaking = '" . mysqli_real_escape_string($conn, $_details['speaking']) . "'
                ,listening = '" . mysqli_real_escape_string($conn, $_details['listening']) . "'     
                ,reading = '" . mysqli_real_escape_string($conn, $_details['reading']) . "' 
                ,writing = '" . mysqli_real_escape_string($conn, $_details['writing']) . "' 
                ,test_date = '" . mysqli_real_escape_string($conn, $_details['test_date']) . "' 
                ,test_description = '" . mysqli_real_escape_string($conn, $_details['test_description']) . "'        
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_english_test SET  
            student_id = " . intval($_details['student_id']) . "     
            ,test_name = '" . mysqli_real_escape_string($conn, $_details['test_name']) . "'   
            ,test_status = '" . mysqli_real_escape_string($conn, $_details['test_status']) . "'
            ,speaking = '" . mysqli_real_escape_string($conn, $_details['speaking']) . "'
            ,listening = '" . mysqli_real_escape_string($conn, $_details['listening']) . "'     
            ,reading = '" . mysqli_real_escape_string($conn, $_details['reading']) . "' 
            ,writing = '" . mysqli_real_escape_string($conn, $_details['writing']) . "' 
            ,test_date = '" . mysqli_real_escape_string($conn, $_details['test_date']) . "' 
            ,test_description = '" . mysqli_real_escape_string($conn, $_details['test_description']) . "' 
            WHERE english_test_id = $english_test_id;";
        }

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($english_test_id == 0) {
                $english_test_id = mysqli_insert_id($conn);
            }
            return $english_test_id;
        } else {
            return 0;
        }
    }


    function saveStudentDependenceDetails($_details)
    {
        $conn = $this->_conn;
        $dependence_id = intval($_details['dependence_id']);

        if ($dependence_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_dependence SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,dependence_type = '" . mysqli_real_escape_string($conn, $_details['dependence_type']) . "'   
                ,dependence_age = '" . mysqli_real_escape_string($conn, $_details['dependence_age']) . "'                         
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_dependence SET  
                student_id = " . intval($_details['student_id']) . "              
                ,dependence_type = '" . mysqli_real_escape_string($conn, $_details['dependence_type']) . "'   
                ,dependence_age = '" . intval($_details['dependence_age']) . "'       
                 WHERE dependence_id = $dependence_id;";
        }

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($dependence_id == 0) {
                $dependence_id = mysqli_insert_id($conn);
            }
            return $dependence_id;
        } else {
            return 0;
        }
    }


    function saveStudentDisabilityDetails($_details)
    {
        $conn = $this->_conn;
        $disability_id = intval($_details['disability_id']);

        if ($disability_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_disability SET  
                 student_id = " . intval($_details['student_id']) . "     
                ,description = '" . mysqli_real_escape_string($conn, $_details['description']) . "'                          
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_student_disability SET  
                student_id = " . intval($_details['student_id']) . "              
                ,description = '" . mysqli_real_escape_string($conn, $_details['description']) . "'    
                 WHERE disability_id = $disability_id;";
        }

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($disability_id == 0) {
                $disability_id = mysqli_insert_id($conn);
            }
            return $disability_id;
        } else {
            return 0;
        }
    }



    function saveStudentDocument($_details)

    {
        $conn = $this->_conn;
        $student_document_id = intval($_details['student_document_id']);

        if ($student_document_id == 0) {
            $query = "INSERT INTO ggportal_tbl_student_document SET  
                student_id = " . intval($_details['student_id']) . "     
                ,file_name = '" . mysqli_real_escape_string($conn, $_details['file_name']) . "'                         
                ,doc_url = '" . mysqli_real_escape_string($conn, $_details['doc_url']) . "'                      
               ;";
        } else {
            //update

        }
        // return $query;
        // die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($student_document_id == 0) {
                $student_document_id = mysqli_insert_id($conn);
            }
            return $student_document_id;
        } else {
            return 0;
        }
    }



    public function deleteStudent($student_id)
    {
        $conn = $this->_conn;
        // DELETE disairability table 

        try{ 
            $conn->begin_transaction();

            // List of tables to delete from
            $tables = [
                'ggportal_tbl_student_dependence',
                'ggportal_tbl_student_disability',
                'ggportal_tbl_student_english_test',
                'ggportal_tbl_student_visa_refusal',
                'ggportal_tbl_student_document',
                'ggportal_tbl_student_passport',
                'ggportal_tbl_student_travel_history',
                'ggportal_tbl_student_education',
                'ggportal_tbl_student_work',
                'ggportal_tbl_student'
            ];
            
            // Execute delete queries
            foreach ($tables as $table) {
                 // Check if record exists
                $checkQuery = "SELECT 1 FROM `$table` WHERE `student_id` = $student_id LIMIT 1;";
                $result = mysqli_query($conn, $checkQuery);

                if (mysqli_num_rows($result) > 0) {
                    // Delete record if exists
                    $deleteQuery = "DELETE FROM `$table` WHERE `student_id` = $student_id;";
                    mysqli_query($conn, $deleteQuery);
                }                
            }

            // Commit transaction
            $conn->commit();
            return $student_id;
        } catch (mysqli_sql_exception $e) {
            // Rollback transaction if any query fails
            $conn->rollback();
            
            // Return the error message
            return $e->getMessage();
        } 
    }

    public function deleteStudentDocument($student_document_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_document` WHERE `student_document_id` = $student_document_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $student_document_id;
        } else {
            return mysqli_error($conn);
        }
    }

    public function deleteVisa($visa_refusal_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_visa_refusal` WHERE `visa_refusal_id` = $visa_refusal_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $visa_refusal_id;
        } else {
            return mysqli_error($conn);
        }
    }

    public function deleteDependence($dependence_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_dependence` WHERE `dependence_id` = $dependence_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $dependence_id;
        } else {
            return mysqli_error($conn);
        }
    }


    function deleteDisability($disability_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_disability` WHERE `disability_id` = $disability_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $disability_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteTravel($travel_history_id)

    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_travel_history` WHERE `travel_history_id` = $travel_history_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $travel_history_id;
        } else {
            return mysqli_error($conn);
        }
    }



    function deleteEducation($education_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_education` WHERE `education_id` = $education_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $education_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteWork($work_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_work` WHERE `work_id` = $work_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $work_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function saveTokenDetails($tokendetails)

    {
        $conn = $this->_conn;

        $student_id = intval($tokendetails['student_id']);
        $token_id = mysqli_real_escape_string($conn, $tokendetails['token_id']);
        $token_created_time = mysqli_real_escape_string($conn, $tokendetails['token_created_time']);

        if ($student_id == 0) {
            $query = "INSERT INTO `ggportal_tbl_student` SET
            `token_id`='$token_id'
            ,`token_created_time`='$token_created_time'
            ";
        } else { //update
            $query = "UPDATE `ggportal_tbl_student` SET
            `token_id`='$token_id'
            ,`token_created_time`='$token_created_time'
            Where student_id = '$student_id'
            ";
        }
        //echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            if ($student_id == 0) {
                $student_id = mysqli_insert_id($conn);
            }
            return $student_id;
        } else {
            return 0;
        }
    }


    public function getTokenDetails($token_id)
    {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond = " and u.token_id = '" . mysqli_real_escape_string($conn, $token_id) . "' ";
        $query = "SELECT u.* FROM ggportal_tbl_student u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    public function savePassDetails($passdetails)
    {
        $conn = $this->_conn;

        $token_id = mysqli_real_escape_string($conn, $passdetails['token_id']);
        $password = mysqli_real_escape_string($conn, $passdetails['password']);
        $password1 = $passdetails['password'];
        $password = convert_string('encrypt', $password1); // Encrypt the password


        // print_r($token_id);
        // print_r($password);
        // die();

        $query = "UPDATE `ggportal_tbl_student` SET
        `password`='$password'
        ,`token_id`=''
        ,`token_created_time`='1971-08-15 05:45:18'
        Where token_id = '$token_id'
        ";

        echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            // if ($token_id == 0) {
            //     $token_id = mysqli_insert_id($conn);
            // }
            return $res;
        } else {
            return 0;
        }
    }
   
    public function savePassDetails2($passdetails)
    {
        $conn = $this->_conn;

        $token_id = mysqli_real_escape_string($conn, $passdetails['token_id']);

        // print_r($token_id);
        // die();

        $query = "UPDATE `ggportal_tbl_student` SET
        `token_id`=''
        ,`token_created_time`='1971-08-15 05:45:18'
        Where token_id = '$token_id'
        ";

        //echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            if ($token_id == 0) {
                $token_id = mysqli_insert_id($conn);
            }
            return $token_id;
        } else {
            return 0;
        }
    }

    public function getTimeline($criteria)
    {
        $conn = $this->_conn;
        $cond = "";


        $query = "select s.* from ggportal_tbl_timeline s
         where 1=1  $cond ";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    public function updateStudentTimeline($student_id, $timeline_id)
    {
        $conn = $this->_conn;
        $query = "UPDATE `ggportal_tbl_student` SET
        `timeline_id` = $timeline_id
        WHERE `student_id`=$student_id
        ";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $student_id;
        } else {
            return 0;
        }
    }

    public function getStudentCount($criteria)
    {
        $conn = $this->_conn;
        $cond = "";
        $user_id = intval($criteria['user_id']);
        $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);

        if ($user_type == 'RA') {

            $query = "SELECT count(*) as total FROM ggportal_tbl_student s WHERE 1";
        } else if ($user_type == 'SF') {
            $query  = "SELECT count(*) as total FROM ggportal_tbl_student s WHERE s.assign_to_staff=$user_id and s.assign_to_type='$user_type'; ";
        } else {
            $query  = "SELECT count(*) as total FROM ggportal_tbl_student s WHERE s.assign_to_staff IN 
        (SELECT staff_id FROM ggportal_tbl_staff sf WHERE sf.parent_user_id = $user_id AND sf.parent_user_type = '$user_type')
        OR s.assign_to_staff = $user_id AND s.assign_to_type = '$user_type' 
        OR s.assign_to_agent =$user_id AND s.assign_to_type = 'SF' ";
        }


        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    public function getStudentAssignStaffDetails($student_id)
    {
        $conn = $this->_conn;
        $query = "SELECT
        sta.staff_id,
        CONCAT(sta.first_name, ' ', sta.last_name) AS staff_name,
        sta.email AS staff_email,
        sta.mobile AS mobile,
        sta.last_seen AS last_seen
    FROM
        ggportal_tbl_student stu
    INNER JOIN
        ggportal_tbl_staff sta
    ON
        stu.assign_to_staff = sta.staff_id
    WHERE
        stu.student_id = $student_id";

        $result = mysqli_query($conn, $query);

        $staffDetails = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $staffDetails[] = $row;
        }

        return $staffDetails;
    }

    public function calStudentOnlineStatus($student_id)
    {
        $conn = $this->_conn;

        $query = "SELECT first_name, last_seen FROM ggportal_tbl_student WHERE student_id = $student_id";
        $res = mysqli_query($conn, $query);
        if ($res === false) {
            error_log("Query failed: " . mysqli_error($conn));
            return null;
        }
        $resultData = mysqli_fetch_assoc($res);

        return $resultData;
    }
    //Get assigned count
    function getAssignedCount($criteria2)
    {
        $conn = $this->_conn;
        $cond = "";
        $staff_id = intval($criteria2['staff_id']);
        $staff_type = mysqli_real_escape_string($conn, $criteria2['staff_type']);

        $cond = "and s.assign_to_staff=$staff_id and s.assign_to_type='$staff_type' ";

        $query = "select count(*) as total from ggportal_tbl_student s
         where 1=1  $cond ";

        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }



    // Student agent section start

    public function getStudentAssignAgentDetails($student_id)
    {
        $conn = $this->_conn;
        $query = "SELECT
        ag.agent_id,
        CONCAT(ag.first_name, ' ', ag.last_name) AS agent_name,
        ag.email AS email,
        ag.mobile AS mobile,
        ag.last_seen AS last_seen
    FROM
        ggportal_tbl_student stu
    INNER JOIN
        ggportal_tbl_agent ag
    ON
        stu.assign_to_agent = ag.agent_id
    WHERE
        stu.student_id = $student_id";

        $result = mysqli_query($conn, $query);

        $staffDetails = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $staffDetails[] = $row;
        }

        return $staffDetails;
    }
    // Student agent section emd



    public function getCountryRelatedProvince($country_id)
    {
        $conn = $this->_conn;
        $query = "SELECT * FROM ggportal_tbl_province WHERE country_id = $country_id order by province_name asc";
        $result = mysqli_query($conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }

        if (!empty($resultset)) {
            return $resultset;
        }
    }

    //get student name by id 
    public function getStudentNameById($student_id)
    {
        $conn = $this->_conn;
        $student_id = intval($student_id);    
             
        $query = "SELECT first_name, last_name  FROM ggportal_tbl_student WHERE student_id = $student_id";
        $res = mysqli_query($conn, $query);
    
        if ($res && mysqli_num_rows($res) > 0) {
            $row = mysqli_fetch_assoc($res);
            return $row['first_name'] . ' ' . $row['last_name']; 
        } else {
            return null; 
        }
    }

    //get student assign id by student id
    public function getStudentAssignee($student_id)
{
    $conn = $this->_conn;
    $student_id = intval($student_id);

    $query = "SELECT created_by, assign_to_staff FROM ggportal_tbl_student WHERE student_id = $student_id";
    $res = mysqli_query($conn, $query);

    if ($res && mysqli_num_rows($res) > 0) {
        $row = mysqli_fetch_assoc($res);
        if ($row['assign_to_staff'] > 0) {
            // If assign_to_staff is greater than 0, return it
            return $row['assign_to_staff'];
        } else {
            // Otherwise, return created_by
            return $row['created_by'];
        }
    } else {
        return null;
    }
}


}
