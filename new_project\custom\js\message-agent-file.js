//Student staff chat from student side

$(document).ready(function () {
  bsCustomFileInput.init();
  //Initialize Select2 Elements
  $(".select2bs4").select2({
    theme: "bootstrap4",
  });

  $("#quickform").validate({
    rules: {
      message: {
        required: true,
      },
    },
    messages: {
      message: {
        required: "Please Enter a message",
      },
    },
    errorElement: "span",
    errorPlacement: function (error, element) {
      error.addClass("invalid-feedback");
      element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
      $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
      $.ajax({
        type: "POST",
        url: "controller/agent-message-controller.php",
        data: $(form).serialize() + "&messagesSave=1",
        dataType: "json",
        success: function(data) {
          console.log(data);
          if (data.data === true) {
            $('input[name="message"]').val("");
            $(form).trigger("reset");
          } else {
            console.error("There is an error while saving.");
          }
        },
        error: function (xhr, status, error) {
          console.error("AJAX error:", error);
          console.log("Server Response:", xhr.responseText);
        }
      });
    }
    
  });

  getchatmessages();

  setInterval(function () {
    getchatmessages();
  }, 15000);

  $("body").on("click", "#show2", function () {
    //console.log("this is working");
    setTimeout(function () {
      getchatmessages();
    }, 500);
  });

  function getchatmessages() {
    let student_id = $("#student_id").val();
    let agent_id = $("#agent_id").val();
    $.ajax({
      method: "post",
      url: "get-agent-messages.php",
      data:
        $("#displaychatdata").serialize() +
        "&agent_id=" +
        agent_id +
        "&student_id=" +
        student_id,
      dataType: "html",
      success: function (response) {
        $("#displaychatdata").html(response);
      },
    });
  }
});
