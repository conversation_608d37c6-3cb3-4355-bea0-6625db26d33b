$(document).ready(function() {

    //console.log("ready!");
    bsCustomFileInput.init();
    //Initialize Select2 Elements
    $('.select2bs4').select2({
        theme: 'bootstrap4'
    });

    $('#summernote').summernote();


    /*Save Country*/
    $('#quickform').validate({
        rules: {
          content_body: {
                required: true,
            },
        },
        messages: {
          content_body: {
                required: "Please Enter a value",
            },
        },
        errorElement: 'span',
        errorPlacement: function(error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function(element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function(element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function(form) {
            //console.log(new FormData(form));
            var formData = new FormData(form);
            formData.append('contentSave', '1');
            $.ajax({
                type: "POST",
                url: "controller/content-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function(data) {
                    //console.log(data);
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        setTimeout(function() {
                            // window.location.href = 'country-list.php';
                            $('#form').trigger("reset");
                        }, 5000);

                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });
        }
    });

        /*Update Agreement*/
        $('#quickform2').validate({
            rules: {
                agreement_signed_yn: {
                    required: true,
                },
                ag_document: {
                    required: true,
                }
            },
            messages: {
                agreement_signed_yn: {
                    required: "Please  Check",
                },
                ag_document: {
                    required: "Please  Upload Signed Agreement",
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function (element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            },
            submitHandler: function (form) {
                var formData = new FormData(form);
                formData.append('updateAgreement', '1');
                console.log(formData);
                $.ajax({
                    type: "POST",
                    url: "controller/agent-controller.php",
                    data: formData,
                    dataType: 'json',
                    mimeType: "multipart/form-data",
                    contentType: false,
                    processData: false,
                    cache: false,
                    success: function (data) {
                        if (data.data > 0) {
                            toastr.success('Saved Successfully');
                                window.location.href = 'dash-agent.php';
                            
                        } else {
                            toastr.error(data.status);
                        }
    
                    }
                });
            }
        });

});