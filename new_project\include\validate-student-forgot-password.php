<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
include_once $include_path . 'common_functions.php';
require_once $include_path . "email_functions.php";
include_once $class_path . 'class.student.php';
include '../form-submit-email.php';
require_once __DIR__ . '/../class/class.sms.php';

$redirect_url = filter_input(INPUT_POST, "redirect", FILTER_SANITIZE_URL); //after loggedin, redirect.
if ($redirect_url == "") {
    $redirect_url = "../dashboard.php";
}

//echo "redirect=".$redirect_url.";";
$login_session = (isset($_SESSION['user']['login_session']) ? $_SESSION['user']['login_session'] : "");
unset($_SESSION['user']['login_session']); //regenerate in login.php
unset($_SESSION['user']);

$start = $base_url;

if (isset($_POST['email'])) {
    $email = $_POST['email'];
    $cstudent = new Student();
    $cSms = new Sms();
    $user_details = $cstudent->getLogin($email);
    // print_r($user_details);
    // die();
    if (isset($user_details[0]['email'])) {
        if ($email == $user_details[0]['email']) {
            if ($user_details[0]['user_active_yn'] == "Y") {
                $student_id = $user_details[0]['student_id'];
                $name = "student";
                $token_id = uniqid();
                $token_created_time = date("Y-m-d H:i:s");

                $tokendetails = array(
                    'student_id' => $student_id
                    , 'token_id' => $token_id
                    , 'token_created_time' => $token_created_time
                );

                $cstudent->saveTokenDetails($tokendetails);

                $address = $email;
                $subject = 'Recover Password';

                $body = '
                <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
    <style>
        .header {
            background-color: #293C4A;
            padding: 20px;
            font-size: 25px;
            text-align: center;
            font-weight: bolder;
        }

        .header h2 {
            color: #fff;
            margin-bottom: 0px;
        }

        .header h2>span {
            color: rgb(230, 251, 0);
        }

        .header p {
            padding-top: 0px;
            margin-top: 0px;
            color: #fff;
            font-size: 16px;
        }

    </style>
</head>

<body>
<span style="opacity: 0"> '.date("Y-m-d H:i:s").'</span>
<div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
<table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
  <tbody><tr>
    <td align="center" valign="top">
      <table width="650" border="0" cellspacing="0" cellpadding="0">
        <tbody><tr>
          <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
            
              <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                              
                  <tbody><tr>
                      <td class="header">
                          <h2 style="font-weight: 700;">Edvios</h2>
                          <p style="padding-top: 30px;">The Student Portal</p>
                      </td>
                  </tr>
              </tbody></table>
            
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tbody><tr>
                <td>
                  <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                    <tbody><tr>
                      <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr>
                            <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                              Hello,</td>
                          </tr>
                          
                        </tbody></table>
                      </td>
                    </tr>
                  </tbody></table>
                </td>
              </tr>
            </tbody></table>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody><tr>
                          <td>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                              <tbody><tr>
                                <td style="padding:0px 20px">
                                  <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr height="5px">
                                      <td width="100%">
                                          &nbsp;                              
                                      </td>
                                    </tr>
                                    
                                    <tr height="50px">
                                       <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                        <p>  Please click the following link to change the password.</p>                             
                                      </td>                             
                                    </tr>
                                      
                                    <tr height="50px">
                                       <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                       <a class="primary-btn" href="' . $start . 'student-recover-password.php?token_id=' . $token_id . '">Click here to change the password</a>
                                    </tr>

                                    <tr height="50px">
                                    <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                    User:<br>' . $email . '                            
                                   </td>                             
                                 </tr>
                                 <tr height="50px">
                                 <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                 If you did not register to the Edvios, please ignore this email.                      
                                </td>                             
                              </tr>
                                    <tr>
                                      <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                        <p>Thank you.<br> Kind Regards,<br> GGPortal</p>
                                      </td>                             
                                    </tr> 
                                        <tr height="15px">
                                            <td width="100%">&nbsp;</td>
                                        </tr>
                                                         
                                                               
                                  </tbody></table>
                                </td>
                              </tr>
                            </tbody></table>
                          </td>
                        </tr>
                      </tbody></table>

            
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tbody><tr>
                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody><tr>
                      <td align="center" style="padding-bottom:10px">
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                          <tr>
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                          </tr>							
                        </tbody></table>
                      </td>
                    </tr>
                      
                  
                  </tbody></table>
                  
                </td>
              </tr>
            </tbody></table>
            
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
              <tbody><tr>
                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                      <tbody><tr>
                      <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                          Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                    </tr>
                   
                  
                  </tbody></table>
                  
                </td>
              </tr>
            </tbody></table>
            
          </td>
        </tr>
      </tbody></table>
    </td>
  </tr>
</tbody></table>
<span style="opacity: 0"> '.date("Y-m-d H:i:s").'</span>
</body>

</html>';

                $status = send_mail($subject, $address, $body);

                $student_phone_number =  $user_details[0]['mobile'];
                //sms to student  
                $message ="You've requested a password reset. Please check your email for further instructions."; 
                $cSms->send_sms($message,$student_phone_number);
                

                echo '<html>
                <head>
                    <!-- Bootstrap CSS -->
                    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
                </head>
                <body>
                    <!-- Modal -->
                    <div class="modal fade" id="noticeModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    Please check your email and click the link to change the password.
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-primary" id="redirectBtn">OK</button>
                                </div>
                            </div>
                        </div>
                    </div>
            
                    <!-- Bootstrap JS and dependencies -->
                    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
                    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.3/dist/umd/popper.min.js"></script>
                    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
                    <!-- Custom Script to show modal and redirect -->
                    <script>
                        $(document).ready(function() {
                            $("#noticeModal").modal("show");
            
                            $("#redirectBtn").on("click", function() {
                                window.location.href = "../login.php";
                            });
                        });
                    </script>
                </body>
                </html>';
            }
            else {
                errorRedirect("User is not active.Please contact the techinical team to activate the account first!", "../student-forgot-password.php?redirect=$redirect_url");
            }
        }
        else {
            errorRedirect("User not found!", "../student-forgot-password.php?redirect=$redirect_url");
        //   print_r("User account not found!");
        }
    }
    else {
        errorRedirect("User not found!", "../student-forgot-password.php?redirect=$redirect_url");
    // $_SESSION['error']="User account not found!";
    // echo '<script>location="../forgot-password.php";</script>';
    //   print_r("User account not found!");
    }
}

?>