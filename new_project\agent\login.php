<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'email_functions.php';

$redirect_url = filter_input(INPUT_GET, "redirect", FILTER_SANITIZE_STRING);


$_SESSION['user']['login_session'] = generate_random_characters(25);

?>
<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../dist/css/login/icomoon/style.css">

    <link rel="stylesheet" href="../dist/css/login/owl.carousel.min.css">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../dist/css/login/bootstrap.min.css">

    <!-- Style -->
    <link rel="stylesheet" href="../dist/css/login/style.css">

    <title>Edvios</title>

    <!-- New Styles -->
    <link rel="stylesheet" href="../dist/css/new-styles.css">

    <!-- Add new Font -- Poppins -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

</head>

<body>
    <div class="content agent-sign-in-sec">
        <div class="container">
            <div class="row row-wrap">
                <div class="col-md-6 img-wrap">
                    <img src="../dist/img/login/agent-login.svg" alt="Image" class="img-fluid">
                </div>
                <div class="col-md-6 contents">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="clicks text-center top-head-wrap">
                                <img src="../dist/img/login/edvious_logo.png" alt="Image" width="130px" height="auto" class="img-fluid">
                                <!-- <h3>Agent Portal - Sign In</h3> -->
                                <!-- <p class="mb-4">Sign In</p> -->
                                <div class="agent-portal-wrap">
                                    <span class="login-box-msg newCardTitle">Agent Portal - Sign In</span>
                                </div>
                            </div>
                            <?php include_once $include_path . 'display_message.php'; ?>
                            <form name="frmLogin" id="frmLogin" action="../include/validate-agent-login.php"
                                method="post" class="login-form-wrap">
                                <input type="hidden" value="<?php echo $redirect_url ?>" name='redirect' />
                                <input type="hidden" value="<?php echo $_SESSION['user']['login_session'] ?>"
                                    name='session_variable' />
                                <div class="form-group first">
                                    <label for="username">Username</label>
                                    <input type="text" class="form-control" id="username" required
                                        name="username">

                                </div>
                                <div class="form-group last mb-4">
                                    <label for="password">Password</label>
                                    <input type="password" class="form-control" id="password" required
                                        name="password">

                                </div>

                                <input type="submit" value="Log In" class="btn-custom">


                            </form>
                            <div class="d-flex justify-content-center forgot-pass mt-3">
        <a href="../agent-forgot-password.php">I forgot my password</a>
</div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
    <script src="../dist/js/login/jquery-3.3.1.min.js"></script>
    <script src="../dist/js/login/popper.min.js"></script>
    <script src="../dist/js/login/bootstrap.min.js"></script>
    <script src="../dist/js/login/main.js"></script>
    <script>
    $(document).ready(function() {});
    </script>
</body>

</html>