<?php

include_once 'connection.php';

class Institute extends connection
{

    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Institute()
    {
        self::__construct();
    }

    private $_conn;


    function getInstituteByID($institute_id)
    {
        $conn = $this->_conn;
        $institute_id = intval($institute_id);
        if ($institute_id > 0) {
            $cond = "and c.institute_id=$institute_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_institute c
                    left join ggportal_tbl_country c1 on c1.country_id=c.institute_country_id
                    WHERE 1=1 $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getInstituteIDByName($institute_name)
    {
        $conn = $this->_conn;

        $query = "SELECT i.institute_id
                    FROM ggportal_tbl_institute i
                    WHERE i.institute_name = '$institute_name' OR i.institute_name LIKE '_$institute_name' OR i.institute_name LIKE '$institute_name' LIMIT 1;";

        $res = mysqli_query($conn, $query);

        if (!$res) {
            return false;
        }

        $row = mysqli_fetch_assoc($res);
        if (!$row || !isset($row['institute_id'])) {
            return false;
        }
        return (int)$row['institute_id'];
    }


    function getInstitutes($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['customer_id']) && strlen($criteria['customer_id']) > 0) {
            $customer_id = intval($criteria['customer_id']);
            $cond .= " and c.customer_id  = $customer_id ";
        }

        $query = "select * from ggportal_tbl_institute s
        left join ggportal_tbl_country c1 on c1.country_id=s.institute_country_id
         where 1=1  $cond order by s.institute_id DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getCommissions($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['customer_id']) && strlen($criteria['customer_id']) > 0) {
            $customer_id = intval($criteria['customer_id']);
            $cond .= " and c.customer_id  = $customer_id ";
        }

        $query = "select *,c1.country_name from ggportal_tbl_commission c left join ggportal_tbl_institute s on c.institute_id=s.institute_id
        left join ggportal_tbl_country c1 on c1.country_id=s.institute_country_id
         where 1=1  $cond order by c1.country_name;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function saveInstitute($_details)
    {
        $conn = $this->_conn;
        $institute_id = intval($_details['institute_id']);


        if ($institute_id == 0) {

            $query = "INSERT INTO ggportal_tbl_institute SET
                  institute_name = '" . mysqli_real_escape_string($conn, $_details['institute_name']) . "'               
                    ,institute_country_id = '" . mysqli_real_escape_string($conn, $_details['institute_country_id']) . "'              
                    ,institute_type = '" . mysqli_real_escape_string($conn, $_details['institute_type']) . "'              
                    ,institute_state = '" . mysqli_real_escape_string($conn, $_details['institute_state']) . "'              
                    ,institute_city = '" . mysqli_real_escape_string($conn, $_details['institute_city']) . "'
                    ,web_url = '" . mysqli_real_escape_string($conn, $_details['web_url']) . "'                         
                    ,logo_url = '" . mysqli_real_escape_string($conn, $_details['logo_url']) . "'                         
                    ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                         
                    ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'                   
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_institute SET     
            institute_name = '" . mysqli_real_escape_string($conn, $_details['institute_name']) . "'               
            ,institute_country_id = '" . mysqli_real_escape_string($conn, $_details['institute_country_id']) . "'              
            ,institute_type = '" . mysqli_real_escape_string($conn, $_details['institute_type']) . "'              
            ,institute_state = '" . mysqli_real_escape_string($conn, $_details['institute_state']) . "'              
            ,institute_city = '" . mysqli_real_escape_string($conn, $_details['institute_city']) . "'
            ,web_url = '" . mysqli_real_escape_string($conn, $_details['web_url']) . "'                         
            ,logo_url = '" . mysqli_real_escape_string($conn, $_details['logo_url']) . "'                         
            ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                         
            ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'
            WHERE institute_id = $institute_id;        
            ";
        }



        // return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($institute_id == 0) {
                $institute_id = mysqli_insert_id($conn);
            }
            return $institute_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteInstitute($institute_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_institute` WHERE `institute_id` = $institute_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $institute_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function getCommissionByID($commission_id)
    {
        $conn = $this->_conn;
        $commission_id = intval($commission_id);
        if ($commission_id > 0) {
            $cond = "and c.commission_id=$commission_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_commission c
                    left join ggportal_tbl_institute c1 on c1.institute_id=c.institute_id
                    WHERE 1=1 $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function saveCommission($_details)
    {
        $conn = $this->_conn;
        $commission_id = intval($_details['commission_id']);


        if ($commission_id == 0) {

            $query = "INSERT INTO ggportal_tbl_commission SET
                  institute_id = " . mysqli_real_escape_string($conn, $_details['institute_id']) . "           
                    ,rate = '" . mysqli_real_escape_string($conn, $_details['rate']) . "'              
                            
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_commission SET     
            institute_id = " . mysqli_real_escape_string($conn, $_details['institute_id']) . "               
            ,rate = '" . mysqli_real_escape_string($conn, $_details['rate']) . "'              
            WHERE commission_id = $commission_id;        
            ";
        }

        // return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($commission_id == 0) {
                $commission_id = mysqli_insert_id($conn);
            }
            return $commission_id;
        } else {
            return mysqli_error($conn);
        }
    }

    //Delete Commission
    function deleteCommission($commission_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_commission` WHERE `commission_id` = $commission_id;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $commission_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteAllCommission()
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_commission`;";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $res;
        } else {
            return mysqli_error($conn);
        }
    }
    public function getInstituteNameById($institute_id) {
        $conn = $this->_conn;
        $institute_id = intval($institute_id);
        
        // Initialize the institute name variable
        $institute_name = "";
    
        // Check if the institute_id is valid
        if ($institute_id > 0) {
            // Use prepared statement to prevent SQL injection
            $query = "SELECT institute_name FROM ggportal_tbl_institute WHERE institute_id = ?";
            $stmt = mysqli_prepare($conn, $query);
    
            if ($stmt) {
                // Bind the institute_id parameter
                mysqli_stmt_bind_param($stmt, "i", $institute_id);
                
                // Execute the query
                mysqli_stmt_execute($stmt);
                
                // Bind the result
                mysqli_stmt_bind_result($stmt, $institute_name);
                
                // Fetch the result
                mysqli_stmt_fetch($stmt);
                
                // Close the statement
                mysqli_stmt_close($stmt);
            } else {
                // Error handling for failed prepared statement
                $error_message = mysqli_error($conn);
                echo "Error creating prepared statement: $error_message";
                // You can log or handle this error as needed
            }
        }
        
        // Return the institute name
        return $institute_name;
    }
}
