$(function() {
    $('.select2').select2();

    var monkeyList = new List('test-list', {
        valueNames: ['name'],
        page: 10,
        pagination: true
    });

    $('.show_results').on('change', function() {
        var searchTerm = $(this).val();

        monkeyList = new List('test-list', {
            valueNames: ['name'],
            page: searchTerm,
            pagination: true
        });
    });
    // .fav_btn button on click function
    $('.fav_btn').on('click', function() {
        var program_id = $(this).data('program_id');
        var fav_btn = $(this);
        var favorite = localStorage.getItem('fav_' + program_id) === 'true';
        favorite = !favorite;
        localStorage.setItem('fav_' + program_id, favorite);
        // console.log({
        //     program_id
        // });
        // var fav_btn = $(this);
        $.ajax({
            type: "POST",
            url: "controller/program-controller.php",
            data: {
                "program_id": program_id,
                "updateFav": favorite ? 1 : 0
            },
            dataType: 'json',
            success: function(data) {

                if (data.data == -404) {
                    toastr.warning('Remove from favorite list');
                    fav_btn.find('i').removeClass('text-danger').addClass('text-gray');
                    localStorage.removeItem('fav_' + program_id);
                }
                if (data.data > 0) {
                    toastr.success('Program added to your favourite list');
                    fav_btn.find('i').removeClass('text-gray').addClass('text-danger');
                    localStorage.setItem('fav_' + program_id, 'true');
                }
                // console.log(data);
                window.location.reload();
            }
        });
    });


});