$(document).ready(function () {

    //console.log("ready!");
      bsCustomFileInput.init();
      //Initialize Select2 Elements
      $('.select2bs4').select2({
            theme: 'bootstrap4'
      });

      $('#reset-button').click(function() {
        window.location.reload();
    });


        /*Save Institute*/
      $('#quickform').validate({
          rules: {
            institute_id: {
                  required: true,
              },
              rate: {
                  required: true,
              }
          },
          messages: {

            institute_id: {
                  required: "Please Enter a value",
              },
              rate: {
                  required: "Please Enter a value",
              }
          },
          errorElement: 'span',
          errorPlacement: function (error, element) {
              error.addClass('invalid-feedback');
              element.closest('.form-group').append(error);
          },
          highlight: function (element, errorClass, validClass) {
              $(element).addClass('is-invalid');
          },
          unhighlight: function (element, errorClass, validClass) {
              $(element).removeClass('is-invalid');
          },
          submitHandler: function (form){
              //console.log(new FormData(form));
              var formData = new FormData(form);
              formData.append('commissionSave', '1');
              $.ajax({
                  type:"POST",
                  url:"controller/institute-controller.php",
                  data:formData,
                  dataType: 'json',
                  mimeType: "multipart/form-data",
                  contentType: false,
                  processData: false,
                  cache: false,
                  success:function(data){
                      console.log(data);
                      if(data.data>0){
                          toastr.success('Saved Successfully');
                          window.location.href = 'commission-list.php';
                        //   $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                      }else {
                        toastr.error(data.status);
                      }

                  }
              });
          }
      });

  });