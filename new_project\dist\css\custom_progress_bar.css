.progress {
  display: block;
  width: 100%;
  height: 12px;
  position: relative;
  z-index: 1;
  padding-right: 8px;
  padding-top: 2px;
}
@media (max-width: 991px) {
  .progress {
    height: 10px;
  }
}
.progress[value] {
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
}
.progress[value]::-ms-fill {
  background-color: #4747ff;
  border: 0;
}
.progress[value]::-moz-progress-bar {
  background-color: #4747ff;
  margin-right: 8px;
}
.progress[value]::-webkit-progress-inner-element {
  background-color: #eee;
}
.progress[value]::-webkit-progress-value {
  background-color: #4747ff;
}
.progress[value]::-webkit-progress-bar {
  background-color: #eee;
}

.progress-circle {
  width: 24px;
  height: 24px;
  position: absolute;
  right: 3px;
  top: -5px;
  z-index: 5;
  border-radius: 50%;
}
.progress-circle:before {
  /*content: "\2713";*/
  width: 6px;
  height: 6px;
  color: white;
  /*background: white;*/
  font-weight: bolder;
  border-radius: 50%;
  display: block;
  transform: translate(-50%, -50%);
  position: absolute;
}

.progress-group {
  margin-top: 36px;
}
@media (max-width: 991px) {
  .progress-group {
    margin-left: -18px;
    margin-right: -18px;
    flex-basis: 100%;
    padding: 18px;
  }
}
@media (max-width: 768px) {
  .progress-group {
    padding: 18px 18px 0;
    margin-bottom: 12px;
  }

  .top-mar {
    margin-top: -50px;
  }
}
.top-mar {
  margin-top: -37px;
}

.progress-group .title {
  margin-bottom: 18px;
}
.progress-group .wrapper {
  background: white;
  border: 1px solid #eee;
  border-radius: 12px;
  height: 14px;
  display: flex;
  filter: drop-shadow(0 0 1px rgba(0, 0, 0, 0.3));
}
.progress-group .step {
  width: 20%;
  position: relative;
}
.progress-group .step:after {
  content: "";
  height: 30px;
  width: 30px;
  background: white;
  border-radius: 50%;
  display: block;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.progress-group .step:first-of-type .progress {
  padding-left: 4px;
}
.progress-group .step:first-of-type .progress[value]::-moz-progress-bar {
  border-radius: 5px 0 0 5px;
}
.progress-group .step:first-of-type .progress[value]::-webkit-progress-value {
  transition: width 0.5s;
  border-radius: 5px 0 0 5px;
}
.progress-group .step:not(:first-of-type) .progress[value]::-moz-progress-bar {
  border-radius: 0;
}
.progress-group
  .step:not(:first-of-type)
  .progress[value]::-webkit-progress-value {
  border-radius: 0;
}
.progress-group .step .progress[value] + .progress-circle {
  background: #eee;
}

.progress-group .step.pg-success .progress[value]::-moz-progress-bar {
  background-color: #0585ad;
}
.progress-group .step.pg-success .progress[value]::-webkit-progress-value {
  background-color: #0585ad;
}
.progress-group .step.pg-success .progress[value="100"] + .progress-circle {
  background-color: #0585ad;
}
.progress-group .step.pg-current .progress[value]::-moz-progress-bar {
  background-color: #0585ad;
}
.progress-group .step.pg-current .progress[value]::-webkit-progress-value {
  background-color: #0585ad;
}
.progress-group .step.pg-current .progress[value="100"] + .progress-circle {
  background-color: #0585ad;
}
.progress-group .step.pg-success1 .progress[value]::-moz-progress-bar {
  background-color: #95BC2E;
}
.progress-group .step.pg-success1 .progress[value]::-webkit-progress-value {
  background-color: #95BC2E;
}
.progress-group .step.pg-success1 .progress[value="100"] + .progress-circle {
  background-color: #95BC2E;
}
.progress-group .step.pg-current .progress[value]::-moz-progress-bar {
  background-color: #95BC2E;
}
.progress-group .step.pg-current .progress[value]::-webkit-progress-value {
  background-color: #95BC2E;
}
.progress-group .step.pg-current .progress[value="100"] + .progress-circle {
  background-color: #95BC2E;
}
.progress-group .step.pg-warning .progress[value]::-moz-progress-bar {
  background-color: #ffc107;
}
.progress-group .step.pg-warning .progress[value]::-webkit-progress-value {
  background-color: #ffc107;
}
.progress-group .step.pg-warning .progress[value="100"] + .progress-circle {
  background-color: #ffc107;
}
.progress-group .step.pg-danger .progress[value]::-moz-progress-bar {
  background-color: #d60017;
}
.progress-group .step.pg-danger .progress[value]::-webkit-progress-value {
  background-color: #d60017;
}
.progress-group .step.pg-danger .progress[value="100"] + .progress-circle {
  background-color: #d60017;
}
.pg-danger .progress-circle:before {
  content: "\2718";
  left: 40%;
  top: 13%;
}
.pg-success .progress-circle:before {
  /* content: "\2713"; */
  left: 40%;
  top: 13%;
}

.pg-complete .progress-circle:before {
  content: "\2713";
  left: 40%;
  top: 13%;
}

.pg-current .progress-circle:before {
  /*font-family: "Font Awesome 5 Free";
  color: #002D88;*/
  content: "";
  background: url("../../dist/img/perdon-shipping.png") no-repeat 0 0;
  border-radius: 0%;
  background-size: 90px !important;
  width: 86px !important;
  height: 114px !important;
  z-index: 5;
  top: -10px;
  left: 10px;
}

.pg-warning .progress-circle:before {
  /* content: "\27F3"; */
  top: 2px;
  left: 8px;
}
.progress-labels {
  display: flex;
  justify-content: space-between;
}
.progress-labels .label {
  text-align: right;
  text-transform: uppercase;
  margin: 15px -12px 0px 0px;
  width: 20%;
  font-size: 11px;
  padding-right: 0px;
  font-weight: 600;
  opacity: 0.7;
}

.page-title {
  letter-spacing: -0.05rem;
}

/*
.progress-group .step.step01 .progress[value]::-moz-progress-bar {
  background-color: #010C4E;
}
.progress-group .step.step01 .progress[value]::-webkit-progress-value {
  background-color: #010C4E;
}
.progress-group .step.step01 .progress[value="100"] + .progress-circle {
  background-color: #010C4E;
}
.progress-group .step.step02 .progress[value]::-moz-progress-bar {
  background-color: #002D88;
}
.progress-group .step.step02 .progress[value]::-webkit-progress-value {
  background-color: #002D88;
}
.progress-group .step.step02 .progress[value="100"] + .progress-circle {
  background-color: #002D88;
}
.progress-group .step.step03 .progress[value]::-moz-progress-bar {
  background-color: #017AA9;
}
.progress-group .step.step03 .progress[value]::-webkit-progress-value {
  background-color: #017AA9;
}
.progress-group .step.step03 .progress[value="100"] + .progress-circle {
  background-color: #017AA9;
}
.progress-group .step.pg-success .progress[value]::-moz-progress-bar {
  background-color: #03C2B2;
}
.progress-group .step.pg-success .progress[value]::-webkit-progress-value {
  background-color: #03C2B2;
}
.progress-group .step.pg-success .progress[value="100"] + .progress-circle {
  background-color: #03C2B2;
}
.progress-group .step.step05 .progress[value]::-moz-progress-bar {
  background-color: #05E8B0;
}
.progress-group .step.step05 .progress[value]::-webkit-progress-value {
  background-color: #05E8B0;
}
.progress-group .step.step05 .progress[value="100"] + .progress-circle {
  background-color: #05E8B0;
}*/
