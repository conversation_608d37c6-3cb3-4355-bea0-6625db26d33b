gapi.loaded_0(function(_){var window=this;
var fa,ha,ja,ka,na,pa,Ea,Sa;_.ea=function(a){return function(){return _.aa[a].apply(this,arguments)}};_.aa=[];fa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
ja=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};ka=ja(this);na=function(a,b){if(b)a:{var c=ka;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
na("Symbol",function(a){if(a)return a;var b=function(f,h){this.KR=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.KR};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
na("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ka[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return pa(fa(this))}})}return a});pa=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.Ba=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:fa(a)}};_.Ca="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if("function"==typeof Object.setPrototypeOf)Ea=Object.setPrototypeOf;else{var Ja;a:{var La={a:!0},Qa={};try{Qa.__proto__=La;Ja=Qa.a;break a}catch(a){}Ja=!1}Ea=Ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
_.Ra=Ea;Sa=function(a,b,c){if(null==a)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};na("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Sa(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});
var Ta=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},Ua="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Ta(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ua});
na("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return"object"===m&&null!==l||"function"===m}function d(l){if(!Ta(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(2!=n.get(l)||3!=n.get(m))return!1;n.delete(l);n.set(m,4);return!n.has(l)&&4==n.get(m)}catch(q){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Ba=(h+=Math.random()+1).toString();if(l){l=_.Ba(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("b");d(l);if(!Ta(l,f))throw Error("c`"+l);l[f][this.Ba]=m;return this};k.prototype.get=function(l){return c(l)&&Ta(l,f)?l[f][this.Ba]:void 0};k.prototype.has=function(l){return c(l)&&Ta(l,f)&&Ta(l[f],this.Ba)};k.prototype.delete=
function(l){return c(l)&&Ta(l,f)&&Ta(l[f],this.Ba)?delete l[f][this.Ba]:!1};return k});
na("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var k=Object.seal({x:4}),l=new a(_.Ba([[k,"s"]]));if("s"!=l.get(k)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||"s"!=n.value[1])return!1;n=m.next();return n.done||4!=n.value[0].x||"t"!=n.value[1]||!m.next().done?!1:!0}catch(q){return!1}}())return a;var b=new WeakMap,c=function(k){this.Bf={};this.Ze=
f();this.size=0;if(k){k=_.Ba(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=0===k?0:k;var m=d(this,k);m.list||(m.list=this.Bf[m.id]=[]);m.ye?m.ye.value=l:(m.ye={next:this.Ze,vj:this.Ze.vj,head:this.Ze,key:k,value:l},m.list.push(m.ye),this.Ze.vj.next=m.ye,this.Ze.vj=m.ye,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.ye&&k.list?(k.list.splice(k.index,1),k.list.length||delete this.Bf[k.id],k.ye.vj.next=k.ye.next,k.ye.next.vj=
k.ye.vj,k.ye.head=null,this.size--,!0):!1};c.prototype.clear=function(){this.Bf={};this.Ze=this.Ze.vj=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).ye};c.prototype.get=function(k){return(k=d(this,k).ye)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;"object"==m||"function"==m?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k.Bf[m];if(n&&Ta(k.Bf,m))for(k=0;k<n.length;k++){var q=n[k];if(l!==l&&q.key!==q.key||l===q.key)return{id:m,list:n,index:k,ye:q}}return{id:m,list:n,index:-1,ye:void 0}},e=function(k,l){var m=k.Ze;return pa(function(){if(m){for(;m.head!=k.Ze;)m=m.vj;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.vj=k.next=k.head=k},h=0;return c});
na("Set",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(_.Ba([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.va=new Map;if(c){c=
_.Ba(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.va.size};b.prototype.add=function(c){c=0===c?0:c;this.va.set(c,c);this.size=this.va.size;return this};b.prototype.delete=function(c){c=this.va.delete(c);this.size=this.va.size;return c};b.prototype.clear=function(){this.va.clear();this.size=0};b.prototype.has=function(c){return this.va.has(c)};b.prototype.entries=function(){return this.va.entries()};b.prototype.values=function(){return this.va.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.va.forEach(function(f){return c.call(d,f,f,e)})};return b});var Wa=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};na("Array.prototype.entries",function(a){return a?a:function(){return Wa(this,function(b,c){return[b,c]})}});
na("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var h=d[f];if(b.call(c,h,f,d)){b=h;break a}}b=void 0}return b}});na("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}});na("Array.prototype.keys",function(a){return a?a:function(){return Wa(this,function(b){return b})}});
na("Array.prototype.values",function(a){return a?a:function(){return Wa(this,function(b,c){return c})}});na("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(k){return k};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});
na("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Ta(b,d)&&c.push(b[d]);return c}});na("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});na("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
na("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==Sa(this,b,"includes").indexOf(b,c||0)}});na("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Ta(b,d)&&c.push([d,b[d]]);return c}});na("Array.prototype.flat",function(a){return a?a:function(b){b=void 0===b?1:b;for(var c=[],d=0;d<this.length;d++){var e=this[d];Array.isArray(e)&&0<b?(e=Array.prototype.flat.call(e,b-1),c.push.apply(c,e)):c.push(e)}return c}});_.r={};
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Xa=_.Xa||{};_.D=this||self;_.Ya="closure_uid_"+(1E9*Math.random()>>>0);_.I=function(a,b){a=a.split(".");var c=_.D;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.$a=function(a,b){function c(){}c.prototype=b.prototype;a.H=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.zq=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};
_.bb=window.osapi=window.osapi||{};

window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0}});

var ib,Fb,Hb,Ib,Jb;_.cb=function(a,b){return _.aa[a]=b};_.gb=function(a){if(a instanceof _.eb)return a.cP;throw Error("e");};ib=function(a){return new _.hb(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.jb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.jb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.BJ=b);this.sP=!0};_.lb=function(a,b){return 0<=(0,_.kb)(a,b)};
_.mb=function(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.nb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.ob=function(a){var b=arguments.length;if(1==b&&Array.isArray(arguments[0]))return _.ob.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};_.rb=function(a){return _.pb(_.qb,a)};_.sb=function(){return _.rb("Opera")};_.tb=function(){return _.rb("Trident")||_.rb("MSIE")};_.vb=function(){return _.rb("Firefox")||_.rb("FxiOS")};
_.wb=function(){return(_.rb("Chrome")||_.rb("CriOS"))&&!_.rb("Edge")};_.xb=function(a){if(null!==a&&void 0!==a.tagName){if("script"===a.tagName.toLowerCase())throw Error("l");if("style"===a.tagName.toLowerCase())throw Error("m");}};_.yb=function(){return _.rb("iPhone")&&!_.rb("iPod")&&!_.rb("iPad")};_.zb=function(){return _.yb()||_.rb("iPad")||_.rb("iPod")};_.Bb=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b};
_.O=function(a,b){a.prototype=(0,_.Ca)(b.prototype);a.prototype.constructor=a;if(_.Ra)(0,_.Ra)(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.H=b.prototype};_.Cb=function(){};_.Db=function(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"};_.Eb=function(a){var b=_.Db(a);return"array"==b||"object"==b&&"number"==typeof a.length};Fb=0;
_.Gb=function(a){return Object.prototype.hasOwnProperty.call(a,_.Ya)&&a[_.Ya]||(a[_.Ya]=++Fb)};Hb=function(a,b,c){return a.call.apply(a.bind,arguments)};Ib=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};
_.R=function(a,b,c){_.R=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?Hb:Ib;return _.R.apply(null,arguments)};Jb=function(a){return a};
/*

 SPDX-License-Identifier: Apache-2.0
*/
_.Kb={};
_.Mb=function(){};_.eb=function(a){this.cP=a};_.O(_.eb,_.Mb);_.eb.prototype.toString=function(){return this.cP};_.Nb=new _.eb("about:invalid#zTSz",_.Kb);
_.hb=function(a){this.ji=a};_.Pb=[ib("data"),ib("http"),ib("https"),ib("mailto"),ib("ftp"),new _.hb(function(a){return/^[^:]*([/?#]|$)/.test(a)})];
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.$a(_.jb,Error);_.jb.prototype.name="CustomError";
var Qb;
_.kb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Rb=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;0>c&&(c=Math.max(0,a.length+c));if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.lastIndexOf(b,c);for(;0<=c;c--)if(c in a&&a[c]===b)return c;return-1};_.Sb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Tb=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d};_.Ub=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};
_.Vb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};
var Yb,Zb=function(){if(void 0===Yb){var a=null,b=_.D.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("gapi#html",{createHTML:Jb,createScript:Jb,createScriptURL:Jb})}catch(c){_.D.console&&_.D.console.error(c.message)}Yb=a}return Yb};
var ac,$b;_.dc=function(a,b){this.YQ=a===$b&&b||"";this.UT=ac};_.dc.prototype.Zh=!0;_.dc.prototype.If=function(){return this.YQ};_.ec=function(a){return a instanceof _.dc&&a.constructor===_.dc&&a.UT===ac?a.YQ:"type_error:Const"};_.fc=function(a){return new _.dc($b,a)};ac={};$b={};
var gc;_.hc=function(a,b){this.JE=b===gc?a:""};_.g=_.hc.prototype;_.g.Zh=!0;_.g.If=function(){return this.JE.toString()};_.g.jD=!0;_.g.uk=function(){return 1};_.g.toString=function(){return this.JE+""};_.jc=function(a){return _.ic(a).toString()};_.ic=function(a){if(a instanceof _.hc&&a.constructor===_.hc)return a.JE;_.Db(a);return"type_error:TrustedResourceUrl"};_.lc=function(a){return _.kc(_.ec(a))};gc={};_.kc=function(a){var b=Zb();a=b?b.createScriptURL(a):a;return new _.hc(a,gc)};
var qc,rc,sc,tc,uc,wc,xc,zc;_.mc=function(a,b){return 0==a.lastIndexOf(b,0)};_.nc=function(a){return/^[\s\xa0]*$/.test(a)};_.oc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
_.yc=function(a,b){if(b)a=a.replace(qc,"&amp;").replace(rc,"&lt;").replace(sc,"&gt;").replace(tc,"&quot;").replace(uc,"&#39;").replace(wc,"&#0;");else{if(!xc.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(qc,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(rc,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(sc,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(tc,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(uc,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(wc,"&#0;"))}return a};qc=/&/g;rc=/</g;sc=/>/g;tc=/"/g;uc=/'/g;
wc=/\x00/g;xc=/[\x00&<>"']/;_.pb=function(a,b){return-1!=a.indexOf(b)};
_.Ac=function(a,b){var c=0;a=(0,_.oc)(String(a)).split(".");b=(0,_.oc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==f[0].length&&0==h[0].length)break;c=zc(0==f[1].length?0:parseInt(f[1],10),0==h[1].length?0:parseInt(h[1],10))||zc(0==f[2].length,0==h[2].length)||zc(f[2],h[2]);f=f[3];h=h[3]}while(0==c)}return c};
zc=function(a,b){return a<b?-1:a>b?1:0};
var Ec,Fc,Gc,Hc;_.Cc=function(a,b){this.IE=b===_.Bc?a:""};_.g=_.Cc.prototype;_.g.Zh=!0;_.g.If=function(){return this.IE.toString()};_.g.jD=!0;_.g.uk=function(){return 1};_.g.toString=function(){return this.IE.toString()};_.Dc=function(a){if(a instanceof _.Cc&&a.constructor===_.Cc)return a.IE;_.Db(a);return"type_error:SafeUrl"};
Ec=RegExp('^(?:audio/(?:3gpp2|3gpp|aac|L16|midi|mp3|mp4|mpeg|oga|ogg|opus|x-m4a|x-matroska|x-wav|wav|webm)|font/\\w+|image/(?:bmp|gif|jpeg|jpg|png|tiff|webp|x-icon)|video/(?:mpeg|mp4|ogg|webm|quicktime|x-matroska))(?:;\\w+=(?:\\w+|"[\\w;,= ]+"))*$',"i");Fc=/^data:(.*);base64,[a-z0-9+\/]+=*$/i;Gc=function(a){a=String(a);a=a.replace(/(%0A|%0D)/g,"");var b=a.match(Fc);return b&&Ec.test(b[1])?new _.Cc(a,_.Bc):null};Hc=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;
_.Ic=function(a){if(a instanceof _.Cc)return a;a="object"==typeof a&&a.Zh?a.If():String(a);return Hc.test(a)?new _.Cc(a,_.Bc):Gc(a)};_.Kc=function(a,b){if(a instanceof _.Cc)return a;a="object"==typeof a&&a.Zh?a.If():String(a);if(b&&/^data:/i.test(a)&&(b=Gc(a)||_.Jc,b.If()==a))return b;Hc.test(a)||(a="about:invalid#zClosurez");return new _.Cc(a,_.Bc)};_.Bc={};_.Jc=new _.Cc("about:invalid#zClosurez",_.Bc);
_.Lc={};_.Mc=function(a,b){this.HE=b===_.Lc?a:"";this.Zh=!0};_.Mc.prototype.If=function(){return this.HE};_.Mc.prototype.toString=function(){return this.HE.toString()};_.Nc=new _.Mc("",_.Lc);_.Oc=RegExp("^[-,.\"'%_!#/ a-zA-Z0-9\\[\\]]+$");_.Pc=RegExp("\\b(url\\([ \t\n]*)('[ -&(-\\[\\]-~]*'|\"[ !#-\\[\\]-~]*\"|[!#-&*-\\[\\]-~]*)([ \t\n]*\\))","g");
_.Qc=RegExp("\\b(calc|cubic-bezier|fit-content|hsl|hsla|linear-gradient|matrix|minmax|repeat|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?|var)\\([-+*/0-9a-zA-Z.%#\\[\\], ]+\\)","g");
_.Rc={};_.Sc=function(a,b){this.GE=b===_.Rc?a:"";this.Zh=!0};_.Uc=function(a){a=_.ec(a);return 0===a.length?Tc:new _.Sc(a,_.Rc)};_.Sc.prototype.If=function(){return this.GE};_.Sc.prototype.toString=function(){return this.GE.toString()};var Tc=new _.Sc("",_.Rc);
a:{var Vc=_.D.navigator;if(Vc){var Wc=Vc.userAgent;if(Wc){_.qb=Wc;break a}}_.qb=""}
;var Xc;Xc={};_.Yc=function(a,b,c){this.FE=c===Xc?a:"";this.vV=b;this.Zh=this.jD=!0};_.Yc.prototype.uk=function(){return this.vV};_.Yc.prototype.If=function(){return this.FE.toString()};_.Yc.prototype.toString=function(){return this.FE.toString()};_.Zc=function(a){if(a instanceof _.Yc&&a.constructor===_.Yc)return a.FE;_.Db(a);return"type_error:SafeHtml"};_.ad=function(a){if(a instanceof _.Yc)return a;var b="object"==typeof a,c=null;b&&a.jD&&(c=a.uk());return _.$c(_.yc(b&&a.Zh?a.If():String(a)),c)};
_.$c=function(a,b){var c=Zb();a=c?c.createHTML(a):a;return new _.Yc(a,b,Xc)};_.bd=new _.Yc(_.D.trustedTypes&&_.D.trustedTypes.emptyHTML||"",0,Xc);_.cd=_.$c("<br>",0);
var dd=function(a){dd[" "](a);return a};dd[" "]=_.Cb;_.ed=function(a,b){try{return dd(a[b]),!0}catch(c){}return!1};_.fd=function(a,b,c){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:a[b]=c(b)};
var wd,xd,Cd,Ed;_.gd=_.sb();_.hd=_.tb();_.id=_.rb("Edge");_.jd=_.id||_.hd;_.kd=_.rb("Gecko")&&!(_.pb(_.qb.toLowerCase(),"webkit")&&!_.rb("Edge"))&&!(_.rb("Trident")||_.rb("MSIE"))&&!_.rb("Edge");_.ld=_.pb(_.qb.toLowerCase(),"webkit")&&!_.rb("Edge");_.md=_.ld&&_.rb("Mobile");_.nd=_.rb("Macintosh");_.od=_.rb("Windows");_.pd=_.rb("Linux")||_.rb("CrOS");_.rd=_.rb("Android");_.sd=_.yb();_.td=_.rb("iPad");_.ud=_.rb("iPod");_.vd=_.zb();wd=function(){var a=_.D.document;return a?a.documentMode:void 0};
a:{var yd="",zd=function(){var a=_.qb;if(_.kd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.id)return/Edge\/([\d\.]+)/.exec(a);if(_.hd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.ld)return/WebKit\/(\S+)/.exec(a);if(_.gd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();zd&&(yd=zd?zd[1]:"");if(_.hd){var Ad=wd();if(null!=Ad&&Ad>parseFloat(yd)){xd=String(Ad);break a}}xd=yd}_.Bd=xd;Cd={};_.Dd=function(a){return _.fd(Cd,a,function(){return 0<=_.Ac(_.Bd,a)})};
if(_.D.document&&_.hd){var Fd=wd();Ed=Fd?Fd:parseInt(_.Bd,10)||void 0}else Ed=void 0;_.Gd=Ed;
try{(new self.OffscreenCanvas(0,0)).getContext("2d")}catch(a){}_.Hd=_.hd||_.ld;
_.Id=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};
var Jd,Nd;Jd=_.Id(function(){var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);b=a.firstChild.firstChild;a.innerHTML=_.Zc(_.bd);return!b.parentElement});_.Kd=function(a,b){if(Jd())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=_.Zc(b)};_.Ld=function(a,b){b=b instanceof _.Cc?b:_.Kc(b);a.href=_.Dc(b)};
_.Md=function(a,b,c,d){a=a instanceof _.Cc?a:_.Kc(a);b=b||_.D;c=c instanceof _.dc?_.ec(c):c||"";return void 0!==d?b.open(_.Dc(a),c,d):b.open(_.Dc(a),c)};Nd=/^[\w+/_-]+[=]{0,2}$/;_.Od=function(a,b){b=(b||_.D).document;return b.querySelector?(a=b.querySelector(a))&&(a=a.nonce||a.getAttribute("nonce"))&&Nd.test(a)?a:"":""};
_.Pd=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.Qd=2147483648*Math.random()|0;
var Wd,$d;_.Td=function(a){return a?new _.Rd(_.Sd(a)):Qb||(Qb=new _.Rd)};
_.Ud=function(a,b,c,d){a=d||a;b=b&&"*"!=b?String(b).toUpperCase():"";if(a.querySelectorAll&&a.querySelector&&(b||c))return a.querySelectorAll(b+(c?"."+c:""));if(c&&a.getElementsByClassName){a=a.getElementsByClassName(c);if(b){d={};for(var e=0,f=0,h;h=a[f];f++)b==h.nodeName&&(d[e++]=h);d.length=e;return d}return a}a=a.getElementsByTagName(b||"*");if(c){d={};for(f=e=0;h=a[f];f++)b=h.className,"function"==typeof b.split&&_.lb(b.split(/\s+/),c)&&(d[e++]=h);d.length=e;return d}return a};
_.Xd=function(a,b){_.nb(b,function(c,d){c&&"object"==typeof c&&c.Zh&&(c=c.If());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:Wd.hasOwnProperty(d)?a.setAttribute(Wd[d],c):_.mc(d,"aria-")||_.mc(d,"data-")?a.setAttribute(d,c):a[d]=c})};Wd={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};
_.Yd=function(a){return a?a.parentWindow||a.defaultView:window};_.ae=function(a,b){var c=b[1],d=_.Zd(a,String(b[0]));c&&("string"===typeof c?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.Xd(d,c));2<b.length&&$d(a,d,b,2);return d};
$d=function(a,b,c,d){function e(k){k&&b.appendChild("string"===typeof k?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.Eb(f)||_.Bb(f)&&0<f.nodeType)e(f);else{a:{if(f&&"number"==typeof f.length){if(_.Bb(f)){var h="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){h="function"==typeof f.item;break a}}h=!1}_.Sb(h?_.mb(f):f,e)}}};_.be=function(a){return _.Zd(document,a)};
_.Zd=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)};_.ce=function(a){if(1!=a.nodeType)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.de=function(a,b){$d(_.Sd(a),a,arguments,1)};_.ee=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.fe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.ge=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.he=function(a){return void 0!=a.children?a.children:Array.prototype.filter.call(a.childNodes,function(b){return 1==b.nodeType})};_.ie=function(a){return _.Bb(a)&&1==a.nodeType};
_.je=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Sd=function(a){return 9==a.nodeType?a:a.ownerDocument||a.document};
_.ke=function(a,b){if("textContent"in a)a.textContent=b;else if(3==a.nodeType)a.data=String(b);else if(a.firstChild&&3==a.firstChild.nodeType){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.ee(a),a.appendChild(_.Sd(a).createTextNode(String(b)))};_.Rd=function(a){this.nb=a||_.D.document||document};_.g=_.Rd.prototype;_.g.Ea=_.Td;_.g.HF=_.ea(0);_.g.Za=function(){return this.nb};_.g.N=_.ea(1);_.g.getElementsByTagName=function(a,b){return(b||this.nb).getElementsByTagName(String(a))};
_.g.ma=function(a,b,c){return _.ae(this.nb,arguments)};_.g.createElement=function(a){return _.Zd(this.nb,a)};_.g.createTextNode=function(a){return this.nb.createTextNode(String(a))};_.g.getWindow=function(){var a=this.nb;return a.parentWindow||a.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.de;_.g.canHaveChildren=_.ce;_.g.Qd=_.ee;_.g.dN=_.fe;_.g.removeNode=_.ge;_.g.YB=_.he;_.g.isElement=_.ie;_.g.contains=_.je;_.g.fi=_.ea(2);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.le=window;_.me=document;_.ne=_.le.location;_.oe=/\[native code\]/;_.pe=function(a,b,c){return a[b]=a[b]||c};_.re=function(){var a;if((a=Object.create)&&_.oe.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.se=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.te=function(a,b){a=a||{};for(var c in a)_.se(a,c)&&(b[c]=a[c])};_.ue=_.pe(_.le,"gapi",{});
_.ve=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.we=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.xe=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.ye=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");
_.Ae=function(a,b,c){_.ze(a,b,c,"add","at")};_.ze=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};
_.Be=_.pe(_.le,"___jsl",_.re());_.pe(_.Be,"I",0);_.pe(_.Be,"hel",10);
var De,Ee,Fe,Ge,He,Ie,Je;De=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Ee=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Fe=function(a){return"object"===typeof a&&/\[native code\]/.test(a.push)};
Ge=function(a,b,c){if(b&&"object"===typeof b)for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&"___goc"===d&&"undefined"===typeof b[d]||(a[d]&&b[d]&&"object"===typeof a[d]&&"object"===typeof b[d]&&!Fe(a[d])&&!Fe(b[d])?Ge(a[d],b[d]):b[d]&&"object"===typeof b[d]?(a[d]=Fe(b[d])?[]:{},Ge(a[d],b[d])):a[d]=b[d])};
He=function(a){if(a&&!/^\s+$/.test(a)){for(;0==a.charCodeAt(a.length-1);)a=a.substring(0,a.length-1);try{var b=window.JSON.parse(a)}catch(c){}if("object"===typeof b)return b;try{b=(new Function("return ("+a+"\n)"))()}catch(c){}if("object"===typeof b)return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return"object"===typeof b?b:{}}};
Ie=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&"undefined"===typeof a[a.length-1].___goc&&(c=a.pop());Ge(c,b);a.push(c)};
Je=function(a){Ee(!0);var b=window.___gcfg,c=De("cu"),d=window.___gu;b&&b!==d&&(Ie(c,b),window.___gu=b);b=De("cu");var e=document.scripts||document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,De("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&0==k.src.indexOf(f[l])&&d.push(k);0==d.length&&0<e.length&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=
f.nodeType,f=3==h||4==h?f.nodeValue:f.textContent||""):f=void 0,(f=He(f))&&b.push(f));a&&Ie(c,a);d=De("cd");a=0;for(b=d.length;a<b;++a)Ge(Ee(),d[a],!0);d=De("ci");a=0;for(b=d.length;a<b;++a)Ge(Ee(),d[a],!0);a=0;for(b=c.length;a<b;++a)Ge(Ee(),c[a],!0)};_.Ke=function(a,b){var c=Ee();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&"object"===typeof c&&d<e;++d)c=c[a[d]];return d===a.length&&void 0!==c?c:b};
_.Le=function(a,b){var c;if("string"===typeof a){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;Je(c)};
var Me=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.pe(_.Be,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};
Me&&Me();Je();
_.I("gapi.config.get",_.Ke);_.I("gapi.config.update",_.Le);

var Se,Te,Ue,Ve,We,Xe,Ye,Ze,$e,af,bf,cf,df,ef,ff,gf,hf,jf,kf,lf,mf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,zf,Af;Ue=void 0;Ve=function(a){try{return _.D.JSON.parse.call(_.D.JSON,a)}catch(b){return!1}};We=function(a){return Object.prototype.toString.call(a)};Xe=We(0);Ye=We(new Date(0));Ze=We(!0);$e=We("");af=We({});bf=We([]);
cf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if("undefined"!==d){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=We(a);if(null!=a&&"function"===typeof a.toJSON&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==bf||a.constructor!==Array&&a.constructor!==Object)&&(e!==af||a.constructor!==Array&&a.constructor!==Object)&&e!==$e&&e!==Xe&&e!==Ze&&e!==Ye))return cf(a.toJSON.call(a),c);if(null==
a)b[b.length]="null";else if(e===Xe)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":-0===a&&0>1/a&&(a="-0"),b[b.length]=String(a);else if(e===Ze)b[b.length]=String(!!Number(a));else{if(e===Ye)return cf(a.toISOString.call(a),c);if(e===bf&&We(a.length)===Xe){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=cf(a[f],c)||"null";b[b.length]="]"}else if(e==$e&&We(a.length)===Xe){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,f),
e=String.prototype.charCodeAt.call(a,f),b[b.length]="\b"===d?"\\b":"\f"===d?"\\f":"\n"===d?"\\n":"\r"===d?"\\r":"\t"===d?"\\t":"\\"===d||'"'===d?"\\"+d:31>=e?"\\u"+(e+65536).toString(16).substr(1):32<=e&&65535>=e?d:"\ufffd";b[b.length]='"'}else if("object"===d){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=cf(a[f],c),void 0!==e&&(d++&&(b[b.length]=","),b[b.length]=cf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};df=/[\0-\x07\x0b\x0e-\x1f]/;
ef=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;ff=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;gf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;hf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;jf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;kf=/[ \t\n\r]+/g;lf=/[^"]:/;mf=/""/g;nf=/true|false|null/g;of=/00/;pf=/[\{]([^0\}]|0[^:])/;qf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;rf=/[^\[,:][\[\{]/;sf=/^(\{|\}|\[|\]|,|:|0)+/;tf=/\u2028/g;
uf=/\u2029/g;
vf=function(a){a=String(a);if(df.test(a)||ef.test(a)||ff.test(a)||gf.test(a))return!1;var b=a.replace(hf,'""');b=b.replace(jf,"0");b=b.replace(kf,"");if(lf.test(b))return!1;b=b.replace(mf,"0");b=b.replace(nf,"0");if(of.test(b)||pf.test(b)||qf.test(b)||rf.test(b)||!b||(b=b.replace(sf,"")))return!1;a=a.replace(tf,"\\u2028").replace(uf,"\\u2029");b=void 0;try{b=Ue?[Ve(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&1===
b.length?b[0]:!1};wf=function(){var a=((_.D.document||{}).scripts||[]).length;if((void 0===Se||void 0===Ue||Te!==a)&&-1!==Te){Se=Ue=!1;Te=-1;try{try{Ue=!!_.D.JSON&&'{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'===_.D.JSON.stringify.call(_.D.JSON,{a:[3,!0,new Date(0)],c:function(){}})&&!0===Ve("true")&&3===Ve('[{"a":3}]')[0].a}catch(b){}Se=Ue&&!Ve("[00]")&&!Ve('"\u0007"')&&!Ve('"\\0"')&&!Ve('"\\v"')}finally{Te=a}}};_.xf=function(a){if(-1===Te)return!1;wf();return(Se?Ve:vf)(a)};
_.yf=function(a){if(-1!==Te)return wf(),Ue?_.D.JSON.stringify.call(_.D.JSON,a):cf(a)};zf=!Date.prototype.toISOString||"function"!==typeof Date.prototype.toISOString||"1970-01-01T00:00:00.000Z"!==(new Date(0)).toISOString();
Af=function(){var a=Date.prototype.getUTCFullYear.call(this);return[0>a?"-"+String(1E6-a).substr(1):9999>=a?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=zf?Af:Date.prototype.toISOString;

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var rg=function(){this.blockSize=-1};
var sg=function(){this.blockSize=-1;this.blockSize=64;this.Bc=[];this.HA=[];this.dU=[];this.Wx=[];this.Wx[0]=128;for(var a=1;a<this.blockSize;++a)this.Wx[a]=0;this.Ez=this.Jo=0;this.reset()};_.$a(sg,rg);sg.prototype.reset=function(){this.Bc[0]=**********;this.Bc[1]=**********;this.Bc[2]=**********;this.Bc[3]=271733878;this.Bc[4]=**********;this.Ez=this.Jo=0};
var tg=function(a,b,c){c||(c=0);var d=a.dU;if("string"===typeof b)for(var e=0;16>e;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;16>e;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(e=16;80>e;e++){var f=d[e-3]^d[e-8]^d[e-14]^d[e-16];d[e]=(f<<1|f>>>31)&**********}b=a.Bc[0];c=a.Bc[1];var h=a.Bc[2],k=a.Bc[3],l=a.Bc[4];for(e=0;80>e;e++){if(40>e)if(20>e){f=k^c&(h^k);var m=**********}else f=c^h^k,m=**********;else 60>e?(f=c&h|k&(c|h),
m=**********):(f=c^h^k,m=**********);f=(b<<5|b>>>27)+f+l+m+d[e]&**********;l=k;k=h;h=(c<<30|c>>>2)&**********;c=b;b=f}a.Bc[0]=a.Bc[0]+b&**********;a.Bc[1]=a.Bc[1]+c&**********;a.Bc[2]=a.Bc[2]+h&**********;a.Bc[3]=a.Bc[3]+k&**********;a.Bc[4]=a.Bc[4]+l&**********};
sg.prototype.update=function(a,b){if(null!=a){void 0===b&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.HA,f=this.Jo;d<b;){if(0==f)for(;d<=c;)tg(this,a,d),d+=this.blockSize;if("string"===typeof a)for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){tg(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){tg(this,e);f=0;break}}this.Jo=f;this.Ez+=b}};
sg.prototype.digest=function(){var a=[],b=8*this.Ez;56>this.Jo?this.update(this.Wx,56-this.Jo):this.update(this.Wx,this.blockSize-(this.Jo-56));for(var c=this.blockSize-1;56<=c;c--)this.HA[c]=b&255,b/=256;tg(this,this.HA);for(c=b=0;5>c;c++)for(var d=24;0<=d;d-=8)a[b]=this.Bc[c]>>d&255,++b;return a};
_.ug=function(){this.bH=new sg};_.g=_.ug.prototype;_.g.reset=function(){this.bH.reset()};_.g.qR=function(a){this.bH.update(a)};_.g.jK=function(){return this.bH.digest()};_.g.Pt=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=0,d=a.length;c<d;++c)b.push(a.charCodeAt(c));this.qR(b)};_.g.Jh=function(){for(var a=this.jK(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};

_.zh=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.Ah=function(a){var b=_.zh();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&"object"===typeof b&&c<d;++c)b=b[a[c]];return c===a.length&&void 0!==b?b:void 0};

var Dh;_.Bh=function(a,b,c,d,e){for(var f=0,h=a.length,k;f<h;){var l=f+(h-f>>>1);var m=c?b.call(e,a[l],l,a):b(d,a[l]);0<m?f=l+1:(h=l,k=!m)}return k?f:-f-1};_.Ch=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};Dh=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.Eh=function(a){var b=_.Ah("googleapis.config/sessionIndex");"string"===typeof b&&254<b.length&&(b=null);null==b&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&254<b.length&&(b=null);if(null==b){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&254<b.length&&(b=null);null==b&&(a=a||window.location.href,b=_.ve(a,"authuser")||null,null==b&&(b=(b=a.match(Dh))?b[1]:null));if(null==b)return null;b=String(b);254<b.length&&(b=null);return b};

var Wh,Vh,bi,ci,Xh,$h,Yh,di,Zh;_.ai=function(){if(Vh){var a=new _.le.Uint32Array(1);Wh.getRandomValues(a);a=Number("0."+a[0])}else a=Xh,a+=parseInt(Yh.substr(0,20),16),Yh=Zh(Yh),a/=$h+Math.pow(16,20);return a};Wh=_.le.crypto;Vh=!1;bi=0;ci=0;Xh=1;$h=0;Yh="";di=function(a){a=a||_.le.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Xh=Xh*b%$h;0<bi&&++ci==bi&&_.ze(_.le,"mousemove",di,"remove","de")};Zh=function(a){var b=new _.ug;b.Pt(a);return b.Jh()};
Vh=!!Wh&&"function"==typeof Wh.getRandomValues;Vh||($h=1E6*(screen.width*screen.width+screen.height),Yh=Zh(_.me.cookie+"|"+_.me.location+"|"+(new Date).getTime()+"|"+Math.random()),bi=_.Ah("random/maxObserveMousemove")||0,0!=bi&&_.Ae(_.le,"mousemove",di));

var Yk,Zk,$k,al,bl,cl,dl,el,fl,gl,hl,il,ml,nl,ol,pl,ql,rl,sl,tl;_.Xk=function(a,b){if(!a)throw Error(b||"");};Yk=/&/g;Zk=/</g;$k=/>/g;al=/"/g;bl=/'/g;cl=function(a){return String(a).replace(Yk,"&amp;").replace(Zk,"&lt;").replace($k,"&gt;").replace(al,"&quot;").replace(bl,"&#39;")};dl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;el=/%([a-f]|[0-9a-fA-F][a-f])/g;fl=/^(https?|ftp|file|chrome-extension):$/i;
gl=function(a){a=String(a);a=a.replace(dl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.xe,function(e){return e.replace(/%/g,"%25")}).replace(el,function(e){return e.toUpperCase()});a=a.match(_.we)||[];var b=_.re(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(fl);b.zq=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Ph=a[7]?[d(a[7])]:[];return b};hl=function(a){return a.zq+(0<a.query.length?"?"+a.query.join("&"):"")+(0<a.Ph.length?"#"+a.Ph.join("&"):"")};il=function(a,b){var c=[];if(a)for(var d in a)if(_.se(a,d)&&null!=a[d]){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.jl=function(a,b,c,d){a=gl(a);a.query.push.apply(a.query,il(b,d));a.Ph.push.apply(a.Ph,il(c,d));return hl(a)};
_.kl=function(a,b){var c=gl(b);b=c.zq;c.query.length&&(b+="?"+c.query.join(""));c.Ph.length&&(b+="#"+c.Ph.join(""));var d="";2E3<b.length&&(c=b,b=b.substr(0,2E3),b=b.replace(_.ye,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=gl(b);b=c.zq;c.query.length&&(b+="?"+c.query.join(""));c.Ph.length&&(b+="#"+c.Ph.join(""));_.Ld(a,new _.Cc(b,_.Bc));e.appendChild(a);b=_.$c(e.innerHTML,null);_.xb(e);e.innerHTML=_.Zc(b);b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);
c=gl(b+d);b=c.zq;c.query.length&&(b+="?"+c.query.join(""));c.Ph.length&&(b+="#"+c.Ph.join(""));return b};_.ll=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;nl=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};ol=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
pl=function(a){var b=_.Ah("googleapis.config/sessionDelegate");"string"===typeof b&&21<b.length&&(b=null);null==b&&(b=(a=(a||window.location.href).match(ol))?a[1]:null);if(null==b)return null;b=String(b);21<b.length&&(b=null);return b};ql=function(){var a=_.Be.onl;if(!a){a=_.re();_.Be.onl=a;var b=_.re();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};rl=function(a,b){b=b.onload;return"function"===typeof b?(ql().a(a,b),b):null};
sl=function(a){_.Xk(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};tl=function(a){ql().r(a)};
var vl,wl,Al;_.ul={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};vl={allowtransparency:!0,onload:!0};wl=0;_.xl=function(a,b){var c=0;do var d=b.id||["I",wl++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&5>++c);_.Xk(5>c,"Error creating iframe id");return d};_.yl=function(a,b){return a?b+"/"+a:""};
_.zl=function(a,b,c,d){var e={},f={};a.documentMode&&9>a.documentMode&&(e.hostiemode=a.documentMode);_.te(d.queryParams||{},e);_.te(d.fragmentParams||{},f);var h=d.pfname;var k=_.re();_.Ah("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.ve(a.location.href,"parent");h=h||"";!h&&c&&(h=_.ve(a.location.href,"_gfid","")||_.ve(a.location.href,"id",""),h=_.yl(h,_.ve(a.location.href,"pfname","")));h||(c=_.xf(_.ve(a.location.href,"jcp","")))&&"object"==
typeof c&&(h=_.yl(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.yf(k),k=h);h=_.ve(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(1E8*_.ai())),k.rpctoken=h);d.rpctoken=h;_.te(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.re();(h=_.ve(k,"_bsh",_.Be.bsh))&&(a._bsh=h);(k=_.Be.dpo?_.Be.h:_.ve(k,"jsh",_.Be.h))&&(a.jsh=k);d.hintInFragment?_.te(a,f):_.te(a,e);return _.jl(b,e,f,d.paramsSerializer)};
Al=function(a){_.Xk(!a||_.ll.test(a),"Illegal url for new iframe - "+a)};
_.Bl=function(a,b,c,d,e){Al(c.src);var f,h=rl(d,c),k=h?sl(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+cl(String(c.frameborder))+'" scrolling="'+cl(String(c.scrolling))+'" '+k+' name="'+cl(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.Td(a).ma("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},tl(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],"style"===l&&"object"===typeof a?_.te(a,f.style):vl[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||nl(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};
var Cl,Fl;Cl=/^:[\w]+$/;_.Dl=/:([a-zA-Z_]+):/g;_.El=function(){var a=_.Eh()||"0",b=pl();var c=_.Eh(void 0)||a;var d=pl(void 0),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=!1===_.Ah("isLoggedIn"))?"_/im/":"")&&(c="");var f=_.Ah("iframes/:socialhost:"),h=_.Ah("iframes/:im_socialhost:");return ml={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};
Fl=function(a,b){return _.El()[b]||""};_.Gl=function(a){return _.kl(_.me,a.replace(_.Dl,Fl))};_.Hl=function(a){var b=a;Cl.test(a)&&(b=_.Ah("iframes/"+b.substring(1)+"/url"),_.Xk(!!b,"Unknown iframe url config for - "+a));return _.Gl(b)};
_.Il=function(a,b,c){c=c||{};var d=c.attributes||{};_.Xk(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.Hl(a);d=b.ownerDocument||_.me;var e=_.xl(d,c);a=_.zl(d,a,e,c);var f=c,h=_.re();_.te(_.ul,h);_.te(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&2E3<a.length){c=gl(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.Bl(d,b,h,e);if(-1!=navigator.userAgent.indexOf("WebKit")){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.Bl(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=hl(c);_.Xk(_.ll.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=h instanceof _.Cc?h:_.Kc(h);c.action=_.Dc(e);for(e=0;e<f.length;e++)h=d.createElement("input"),
h.type="hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.Bl(d,b,h,e,f);return b};

var Cf=function(){this.Uf=window.console};_.g=Cf.prototype;_.g.log=function(a){this.Uf&&this.Uf.log&&this.Uf.log(a)};_.g.error=function(a){this.Uf&&(this.Uf.error?this.Uf.error(a):this.Uf.log&&this.Uf.log(a))};_.g.warn=function(a){this.Uf&&(this.Uf.warn?this.Uf.warn(a):this.Uf.log&&this.Uf.log(a))};_.g.debug=function(){};_.g.BK=function(){this.debug=this.log};_.Df=new Cf;

_.pg=function(a){if(!a)return"";a=a.split("#")[0].split("?")[0];a=a.toLowerCase();0==a.indexOf("//")&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");-1!=c&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("u`"+a);if("http"!==c&&"https"!==c&&"chrome-extension"!==c&&"moz-extension"!==c&&"file"!==c&&"android-app"!==c&&"chrome-search"!==c&&"chrome-untrusted"!==c&&"chrome"!==c&&"app"!==c&&"devtools"!==
c)throw Error("v`"+c);a="";var d=b.indexOf(":");if(-1!=d){var e=b.substring(d+1);b=b.substring(0,d);if("http"===c&&"80"!==e||"https"===c&&"443"!==e)a=":"+e}return c+"://"+b+a};

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Yg=function(){return _.rb("Safari")&&!(_.wb()||_.rb("Coast")||_.sb()||_.rb("Edge")||_.rb("Edg/")||_.rb("OPR")||_.vb()||_.rb("Silk")||_.rb("Android"))};_.Zg=_.vb();_.$g=_.yb()||_.rb("iPod");_.ah=_.rb("iPad");_.bh=_.rb("Android")&&!(_.wb()||_.vb()||_.sb()||_.rb("Silk"));_.ch=_.wb();_.dh=_.Yg()&&!_.zb();

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var pi;_.qi=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<pi.length;f++)c=pi[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};_.ri=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};pi="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.si=[];_.ti=[];_.ui=!1;
_.vi=function(a){_.si[_.si.length]=a;if(_.ui)for(var b=0;b<_.ti.length;b++)a((0,_.R)(_.ti[b].wrap,_.ti[b]))};

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.ni=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};_.oi=function(a,b){return"string"===typeof b?a.getElementById(b):b};

var fj=function(a){this.O=a};_.g=fj.prototype;_.g.value=function(){return this.O};_.g.ke=function(a){this.O.width=a;return this};_.g.Ob=function(){return this.O.width};_.g.Ad=function(a){this.O.height=a;return this};_.g.Ac=function(){return this.O.height};_.g.xg=function(a){this.O.style=a;return this};_.g.getStyle=function(){return this.O.style};
_.gj=function(a){this.O=a||{}};_.g=_.gj.prototype;_.g.value=function(){return this.O};_.g.setUrl=function(a){this.O.url=a;return this};_.g.getUrl=function(){return this.O.url};_.g.xg=function(a){this.O.style=a;return this};_.g.getStyle=function(){return this.O.style};_.g.Rd=function(a){this.O.id=a;return this};_.g.getId=function(){return this.O.id};_.g.rl=function(a){this.O.rpctoken=a;return this};_.hj=function(a,b){a.O.messageHandlers=b;return a};_.ij=function(a,b){a.O.messageHandlersFilter=b;return a};
_.gj.prototype.Ep=_.ea(4);_.gj.prototype.getContext=function(){return this.O.context};_.gj.prototype.Lc=function(){return this.O.openerIframe};_.gj.prototype.Yl=function(){this.O.attributes=this.O.attributes||{};return new fj(this.O.attributes)};

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/

/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var rj;_.jj=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.Eb(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.kj=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.Bb(f)?"o"+_.Gb(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.lj=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};
_.mj=function(a){var b={},c;for(c in a)b[c]=a[c];return b};_.nj=function(a){_.D.setTimeout(function(){throw a;},0)};_.oj=function(a){return a};_.pj=function(a){a.prototype.$goog_Thenable=!0};_.qj=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};rj=function(a,b){this.rV=a;this.A1=b;this.Fx=0;this.Ze=null};rj.prototype.get=function(){if(0<this.Fx){this.Fx--;var a=this.Ze;this.Ze=a.next;a.next=null}else a=this.rV();return a};
rj.prototype.put=function(a){this.A1(a);100>this.Fx&&(this.Fx++,a.next=this.Ze,this.Ze=a)};
var tj,uj,sj;_.vj=function(a){a=sj(a);"function"!==typeof _.D.setImmediate||_.D.Window&&_.D.Window.prototype&&!_.rb("Edge")&&_.D.Window.prototype.setImmediate==_.D.setImmediate?(tj||(tj=uj()),tj(a)):_.D.setImmediate(a)};
uj=function(){var a=_.D.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!_.rb("Presto")&&(a=function(){var e=_.be("IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var h="callImmediate"+Math.random(),k="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=(0,_.R)(function(l){if(("*"==k||l.origin==k)&&l.data==h)this.port1.onmessage()},this);
f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(h,k)}}});if("undefined"!==typeof a&&!_.tb()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.cb;c.cb=null;e()}};return function(e){d.next={cb:e};d=d.next;b.port2.postMessage(0)}}return function(e){_.D.setTimeout(e,0)}};sj=_.oj;_.vi(function(a){sj=a});
var wj=function(){this.Tz=this.iq=null};wj.prototype.add=function(a,b){var c=xj.get();c.set(a,b);this.Tz?this.Tz.next=c:this.iq=c;this.Tz=c};wj.prototype.remove=function(){var a=null;this.iq&&(a=this.iq,this.iq=this.iq.next,this.iq||(this.Tz=null),a.next=null);return a};var xj=new rj(function(){return new yj},function(a){return a.reset()}),yj=function(){this.next=this.scope=this.Wg=null};yj.prototype.set=function(a,b){this.Wg=a;this.scope=b;this.next=null};
yj.prototype.reset=function(){this.next=this.scope=this.Wg=null};
var zj,Aj,Bj,Cj,Jj;_.Ij=function(a,b){zj||Aj();Bj||(zj(),Bj=!0);Cj.add(a,b)};Aj=function(){if(_.D.Promise&&_.D.Promise.resolve){var a=_.D.Promise.resolve(void 0);zj=function(){a.then(Jj)}}else zj=function(){_.vj(Jj)}};Bj=!1;Cj=new wj;Jj=function(){for(var a;a=Cj.remove();){try{a.Wg.call(a.scope)}catch(b){_.nj(b)}xj.put(a)}Bj=!1};
var Mj,Nj,Yj,Wj;_.Lj=function(a,b){this.Da=0;this.yj=void 0;this.Nn=this.$j=this.yb=null;this.jw=this.EB=!1;if(a!=_.Cb)try{var c=this;a.call(b,function(d){Kj(c,2,d)},function(d){Kj(c,3,d)})}catch(d){Kj(this,3,d)}};Mj=function(){this.next=this.context=this.ep=this.As=this.Fl=null;this.vq=!1};Mj.prototype.reset=function(){this.context=this.ep=this.As=this.Fl=null;this.vq=!1};Nj=new rj(function(){return new Mj},function(a){a.reset()});_.Oj=function(a,b,c){var d=Nj.get();d.As=a;d.ep=b;d.context=c;return d};
_.Pj=function(a){if(a instanceof _.Lj)return a;var b=new _.Lj(_.Cb);Kj(b,2,a);return b};_.Qj=function(a){return new _.Lj(function(b,c){c(a)})};_.Sj=function(a,b,c){Rj(a,b,c,null)||_.Ij(_.ri(b,a))};_.Tj=function(a){return new _.Lj(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;0==d&&b(e)},h=function(m){c(m)},k=0,l;k<a.length;k++)l=a[k],_.Sj(l,_.ri(f,k),h);else b(e)})};_.Vj=function(){var a,b,c=new _.Lj(function(d,e){a=d;b=e});return new Uj(c,a,b)};
_.Lj.prototype.then=function(a,b,c){return Wj(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};_.pj(_.Lj);_.Lj.prototype.Cz=function(a,b){return Wj(this,null,a,b)};_.Lj.prototype.cancel=function(a){if(0==this.Da){var b=new Xj(a);_.Ij(function(){Yj(this,b)},this)}};
Yj=function(a,b){if(0==a.Da)if(a.yb){var c=a.yb;if(c.$j){for(var d=0,e=null,f=null,h=c.$j;h&&(h.vq||(d++,h.Fl==a&&(e=h),!(e&&1<d)));h=h.next)e||(f=h);e&&(0==c.Da&&1==d?Yj(c,b):(f?(d=f,d.next==c.Nn&&(c.Nn=d),d.next=d.next.next):Zj(c),ak(c,e,3,b)))}a.yb=null}else Kj(a,3,b)};_.ck=function(a,b){a.$j||2!=a.Da&&3!=a.Da||bk(a);a.Nn?a.Nn.next=b:a.$j=b;a.Nn=b};
Wj=function(a,b,c,d){var e=_.Oj(null,null,null);e.Fl=new _.Lj(function(f,h){e.As=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.ep=c?function(k){try{var l=c.call(d,k);void 0===l&&k instanceof Xj?h(k):f(l)}catch(m){h(m)}}:h});e.Fl.yb=a;_.ck(a,e);return e.Fl};_.Lj.prototype.S3=function(a){this.Da=0;Kj(this,2,a)};_.Lj.prototype.T3=function(a){this.Da=0;Kj(this,3,a)};
var Kj=function(a,b,c){0==a.Da&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Da=1,Rj(c,a.S3,a.T3,a)||(a.yj=c,a.Da=b,a.yb=null,bk(a),3!=b||c instanceof Xj||dk(a,c)))},Rj=function(a,b,c,d){if(a instanceof _.Lj)return _.ck(a,_.Oj(b||_.Cb,c||null,d)),!0;if(_.qj(a))return a.then(b,c,d),!0;if(_.Bb(a))try{var e=a.then;if("function"===typeof e)return ek(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},ek=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||
(f=!0,d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},bk=function(a){a.EB||(a.EB=!0,_.Ij(a.nv,a))},Zj=function(a){var b=null;a.$j&&(b=a.$j,a.$j=b.next,b.next=null);a.$j||(a.Nn=null);return b};_.Lj.prototype.nv=function(){for(var a;a=Zj(this);)ak(this,a,this.Da,this.yj);this.EB=!1};
var ak=function(a,b,c,d){if(3==c&&b.ep&&!b.vq)for(;a&&a.jw;a=a.yb)a.jw=!1;if(b.Fl)b.Fl.yb=null,fk(b,c,d);else try{b.vq?b.As.call(b.context):fk(b,c,d)}catch(e){gk.call(null,e)}Nj.put(b)},fk=function(a,b,c){2==b?a.As.call(a.context,c):a.ep&&a.ep.call(a.context,c)},dk=function(a,b){a.jw=!0;_.Ij(function(){a.jw&&gk.call(null,b)})},gk=_.nj,Xj=function(a){_.jb.call(this,a);this.sP=!1};_.$a(Xj,_.jb);Xj.prototype.name="cancel";var Uj=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};

_.ik=function(){return!0};

_.jk=function(a){return new _.Lj(a)};

var rk=function(){this.Lt={nP:kk?"../"+kk:null,nB:lk,XL:mk,rda:nk,km:ok,dea:pk};this.vf=_.le;this.YO=this.wV;this.iW=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.Lt.nP){this.vf=this.Lt.XL(this.vf,this.Lt.nP);var a=this.vf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.YO=this.vf.doPostMsg}this.cH={};this.GH={};a=(0,_.R)(this.PC,
this);_.Ae(this.vf,"message",a);_.pe(_.Be,"RPMQ",[]).push(a);this.vf!=this.vf.parent&&qk(this,this.vf.parent,this.OD(this.vf.name),"*")};rk.prototype.OD=function(a){return'{"h":"'+escape(a)+'"}'};var sk=function(a){var b=null;0===a.indexOf('{"h":"')&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},tk=function(a){if(!/^\s*{/.test(a))return!1;a=_.xf(a);return null!==a&&"object"===typeof a&&!!a.g};
rk.prototype.PC=function(a){var b=String(a.data);_.Df.debug("gapix.rpc.receive("+nk+"): "+(!b||512>=b.length?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=0!==b.indexOf("!_");c||(b=b.substring(2));var d=tk(b);if(!c&&!d){if(!d&&(c=sk(b))){if(this.cH[c])this.cH[c]();else this.GH[c]=1;return}var e=a.origin,f=this.Lt.nB;this.iW?_.le.setTimeout(function(){f(b,e)},0):f(b,e)}};rk.prototype.Cb=function(a,b){".."===a||this.GH[a]?(b(),delete this.GH[a]):this.cH[a]=b};
var qk=function(a,b,c,d){var e=tk(c)?"":"!_";_.Df.debug("gapix.rpc.send("+nk+"): "+(!c||512>=c.length?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.YO(b,e+c,d)};rk.prototype.wV=function(a,b,c){a.postMessage(b,c)};rk.prototype.send=function(a,b,c){(a=this.Lt.XL(this.vf,a))&&!a.closed&&qk(this,a,b,c)};
var uk,vk,wk,xk,yk,zk,Ak,kk,nk,Bk,Ck,Dk,mk,ok,Fk,Gk,Lk,Mk,Ok,pk,Qk,Pk,Hk,Ik,Rk,lk,Sk,Tk;uk=0;vk=[];wk={};xk={};yk=_.le.location.href;zk=_.ve(yk,"rpctoken");Ak=_.ve(yk,"parent")||_.me.referrer;kk=_.ve(yk,"rly");nk=kk||(_.le!==_.le.top||_.le.opener)&&_.le.name||"..";Bk=null;Ck={};Dk=function(){};_.Ek={send:Dk,Cb:Dk,OD:Dk};
mk=function(a,b){"/"==b.charAt(0)&&(b=b.substring(1),a=_.le.top);if(0===b.length)return a;for(b=b.split("/");b.length;){var c=b.shift();"{"==c.charAt(0)&&"}"==c.charAt(c.length-1)&&(c=c.substring(1,c.length-1));if(".."===c)a=a==a.parent?a.opener:a.parent;else if(".."!==c&&a.frames[c]){if(a=a.frames[c],!("postMessage"in a))throw"Not a window";}else return null}return a};ok=function(a){return(a=wk[a])&&a.Dz};
Fk=function(a){if(a.f in{})return!1;var b=a.t,c=wk[a.r];a=a.origin;return c&&(c.Dz===b||!c.Dz&&!b)&&(a===c.origin||"*"===c.origin)};Gk=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||"*"==d)}};_.Jk=function(a,b,c){a=Hk(a);xk[a.name]={Wg:b,hs:a.hs,cq:c||Fk};Ik()};_.Kk=function(a){delete xk[Hk(a).name]};Lk={};Mk=function(a,b){(a=Lk["_"+a])&&a[1](this)&&a[0].call(this,b)};
Ok=function(a){var b=a.c;if(!b)return Dk;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.Nk.apply(null,e)}};pk=function(a){Bk=a};Qk=function(a){Ck[a]||(Ck[a]=_.le.setTimeout(function(){Ck[a]=!1;Pk(a)},0))};Pk=function(a){var b=wk[a];if(b&&b.ready){var c=b.LE;for(b.LE=[];c.length;)_.Ek.send(a,_.yf(c.shift()),b.origin)}};Hk=function(a){return 0===a.indexOf("legacy__")?{name:a.substring(8),hs:!0}:{name:a,hs:!1}};
Ik=function(){for(var a=_.Ah("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c=0,d;d=vk[c];++c){var e=d.Xm;if(!e||0<a&&b-d.timestamp>a)vk.splice(c,1),--c;else{var f=e.s,h=xk[f]||xk["*"];if(h)if(vk.splice(c,1),--c,e.origin=d.origin,d=Ok(e),e.callback=d,h.cq(e)){if("__cb"!==f&&!!h.hs!=!!e.g)break;e=h.Wg.apply(e,e.a);void 0!==e&&d(e)}else _.Df.debug("gapix.rpc.rejected("+nk+"): "+f)}}};Rk=function(a,b,c){vk.push({Xm:a,origin:b,timestamp:(new Date).getTime()/1E3});c||Ik()};
lk=function(a,b){a=_.xf(a);Rk(a,b,!1)};Sk=function(a){for(;a.length;)Rk(a.shift(),this.origin,!0);Ik()};Tk=function(a){var b=!1;a=a.split("|");var c=a[0];0<=c.indexOf("/")&&(b=!0);return{id:c,origin:a[1]||"*",yD:b}};
_.Uk=function(a,b,c,d){var e=Tk(a);d&&(_.le.frames[e.id]=_.le.frames[e.id]||d);a=e.id;if(!wk.hasOwnProperty(a)){c=c||null;d=e.origin;if(".."===a)d=_.pg(Ak),c=c||zk;else if(!e.yD){var f=_.me.getElementById(a);f&&(f=f.src,d=_.pg(f),c=c||_.ve(f,"rpctoken"))}"*"===e.origin&&d||(d=e.origin);wk[a]={Dz:c,LE:[],origin:d,M1:b,iP:function(){var h=a;wk[h].ready=1;Pk(h)}};_.Ek.Cb(a,wk[a].iP)}return wk[a].iP};
_.Nk=function(a,b,c,d){a=a||"..";_.Uk(a);a=a.split("|",1)[0];var e=b,f=[].slice.call(arguments,3),h=c,k=nk,l=zk,m=wk[a],n=k,q=Tk(a);if(m&&".."!==a){if(q.yD){if(!(l=wk[a].M1)){l=Bk?Bk.substring(1).split("/"):[nk];n=l.length-1;for(var p=_.le.parent;p!==_.le.top;){var t=p.parent;if(!n--){for(var v=null,u=t.frames.length,w=0;w<u;++w)t.frames[w]==p&&(v=w);l.unshift("{"+v+"}")}p=t}l="/"+l.join("/")}n=l}else n=k="..";l=m.Dz}h&&q?(m=Fk,q.yD&&(m=Gk(q)),Lk["_"+ ++uk]=[h,m],h=uk):h=null;f={s:e,f:k,r:n,t:l,c:h,
a:f};e=Hk(e);f.s=e.name;f.g=e.hs;wk[a].LE.push(f);Qk(a)};if("function"===typeof _.le.postMessage||"object"===typeof _.le.postMessage)_.Ek=new rk,_.Jk("__cb",Mk,function(){return!0}),_.Jk("_processBatch",Sk,function(){return!0}),_.Uk("..");

var Vk;
Vk=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];0<c.length;){var d=c.shift(),e=d.childNodes;if("undefined"!==typeof d.style){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if("visible"!=f&&"inherit"!=f&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),0<f.length&&"auto"!=f))continue}for(d=0;d<e.length;d++){f=e[d];
if("undefined"!==typeof f.offsetTop&&"undefined"!==typeof f.offsetHeight){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.Wk=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if("CSS1Compat"===document.compatMode&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(0<=navigator.userAgent.indexOf("AppleWebKit"))return Vk();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};

var Jl=function(a,b){return _.Bh(a,b,!0,void 0,void 0)},Kl=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,d){a().Context.prototype.connectIframes.apply(this,
[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,
[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=
function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},Ll=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,
[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,
[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,
[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,[c,d])};return b},Ml,Nl,Ul,Wl,bm,km,lm,mm,pm,qm,tm,um,vm,xm,wm,ym;
_.gj.prototype.Ep=_.cb(4,function(a){this.O.apis=a;return this});Ml=function(a,b){a.O.onload=b};Nl=function(a){return a.O.rpctoken};_.Ol=function(a,b){a.O.queryParams=b;return a};_.Pl=function(a,b){a.O.relayOpen=b;return a};_.Tl=function(a,b){a.O.onClose=b;return a};Ul=function(a,b){a.O.controllerData=b};_.Vl=function(a){a.O.waitForOnload=!0;return a};Wl=function(a){return(a=a.O.timeout)?a:null};_.Xl=function(){return _.hk};_.Yl=function(a){return!!a&&"object"===typeof a&&_.oe.test(a.push)};
_.Zl=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.$l=function(a,b,c){if(a){_.Xk(_.Yl(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};_.am=function(a,b,c){if(a)if(_.Yl(a))_.$l(a,b,c);else{_.Xk("object"===typeof a,"objectForEach was called with a non object value");c=c||a;for(var d in a)_.se(a,d)&&void 0!==a[d]&&b.call(c,a[d],d)}};bm=function(a){this.O=a||{}};bm.prototype.value=function(){return this.O};
bm.prototype.getIframe=function(){return this.O.iframe};var cm=function(a,b){a.O.role=b;return a},dm=function(a,b){a.O.data=b;return a};bm.prototype.Dj=function(a){this.O.setRpcReady=a;return this};var em=function(a){return a.O.setRpcReady};bm.prototype.rl=function(a){this.O.rpctoken=a;return this};var fm=function(a){a.O.selfConnect=!0;return a},gm=function(a){this.O=a||{}};gm.prototype.value=function(){return this.O};var im=function(a){var b=new hm;b.O.role=a;return b};gm.prototype.QL=function(){return this.O.role};
gm.prototype.qc=function(a){this.O.handler=a;return this};gm.prototype.kb=function(){return this.O.handler};var jm=function(a,b){a.O.filter=b;return a};gm.prototype.Ep=function(a){this.O.apis=a;return this};mm=/^[\w\.\-]*$/;_.nm=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.om=function(a){for(var b=_.re(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.zd]}};pm=function(a,b,c){a=km[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.Pj(a[e].call(c,b,c)));return d};
qm=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.Xk(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=pm(a,d,b);!c&&0<d.length&&_.Tj(d).then(e)}}};_.rm=function(a,b,c){_.Xk("_default"!=a,"Cannot update default api");lm[a]={map:b,filter:c}};_.sm=function(a,b,c){_.Xk("_default"!=a,"Cannot update default api");_.pe(lm,a,{map:{},filter:_.nm}).map[b]=c};
tm=function(a,b){_.pe(lm,"_default",{map:{},filter:_.ik}).map[a]=b;_.am(_.hk.Af,function(c){c.register(a,b,_.ik)})};um=/^https?:\/\/[^\/%\\?#\s]+$/i;vm={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};
xm=function(a){this.resolve=this.reject=null;this.promise=_.jk((0,_.R)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=wm(this.promise,a))};wm=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};ym=function(a){this.we=a;this.Context=Kl(a);this.Iframe=Ll(a)};_.g=ym.prototype;_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.we().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.we().SAME_ORIGIN_IFRAMES_FILTER(a)};
_.g.create=function(a,b,c){return this.we().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.we().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.we().getContext()};_.g.getStyle=function(a){return this.we().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.we().makeWhiteListIframesFilter(a)};_.g.registerBeforeOpenStyle=function(a,b){return this.we().registerBeforeOpenStyle(a,b)};
_.g.registerIframesApi=function(a,b,c){return this.we().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.we().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.we().registerStyle(a,b)};
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var zm=function(){this.wh=[]};zm.prototype.we=function(a){return this.wh.length?Am(this.wh[0],a):void 0};var Am=function(a,b){b=void 0===b?function(c){return new c}:b;return a.eB?b(a.eB):a.instance},Bm=function(){zm.apply(this,arguments)};_.O(Bm,zm);var Dm=function(a){var b=Cm.eK,c=a.priority,d=~Jl(b.wh,function(e){return e.priority<c?-1:1});b.wh.splice(d,0,a)};
var Cm=new function(){var a=this;this.eK=new Bm;this.instance=new ym(function(){return a.eK.we()()})};Dm({instance:function(){return window.gapi.iframes},priority:1});_.Em=Cm.instance;
var Fm,Gm;Fm={height:!0,width:!0};Gm=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.Hm=function(a){"number"===typeof a&&(a=String(a)+"px");return a};
var Im=function(){bm.apply(this,arguments)};_.O(Im,bm);var hm=function(){gm.apply(this,arguments)};_.O(hm,gm);var Jm=function(){_.gj.apply(this,arguments)};_.O(Jm,_.gj);
var Km=function(a){Jm.call(this,a)};_.O(Km,Jm);var Lm=function(a,b){a.O.frameName=b;return a};Km.prototype.getFrameName=function(){return this.O.frameName};var Mm=function(a,b){a.O.rpcAddr=b;return a};Km.prototype.Hf=function(){return this.O.rpcAddr};var Nm=function(a,b){a.O.retAddr=b;return a};_.g=Km.prototype;_.g.dh=function(){return this.O.retAddr};_.g.Ci=function(a){this.O.origin=a;return this};_.g.getOrigin=function(){return this.O.origin};_.g.Dj=function(a){this.O.setRpcReady=a;return this};
_.g.Hp=function(a){this.O.context=a};var Om=function(a,b){a.O._rpcReadyFn=b};Km.prototype.getIframeEl=function(){return this.O.iframeEl};
var Pm=function(a,b,c){var d=a.Hf(),e=b.dh();Nm(Mm(c,a.dh()+"/"+b.Hf()),e+"/"+d);Lm(c,b.getFrameName()).Ci(b.getOrigin())};
var Rm=function(a,b){if(_.md&&_.dh&&a){a.focus();var c=0,d=null;d=a.setInterval(function(){b.closed||5==c?(a.clearInterval(d),Qm(b)):(b.close(),c++)},150)}else b.close(),Qm(b)},Qm=function(a){a.closed||a.document&&a.document.body&&_.ke(a.document.body,"Please close this window.")};
_.Sm=function(a,b,c,d){this.eg=!1;this.Ll=a;this.YE=b;this.$n=c;this.Ia=d;this.BP=this.Ia.dh();this.zd=this.Ia.getOrigin();this.tZ=this.Ia.getIframeEl();this.QQ=this.Ia.O.where;this.wh=[];this.applyIframesApi("_default");a=this.Ia.O.apis||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.Ll.Af[c]=this};_.g=_.Sm.prototype;_.g.isDisposed=function(){return this.eg};
_.g.Ga=function(){if(!this.isDisposed()){for(var a=0;a<this.wh.length;a++)this.unregister(this.wh[a]);delete _.hk.Af[this.getFrameName()];this.eg=!0}};_.g.getContext=function(){return this.Ll};_.g.getOptions=function(){return this.Ia};_.g.Hf=function(){return this.YE};_.g.dh=function(){return this.BP};_.g.getFrameName=function(){return this.$n};_.g.getIframeEl=function(){return this.tZ};_.g.getSiteEl=function(){return this.QQ};_.g.setSiteEl=function(a){this.QQ=a};_.g.Dj=function(){(0,this.Ia.O._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ia.value()[a]=b};_.g.getParam=function(a){return this.Ia.value()[a]};_.g.$b=function(){return this.Ia.value()};_.g.getId=function(){return this.Ia.getId()};_.g.getOrigin=function(){return this.zd};var Tm=function(a,b){var c=a.Ll.getFrameName();return a.$n+":"+c+":"+b};_.g=_.Sm.prototype;
_.g.register=function(a,b,c){_.Xk(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.Xk((c||_.nm)(this),"Rejecting untrusted message "+a);c=Tm(this,a);1==_.pe(km,c,[]).push(b)&&(this.wh.push(a),_.Jk(c,qm(c,this,"_g_wasClosed"===a)))};_.g.unregister=function(a,b){var c=Tm(this,a),d=km[c];d&&(b?(b=_.Zl.call(d,b),0<=b&&d.splice(b,1)):d.splice(0,d.length),0==d.length&&(b=_.Zl.call(this.wh,a),0<=b&&this.wh.splice(b,1),_.Kk(c)))};_.g.vX=function(){return this.wh};
_.g.applyIframesApi=function(a){this.uA=this.uA||[];if(!(0<=_.Zl.call(this.uA,a))){this.uA.push(a);a=lm[a]||{map:{}};for(var b in a.map)_.se(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.nm(this))return null;var a=this.Ia.O._popupWindow;if(a)return a;var b=this.YE.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var Um=function(a){var b={};if(a)for(var c in a)_.se(a,c)&&_.se(Fm,c)&&Gm.test(a[c])&&(b[c]=a[c]);return b};_.g=_.Sm.prototype;_.g.close=function(a,b){return Vm(this,"_g_close",a,b)};_.g.restyle=function(a,b){return Vm(this,"_g_restyle",a,b)};_.g.wp=function(a,b){return Vm(this,"_g_restyleDone",a,b)};_.g.iV=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.I1=function(a){if(a&&"object"===typeof a)return this.getContext().restyleSelf(a,void 0,this)};
_.g.J1=function(a){var b=this.Ia.O.onRestyle;b&&b.call(this,a,this);a=a&&"object"===typeof a?Um(a):{};(b=this.getIframeEl())&&a&&"object"===typeof a&&(_.se(a,"height")&&(a.height=_.Hm(a.height)),_.se(a,"width")&&(a.width=_.Hm(a.width)),_.te(a,b.style))};
_.g.jV=function(a){var b=this.Ia.O.onClose;b&&b.call(this,a,this);if(b=this.getOptions().O._popupWindow){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);Rm(this.getContext().getWindow(),b)}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ia.O.controller)c={},c.frameName=this.getFrameName(),Vm(b,"_g_disposeControl",c);b=Tm(this,"_g_wasClosed");pm(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.j4=function(){delete this.getContext().Af[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.R)(function(){this.Ga()},this),0)};
_.g.send=function(a,b,c,d){_.Xk(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.Xk((d||_.nm)(this),"Wrong target for message "+a);c=new xm(c);a=this.Ll.getFrameName()+":"+this.$n+":"+a;_.Nk(this.YE,a,c.resolve,b);return c.promise};var Vm=function(a,b,c,d){return a.send(b,c,d,_.ik)};_.g=_.Sm.prototype;_.g.K0=function(a){return a};_.g.ping=function(a,b){return Vm(this,"_g_ping",b,a)};
_.g.nV=function(a){a=a&&"object"===typeof a?a:{};for(var b=a.rpcAddr,c=(this.Hf()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=".."==e?d.parent:d.frames[e];_.Xk(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.Hf();a._parentRetAddr=this.dh();this.getContext();b=new _.Wm(a);this.$_&&this.$_(b,a.controllerData);this.ZA=this.ZA||[];this.ZA.push(b,a.controllerData)};
_.g.BV=function(a){a=(a||{}).frameName;for(var b=this.ZA||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.Ga();this.d0&&this.d0(a);return}_.Xk(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.lV=function(a){var b=new Km(a);a=new Im(b.value());if(a.O.selfConnect)var c=this;else(_.Xk(um.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().Af[b.getFrameName()],c)?em(b)&&(c.Dj(),Vm(c,"_g_rpcReady")):(b=Lm(Nm(Mm(new Km,b.Hf()),b.dh()).Ci(b.getOrigin()),b.getFrameName()).Dj(em(b)).rl(Nl(b)),c=this.getContext().Xj(b.value()));b=this.getContext();var d=a.O.role;a=a.O.data;Xm(b);d=d||"";_.pe(b.WA,d,[]).push({Yh:c,data:a});Ym(c,a,b.iE[d])};
_.g.pG=function(a,b){(new Km(b)).O._relayedDepth||(b={},fm(cm(new Im(b),"_opener")),Vm(a,"_g_connect",b))};
_.g.JO=function(a){var b=this,c=a.O.messageHandlers,d=a.O.messageHandlersFilter,e=a.O.onClose;_.Tl(_.ij(_.hj(a,null),null),null);return Vm(this,"_g_open",a.value()).then(function(f){var h=new Km(f[0]),k=h.getFrameName();f=new Km;var l=b.dh(),m=h.dh();Nm(Mm(f,b.Hf()+"/"+h.Hf()),m+"/"+l);Lm(f,k);f.Ci(h.getOrigin());f.Ep(h.O.apis);f.rl(Nl(a));_.hj(f,c);_.ij(f,d);_.Tl(f,e);(h=b.getContext().Af[k])||(h=b.getContext().Xj(f.value()));return h})};
_.g.ZE=function(a){var b=a.getUrl();_.Xk(!b||_.ll.test(b),"Illegal url for new iframe - "+b);var c=a.Yl().value();b={};for(var d in c)_.se(c,d)&&_.se(vm,d)&&(b[d]=c[d]);_.se(c,"style")&&(d=c.style,"object"===typeof d&&(b.style=Um(d)));a.value().attributes=b};
_.g.x0=function(a){a=new Km(a);this.ZE(a);var b=a.O._relayedDepth||0;a.O._relayedDepth=b+1;a.O.openerIframe=this;var c=Nl(a);a.rl(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=(new Km(e.$b())).O.apis,h=new Km;Pm(e,d,h);0==b&&cm(new Im(h.value()),"_opener");h.Dj(!0);h.rl(c);Vm(e,"_g_connect",h.value());h=new Km;Lm(Nm(Mm(h,e.Hf()),e.BP),e.getFrameName()).Ci(e.getOrigin()).Ep(f);return h.value()})};
_.g.H1=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.ik)},null,_.ik)};
var cn;_.Zm=_.re();_.$m=_.re();_.an=function(a,b){_.Zm[a]=b};_.bn=function(a){return _.Zm[a]};cn=function(a,b){_.ue.load("gapi.iframes.style."+a,b)};_.dn=function(a,b){_.$m[a]=b};_.en=function(a){return _.$m[a]};
_.Wm=function(a){a=a||{};this.eg=!1;this.PO=_.re();this.Af=_.re();this.vf=a._window||_.le;this.$c=this.vf.location.href;this.QO=(this.wE=fn(this.$c,"parent"))?fn(this.$c,"pfname"):"";this.Ba=this.wE?fn(this.$c,"_gfid")||fn(this.$c,"id"):"";this.$n=_.yl(this.Ba,this.QO);this.zd=_.pg(this.$c);if(this.Ba){var b=new Km;Mm(b,a._parentRpcAddr||"..");Nm(b,a._parentRetAddr||this.Ba);b.Ci(_.pg(this.wE||this.$c));Lm(b,this.QO);this.yb=this.Xj(b.value())}else this.yb=null};_.g=_.Wm.prototype;
_.g.isDisposed=function(){return this.eg};_.g.Ga=function(){if(!this.isDisposed()){for(var a=_.Ba(Object.values(this.Af)),b=a.next();!b.done;b=a.next())b.value.Ga();this.eg=!0}};_.g.getFrameName=function(){return this.$n};_.g.getOrigin=function(){return this.zd};_.g.getWindow=function(){return this.vf};_.g.Za=function(){return this.vf.document};_.g.setGlobalParam=function(a,b){this.PO[a]=b};_.g.getGlobalParam=function(a){return this.PO[a]};
_.g.Xj=function(a){_.Xk(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new Km(a);a.Hf()||Mm(a,a.getId());a.dh()||Nm(a,"..");a.getOrigin()||a.Ci(_.pg(a.getUrl()));a.getFrameName()||Lm(a,_.yl(a.getId(),this.$n));var b=a.getFrameName();if(this.Af[b])return this.Af[b];var c=a.Hf(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.dh(),f=Nl(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.ve(f,"rpctoken"));Om(a,_.Uk(d,e,f,a.O._popupWindow));d=
((window.gadgets||{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.Sm(this,c,b,a),k=a.O.messageHandlersFilter;_.am(a.O.messageHandlers,function(l,m){h.register(m,l,k)});em(a)&&h.Dj();Vm(h,"_g_rpcReady");return h};_.g.ZE=function(a){Lm(a,null);var b=a.getId();!b||mm.test(b)&&!this.getWindow().document.getElementById(b)||(_.Df.log("Ignoring requested iframe ID - "+b),a.Rd(null))};var fn=function(a,b){var c=_.ve(a,b);c||(c=_.xf(_.ve(a,"jcp",""))[b]);return c||""};
_.Wm.prototype.openChild=function(a){_.Xk(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new Km(a);gn(this,b);var c=b.getFrameName();if(c&&this.Af[c])return this.Af[c];this.ZE(b);c=b.getUrl();_.Xk(c,"No url for new iframe");var d=b.O.queryParams||{};d.usegapi="1";_.Ol(b,d);d=this.pM&&this.pM(c,b);d||(d=b.O.where,_.Xk(!!d,"No location for new iframe"),c=_.Il(c,d,a),b.O.iframeEl=c,d=c.getAttribute("id"));Mm(b,d).Rd(d);b.Ci(_.pg(b.O.eurl||""));this.QN&&this.QN(b,b.getIframeEl());
c=this.Xj(a);c.pG&&c.pG(c,a);(a=b.O.onCreate)&&a(c);b.O.disableRelayOpen||c.applyIframesApi("_open");return c};
var hn=function(a,b,c){var d=b.O.canvasUrl;if(!d)return c;_.Xk(!b.O.allowPost&&!b.O.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.Xk(e&&_.pg(e)===a.zd&&_.pg(d)===a.zd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.Vl(b);b.O.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.Hl(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},jn=function(a,b,c){var d=b.O.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.Sm?
(e=d,_.Pl(b,0)):0<Number(d)&&_.Pl(b,Number(d)-1);if(e){_.Xk(!!e.JO,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.$m[d])b.Hp(a),d(b.value()),b.Hp(null);b.O.openerIframe=null;c.resolve(e.JO(b));return!0}}return!1},kn=function(a,b,c){var d=b.getStyle();if(d)if(_.Xk(!!_.bn,"Defer style is disabled, when requesting style "+d),_.Zm[d])gn(a,b);else return cn(d,function(){_.Xk(!!_.Zm[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.Wm.prototype.open=function(a,b){_.Xk(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new Km(a);b=hn(this,c,b);var d=new xm(b);(b=c.getUrl())&&c.setUrl(_.Hl(b));if(jn(this,c,d)||kn(this,c,d)||jn(this,c,d))return d.promise;if(null!=Wl(c)){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+Wl(c)+"milliseconds"})},Wl(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.O.waitForOnload&&Ml(c.Yl(),function(){d.resolve(h)});
var h=this.openChild(a);c.O.waitForOnload||d.resolve(h);return d.promise};_.Wm.prototype.getParentIframe=function(){return this.yb};var ln=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.Yh,b.params));return _.Pj(d).then(function(e){return e&&c?(b.OO&&b.OO.call(a,b.params),e=b.sender?b.sender(b.params):Vm(c,b.message,b.params),b.i4?e.then(function(){return!0}):!0):!1})};_.g=_.Wm.prototype;
_.g.closeSelf=function(a,b,c){a=ln(this,{sender:function(d){var e=_.hk.getParentIframe();_.am(_.hk.Af,function(f){f!==e&&Vm(f,"_g_wasClosed",d)});return Vm(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,Yh:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new xm(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new xm(b);b.resolve(ln(this,{message:"_g_restyleMe",params:a,Yh:c,filter:this.getGlobalParam("onRestyleSelfFilter"),i4:!0,OO:this.pR}));return b.promise};
_.g.pR=function(a){"auto"===a.height&&(a.height=_.Wk())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var gn=function(a,b){var c=b.getStyle();if(c){b.xg(null);var d=_.Zm[c];_.Xk(d,"No such style: "+c);b.Hp(a);d(b.value());b.Hp(null)}};
_.Wm.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.am(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.pR(h);f&&f.send("_ready",h,c,_.ik)};
_.Wm.prototype.connectIframes=function(a,b){a=new Im(a);var c=new Im(b),d=em(a);b=a.getIframe();var e=c.getIframe();if(e){var f=Nl(a),h=new Km;Pm(b,e,h);dm(cm((new Im(h.value())).rl(f),a.O.role),a.O.data).Dj(d);var k=new Km;Pm(e,b,k);dm(cm((new Im(k.value())).rl(f),c.O.role),c.O.data).Dj(!0);Vm(b,"_g_connect",h.value(),function(){d||Vm(e,"_g_connect",k.value())});d&&Vm(e,"_g_connect",k.value())}else c={},dm(cm(fm(new Im(c)),a.O.role),a.O.data),Vm(b,"_g_connect",c)};
var Xm=function(a){a.WA||(a.WA=_.re(),a.iE=_.re())};_.Wm.prototype.addOnConnectHandler=function(a,b,c,d){Xm(this);"object"===typeof a?(b=new hm(a),c=b.QL()||""):(b=jm(im(a).qc(b).Ep(c),d),c=a);d=this.WA[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Ym(this.Af[d[e].Yh.getFrameName()],d[e].data,[b]),a=b.O.runOnce;c=_.pe(this.iE,c,[]);a||b.O.dontWait||c.push(b)};
_.Wm.prototype.removeOnConnectHandler=function(a,b){a=_.pe(this.iE,a,[]);if(b)for(var c=0,d=!1;!d&&c<a.length;c++)a[c].kb()===b&&(d=!0,a.splice(c,1));else a.splice(0,a.length)};var Ym=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.O.filter||_.nm;if(a&&f(a)){f=e.O.apis||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.kb()&&e.kb()(a,b);e.O.runOnce&&(c.splice(d,1),--d)}}}};
_.Wm.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=jm(im("_opener").qc(a).Ep(b),c);a.O.runOnce=!0;d.call(this,a.value())};_.Wm.prototype.QN=function(a,b){var c=a.O.controller;if(c){_.Xk(c.zd===a.getOrigin(),"Wrong controller origin "+this.zd+" !== "+a.getOrigin());var d=a.Hf();Mm(a,c.Hf());Nm(a,c.dh());var e=new Km;Ul(Mm(e,d),a.O.controllerData);_.Ae(b,"load",function(){c.send("_g_control",e.value())})}};
var mn=function(a,b,c){a=a.getWindow();var d=a.document,e=c.O.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.xl(d,c);var h=f,k=c.O.rpcRelayUrl;if(k){k=_.Gl(k);h=c.O.fragmentParams||{};h.rly=f;c.O.fragmentParams=h;h=c.O.where||d.body;_.Xk(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.Bl(d,h,l,f);h=f+"_relay"}b=_.Hl(b);var m=_.zl(d,b,f,c.value());c.O.eurl=m;b=c.O.openAsWindow;"string"!==typeof b&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.le.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Md(m,a,h,b);return{id:f,GR:n}};_.Wm.prototype.pM=function(a,b){if(b.O.openAsWindow){a=mn(this,a,b);var c=a.id;_.Xk(!!a.GR,"Open popup window failed");b.O._popupWindow=a.GR}return c};
km=_.re();lm=_.re();_.hk=new _.Wm;tm("_g_rpcReady",_.Sm.prototype.Dj);tm("_g_discover",_.Sm.prototype.vX);tm("_g_ping",_.Sm.prototype.K0);tm("_g_close",_.Sm.prototype.iV);tm("_g_closeMe",_.Sm.prototype.jV);tm("_g_restyle",_.Sm.prototype.I1);tm("_g_restyleMe",_.Sm.prototype.J1);tm("_g_wasClosed",_.Sm.prototype.j4);_.sm("control","_g_control",_.Sm.prototype.nV);_.sm("control","_g_disposeControl",_.Sm.prototype.BV);var nn=_.hk.getParentIframe();nn&&nn.register("_g_restyleDone",_.Sm.prototype.H1,_.ik);
tm("_g_connect",_.Sm.prototype.lV);var on={};on._g_open=_.Sm.prototype.x0;_.rm("_open",on,_.ik);
var pn={Context:_.Wm,Iframe:_.Sm,SAME_ORIGIN_IFRAMES_FILTER:_.nm,CROSS_ORIGIN_IFRAMES_FILTER:_.ik,makeWhiteListIframesFilter:_.om,getContext:_.Xl,registerIframesApi:_.rm,registerIframesApiHandler:_.sm,registerStyle:_.an,registerBeforeOpenStyle:_.dn,getStyle:_.bn,getBeforeOpenStyle:_.en,create:_.Il};
Dm({instance:function(){return pn},priority:2});
_.sm("gapi.load","_g_gapi.load",function(a){return new _.Lj(function(b){_.ue.load(a&&"object"===typeof a&&a.features||"",b)})});

_.I("gapi.iframes.create",_.Il);

_.I("gapi.iframes.registerStyle",_.an);_.I("gapi.iframes.registerBeforeOpenStyle",_.dn);_.I("gapi.iframes.getStyle",_.bn);_.I("gapi.iframes.getBeforeOpenStyle",_.en);_.I("gapi.iframes.registerIframesApi",_.rm);_.I("gapi.iframes.registerIframesApiHandler",_.sm);_.I("gapi.iframes.getContext",_.Xl);_.I("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",_.nm);_.I("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.ik);_.I("gapi.iframes.makeWhiteListIframesFilter",_.om);_.I("gapi.iframes.Context",_.Wm);
_.I("gapi.iframes.Context.prototype.isDisposed",_.Wm.prototype.isDisposed);_.I("gapi.iframes.Context.prototype.getWindow",_.Wm.prototype.getWindow);_.I("gapi.iframes.Context.prototype.getFrameName",_.Wm.prototype.getFrameName);_.I("gapi.iframes.Context.prototype.getGlobalParam",_.Wm.prototype.getGlobalParam);_.I("gapi.iframes.Context.prototype.setGlobalParam",_.Wm.prototype.setGlobalParam);_.I("gapi.iframes.Context.prototype.open",_.Wm.prototype.open);
_.I("gapi.iframes.Context.prototype.openChild",_.Wm.prototype.openChild);_.I("gapi.iframes.Context.prototype.getParentIframe",_.Wm.prototype.getParentIframe);_.I("gapi.iframes.Context.prototype.closeSelf",_.Wm.prototype.closeSelf);_.I("gapi.iframes.Context.prototype.restyleSelf",_.Wm.prototype.restyleSelf);_.I("gapi.iframes.Context.prototype.setCloseSelfFilter",_.Wm.prototype.setCloseSelfFilter);_.I("gapi.iframes.Context.prototype.setRestyleSelfFilter",_.Wm.prototype.setRestyleSelfFilter);
_.I("gapi.iframes.Context.prototype.addOnConnectHandler",_.Wm.prototype.addOnConnectHandler);_.I("gapi.iframes.Context.prototype.removeOnConnectHandler",_.Wm.prototype.removeOnConnectHandler);_.I("gapi.iframes.Context.prototype.addOnOpenerHandler",_.Wm.prototype.addOnOpenerHandler);_.I("gapi.iframes.Context.prototype.connectIframes",_.Wm.prototype.connectIframes);_.I("gapi.iframes.Iframe",_.Sm);_.I("gapi.iframes.Iframe.prototype.isDisposed",_.Sm.prototype.isDisposed);
_.I("gapi.iframes.Iframe.prototype.getContext",_.Sm.prototype.getContext);_.I("gapi.iframes.Iframe.prototype.getFrameName",_.Sm.prototype.getFrameName);_.I("gapi.iframes.Iframe.prototype.getId",_.Sm.prototype.getId);_.I("gapi.iframes.Iframe.prototype.register",_.Sm.prototype.register);_.I("gapi.iframes.Iframe.prototype.unregister",_.Sm.prototype.unregister);_.I("gapi.iframes.Iframe.prototype.send",_.Sm.prototype.send);_.I("gapi.iframes.Iframe.prototype.applyIframesApi",_.Sm.prototype.applyIframesApi);
_.I("gapi.iframes.Iframe.prototype.getIframeEl",_.Sm.prototype.getIframeEl);_.I("gapi.iframes.Iframe.prototype.getSiteEl",_.Sm.prototype.getSiteEl);_.I("gapi.iframes.Iframe.prototype.setSiteEl",_.Sm.prototype.setSiteEl);_.I("gapi.iframes.Iframe.prototype.getWindow",_.Sm.prototype.getWindow);_.I("gapi.iframes.Iframe.prototype.getOrigin",_.Sm.prototype.getOrigin);_.I("gapi.iframes.Iframe.prototype.close",_.Sm.prototype.close);_.I("gapi.iframes.Iframe.prototype.restyle",_.Sm.prototype.restyle);
_.I("gapi.iframes.Iframe.prototype.restyleDone",_.Sm.prototype.wp);_.I("gapi.iframes.Iframe.prototype.registerWasRestyled",_.Sm.prototype.registerWasRestyled);_.I("gapi.iframes.Iframe.prototype.registerWasClosed",_.Sm.prototype.registerWasClosed);_.I("gapi.iframes.Iframe.prototype.getParam",_.Sm.prototype.getParam);_.I("gapi.iframes.Iframe.prototype.setParam",_.Sm.prototype.setParam);_.I("gapi.iframes.Iframe.prototype.ping",_.Sm.prototype.ping);_.I("gapi.iframes.Iframe.prototype.getOpenParams",_.Sm.prototype.$b);

_.Ne=_.Ne||{};

_.Ne=_.Ne||{};
(function(){function a(c){var d="undefined"===typeof c;if(null!==b&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(-1===h?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(-1!==l){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.Ne.kg=a;a()})();
_.I("gadgets.util.getUrlParameters",_.Ne.kg);

_.qg=window.googleapis&&window.googleapis.server||{};

_.Re=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Le(a());return{register:function(b,c,d){d&&d(_.Ke())},get:function(b){return _.Ke(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Le(b)},wd:function(){}}}();
_.I("gadgets.config.register",_.Re.register);_.I("gadgets.config.get",_.Re.get);_.I("gadgets.config.init",_.Re.wd);_.I("gadgets.config.update",_.Re.update);

_.I("gadgets.json.stringify",_.yf);_.I("gadgets.json.parse",_.xf);

(function(){function a(e,f){if(!(e<c)&&d)if(2===e&&d.warn)d.warn(f);else if(3===e&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.Oe=function(e){a(2,e)};_.Pe=function(e){a(3,e)};_.Qe=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();

_.Ne=_.Ne||{};(function(){var a=[];_.Ne.Dda=function(b){a.push(b)};_.Ne.Pda=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();

_.I("gapi.logutil.enableDebugLogging",_.Df.BK);

_.Ef=function(){var a=_.me.readyState;return"complete"===a||"interactive"===a&&-1==navigator.userAgent.indexOf("MSIE")};_.Ff=function(a){if(_.Ef())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.le.addEventListener?(_.le.addEventListener("load",c,!1),_.le.addEventListener("DOMContentLoaded",c,!1)):_.le.attachEvent&&(_.le.attachEvent("onreadystatechange",function(){_.Ef()&&c.apply(this,arguments)}),_.le.attachEvent("onload",c))}};
_.Gf=function(a,b){var c=_.pe(_.Be,"watt",_.re());_.pe(c,a,b)};_.ve(_.le.location.href,"rpctoken")&&_.Ae(_.me,"unload",function(){});
var Hf=Hf||{};Hf.DP=null;Hf.gO=null;Hf.Cw=null;Hf.frameElement=null;
Hf=Hf||{};
Hf.$H||(Hf.$H=function(){function a(f,h,k){"undefined"!=typeof window.addEventListener?window.addEventListener(f,h,k):"undefined"!=typeof window.attachEvent&&window.attachEvent("on"+f,h);"message"===f&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.xf(f.data);if(h&&h.f){_.Qe();var k=_.If.jm(h.f);e&&("undefined"!==typeof f.origin?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.Pe("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{qL:function(){return"wpm"},XZ:function(){return!0},wd:function(f,h){_.Re.register("rpc",null,function(k){"true"===String((k&&k.rpc||{}).disableForceSecure)&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Cb:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.If.jm(f),m=_.If.MI(f);l?window.setTimeout(function(){var n=_.yf(k);_.Qe();m.postMessage(n,l)},0):".."!=f&&_.Pe("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());
if(window.gadgets&&window.gadgets.rpc)"undefined"!=typeof _.If&&_.If||(_.If=window.gadgets.rpc,_.If.config=_.If.config,_.If.register=_.If.register,_.If.unregister=_.If.unregister,_.If.lP=_.If.registerDefault,_.If.oR=_.If.unregisterDefault,_.If.dL=_.If.forceParentVerifiable,_.If.call=_.If.call,_.If.Er=_.If.getRelayUrl,_.If.Ei=_.If.setRelayUrl,_.If.Ty=_.If.setAuthToken,_.If.nt=_.If.setupReceiver,_.If.Vl=_.If.getAuthToken,_.If.PE=_.If.removeReceiver,_.If.NL=_.If.getRelayChannel,_.If.jP=_.If.receive,
_.If.kP=_.If.receiveSameDomain,_.If.getOrigin=_.If.getOrigin,_.If.jm=_.If.getTargetOrigin,_.If.MI=_.If._getTargetWin,_.If.hU=_.If._parseSiblingId);else{_.If=function(){function a(H,X){if(!T[H]){var Aa=sa;X||(Aa=db);T[H]=Aa;X=M[H]||[];for(var Ia=0;Ia<X.length;++Ia){var ua=X[Ia];ua.t=E[H];Aa.call(H,ua.f,ua)}M[H]=[]}}function b(){function H(){ma=!0}da||("undefined"!=typeof window.addEventListener?window.addEventListener("unload",H,!1):"undefined"!=typeof window.attachEvent&&window.attachEvent("onunload",
H),da=!0)}function c(H,X,Aa,Ia,ua){E[X]&&E[X]===Aa||(_.Pe("Invalid gadgets.rpc token. "+E[X]+" vs "+Aa),ra(X,2));ua.onunload=function(){J[X]&&!ma&&(ra(X,1),_.If.PE(X))};b();Ia=_.xf(decodeURIComponent(Ia))}function d(H,X){if(H&&"string"===typeof H.s&&"string"===typeof H.f&&H.a instanceof Array)if(E[H.f]&&E[H.f]!==H.t&&(_.Pe("Invalid gadgets.rpc token. "+E[H.f]+" vs "+H.t),ra(H.f,2)),"__ack"===H.s)window.setTimeout(function(){a(H.f,!0)},0);else{H.c&&(H.callback=function(wa){_.If.call(H.f,(H.g?"legacy__":
"")+"__cb",null,H.c,wa)});if(X){var Aa=e(X);H.origin=X;var Ia=H.r;try{var ua=e(Ia)}catch(wa){}Ia&&ua==Aa||(Ia=X);H.referer=Ia}X=(u[H.s]||u[""]).apply(H,H.a);H.c&&"undefined"!==typeof X&&_.If.call(H.f,"__cb",null,H.c,X)}}function e(H){if(!H)return"";H=H.split("#")[0].split("?")[0];H=H.toLowerCase();0==H.indexOf("//")&&(H=window.location.protocol+H);-1==H.indexOf("://")&&(H=window.location.protocol+"//"+H);var X=H.substring(H.indexOf("://")+3),Aa=X.indexOf("/");-1!=Aa&&(X=X.substring(0,Aa));H=H.substring(0,
H.indexOf("://"));if("http"!==H&&"https"!==H&&"chrome-extension"!==H&&"file"!==H&&"android-app"!==H&&"chrome-search"!==H&&"chrome-untrusted"!==H&&"chrome"!==H&&"devtools"!==H)throw Error("n");Aa="";var Ia=X.indexOf(":");if(-1!=Ia){var ua=X.substring(Ia+1);X=X.substring(0,Ia);if("http"===H&&"80"!==ua||"https"===H&&"443"!==ua)Aa=":"+ua}return H+"://"+X+Aa}function f(H){if("/"==H.charAt(0)){var X=H.indexOf("|");return{id:0<X?H.substring(1,X):H.substring(1),origin:0<X?H.substring(X+1):null}}return null}
function h(H){if("undefined"===typeof H||".."===H)return window.parent;var X=f(H);if(X)return window.top.frames[X.id];H=String(H);return(X=window.frames[H])?X:(X=document.getElementById(H))&&X.contentWindow?X.contentWindow:null}function k(H,X){if(!0!==J[H]){"undefined"===typeof J[H]&&(J[H]=0);var Aa=h(H);".."!==H&&null==Aa||!0!==sa.Cb(H,X)?!0!==J[H]&&10>J[H]++?window.setTimeout(function(){k(H,X)},500):(T[H]=db,J[H]=!0):J[H]=!0}}function l(H){(H=w[H])&&"/"===H.substring(0,1)&&(H="/"===H.substring(1,
2)?document.location.protocol+H:document.location.protocol+"//"+document.location.host+H);return H}function m(H,X,Aa){X&&!/http(s)?:\/\/.+/.test(X)&&(0==X.indexOf("//")?X=window.location.protocol+X:"/"==X.charAt(0)?X=window.location.protocol+"//"+window.location.host+X:-1==X.indexOf("://")&&(X=window.location.protocol+"//"+X));w[H]=X;"undefined"!==typeof Aa&&(z[H]=!!Aa)}function n(H,X){X=X||"";E[H]=String(X);k(H,X)}function q(H){H=(H.passReferrer||"").split(":",2);L=H[0]||"none";V=H[1]||"origin"}
function p(H){"true"===String(H.useLegacyProtocol)&&(sa=Hf.Cw||db,sa.wd(d,a))}function t(H,X){function Aa(Ia){Ia=Ia&&Ia.rpc||{};q(Ia);var ua=Ia.parentRelayUrl||"";ua=e(Q.parent||X)+ua;m("..",ua,"true"===String(Ia.useLegacyProtocol));p(Ia);n("..",H)}!Q.parent&&X?Aa({}):_.Re.register("rpc",null,Aa)}function v(H,X,Aa){if(".."===H)t(Aa||Q.rpctoken||Q.ifpctok||"",X);else a:{var Ia=null;if("/"!=H.charAt(0)){if(!_.Ne)break a;Ia=document.getElementById(H);if(!Ia)throw Error("o`"+H);}Ia=Ia&&Ia.src;X=X||e(Ia);
m(H,X);X=_.Ne.kg(Ia);n(H,Aa||X.rpctoken)}}var u={},w={},z={},E={},A=0,y={},J={},Q={},T={},M={},L=null,V=null,oa=window.top!==window.self,ia=window.name,ra=function(){},Ka=window.console,Ga=Ka&&Ka.log&&function(H){Ka.log(H)}||function(){},db=function(){function H(X){return function(){Ga(X+": call ignored")}}return{qL:function(){return"noop"},XZ:function(){return!0},wd:H("init"),Cb:H("setup"),call:H("call")}}();_.Ne&&(Q=_.Ne.kg());var ma=!1,da=!1,sa=function(){if("rmr"==Q.rpctx)return Hf.DP;var H="function"===
typeof window.postMessage?Hf.$H:"object"===typeof window.postMessage?Hf.$H:window.ActiveXObject?Hf.gO?Hf.gO:Hf.Cw:0<navigator.userAgent.indexOf("WebKit")?Hf.DP:"Gecko"===navigator.product?Hf.frameElement:Hf.Cw;H||(H=db);return H}();u[""]=function(){Ga("Unknown RPC service: "+this.s)};u.__cb=function(H,X){var Aa=y[H];Aa&&(delete y[H],Aa.call(this,X))};return{config:function(H){"function"===typeof H.KP&&(ra=H.KP)},register:function(H,X){if("__cb"===H||"__ack"===H)throw Error("p");if(""===H)throw Error("q");
u[H]=X},unregister:function(H){if("__cb"===H||"__ack"===H)throw Error("r");if(""===H)throw Error("s");delete u[H]},lP:function(H){u[""]=H},oR:function(){delete u[""]},dL:function(){},call:function(H,X,Aa,Ia){H=H||"..";var ua="..";".."===H?ua=ia:"/"==H.charAt(0)&&(ua=e(window.location.href),ua="/"+ia+(ua?"|"+ua:""));++A;Aa&&(y[A]=Aa);var wa={s:X,f:ua,c:Aa?A:0,a:Array.prototype.slice.call(arguments,3),t:E[H],l:!!z[H]};a:if("bidir"===L||"c2p"===L&&".."===H||"p2c"===L&&".."!==H){var Pa=window.location.href;
var P="?";if("query"===V)P="#";else if("hash"===V)break a;P=Pa.lastIndexOf(P);P=-1===P?Pa.length:P;Pa=Pa.substring(0,P)}else Pa=null;Pa&&(wa.r=Pa);if(".."===H||null!=f(H)||document.getElementById(H))(Pa=T[H])||null===f(H)||(Pa=sa),0===X.indexOf("legacy__")&&(Pa=sa,wa.s=X.substring(8),wa.c=wa.c?wa.c:A),wa.g=!0,wa.r=ua,Pa?(z[H]&&(Pa=Hf.Cw),!1===Pa.call(H,ua,wa)&&(T[H]=db,sa.call(H,ua,wa))):M[H]?M[H].push(wa):M[H]=[wa]},Er:l,Ei:m,Ty:n,nt:v,Vl:function(H){return E[H]},PE:function(H){delete w[H];delete z[H];
delete E[H];delete J[H];delete T[H]},NL:function(){return sa.qL()},jP:function(H,X){4<H.length?sa.iba(H,d):c.apply(null,H.concat(X))},kP:function(H){H.a=Array.prototype.slice.call(H.a);window.setTimeout(function(){d(H)},0)},getOrigin:e,jm:function(H){var X=null,Aa=l(H);Aa?X=Aa:(Aa=f(H))?X=Aa.origin:".."==H?X=Q.parent:(H=document.getElementById(H))&&"iframe"===H.tagName.toLowerCase()&&(X=H.src);return e(X)},wd:function(){!1===sa.wd(d,a)&&(sa=db);oa?v(".."):_.Re.register("rpc",null,function(H){H=H.rpc||
{};q(H);p(H)})},MI:h,hU:f,w4:"__ack",J9:ia||"..",T9:0,S9:1,R9:2}}();_.If.wd()}
;_.If.config({KP:function(a){throw Error("t`"+a);}});
_.I("gadgets.rpc.config",_.If.config);_.I("gadgets.rpc.register",_.If.register);_.I("gadgets.rpc.unregister",_.If.unregister);_.I("gadgets.rpc.registerDefault",_.If.lP);_.I("gadgets.rpc.unregisterDefault",_.If.oR);_.I("gadgets.rpc.forceParentVerifiable",_.If.dL);_.I("gadgets.rpc.call",_.If.call);_.I("gadgets.rpc.getRelayUrl",_.If.Er);_.I("gadgets.rpc.setRelayUrl",_.If.Ei);_.I("gadgets.rpc.setAuthToken",_.If.Ty);_.I("gadgets.rpc.setupReceiver",_.If.nt);_.I("gadgets.rpc.getAuthToken",_.If.Vl);
_.I("gadgets.rpc.removeReceiver",_.If.PE);_.I("gadgets.rpc.getRelayChannel",_.If.NL);_.I("gadgets.rpc.receive",_.If.jP);_.I("gadgets.rpc.receiveSameDomain",_.If.kP);_.I("gadgets.rpc.getOrigin",_.If.getOrigin);_.I("gadgets.rpc.getTargetOrigin",_.If.jm);

_.Ne=_.Ne||{};_.Ne.xU=function(a){var b=window;"undefined"!=typeof b.addEventListener?b.addEventListener("mousemove",a,!1):"undefined"!=typeof b.attachEvent?b.attachEvent("onmousemove",a):_.Oe("cannot attachBrowserEvent: mousemove")};_.Ne.f1=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.Oe("cannot removeBrowserEvent: mousemove")};

_.vg=function(){function a(m){var n=new _.ug;n.Pt(m);return n.Jh()}var b=window.crypto;if(b&&"function"==typeof b.getRandomValues)return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Ke("random/maxObserveMousemove");null==c&&(c=-1);var d=0,e=Math.random(),f=1,h=1E6*(screen.width*screen.width+screen.height),k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;0<c&&++d==c&&_.Ne.f1(k)};
0!=c&&_.Ne.xU(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+Math.pow(16,20))}}();
_.I("shindig.random",_.vg);

var wg=function(a){return{execute:function(b){var c={method:a.httpMethod||"GET",root:a.root,path:a.url,params:a.urlParams,headers:a.headers,body:a.body},d=window.gapi,e=function(){var f=d.config.get("client/apiKey"),h=d.config.get("client/version");try{var k=d.config.get("googleapis.config/developerKey"),l=d.config.get("client/apiKey",k);d.config.update("client/apiKey",l);d.config.update("client/version","1.0.0-alpha");var m=d.client;m.request.call(m,c).then(b,b)}finally{d.config.update("client/apiKey",
f),d.config.update("client/version",h)}};d.client?e():d.load.call(d,"client",e)}}},xg=function(a,b){return function(c){var d={};c=c.body;var e=_.xf(c),f={};if(e&&e.length)for(var h=0,k=e.length;h<k;++h){var l=e[h];f[l.id]=l}h=0;for(k=b.length;h<k;++h)l=b[h].id,d[l]=e&&e.length?f[l]:e;a(d,c)}},yg=function(a){a.transport={name:"googleapis",execute:function(b,c){for(var d=[],e=0,f=b.length;e<f;++e){var h=b[e],k=h.method,l=String(k).split(".")[0];l=_.Ke("googleapis.config/versions/"+k)||_.Ke("googleapis.config/versions/"+
l)||"v1";d.push({jsonrpc:"2.0",id:h.id,method:k,apiVersion:String(l),params:h.params})}b=wg({httpMethod:"POST",root:a.transport.root,url:"/rpc?pp=0",headers:{"Content-Type":"application/json"},body:d});b.execute.call(b,xg(c,d))},root:void 0}},zg=function(a){var b=this.method,c=this.transport;c.execute.call(c,[{method:b,id:b,params:this.rpc}],function(d){d=d[b];d.error||(d=d.data||d.result);a(d)})},Bg=function(){for(var a=Ag,b=a.split("."),c=function(k){k=k||{};k.groupId=k.groupId||"@self";k.userId=
k.userId||"@viewer";k={method:a,rpc:k||{}};yg(k);k.execute=zg;return k},d=_.D,e=0,f=b.length;e<f;++e){var h=d[b[e]]||{};e+1==f&&(h=c);d=d[b[e]]=h}if(1<b.length&&"googleapis"!=b[0])for(b[0]="googleapis","delete"==b[b.length-1]&&(b[b.length-1]="remove"),d=_.D,e=0,f=b.length;e<f;++e)h=d[b[e]]||{},e+1==f&&(h=c),d=d[b[e]]=h},Ag;for(Ag in _.Ke("googleapis.config/methods"))Bg();
_.I("googleapis.newHttpRequest",function(a){return wg(a)});_.I("googleapis.setUrlParameter",function(a,b){if("trace"!==a)throw Error("w");_.Le("client/trace",b)});

});
// Google Inc.
