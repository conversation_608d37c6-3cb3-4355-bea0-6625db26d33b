<?php
/**
 * Copyright (c) 2021.  @aashif
 */

session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
include_once $include_path . 'common_functions.php';
require_once $include_path . "email_functions.php";
include_once $class_path . 'class.student.php';
include '../form-submit-email.php';
require_once __DIR__ . '/../class/class.sms.php';
$cSms = new Sms();

// $base_url =  "http://localhost/gg-portal/";
// $base_url =  "https://ggportal.acutetech.lk/";


$student_id = filter_input(INPUT_POST, "student_id", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$first_name = filter_input(INPUT_POST, "first_name", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$last_name = filter_input(INPUT_POST, "last_name", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$mobile = filter_input(INPUT_POST, "mobile", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$email = filter_input(INPUT_POST, "email", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$password = filter_input(INPUT_POST, "password", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session

if($email!="" && $first_name!="" && $last_name!="" && $password!=""  && $mobile!="" ){
    $cStudent = new Student();

    //check if student email exists
    $res_email = $cStudent -> getLogin($email);

    if (count($res_email)) {
        errorRedirect("Email Already Exist ","../signup.php"); 
        die();  
    }
    $password = convert_string('encrypt',$password); //encrypt the password
    $criteria = array(
        'student_id' => $student_id,
        'first_name' => $first_name,
        'last_name' => $last_name,
        'username' => $email,
        'email' => $email,
        'mobile' => $mobile,
        'category' => 'EX',
        'gender' => '',
        'date_of_birth' => '',
        'marital_status' => '',
        'country' => '',
        'state' => '',
        'city' => '',
        'password' => $password,
        'password_salt' => '',
        'user_type' => 'ST',
        'email_validate_yn' => 'N',
        'otp_validate_yn' => 'N',
        'profile_picture' => '',
        'user_active_yn' => 'N',
        'user_id' => 0,
        'emergency_contact_name' => '',
        'emergency_contact_mobile' => '',
        'emergency_contact_email' => '',
        'remarks' => '',
        'real_time_status' => '',
    );


    $result = $cStudent->registerStudent($criteria);
    
    if ($result>0) {
        //$cStudent->sendEmail($email, $password);
        $token_id = uniqid();
        $address = $email;
        $subject = 'Activate your account';
        
        $body = '<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
    <style>
        .header {
            background-color: #293C4A;
            padding: 20px;
            font-size: 25px;
            text-align: center;
            font-weight: bolder;
        }

        .header h2 {
            color: #fff;
            margin-bottom: 0px;
        }

 

        .header p {
            padding-top: 0px;
            margin-top: 0px;
            color: #fff;
            font-size: 16px;
        }

    
    </style>
</head>

<body>
<span style="opacity: 0"> '.date("Y-m-d H:i:s").'</span>
<div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
<table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
  <tbody><tr>
    <td align="center" valign="top">
      <table width="650" border="0" cellspacing="0" cellpadding="0">
        <tbody><tr>
          <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
            
              <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                              
                  <tbody><tr>
                      <td class="header">
                          <h2 style="font-weight: 700;">Edvios</h2>
                          <p style="padding-top: 30px;">The Student Portal</p>
                      </td>
                  </tr>
              </tbody></table>
            
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tbody><tr>
                <td>
                  <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                    <tbody><tr>
                      <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr>
                            <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                              Hi '.$last_name.'</td>
                          </tr>
                          
                        </tbody></table>
                      </td>
                    </tr>
                  </tbody></table>
                </td>
              </tr>
            </tbody></table>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody><tr>
                          <td>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                              <tbody><tr>
                                <td style="padding:0px 20px">
                                  <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr height="5px">
                                      <td width="100%">
                                          &nbsp;                              
                                      </td>
                                    </tr>
                                    
                                    <tr height="50px">
                                       <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                        <p> You have been registered to the Edvios. Please click on the link below to verify your account.</p>                             
                                      </td>                             
                                    </tr>
                                      
                                    <tr height="50px">
                                       <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                       <a class="primary-btn" href="'.$base_url.'student-activate-account.php?token_id='.$token_id.'&st='.$result.'">Verify Account</a>                            
                                      </td>                             
                                    </tr>

                                    <tr height="50px">
                                    <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                    If you did not register to the Edvios, please ignore this email.                            
                                   </td>                             
                                 </tr>
                                    <tr>
                                      <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                        <p>Thank you.<br> Kind Regards,<br> Edvios.io</p>
                                      </td>                             
                                    </tr> 
                                        <tr height="15px">
                                            <td width="100%">&nbsp;</td>
                                        </tr>
                                                         
                                                               
                                  </tbody></table>
                                </td>
                              </tr>
                            </tbody></table>
                          </td>
                        </tr>
                      </tbody></table>

            
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
              <tbody><tr>
                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody><tr>
                      <td align="center" style="padding-bottom:10px">
                        <table border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                          <tr>
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                          </tr>							
                        </tbody></table>
                      </td>
                    </tr>
                      
                  
                  </tbody></table>
                  
                </td>
              </tr>
            </tbody></table>
            
            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
              <tbody><tr>
                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                      <tbody><tr>
                      <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                          Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                    </tr>
                   
                  
                  </tbody></table>
                  
                </td>
              </tr>
            </tbody></table>
            
          </td>
        </tr>
      </tbody></table>
    </td>
  </tr>
</tbody></table>
<span style="opacity: 0"> '.date("Y-m-d H:i:s").'</span>

</body>

</html>';
        
        $status = send_mail($subject,$address,$body);
        $message = "Welcome to Edvios! Your account has been successfully created.";       
        $student_phone_number = (string)$mobile;
        $cSms->send_sms($message, $student_phone_number);
        errorRedirect("Registration Successful, check your mail and activate","../login.php",'success');
    } else {
        errorRedirect($result,"../signup.php");
    }
    
}else{   
    header("location:../signup.php");
    die();
}


 //otp send
if($email!="" && $first_name!="" && $last_name!="" && $password!=""  && $mobile!="" ){
    $cStudent = new Student();
 
    $res_mobile = $cStudent -> getLogin($mobile);

    if (count($res_mobile)) {
        errorRedirect("OTP Already Exist ","../signup.php"); 
        die();  
    }
    $password = convert_string('encrypt',$password); //encrypt the password
    $criteria = array(
        'student_id' => $student_id,
        'first_name' => $first_name,
        'last_name' => $last_name,
        'username' => $email,
        'email' => $email,
        'mobile' => $mobile,
        'category' => 'EX',
        'gender' => '',
        'date_of_birth' => '1990-01-01',
        'marital_status' => '',
        'country' => '',
        'state' => '',
        'city' => '',
        'password' => $password,
        'password_salt' => '',
        'user_type' => 'ST',
        'email_validate_yn' => 'N',
        'otp_validate_yn' => 'N',
        'profile_picture' => '',
        'user_active_yn' => 'N',
        'user_id' => 0,
        'emergency_contact_name' => '',
        'emergency_contact_mobile' => '',
        'emergency_contact_email' => '',
        'remarks' => '',
        'real_time_status' => '',
    );


    $result = $cStudent->registerStudent($criteria);
    
    if ($result>0) {
        //$cStudent->sendotp($otp, $password);
        $token_id = uniqid();
        $address = $mobile;
        $subject = 'Activate your account';
        
        $body =  
        
        $status = send_otp($subject,$address,$body);
        errorRedirect("Registration Successful, check your phone number and activate","../login.php",'success');
    } else {
        errorRedirect($result,"../signup.php");
    }
    
}else{   
    header("location:../signup.php");
    die();
}


?>