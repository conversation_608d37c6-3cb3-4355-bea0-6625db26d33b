<?php

$access_level = $_SESSION['user']['access_level'];
$staff_id = $_SESSION['user']['user_id'];

?>
<!-- Main Sidebar Container -->
<aside class="main-sidebar elevation-4 sidebar-primary">
    <!-- Brand Logo -->
    <a href="dashboard.php" class="brand-link ctm-brand-link">
        <img src="dist/img/Icon.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">Edvios</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-child-indent  nav-sidebar flex-column" data-widget="treeview" role="menu"
                data-accordion="false">
                <!-- Add icons to the links using the .nav-icon class
                     with font-awesome or any other icon font library -->
                <li class="nav-item">
                    <a href="dash-staff.php" class="nav-link">
                        <i class="nav-icon fas fa-th"></i>
                        <p>
                            Dashboard
                            <!-- <span class="right badge badge-danger">New</span> -->
                        </p>
                    </a>
                </li>

                <?php
                if (
                    $access_level == 80
                    || $access_level == 85
                    || $access_level == 90
                    || $access_level == 75
                    || $access_level == 40
                    || $access_level == 100
                ) {
                ?>
                <li class="nav-item">
                    <a href="program.php?staff_id=<?= $staff_id ?>" class="nav-link">
                    <i class="nav-icon fas fa-book-open"></i>
                        <p>
                            Program Finder
                        </p>
                    </a>
                </li>
                <?php
                }
                ?>
                
                <?php
                if (
                    $access_level == 80
                    || $access_level == 85
                    || $access_level == 90
                    || $access_level == 75
                    || $access_level == 40
                    || $access_level == 100
                ) {
                ?>
                <li class="nav-item">
                    <a href="student-list-view-satff.php?staff_id=<?= $staff_id ?>" class="nav-link">
                        <i class="nav-icon fas fa-graduation-cap"></i>
                        <p>
                            Students
                        </p>
                    </a>
                </li>
                <?php
                }
                ?>
                <?php
                if (
                    $access_level == 80
                    || $access_level == 85
                    || $access_level == 90
                    || $access_level == 75
                    || $access_level == 40
                    || $access_level == 100
                ) {
                ?>
                <li class="nav-item">
                    <a href="student-application-view-all.php?staff_id=<?= $staff_id ?>" class="nav-link">
                        <i class="nav-icon fas fa-book"></i>
                        <p>
                            Student Applications
                        </p>
                    </a>
                </li>
                
                <?php
                }
                ?>
                <?php
                if ($access_level == 40) {
                ?>
                <!-- <li class="nav-item">
                    <a href="staff-list.php" class="nav-link">
                        <i class="nav-icon fas fa-user-check"></i>
                        <p>
                            Manage Staff
                        </p>
                    </a>
                </li> -->
                <?php
                }
                ?>
                <?php
                if ($access_level == 80 || $access_level == 85 || $access_level == 90 || $access_level == 75) {
                ?>
                <!-- <li class="nav-item">
                    <a href="staff-student-list.php" class="nav-link">
                        <i class="nav-icon fas fa-user-check"></i>
                        <p>
                            Job Assigned
                        </p>
                    </a>
                </li> -->
                <?php
                }
                ?>
                <?php
                if ($access_level == 50) {
                ?>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fas fa-receipt"></i>
                        <p>
                            Invoices
                        </p>
                    </a>
                </li>
                <?php
                }
                ?>
                <?php
                if ($access_level == 50) {
                ?>

                <li class="nav-item">
                    <a href="commission-list.php" class="nav-link">
                        <i class="nav-icon fas fa-percentage"></i>
                        <p>
                            Commission Structure
                        </p>
                    </a>
                </li>
                <?php
                }
                ?>

            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
<script>
$(document).ready(function() {

    var url = window.location;
    // for single sidebar menu
    $('ul.nav-sidebar a').filter(function() {
        return this.href == url;
    }).addClass('active');

    // for sidebar menu and treeview
    $('ul.nav-treeview a').filter(function() {
            return this.href == url;
        }).parentsUntil(".nav-sidebar > .nav-treeview")
        .css({
            'display': 'block'
        })
        .addClass('menu-open').prev('a')
        .addClass('active');




});
</script>