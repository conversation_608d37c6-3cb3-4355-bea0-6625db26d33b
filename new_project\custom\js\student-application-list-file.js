$(document).ready(function () {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });


    $('.delete_btn').on('click',function(){
      var country_id = $(this).data('country_id');
      
      Swal.fire({
              title: 'Are you sure want to delete?',
              showCancelButton: true,
              confirmButtonText: `Delete`,
              confirmButtonColor: '#d33',
          }).then((result) => {
              /* Read more about isConfirmed, isDenied below */
              if (result.isConfirmed) {

                $.ajax({
                    type:"POST",
                    url:"controller/country-controller.php",
                    data:{"country_id":country_id,"deleteCountry":1},
                    dataType: 'json',
                    success:function(data){
                        //console.log(data);
                        if(data.data>0){
                            Swal.fire(
                              'Deleted!',
                              'Your file has been deleted.',
                              'success'
                            )
                            window.location.reload();
                        }else {
                            toastr.error(data.status);
                        }

                    }
                });

         
              }
          })
    });
   

  });