<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cCourse = new Course();
$cLoadCombo = new LoadCombo();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

// get course  Combo
if(isset($_POST['getCourseCombo'])){
    
    
    // $course_id = filter_input(INPUT_POST, 'course_id');
    // $institute_id = filter_input(INPUT_POST, 'institute_id');

    // $course_combo_dts = array(
    //     'id_match' => ( $course_id > 0 ? $course_id : 0 ),
    //     'institute_id' => ( $institute_id > 0 ? $institute_id : 0 )
    // );

    // $result = $cLoadCombo->getCourseMultipleCombo($course_combo_dts);

    // $json = array(
    //     "data" =>$result
    //     ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    // );
    // echo json_encode($json);
    // die();
}


// get course  Details
if(isset($_POST['getCourseDetails'])){
    
    $course_id_str = filter_input(INPUT_POST, 'course_id_str');

    $criteria = array(
        "course_id_str" => $course_id_str
    );

    $result = $cCourse->getCourses($criteria);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// save Course
if(isset($_POST['courseSave'])){

    $course_id = filter_input(INPUT_POST, 'course_id');
    $course_name = filter_input(INPUT_POST, 'course_name');
    $course_type = filter_input(INPUT_POST, 'course_type');
    $course_country_id = filter_input(INPUT_POST, 'country_id');
    $course_state = filter_input(INPUT_POST, 'state_id');
    $course_city = filter_input(INPUT_POST, 'city');
    $web_url = filter_input(INPUT_POST, 'web_url');
    $logo_url = filter_input(INPUT_POST, 'logo_url');
    $email = filter_input(INPUT_POST, 'email');
    $mobile = filter_input(INPUT_POST, 'mobile');


    
    if(isset($_FILES['logo_url']) && $_FILES['logo_url']['name'] != "") {
        $file_name = $_FILES['logo_url']['name'];
        $file_size = $_FILES['logo_url']['size'];
        $file_tmp = $_FILES['logo_url']['tmp_name'];
        $file_type = $_FILES['logo_url']['type'];
        
        $exp = explode('.', $_FILES['logo_url']['name']);
        $file_ext = strtolower(end($exp));

        // timestamp
        $time = time();
        
        //remove a file already exists
        if($logo_url != ""){
            if(file_exists("../".$logo_url)) {
                unlink("../".$logo_url);
            }
        }

        //upload the file
        if( move_uploaded_file($file_tmp,"../dist/uploads/course/".$time.'.'.$file_ext)){
            $logo_url = "dist/uploads/course/".$time.'.'.$file_ext;
        }
        
    }
    
    //add to array 
    $criteria = array(
        'course_id' => $course_id,
        'course_name' => $course_name,
        'course_type' => $course_type,
        'course_state' => $course_state,
        'course_country_id' => $course_country_id,
        'course_city' => $course_city,
        'web_url' => $web_url,
        'logo_url' => $logo_url,
        'email' => $email,
        'mobile' => $mobile
    );

    
         

    $result = $ccourse->saveCourse($criteria);
    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}


// delete Course
if(isset($_POST['deleteCourse'])){

    $course_id = filter_input(INPUT_POST,'course_id');
    
    $result = $cCourse-> deleteCourse($course_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}