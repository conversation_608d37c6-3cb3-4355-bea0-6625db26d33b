$(document).ready(function() {


    var monkeyList = new List('test-list', {
        valueNames: ['name', 'application_id', 'student_id', 'status_name', 'institiute_name',
            'student_mail', 'student_mobile'
        ],
        page: 100,
        pagination: true
    });

    $('.show_results').on('change', function() {
        var searchTerm = $(this).val();

        monkeyList = new List('test-list', {
            valueNames: ['name'],
            page: searchTerm,
            pagination: true
        });
    });

    /*On here to anime end is progress animation*/

    // foreach .progress_count 

    $('.progress_count').each(function() {
        var progress_count = $(this).data('progress');
        var application_no = $(this).data('application_no');
        //console.log({progress_count, application_no});

        // if progress_count is greater than 0

        if (progress_count > 0) {

            var i = 1;
            animateProgress(i, application_no, $('.step01_' + application_no + '  .progress_' +
                application_no), 100, progress_count);
        }


    });

    //var progress_count = $('.progress_count').val();

    function animateProgress(i, applicationNo, $progressBar, val, stepDone, currentVal) {
        //console.log({$progressBar, val, stepDone, currentVal,i,applicationNo});
        currentVal = currentVal || 0;
        var step = val * 15 / 300;

        function animate(currentVal) {
            currentVal += step;
            $progressBar.val(currentVal);
            currentVal < val && requestAnimationFrame(function() {
                animate(currentVal);
            });
        }

        animate(currentVal);
        stepDone--;
        i++;
        if (stepDone > 0)
            animateProgress(i, applicationNo, $('.step0' + i + '_' + applicationNo + '  .progress_' +
                applicationNo), 100, stepDone);
    }

    /*anime end*/
    $('.modal-open-btn-spl').on('click', function() {
        // console.log('rea');
        $('.detele_special_note').addClass('d-none');
        var status_id = $(this).data('status_id');
        var student_application_id = $(this).data('student_application_id');
       
        // $('name=status_id').val(status_id);
        // $('#status_id').val(status_id);

        //disable all option status before selected status

        // find status_id by select name
        var application_log_id = $('input[name="current_application_log_id_'+student_application_id+'"]').val();
        var special_note = $('input[name="special_note_'+student_application_id+'"]').val();

        $('input[name="application_log_id"]').val(application_log_id);
        $('textarea[name="special_note"]').val(special_note);

        if (special_note != '') {
            $('.detele_special_note').removeClass('d-none');
        }
            

    });


    $('.modal-open-btn').on('click', function() {
        var status_id = $(this).data('status_id');
        $('#select_status_combo').val(status_id);  // Set the value for select_status_combo
    
        // Disable all options before the selected status
        $('select[name="application_status_id"]').val(parseInt(status_id) + 1);

    });
    


    $('.updateStatusForm_submit').on('click', function() {

        var id = $(this).data('form_id');
        //console.log(id);
        $('.loading-status').removeClass('d-none');

        $('#' + id).validate({
            rules: {
                select_status_combo: {
                    required: true,
                }
            },
            messages: {

                select_status_combo: {
                    required: "Please choose a One",
                },
            },
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            },
            submitHandler: function(form) {
                //log serialized form
                // var formData = $(form).serialize();
                // console.log(formData);

                var formData = new FormData(form);
                formData.append('updateStatus', '1');

                $.ajax({
                    type: "POST",
                    url: "controller/application-controller.php",
                    data: formData,
                    dataType: 'json',
                    mimeType: "multipart/form-data",
                    contentType: false,
                    processData: false,
                    cache: false,
                    success: function(data) {
                        
                        $('.loading-status').addClass('d-none');
                        //console.log(data.data);
                        if (data.data > 0) {
                            toastr.success('Saved Successfully');
                            $('.modal').modal("toggle");

                            window.location.reload();
                        } else {
                            toastr.error('There is an error while saving.');
                        }

                    }
                });
            }
        });
        var isValid = $('#' + id).valid();
        if (isValid) {
            $('#' + id).submit();
        } else {
            $('.loading-status').addClass('d-none');
        }
    });

     /*Save program*/
     $('#updateSpecialNoteForm').validate({
        rules: {
            special_note: {
                required: true,
            },

        },
        messages: {

            special_note: {
                required: "Please Enter a value",
            },
        },
        errorElement: 'span',
        errorPlacement: function(error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function(element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function(element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function(form) {
            // console.log(new FormData(form));
            //console.log('sd');
            $('.loading-note').removeClass('d-none');
            var formData = new FormData(form);
            formData.append('updateSpecialNoteSave', '1');
            $.ajax({
                type: "POST",
                url: "controller/application-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function(data) {
                   // console.log(data);
                   
                    $('.loading-note').addClass('d-none');
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');                        
                        window.location.reload();
                        // window.location.href = 'student-applications.php';
                        $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });
        }
    });


    $('.detele_special_note').on('click', function() {

        var application_log_id = $('input[name="application_log_id"]').val();
        var special_note = $('textarea[name="special_note"]').val();
       
        $.ajax({
                type: "POST",
                url: "controller/application-controller.php",
                data: {
                    updateSpecialNoteSave: '1',
                    application_log_id: application_log_id,
                    special_note: ''
                },
                dataType: 'json',
                cache: false,
                success: function(data) {
                   // console.log(data);
                   
                    $('.loading-note').addClass('d-none');
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        window.location.reload();
                        // window.location.href = 'student-applications.php';
                        $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });

    } );  

});