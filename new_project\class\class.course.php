<?php

include_once 'connection.php';

class Course extends connection
{

    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Course()
    {
        self::__construct();
    }

    private $_conn;


    function getCourseByID($course_id)
    {
        $conn = $this->_conn;
        $course_id = intval($course_id);
        if ($course_id > 0) {
            $cond = "and c.course_id=$course_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_course c
                    WHERE 1=1 $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getCourseIDByName($course_name)
    {
        $conn = $this->_conn;

        $cond = "and c.course_name LIKE '$course_name' OR c.course_name='$course_name'  LIMIT 1 ";
        $query = "SELECT course_id
                  FROM ggportal_tbl_course c
                  WHERE 1=1 $cond;";

        $res = mysqli_query($conn, $query);

        if (!$res) {
            return false;
        }

        $row = mysqli_fetch_assoc($res);

        if (!$row || !isset($row['course_id'])) {
            return false;
        }
        return (int)$row['course_id'];
    }


    function getCourses($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['course_id_str']) && strlen($criteria['course_id_str']) > 0) {
            $course_id_string = mysqli_real_escape_string($conn, $criteria['course_id_str']);
            $cond .= " and s.course_id in ($course_id_string) ";
        }


        if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0) {
            $institute_id = intval($criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }


        $query = "select * from ggportal_tbl_course s
         where 1=1  $cond  group by s.course_id order by s.course_id DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }



    function saveCourse($_details)
    {
        $conn = $this->_conn;
        $course_id = intval($_details['course_id']);


        if ($course_id == 0) {

            $query = "INSERT INTO ggportal_tbl_course SET
                  course_name = '" . mysqli_real_escape_string($conn, $_details['course_name']) . "'               
                    ,course_country_id = '" . mysqli_real_escape_string($conn, $_details['course_country_id']) . "'              
                    ,course_type = '" . mysqli_real_escape_string($conn, $_details['course_type']) . "'              
                    ,course_state = '" . mysqli_real_escape_string($conn, $_details['course_state']) . "'              
                    ,course_city = '" . mysqli_real_escape_string($conn, $_details['course_city']) . "'
                    ,web_url = '" . mysqli_real_escape_string($conn, $_details['web_url']) . "'                         
                    ,logo_url = '" . mysqli_real_escape_string($conn, $_details['logo_url']) . "'                         
                    ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                         
                    ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'                   
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_course SET     
            course_name = '" . mysqli_real_escape_string($conn, $_details['course_name']) . "'               
            ,course_country_id = '" . mysqli_real_escape_string($conn, $_details['course_country_id']) . "'              
            ,course_type = '" . mysqli_real_escape_string($conn, $_details['course_type']) . "'              
            ,course_state = '" . mysqli_real_escape_string($conn, $_details['course_state']) . "'              
            ,course_city = '" . mysqli_real_escape_string($conn, $_details['course_city']) . "'
            ,web_url = '" . mysqli_real_escape_string($conn, $_details['web_url']) . "'                         
            ,logo_url = '" . mysqli_real_escape_string($conn, $_details['logo_url']) . "'                         
            ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                         
            ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'
            WHERE course_id = $course_id;        
            ";
        }

        // return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($course_id == 0) {
                $course_id = mysqli_insert_id($conn);
            }
            return $course_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteCourse($course_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_course` WHERE `course_id` = $course_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $course_id;
        } else {
            return mysqli_error($conn);
        }
    }
    public function getCourseNameById($course_id) {
        $conn = $this->_conn;
        $course_id = intval($course_id);
        
        // Initialize the course name variable
        $course_name = "";
    
        // Check if the course_id is valid
        if ($course_id > 0) {
            // Use prepared statement to prevent SQL injection
            $query = "SELECT course_name FROM ggportal_tbl_course WHERE course_id = ?";
            $stmt = mysqli_prepare($conn, $query);
    
            if ($stmt) {
                // Bind the course_id parameter
                mysqli_stmt_bind_param($stmt, "i", $course_id);
                
                // Execute the query
                mysqli_stmt_execute($stmt);
                
                // Bind the result
                mysqli_stmt_bind_result($stmt, $course_name);
                
                // Fetch the result
                mysqli_stmt_fetch($stmt);
                
                // Close the statement
                mysqli_stmt_close($stmt);
            } else {
                // Error handling for failed prepared statement
                $error_message = mysqli_error($conn);
                echo "Error creating prepared statement: $error_message";
                // You can log or handle this error as needed
            }
        }
        
        // Return the course name
        return $course_name;
    }
}
