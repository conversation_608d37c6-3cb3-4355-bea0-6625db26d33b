<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';
include '../form-submit-email.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST";
if (!validate_page_access($access_available_for)) {
  //will automatically redirect to login page
  die("No Access");
}

$cAgent = new Agent();





// save Agent
if (isset($_POST['agentSave'])) {

  $agent_id = filter_input(INPUT_POST, 'agent_id');
  $ag_company_name = filter_input(INPUT_POST, 'ag_company_name');
  $first_name = filter_input(INPUT_POST, 'first_name');
  $last_name = filter_input(INPUT_POST, 'last_name');
  $username = filter_input(INPUT_POST, 'email');
  $mobile = filter_input(INPUT_POST, 'mobile');
  $email = filter_input(INPUT_POST, 'email');
  $country = filter_input(INPUT_POST, 'country');
  $city = filter_input(INPUT_POST, 'city');
  $commission_rate = filter_input(INPUT_POST, 'commission_rate');
  $parent_agent_id = filter_input(INPUT_POST, 'parent_agent_id');
  $agreement_signed_yn = filter_input(INPUT_POST, 'agreement_signed_yn');


  //check email already exist
  if ($agent_id > 0) {
    $agent_deatils = $cAgent->getAgentByID($agent_id);

    if ($agent_deatils[0]['email'] != $email) {
      $check_email = $cAgent->checkEmail($email, $agent_id);
      if (count($check_email) > 0) {
        $json = array(
          "data" => 0, "status" => "Email already exist"
        );
        echo json_encode($json);
        die();
      }
    }
  } else {
    $res_email = $cAgent->getLogin($email);
    if (count($res_email)) {
      $json = array(
        "data" => 0, "status" => 'Email already exist'
      );
      echo json_encode($json);
      die();
    }
  }


  //format date 
  // $country = filter_input(INPUT_POST, 'country');
  $user_type = 'AG';
  $profile_picture = filter_input(INPUT_POST, 'profile_picture');
  $email_validate_yn = filter_input(INPUT_POST, 'email_validate_yn');
  $active_yn = filter_input(INPUT_POST, 'active_yn');
  $agreement_yn = filter_input(INPUT_POST, 'agreement_yn');
  $pwdChanged = 0;
  $password = filter_input(INPUT_POST, 'password');
  $password_salt = '';
  $br_document = filter_input(INPUT_POST, 'br_document');
  $ag_document = filter_input(INPUT_POST, 'ag_document');

  //if is empty then generate 8 digit passwod
  if (empty($password)) {
    $password1 = substr(rand(1, 99999999), 0, 8);
    //encrypt password
    $password = convert_string('encrypt', $password1);
  } else {
    $old = $cAgent->getAgentByID($agent_id);
    $oldPwd = $old[0]['password'];
    if ($oldPwd != $password) {
      $pwdChanged = 1;
      $password1 = $password;
      $password = convert_string('encrypt', $password);
    } else {
      $pwdChanged = 0;
      $password = $oldPwd;
    }
  }


  //profile picture upload
  if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['name'] != "") {
    $file_name = $_FILES['profile_picture']['name'];
    $file_size = $_FILES['profile_picture']['size'];
    $file_tmp = $_FILES['profile_picture']['tmp_name'];
    $file_type = $_FILES['profile_picture']['type'];

    $exp = explode('.', $_FILES['profile_picture']['name']);
    $file_ext = strtolower(end($exp));

    // timestamp
    $time = time();

    //remove a file already exists
    if ($profile_picture != "") {
      if (file_exists("../" . $profile_picture)) {
        unlink("../" . $profile_picture);
      }
    }

    //upload the file
    if (move_uploaded_file($file_tmp, "../dist/uploads/agent/" . $time . '.' . $file_ext)) {
      $profile_picture = "dist/uploads/agent/" . $time . '.' . $file_ext;
    }
  }


  if ($profile_picture == "") {
    $profile_picture = 'dist/img/user2-160x160.jpg';
  }

  //BR upload
  if (isset($_FILES['br_document']) && $_FILES['br_document']['name'] != "") {
    $file_name = $_FILES['br_document']['name'];
    $file_size = $_FILES['br_document']['size'];
    $file_tmp = $_FILES['br_document']['tmp_name'];
    $file_type = $_FILES['br_document']['type'];

    $exp = explode('.', $_FILES['br_document']['name']);
    $file_ext = strtolower(end($exp));

    // timestamp
    $time = time();

    //remove a file already exists
    if ($br_document != "") {
      if (file_exists("../" . $br_document)) {
        unlink("../" . $br_document);
      }
    }

    //upload the file
    if (move_uploaded_file($file_tmp, "../dist/uploads/agent/" . $time . '.' . $file_ext)) {
      $br_document = "dist/uploads/agent/" . $time . '.' . $file_ext;
    }
  }

  //Agreement upload
  if (isset($_FILES['ag_document']) && $_FILES['ag_document']['name'] != "") {
    $file_name = $_FILES['ag_document']['name'];
    $file_size = $_FILES['ag_document']['size'];
    $file_tmp = $_FILES['ag_document']['tmp_name'];
    $file_type = $_FILES['ag_document']['type'];

    $exp = explode('.', $_FILES['ag_document']['name']);
    $file_ext = strtolower(end($exp));

    // timestamp
    $time = time();

    //remove a file already exists
    if ($ag_document != "") {
      if (file_exists("../" . $ag_document)) {
        unlink("../" . $ag_document);
      }
    }

    //upload the file
    if (move_uploaded_file($file_tmp, "../dist/uploads/agent/" . $time . '.' . $file_ext)) {
      $ag_document = "dist/uploads/agent/" . $time . '.' . $file_ext;
    }
  }




  //bank details
  $bank_id = filter_input(INPUT_POST, 'bank_id');
  $bank_name = filter_input(INPUT_POST, 'bank_name');
  $branch_code = filter_input(INPUT_POST, 'branch_code');
  $branch_name = filter_input(INPUT_POST, 'branch_name');
  $account_name = filter_input(INPUT_POST, 'account_name');
  $is_active = filter_input(INPUT_POST, 'is_active');
  $account_no = filter_input(INPUT_POST, 'account_no');
  $swift_code = filter_input(INPUT_POST, 'swift_code');
  $iban = filter_input(INPUT_POST, 'iban');
  $bank_code = filter_input(INPUT_POST, 'bank_code');


  //add to array 
  $criteria = array(
    'agent_id' => ($agent_id > 0 ? $agent_id : 0),
    'parent_agent_id' => $parent_agent_id,
    'ag_company_name' => $ag_company_name,
    'first_name' => $first_name,
    'last_name' => $last_name,
    'username' => $username,
    'email' => $email,
    'country' => $country,
    'city' => $city,
    'br_document' => $br_document,
    'mobile' => $mobile,
    'password' => $password,
    'password_salt' => $password_salt,
    'user_type' => $user_type,
    'email_validate_yn' => $email_validate_yn,
    'profile_picture' => $profile_picture,
    'active_yn' => $active_yn,
    'agreement_signed_yn' => 'N',
    'commission_rate' => ($commission_rate > 0 ? $commission_rate : 0),
    'ag_document' => $ag_document,

  );

  $result = $cAgent->saveAgent($criteria);



  //if success save agent documents
  if ($result > 0) {
    if ($agent_id < 1 || $pwdChanged == 1) {
      $token_id = uniqid();
      $address = $email;
      $subject = 'Activate your account';

      $body = '<!DOCTYPE html>
    <html>
    
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
        <style>
            .header {
                background-color: #293C4A;
                padding: 20px;
                font-size: 25px;
                text-align: center;
                font-weight: bolder;
            }
    
            .header h2 {
                color: #fff;
                margin-bottom: 0px;
            }
    
     
    
            .header p {
                padding-top: 0px;
                margin-top: 0px;
                color: #fff;
                font-size: 16px;
            }
    
        
        </style>
    </head>
    
    <body>
    <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
    <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
      <tbody><tr>
        <td align="center" valign="top">
          <table width="650" border="0" cellspacing="0" cellpadding="0">
            <tbody><tr>
              <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                
                  <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                  
                      <tbody><tr>
                          <td class="header">
                              <h2 style="font-weight: 700;">Edvios</h2>
                              <p style="padding-top: 30px;">The Agent Portal</p>
                          </td>
                      </tr>
                  </tbody></table>
                
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                  <tbody><tr>
                    <td>
                      <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                        <tbody><tr>
                          <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                              <tbody><tr>
                                <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                  Hi ' . $last_name . '</td>
                              </tr>
                              
                            </tbody></table>
                          </td>
                        </tr>
                      </tbody></table>
                    </td>
                  </tr>
                </tbody></table>
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody><tr>
                              <td>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                  <tbody><tr>
                                    <td style="padding:0px 20px">
                                      <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                        <tbody><tr height="5px">
                                          <td width="100%">
                                              &nbsp;                              
                                          </td>
                                        </tr>
                                        
                                        <tr height="30px">
                                           <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                           <p> You have been registered to the Edvios by a system admin. Please click on the link below to verify your account.</p> 
                                            <p> If you do not need to register, please ignore this email. .</p>                               
                                          </td>                             
                                        </tr>

                                        <tr height="30px">
                                           <td width="100%" style="color:#000000;text-align:left;font-weight:600;font-size:12px;line-height:20px">                             
                                             <p>Use below credentials to login to your account</p>
                                             <p>Username : ' . $email . ' <br> 
                                               Password : ' . $password1 . '
                                             </p>                             
                                          </td>                             
                                        </tr>

                                        <tr height="20px">
                                           <td width="100%" style="color:#FF0000;text-align:left;font-weight:700;font-size:12px;line-height:20px"> 
                                            <p>Please Note : Sign the agreement content and upload the signed PDF to the portal after first login !</p>  
                                          </td>
                                        </tr>


                                        <tr height="25px">
                                           <td width="100%" style="color:#000000;text-align:left;font-weight:700;font-size:14px;line-height:20px">                             
                                           <a class="primary-btn" href="' . $base_url . 'agent-activate-account.php?token_id=' . $token_id . '&st=' . $result . '">Verify Account</a>                            
                                          </td>                             
                                        </tr>
    
                                        <tr>
                                          <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                            <p>Thank you.<br> Kind Regards,<br> Edvios</p>
                                          </td>                             
                                        </tr> 
                                            <tr height="15px">
                                                <td width="100%">&nbsp;</td>
                                            </tr>
                                                             
                                                                   
                                      </tbody></table>
                                    </td>
                                  </tr>
                                </tbody></table>
                              </td>
                            </tr>
                          </tbody></table>
    
                
                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                  <tbody><tr>
                    <td style="padding:10px 30px" bgcolor="#1b2f3e">
                      <table width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tbody><tr>
                          <td align="center" style="padding-bottom:10px">
                            <table border="0" cellspacing="0" cellpadding="0">
                              <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                              <tr>
                                <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                  <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                  <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                
                                <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                  <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                              </tr>							
                            </tbody></table>
                          </td>
                        </tr>
                          
                      
                      </tbody></table>
                      
                    </td>
                  </tr>
                </tbody></table>
                
                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                  <tbody><tr>
                    <td style="padding:10px 30px" bgcolor="#1b2f3e">
                      <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                          <tbody><tr>
                          <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                              Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                        </tr>
                       
                      
                      </tbody></table>
                      
                    </td>
                  </tr>
                </tbody></table>
                
              </td>
            </tr>
          </tbody></table>
        </td>
      </tr>
    </tbody></table>
    <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
    
    </body>
    
    </html>';

      send_mail($subject, $address, $body);
    }

    // save bank details
    $bank_criteria = array(
      'bank_id' => $bank_id,
      'user_id' => $result,
      'user_type' => 'AG',
      'bank_name' => $bank_name,
      'branch_code' => $branch_code,
      'branch_name' => $branch_name,
      'account_name' => $account_name,
      'account_no' => $account_no,
      'swift_code' => $swift_code,
      'is_active' => 'Y',
      'iban' => $iban,
      'bank_code' => $bank_code
    );

    $res_bank = $cAgent->saveAgentBank($bank_criteria);

    // $json = array(
    //    "data" =>$res_bank
    // );
    // echo json_encode($json);
    // die();

  }

  $json = array(
    "data" => $result, "status" => $result
  );
  echo json_encode($json);
  die();
}


// delete Agent
if (isset($_POST['deleteAgent'])) {

  $agent_id = filter_input(INPUT_POST, 'agent_id');

  $result = $cAgent->deleteAgent($agent_id);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while deleting')
  );
  echo json_encode($json);
  die();
}


if (isset($_POST['disableAgent'])) {

  $agent_id = filter_input(INPUT_POST, 'agent_id');

  $result = $cAgent->disableAgent($agent_id);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while action')
  );
  echo json_encode($json);
  die();
}

if (isset($_POST['enableAgent'])) {

  $agent_id = filter_input(INPUT_POST, 'agent_id');

  $result = $cAgent->enableAgent($agent_id);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while action')
  );
  echo json_encode($json);
  die();
}

//update agreement
if (isset($_POST['updateAgreement'])) {

  $agent_id = filter_input(INPUT_POST, 'agent_id');
  $agreement_signed_yn = filter_input(INPUT_POST, 'agreement_signed_yn');
  $ag_document = filter_input(INPUT_POST, 'ag_document');

    //Agreement upload
    if (isset($_FILES['ag_document']) && $_FILES['ag_document']['name'] != "") {
      $file_name = $_FILES['ag_document']['name'];
      $file_size = $_FILES['ag_document']['size'];
      $file_tmp = $_FILES['ag_document']['tmp_name'];
      $file_type = $_FILES['ag_document']['type'];
  
      $exp = explode('.', $_FILES['ag_document']['name']);
      $file_ext = strtolower(end($exp));
  
      // timestamp
      $time = time();
  
      //remove a file already exists
      if ($ag_document != "") {
        if (file_exists("../" . $ag_document)) {
          unlink("../" . $ag_document);
        }
      }
  
      //upload the file
      if (move_uploaded_file($file_tmp, "../dist/uploads/agent/" . $time . '.' . $file_ext)) {
        $ag_document = "dist/uploads/agent/" . $time . '.' . $file_ext;
      }
    }

  $criteria = array(
    'agent_id' => $agent_id,
    'agreement_signed_yn' => ($agreement_signed_yn == 'on' ? 'Y' : 'N'),
    'ag_document' => $ag_document
  );

  $result = $cAgent->updateAgreement($criteria);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}



if (isset($_POST['action']) && $_POST['action'] === 'getLastSeen') {
  $cAgent = new Agent();
  $cStudent = new Student();
  $agent_id = filter_input(INPUT_POST, 'agent_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $user_type = filter_input(INPUT_POST, 'user_type');
  $result = null;
  $last_seen = null;
  $last_seen_status = null;
  $first_name = null;
  
  if ($user_type === "ST") {
    $result = $cAgent->calAgentOnlineStatus($agent_id);  
    $first_name = $result['first_name'];
    $last_seen = $result['last_seen'];
    $last_seen_status = last_seen($last_seen);

    } 
  else if ($user_type === "AG") {
    $result = $cStudent->calStudentOnlineStatus($student_id);     
    $first_name = $result['first_name'];
    $last_seen = $result['last_seen'];
    $last_seen_status = last_seen($last_seen);
     }

  $response = array(
    'first_name' => $first_name,
    'last_seen_status' => $last_seen_status,
    'last_seen' => $last_seen
);
  echo json_encode($response);
  exit;
}


//Assign staff member for agent
if(isset($_POST['agentAssignSave'])){
  $agent_id = filter_input(INPUT_POST, 'agent_id');
  $staff_id = filter_input(INPUT_POST, 'staff_id');
  $result = $cAgent->assignStaff($agent_id,$staff_id);
  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}