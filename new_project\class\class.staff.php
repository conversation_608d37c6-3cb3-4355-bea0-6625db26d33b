<?php

include_once 'connection.php';

class Staff extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    public function Staff()
    {
        self::__construct();
    }

    private $_conn;

    public function getLogin($username)
    {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $username) . "' ";
        $query = "SELECT u.*,sp.* FROM ggportal_tbl_staff u 
         left join ggportal_tbl_staff_privilege sp on sp.staff_privilege_id = u.user_privilege_id
        WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    public function checkEmail($email, $staff_id)
    {
        // print_r($email);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $email) . "' ";
        $cond = $cond." and u.staff_id not in( '" . mysqli_real_escape_string($conn, $staff_id) . "') ";
        $query = "SELECT u.* FROM ggportal_tbl_staff u 
         left join ggportal_tbl_staff_privilege sp on sp.staff_privilege_id = u.user_privilege_id
        WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    public function randomPassword()
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $password = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < 8; $i++) {
            $n = rand(0, $alphaLength);
            $password[] = $alphabet[$n];
        }
        return implode($password); //turn the array into a string
    }

    public function getStaffByID($staff_id)
    {
        $conn = $this->_conn;
        $staff_id = intval($staff_id);
        if ($staff_id > 0) {
            $cond = "and s.staff_id=$staff_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "select s.* from ggportal_tbl_staff s
        left join ggportal_tbl_staff_privilege sp on sp.staff_privilege_id = s.user_privilege_id
                    WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    public function getStaffs($criteria)
    {
        $conn = $this->_conn;
        $cond = "";


        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $agent_id = mysqli_real_escape_string($conn, $criteria['agent_id']);
        }

        if (isset($criteria['parent_user_id']) && strlen($criteria['parent_user_id']) > 0) {
            $parent_user_id = intval($criteria['parent_user_id']);
            $cond .= " and s.parent_user_id  = $parent_user_id ";
        }

        if (isset($criteria['parent_user_type']) && strlen($criteria['parent_user_type']) > 0) {
            $parent_user_type = mysqli_real_escape_string($conn, $criteria['parent_user_type']);
            $cond .= " and s.parent_user_type = '$parent_user_type' ";
        }

        if (isset($criteria['privilage_active']) && strlen($criteria['privilage_active']) > 0) {
            $privilage_active = mysqli_real_escape_string($conn, $criteria['privilage_active']);
            $cond .= " and sp.active_yn = '$privilage_active' ";
        }

        $query = "select s.*,sp.name as user_type_name, sp.access_level from ggportal_tbl_staff s
        left join ggportal_tbl_staff_privilege sp on sp.staff_privilege_id = s.user_privilege_id
         where 1=1  $cond order by s.staff_id DESC;";
        //        echo $query;
        //    die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }
    function updateStaffAgent($staff_id, $agent_id)
    {
        $conn = $this->_conn;

        $query = "UPDATE ggportal_tbl_staff
            SET assign_to_agent = $agent_id
            WHERE staff_id = $staff_id ;";

        $res = mysqli_query($conn, $query);
        if ($res) {
            return $staff_id;
        } else {
            return mysqli_error($conn);
        }
    }

    public function getStaffsParent($criteria)
    {
        $conn = $this->_conn;
        $cond = "";
        $table_name = "ggportal_tbl_user";


        if (isset($criteria['parent_user_type']) && strlen($criteria['parent_user_type']) > 0 && isset($criteria['parent_user_id']) && strlen($criteria['parent_user_id']) > 0) {
            $parent_user_type = mysqli_real_escape_string($conn, $criteria['parent_user_type']);
            $parent_user_id = intval($criteria['parent_user_id']);

            // $cond .= " and s.parent_user_type = '$parent_user_type' ";

            if ($parent_user_type == 'RA') {
                $table_name = "ggportal_tbl_user";
                $cond .= " and s.user_id  = $parent_user_id ";
            } elseif ($parent_user_type == 'AG') {
                $table_name = "ggportal_tbl_agent";
                $cond .= " and s.agent_id  = $parent_user_id ";
            }
        }

        $query = "select s.* from $table_name s
         where 1=1  $cond ;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }


    public function saveStaff($_details)
    {
        $conn = $this->_conn;
        $staff_id = intval($_details['staff_id']);

        $order_no_check = "SELECT * FROM `ggportal_tbl_staff` WHERE 1 ORDER BY staff_no DESC LIMIT 1";
        $result = mysqli_query($conn, $order_no_check);
        $latest_order = db_to_array($result);
        if (count($latest_order) > 0) {
            //random number generator between 1-10
            // $random_number = rand(2, 4);
            $staff_no = $latest_order[0]['staff_no'] + 1;
        } else {
            $staff_no = '1001';
        }

        if ($staff_id == 0) {
            $query = "INSERT INTO ggportal_tbl_staff SET  
                 staff_no = " . intval($staff_no) . "   
                ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,country_id = '" . mysqli_real_escape_string($conn, $_details['country_id']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,user_privilege_id = '" . mysqli_real_escape_string($conn, $_details['user_privilege_id']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'
                ,created_by =  '" . intval($_details['created_by']) . "'
                ,created_at = now()      
                ,updated_by =  '" . intval($_details['updated_by']) . "'
                ,updated_at = now()    
                ,parent_user_type = '" . mysqli_real_escape_string($conn, $_details['parent_user_type']) . "'                                   
                ,parent_user_id = '" . mysqli_real_escape_string($conn, $_details['parent_user_id']) . "'                  
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_staff SET            
                first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,country_id = '" . mysqli_real_escape_string($conn, $_details['country_id']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,user_privilege_id = '" . mysqli_real_escape_string($conn, $_details['user_privilege_id']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'
                ,updated_by =  '" . intval($_details['updated_by']) . "'
                ,updated_at = now()    
                ,parent_user_type = '" . mysqli_real_escape_string($conn, $_details['parent_user_type']) . "'                       
                ,parent_user_id = '" . mysqli_real_escape_string($conn, $_details['parent_user_id']) . "'                 
            WHERE staff_id = $staff_id";
        }
        // return $query;
        //    die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($staff_id == 0) {
                $staff_id = mysqli_insert_id($conn);
            }
            return $staff_id;
        } else {
            return mysqli_error($conn);
        }
    }


    public function saveUserLogin($user_id, $login_id = 0)
    {
        global $_conn;
        $user_id = intval($user_id);
        $login_id = intval($login_id);
        if ($login_id > 0) {
            // $query="update ggportal_tbl_user_login set last_activity=now() where user_login_id=$login_id;";
        } else {
            //update tbl_user and reset the login try
            // $query="update ggportal_tbl_user SET last_login_date=now()
            // ,login_failure_try=0
            // ,login_failure_ip=''
            // ,login_failure_date=null
            // where user_id=$user_id;";
            // $res=mysqli_query($_conn,$query);

            //insert to tbl_user_login
            $query = "Insert into ggportal_tbl_staff_login SET
                user_id=$user_id
                ,date_login=now()
                ,ip_address='" . getIP() . "'
                ,last_activity=now(); ";
        }

        $res = mysqli_query($_conn, $query);
        //echo $query;
        //die();
        if ($res) {
            if ($login_id == 0) {
                $login_id = mysqli_insert_id($_conn);
            }
        } else {
            $login_id = 0;
        }
        return $login_id;
    }


    public function deleteStaff($staff_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_staff` WHERE `staff_id` = $staff_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $staff_id;
        } else {
            return mysqli_error($conn);
        }
    }

    public function activateAccount($staff_id, $token_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_staff SET email_validate_yn = 'Y' WHERE staff_id = $staff_id";
        $res = mysqli_query($conn, $query);
        // echo $query;
        // die();

        if ($res) {
            return true;
        } else {
            return mysqli_error($conn);
        }
    }

    public function calStaffOnlineStatus($staff_id, $student_id)
    {
        $conn = $this->_conn;

        $query = "SELECT first_name, last_seen FROM ggportal_tbl_staff WHERE staff_id = $staff_id";
        $res = mysqli_query($conn, $query);
        if ($res === false) {
            error_log("Query failed: " . mysqli_error($conn));
            return null;
        }
        $resultData = mysqli_fetch_assoc($res);

        return $resultData;
    }


    //Get staff by parent user id
    function getStaffByParentID($criteria)
    {
        $conn = $this->_conn;
        $cond = "";
        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0 && isset($criteria['user_id']) && strlen($criteria['user_id']) > 0) {
            $parent_user_id = intval($criteria['user_id']);
            $parent_user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond = "and s.parent_user_id=$parent_user_id and s.parent_user_type='$parent_user_type' ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "select s.* from ggportal_tbl_staff s
                  WHERE 1=1 $cond;";
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    public function savePassDetails($passdetails)
    {
        $conn = $this->_conn;

        // Sanitize input
        $staff_id = mysqli_real_escape_string($conn, $passdetails['staff_id']);
        $password1 = $passdetails['password'];
        $password = convert_string('encrypt', $password1); // Encrypt the password

        // Construct the SQL query to update the password using agent_id
        $query = "UPDATE `ggportal_tbl_staff` SET `password` = '$password' WHERE staff_id = '$staff_id'";

        // Execute the query
        $res = mysqli_query($conn, $query);

        if ($res) {
            // Password updated successfully
            return true;
        } else {
            // Error updating password
            echo "Error: " . mysqli_error($conn);
            return false;
        }
    }
    public function getStaffMobileNumberById($staff_id){
        $conn = $this->_conn;
        $staff_id = intval($staff_id);    
             
        $query = "SELECT mobile FROM ggportal_tbl_staff WHERE staff_id = $staff_id";
        $res = mysqli_query($conn, $query);
    
        if ($res && mysqli_num_rows($res) > 0) {
            $row = mysqli_fetch_assoc($res);
            return $row['mobile']; 
        } else {
            return null; 
        }
    }

    //get staff name by id 
    public function getStaffNameById($staff_id){
        $conn = $this->_conn;
        $staff_id = intval($staff_id);    
             
        $query = "SELECT first_name, last_name  FROM ggportal_tbl_staff WHERE staff_id = $staff_id";
        $res = mysqli_query($conn, $query);
    
        if ($res && mysqli_num_rows($res) > 0) {
            $row = mysqli_fetch_assoc($res);
            return $row['first_name'] . ' ' . $row['last_name']; 
        } else {
            return null; 
        }
    }

    // remove assignment of staff to student
    public function removeAssignment($staff_id, $student_id="")
    {
        $conn = $this->_conn;
        $staff_id = intval($staff_id);        
        $query = "UPDATE ggportal_tbl_student SET assign_to_staff = 0 WHERE assign_to_staff = $staff_id";
        $res = mysqli_query($conn, $query);
        if ($res) { 
            return true;    
        } else {
            return false;
        }
    }
}
