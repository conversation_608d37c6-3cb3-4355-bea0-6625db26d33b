$(document).ready(function() {
        
        
      
    var progress_count = $('.progress_count').data('progress');
    //console.log(progress_count);
    var i = 1;
    animateProgress(i,$('.step01  .progress'), 100,progress_count);

    function animateProgress(i,$progressBar, val, stepDone,currentVal) {
        currentVal = currentVal || 0;
        var step = val * 15 / 300;

        function animate(currentVal) {
            currentVal += step;
            $progressBar.val(currentVal);
            currentVal < val && requestAnimationFrame(function() {
                animate(currentVal);
            });
        }
        animate(currentVal);
        stepDone--;
        i++;
        //console.log({stepDone,i});
        if(stepDone>0)
            animateProgress(i,$('.step0'+i+'  .progress'), 100,stepDone);
    }
});