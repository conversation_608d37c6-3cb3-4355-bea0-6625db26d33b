/*!
 *   AdminLTE v3.1.0
 *     Only Extra Components
 *   Author: Colorlib
 *   Website: AdminLTE.io <https://adminlte.io>
 *   License: Open source - MIT <https://opensource.org/licenses/MIT>
 */
@-webkit-keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@-webkit-keyframes shake {
  0% {
    -webkit-transform: translate(2px, 1px) rotate(0deg);
    transform: translate(2px, 1px) rotate(0deg);
  }
  10% {
    -webkit-transform: translate(-1px, -2px) rotate(-2deg);
    transform: translate(-1px, -2px) rotate(-2deg);
  }
  20% {
    -webkit-transform: translate(-3px, 0) rotate(3deg);
    transform: translate(-3px, 0) rotate(3deg);
  }
  30% {
    -webkit-transform: translate(0, 2px) rotate(0deg);
    transform: translate(0, 2px) rotate(0deg);
  }
  40% {
    -webkit-transform: translate(1px, -1px) rotate(1deg);
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    -webkit-transform: translate(-1px, 2px) rotate(-1deg);
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    -webkit-transform: translate(-3px, 1px) rotate(0deg);
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    -webkit-transform: translate(2px, 1px) rotate(-2deg);
    transform: translate(2px, 1px) rotate(-2deg);
  }
  80% {
    -webkit-transform: translate(-1px, -1px) rotate(4deg);
    transform: translate(-1px, -1px) rotate(4deg);
  }
  90% {
    -webkit-transform: translate(2px, 2px) rotate(0deg);
    transform: translate(2px, 2px) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(1px, -2px) rotate(-1deg);
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@keyframes shake {
  0% {
    -webkit-transform: translate(2px, 1px) rotate(0deg);
    transform: translate(2px, 1px) rotate(0deg);
  }
  10% {
    -webkit-transform: translate(-1px, -2px) rotate(-2deg);
    transform: translate(-1px, -2px) rotate(-2deg);
  }
  20% {
    -webkit-transform: translate(-3px, 0) rotate(3deg);
    transform: translate(-3px, 0) rotate(3deg);
  }
  30% {
    -webkit-transform: translate(0, 2px) rotate(0deg);
    transform: translate(0, 2px) rotate(0deg);
  }
  40% {
    -webkit-transform: translate(1px, -1px) rotate(1deg);
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    -webkit-transform: translate(-1px, 2px) rotate(-1deg);
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    -webkit-transform: translate(-3px, 1px) rotate(0deg);
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    -webkit-transform: translate(2px, 1px) rotate(-2deg);
    transform: translate(2px, 1px) rotate(-2deg);
  }
  80% {
    -webkit-transform: translate(-1px, -1px) rotate(4deg);
    transform: translate(-1px, -1px) rotate(4deg);
  }
  90% {
    -webkit-transform: translate(2px, 2px) rotate(0deg);
    transform: translate(2px, 2px) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(1px, -2px) rotate(-1deg);
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@-webkit-keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

.small-box {
  border-radius: 0.25rem;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);
  display: block;
  margin-bottom: 20px;
  position: relative;
}

.small-box > .inner {
  padding: 10px;
}

.small-box > .small-box-footer {
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.8);
  display: block;
  padding: 3px 0;
  position: relative;
  text-align: center;
  text-decoration: none;
  z-index: 10;
}

.small-box > .small-box-footer:hover {
  background-color: rgba(0, 0, 0, 0.15);
  color: #fff;
}

.small-box h3 {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 0 10px;
  padding: 0;
  white-space: nowrap;
}

@media (min-width: 992px) {
  .col-xl-2 .small-box h3,
  .col-lg-2 .small-box h3,
  .col-md-2 .small-box h3 {
    font-size: 1.6rem;
  }
  .col-xl-3 .small-box h3,
  .col-lg-3 .small-box h3,
  .col-md-3 .small-box h3 {
    font-size: 1.6rem;
  }
}

@media (min-width: 1200px) {
  .col-xl-2 .small-box h3,
  .col-lg-2 .small-box h3,
  .col-md-2 .small-box h3 {
    font-size: 2.2rem;
  }
  .col-xl-3 .small-box h3,
  .col-lg-3 .small-box h3,
  .col-md-3 .small-box h3 {
    font-size: 2.2rem;
  }
}

.small-box p {
  font-size: 1rem;
}

.small-box p > small {
  color: #f8f9fa;
  display: block;
  font-size: .9rem;
  margin-top: 5px;
}

.small-box h3,
.small-box p {
  z-index: 5;
}

.small-box .icon {
  color: rgba(0, 0, 0, 0.15);
  z-index: 0;
}

.small-box .icon > i {
  font-size: 90px;
  position: absolute;
  right: 15px;
  top: 15px;
  transition: -webkit-transform 0.3s linear;
  transition: transform 0.3s linear;
  transition: transform 0.3s linear, -webkit-transform 0.3s linear;
}

.small-box .icon > i.fa, .small-box .icon > i.fas, .small-box .icon > i.far, .small-box .icon > i.fab, .small-box .icon > i.fal, .small-box .icon > i.fad, .small-box .icon > i.ion {
  font-size: 70px;
  top: 20px;
}

.small-box .icon svg {
  font-size: 70px;
  position: absolute;
  right: 15px;
  top: 15px;
  transition: -webkit-transform 0.3s linear;
  transition: transform 0.3s linear;
  transition: transform 0.3s linear, -webkit-transform 0.3s linear;
}

.small-box:hover {
  text-decoration: none;
}

.small-box:hover .icon > i, .small-box:hover .icon > i.fa, .small-box:hover .icon > i.fas, .small-box:hover .icon > i.far, .small-box:hover .icon > i.fab, .small-box:hover .icon > i.fal, .small-box:hover .icon > i.fad, .small-box:hover .icon > i.ion {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.small-box:hover .icon > svg {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

@media (max-width: 767.98px) {
  .small-box {
    text-align: center;
  }
  .small-box .icon {
    display: none;
  }
  .small-box p {
    font-size: 12px;
  }
}

.info-box {
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
  background-color: #fff;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1rem;
  min-height: 80px;
  padding: .5rem;
  position: relative;
  width: 100%;
}

.info-box .progress {
  background-color: rgba(0, 0, 0, 0.125);
  height: 2px;
  margin: 5px 0;
}

.info-box .progress .progress-bar {
  background-color: #fff;
}

.info-box .info-box-icon {
  border-radius: 0.25rem;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 1.875rem;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  width: 70px;
}

.info-box .info-box-icon > img {
  max-width: 100%;
}

.info-box .info-box-content {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  line-height: 1.8;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0 10px;
}

.info-box .info-box-number {
  display: block;
  margin-top: .25rem;
  font-weight: 700;
}

.info-box .progress-description,
.info-box .info-box-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-box .info-box .bg-primary,
.info-box .info-box .bg-gradient-primary {
  color: #fff;
}

.info-box .info-box .bg-primary .progress-bar,
.info-box .info-box .bg-gradient-primary .progress-bar {
  background-color: #fff;
}

.info-box .info-box .bg-secondary,
.info-box .info-box .bg-gradient-secondary {
  color: #fff;
}

.info-box .info-box .bg-secondary .progress-bar,
.info-box .info-box .bg-gradient-secondary .progress-bar {
  background-color: #fff;
}

.info-box .info-box .bg-success,
.info-box .info-box .bg-gradient-success {
  color: #fff;
}

.info-box .info-box .bg-success .progress-bar,
.info-box .info-box .bg-gradient-success .progress-bar {
  background-color: #fff;
}

.info-box .info-box .bg-info,
.info-box .info-box .bg-gradient-info {
  color: #fff;
}

.info-box .info-box .bg-info .progress-bar,
.info-box .info-box .bg-gradient-info .progress-bar {
  background-color: #fff;
}

.info-box .info-box .bg-warning,
.info-box .info-box .bg-gradient-warning {
  color: #1f2d3d;
}

.info-box .info-box .bg-warning .progress-bar,
.info-box .info-box .bg-gradient-warning .progress-bar {
  background-color: #1f2d3d;
}

.info-box .info-box .bg-danger,
.info-box .info-box .bg-gradient-danger {
  color: #fff;
}

.info-box .info-box .bg-danger .progress-bar,
.info-box .info-box .bg-gradient-danger .progress-bar {
  background-color: #fff;
}

.info-box .info-box .bg-light,
.info-box .info-box .bg-gradient-light {
  color: #1f2d3d;
}

.info-box .info-box .bg-light .progress-bar,
.info-box .info-box .bg-gradient-light .progress-bar {
  background-color: #1f2d3d;
}

.info-box .info-box .bg-dark,
.info-box .info-box .bg-gradient-dark {
  color: #fff;
}

.info-box .info-box .bg-dark .progress-bar,
.info-box .info-box .bg-gradient-dark .progress-bar {
  background-color: #fff;
}

.info-box .info-box-more {
  display: block;
}

.info-box .progress-description {
  margin: 0;
}

@media (min-width: 768px) {
  .col-xl-2 .info-box .progress-description,
  .col-lg-2 .info-box .progress-description,
  .col-md-2 .info-box .progress-description {
    display: none;
  }
  .col-xl-3 .info-box .progress-description,
  .col-lg-3 .info-box .progress-description,
  .col-md-3 .info-box .progress-description {
    display: none;
  }
}

@media (min-width: 992px) {
  .col-xl-2 .info-box .progress-description,
  .col-lg-2 .info-box .progress-description,
  .col-md-2 .info-box .progress-description {
    font-size: 0.75rem;
    display: block;
  }
  .col-xl-3 .info-box .progress-description,
  .col-lg-3 .info-box .progress-description,
  .col-md-3 .info-box .progress-description {
    font-size: 0.75rem;
    display: block;
  }
}

@media (min-width: 1200px) {
  .col-xl-2 .info-box .progress-description,
  .col-lg-2 .info-box .progress-description,
  .col-md-2 .info-box .progress-description {
    font-size: 1rem;
    display: block;
  }
  .col-xl-3 .info-box .progress-description,
  .col-lg-3 .info-box .progress-description,
  .col-md-3 .info-box .progress-description {
    font-size: 1rem;
    display: block;
  }
}

.dark-mode .info-box {
  background-color: #343a40;
  color: #fff;
}

.dark-mode .info-box .info-box .bg-primary,
.dark-mode .info-box .info-box .bg-gradient-primary {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-primary .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-primary .progress-bar {
  background-color: #fff;
}

.dark-mode .info-box .info-box .bg-secondary,
.dark-mode .info-box .info-box .bg-gradient-secondary {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-secondary .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-secondary .progress-bar {
  background-color: #fff;
}

.dark-mode .info-box .info-box .bg-success,
.dark-mode .info-box .info-box .bg-gradient-success {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-success .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-success .progress-bar {
  background-color: #fff;
}

.dark-mode .info-box .info-box .bg-info,
.dark-mode .info-box .info-box .bg-gradient-info {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-info .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-info .progress-bar {
  background-color: #fff;
}

.dark-mode .info-box .info-box .bg-warning,
.dark-mode .info-box .info-box .bg-gradient-warning {
  color: #1f2d3d;
}

.dark-mode .info-box .info-box .bg-warning .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-warning .progress-bar {
  background-color: #1f2d3d;
}

.dark-mode .info-box .info-box .bg-danger,
.dark-mode .info-box .info-box .bg-gradient-danger {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-danger .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-danger .progress-bar {
  background-color: #fff;
}

.dark-mode .info-box .info-box .bg-light,
.dark-mode .info-box .info-box .bg-gradient-light {
  color: #1f2d3d;
}

.dark-mode .info-box .info-box .bg-light .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-light .progress-bar {
  background-color: #1f2d3d;
}

.dark-mode .info-box .info-box .bg-dark,
.dark-mode .info-box .info-box .bg-gradient-dark {
  color: #fff;
}

.dark-mode .info-box .info-box .bg-dark .progress-bar,
.dark-mode .info-box .info-box .bg-gradient-dark .progress-bar {
  background-color: #fff;
}

.timeline {
  margin: 0 0 45px;
  padding: 0;
  position: relative;
}

.timeline::before {
  border-radius: 0.25rem;
  background-color: #dee2e6;
  bottom: 0;
  content: "";
  left: 31px;
  margin: 0;
  position: absolute;
  top: 0;
  width: 4px;
}

.timeline > div {
  margin-bottom: 15px;
  margin-right: 10px;
  position: relative;
}

.timeline > div::before, .timeline > div::after {
  content: "";
  display: table;
}

.timeline > div > .timeline-item {
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
  background-color: #fff;
  color: #495057;
  margin-left: 60px;
  margin-right: 15px;
  margin-top: 0;
  padding: 0;
  position: relative;
}

.timeline > div > .timeline-item > .time {
  color: #999;
  float: right;
  font-size: 12px;
  padding: 10px;
}

.timeline > div > .timeline-item > .timeline-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  color: #495057;
  font-size: 16px;
  line-height: 1.1;
  margin: 0;
  padding: 10px;
}

.timeline > div > .timeline-item > .timeline-header > a {
  font-weight: 600;
}

.timeline > div > .timeline-item > .timeline-body,
.timeline > div > .timeline-item > .timeline-footer {
  padding: 10px;
}

.timeline > div > .timeline-item > .timeline-body > img {
  margin: 10px;
}

.timeline > div > .timeline-item > .timeline-body > dl,
.timeline > div > .timeline-item > .timeline-body ol,
.timeline > div > .timeline-item > .timeline-body ul {
  margin: 0;
}

.timeline > div > .timeline-item > .timeline-footer > a {
  color: #fff;
}

.timeline > div > .fa,
.timeline > div > .fas,
.timeline > div > .far,
.timeline > div > .fab,
.timeline > div > .fal,
.timeline > div > .fad,
.timeline > div > .svg-inline--fa,
.timeline > div > .ion {
  background-color: #adb5bd;
  border-radius: 50%;
  font-size: 16px;
  height: 30px;
  left: 18px;
  line-height: 30px;
  position: absolute;
  text-align: center;
  top: 0;
  width: 30px;
}

.timeline > div > .svg-inline--fa {
  padding: 7px;
}

.timeline > .time-label > span {
  border-radius: 4px;
  background-color: #fff;
  display: inline-block;
  font-weight: 600;
  padding: 5px;
}

.timeline-inverse > div > .timeline-item {
  box-shadow: none;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.timeline-inverse > div > .timeline-item > .timeline-header {
  border-bottom-color: #dee2e6;
}

.dark-mode .timeline::before {
  background-color: #6c757d;
}

.dark-mode .timeline > div > .timeline-item {
  background-color: #343a40;
  color: #fff;
  border-color: #6c757d;
}

.dark-mode .timeline > div > .timeline-item > .timeline-header {
  color: #ced4da;
  border-color: #6c757d;
}

.dark-mode .timeline > div > .timeline-item > .time {
  color: #ced4da;
}

.products-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.products-list > .item {
  border-radius: 0.25rem;
  background-color: #fff;
  padding: 10px 0;
}

.products-list > .item::after {
  display: block;
  clear: both;
  content: "";
}

.products-list .product-img {
  float: left;
}

.products-list .product-img img {
  height: 50px;
  width: 50px;
}

.products-list .product-info {
  margin-left: 60px;
}

.products-list .product-title {
  font-weight: 600;
}

.products-list .product-description {
  color: #6c757d;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-list-in-card > .item {
  border-radius: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.product-list-in-card > .item:last-of-type {
  border-bottom-width: 0;
}

.dark-mode .products-list > .item {
  background-color: #343a40;
  color: #fff;
  border-bottom-color: #6c757d;
}

.dark-mode .product-description {
  color: #ced4da;
}

.direct-chat .card-body {
  overflow-x: hidden;
  padding: 0;
  position: relative;
}

.direct-chat.chat-pane-open .direct-chat-contacts {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.direct-chat.timestamp-light .direct-chat-timestamp {
  color: #30465f;
}

.direct-chat.timestamp-dark .direct-chat-timestamp {
  color: #cccccc;
}

.direct-chat-messages {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  height: 250px;
  overflow: auto;
  padding: 10px;
}

.direct-chat-msg,
.direct-chat-text {
  display: block;
}

.direct-chat-msg {
  margin-bottom: 10px;
}

.direct-chat-msg::after {
  display: block;
  clear: both;
  content: "";
}

.direct-chat-messages,
.direct-chat-contacts {
  transition: -webkit-transform .5s ease-in-out;
  transition: transform .5s ease-in-out;
  transition: transform .5s ease-in-out, -webkit-transform .5s ease-in-out;
}

.direct-chat-text {
  border-radius: 0.3rem;
  background-color: #d2d6de;
  border: 1px solid #d2d6de;
  color: #444;
  margin: 5px 0 0 50px;
  padding: 5px 10px;
  position: relative;
}

.direct-chat-text::after, .direct-chat-text::before {
  border: solid transparent;
  border-right-color: #d2d6de;
  content: " ";
  height: 0;
  pointer-events: none;
  position: absolute;
  right: 100%;
  top: 15px;
  width: 0;
}

.direct-chat-text::after {
  border-width: 5px;
  margin-top: -5px;
}

.direct-chat-text::before {
  border-width: 6px;
  margin-top: -6px;
}

.right .direct-chat-text {
  margin-left: 0;
  margin-right: 50px;
}

.right .direct-chat-text::after, .right .direct-chat-text::before {
  border-left-color: #d2d6de;
  border-right-color: transparent;
  left: 100%;
  right: auto;
}

.direct-chat-img {
  border-radius: 50%;
  float: left;
  height: 40px;
  width: 40px;
}

.right .direct-chat-img {
  float: right;
}

.direct-chat-infos {
  display: block;
  font-size: 0.875rem;
  margin-bottom: 2px;
}

.direct-chat-name {
  font-weight: 600;
}

.direct-chat-timestamp {
  color: #697582;
}

.direct-chat-contacts-open .direct-chat-contacts {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.direct-chat-contacts {
  -webkit-transform: translate(101%, 0);
  transform: translate(101%, 0);
  background-color: #343a40;
  bottom: 0;
  color: #fff;
  height: 250px;
  overflow: auto;
  position: absolute;
  top: 0;
  width: 100%;
}

.direct-chat-contacts-light {
  background-color: #f8f9fa;
}

.direct-chat-contacts-light .contacts-list-name {
  color: #495057;
}

.direct-chat-contacts-light .contacts-list-date {
  color: #6c757d;
}

.direct-chat-contacts-light .contacts-list-msg {
  color: #545b62;
}

.contacts-list {
  padding-left: 0;
  list-style: none;
}

.contacts-list > li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  margin: 0;
  padding: 10px;
}

.contacts-list > li::after {
  display: block;
  clear: both;
  content: "";
}

.contacts-list > li:last-of-type {
  border-bottom: 0;
}

.contacts-list-img {
  border-radius: 50%;
  float: left;
  width: 40px;
}

.contacts-list-info {
  color: #fff;
  margin-left: 45px;
}

.contacts-list-name,
.contacts-list-status {
  display: block;
}

.contacts-list-name {
  font-weight: 600;
}

.contacts-list-status {
  font-size: 0.875rem;
}

.contacts-list-date {
  color: #ced4da;
  font-weight: 400;
}

.contacts-list-msg {
  color: #b1bbc4;
}

.direct-chat-primary .right > .direct-chat-text {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.direct-chat-primary .right > .direct-chat-text::after, .direct-chat-primary .right > .direct-chat-text::before {
  border-left-color: #007bff;
}

.direct-chat-secondary .right > .direct-chat-text {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.direct-chat-secondary .right > .direct-chat-text::after, .direct-chat-secondary .right > .direct-chat-text::before {
  border-left-color: #6c757d;
}

.direct-chat-success .right > .direct-chat-text {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

.direct-chat-success .right > .direct-chat-text::after, .direct-chat-success .right > .direct-chat-text::before {
  border-left-color: #28a745;
}

.direct-chat-info .right > .direct-chat-text {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

.direct-chat-info .right > .direct-chat-text::after, .direct-chat-info .right > .direct-chat-text::before {
  border-left-color: #17a2b8;
}

.direct-chat-warning .right > .direct-chat-text {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #1f2d3d;
}

.direct-chat-warning .right > .direct-chat-text::after, .direct-chat-warning .right > .direct-chat-text::before {
  border-left-color: #ffc107;
}

.direct-chat-danger .right > .direct-chat-text {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.direct-chat-danger .right > .direct-chat-text::after, .direct-chat-danger .right > .direct-chat-text::before {
  border-left-color: #dc3545;
}

.direct-chat-light .right > .direct-chat-text {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #1f2d3d;
}

.direct-chat-light .right > .direct-chat-text::after, .direct-chat-light .right > .direct-chat-text::before {
  border-left-color: #f8f9fa;
}

.direct-chat-dark .right > .direct-chat-text {
  background-color: #343a40;
  border-color: #343a40;
  color: #fff;
}

.direct-chat-dark .right > .direct-chat-text::after, .direct-chat-dark .right > .direct-chat-text::before {
  border-left-color: #343a40;
}

.direct-chat-lightblue .right > .direct-chat-text {
  background-color: #3c8dbc;
  border-color: #3c8dbc;
  color: #fff;
}

.direct-chat-lightblue .right > .direct-chat-text::after, .direct-chat-lightblue .right > .direct-chat-text::before {
  border-left-color: #3c8dbc;
}

.direct-chat-navy .right > .direct-chat-text {
  background-color: #001f3f;
  border-color: #001f3f;
  color: #fff;
}

.direct-chat-navy .right > .direct-chat-text::after, .direct-chat-navy .right > .direct-chat-text::before {
  border-left-color: #001f3f;
}

.direct-chat-olive .right > .direct-chat-text {
  background-color: #3d9970;
  border-color: #3d9970;
  color: #fff;
}

.direct-chat-olive .right > .direct-chat-text::after, .direct-chat-olive .right > .direct-chat-text::before {
  border-left-color: #3d9970;
}

.direct-chat-lime .right > .direct-chat-text {
  background-color: #01ff70;
  border-color: #01ff70;
  color: #1f2d3d;
}

.direct-chat-lime .right > .direct-chat-text::after, .direct-chat-lime .right > .direct-chat-text::before {
  border-left-color: #01ff70;
}

.direct-chat-fuchsia .right > .direct-chat-text {
  background-color: #f012be;
  border-color: #f012be;
  color: #fff;
}

.direct-chat-fuchsia .right > .direct-chat-text::after, .direct-chat-fuchsia .right > .direct-chat-text::before {
  border-left-color: #f012be;
}

.direct-chat-maroon .right > .direct-chat-text {
  background-color: #d81b60;
  border-color: #d81b60;
  color: #fff;
}

.direct-chat-maroon .right > .direct-chat-text::after, .direct-chat-maroon .right > .direct-chat-text::before {
  border-left-color: #d81b60;
}

.direct-chat-blue .right > .direct-chat-text {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.direct-chat-blue .right > .direct-chat-text::after, .direct-chat-blue .right > .direct-chat-text::before {
  border-left-color: #007bff;
}

.direct-chat-indigo .right > .direct-chat-text {
  background-color: #6610f2;
  border-color: #6610f2;
  color: #fff;
}

.direct-chat-indigo .right > .direct-chat-text::after, .direct-chat-indigo .right > .direct-chat-text::before {
  border-left-color: #6610f2;
}

.direct-chat-purple .right > .direct-chat-text {
  background-color: #6f42c1;
  border-color: #6f42c1;
  color: #fff;
}

.direct-chat-purple .right > .direct-chat-text::after, .direct-chat-purple .right > .direct-chat-text::before {
  border-left-color: #6f42c1;
}

.direct-chat-pink .right > .direct-chat-text {
  background-color: #e83e8c;
  border-color: #e83e8c;
  color: #fff;
}

.direct-chat-pink .right > .direct-chat-text::after, .direct-chat-pink .right > .direct-chat-text::before {
  border-left-color: #e83e8c;
}

.direct-chat-red .right > .direct-chat-text {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.direct-chat-red .right > .direct-chat-text::after, .direct-chat-red .right > .direct-chat-text::before {
  border-left-color: #dc3545;
}

.direct-chat-orange .right > .direct-chat-text {
  background-color: #fd7e14;
  border-color: #fd7e14;
  color: #1f2d3d;
}

.direct-chat-orange .right > .direct-chat-text::after, .direct-chat-orange .right > .direct-chat-text::before {
  border-left-color: #fd7e14;
}

.direct-chat-yellow .right > .direct-chat-text {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #1f2d3d;
}

.direct-chat-yellow .right > .direct-chat-text::after, .direct-chat-yellow .right > .direct-chat-text::before {
  border-left-color: #ffc107;
}

.direct-chat-green .right > .direct-chat-text {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

.direct-chat-green .right > .direct-chat-text::after, .direct-chat-green .right > .direct-chat-text::before {
  border-left-color: #28a745;
}

.direct-chat-teal .right > .direct-chat-text {
  background-color: #20c997;
  border-color: #20c997;
  color: #fff;
}

.direct-chat-teal .right > .direct-chat-text::after, .direct-chat-teal .right > .direct-chat-text::before {
  border-left-color: #20c997;
}

.direct-chat-cyan .right > .direct-chat-text {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
}

.direct-chat-cyan .right > .direct-chat-text::after, .direct-chat-cyan .right > .direct-chat-text::before {
  border-left-color: #17a2b8;
}

.direct-chat-white .right > .direct-chat-text {
  background-color: #fff;
  border-color: #fff;
  color: #1f2d3d;
}

.direct-chat-white .right > .direct-chat-text::after, .direct-chat-white .right > .direct-chat-text::before {
  border-left-color: #fff;
}

.direct-chat-gray .right > .direct-chat-text {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.direct-chat-gray .right > .direct-chat-text::after, .direct-chat-gray .right > .direct-chat-text::before {
  border-left-color: #6c757d;
}

.direct-chat-gray-dark .right > .direct-chat-text {
  background-color: #343a40;
  border-color: #343a40;
  color: #fff;
}

.direct-chat-gray-dark .right > .direct-chat-text::after, .direct-chat-gray-dark .right > .direct-chat-text::before {
  border-left-color: #343a40;
}

.dark-mode .direct-chat-text {
  background-color: #454d55;
  border-color: #4b545c;
  color: #fff;
}

.dark-mode .direct-chat-text::after, .dark-mode .direct-chat-text::before {
  border-right-color: #4b545c;
}

.dark-mode .direct-chat-timestamp {
  color: #adb5bd;
}

.dark-mode .right > .direct-chat-text::after, .dark-mode .right > .direct-chat-text::before {
  border-right-color: transparent;
}

.dark-mode .direct-chat-primary .right > .direct-chat-text {
  background-color: #3f6791;
  border-color: #3f6791;
  color: #fff;
}

.dark-mode .direct-chat-primary .right > .direct-chat-text::after, .dark-mode .direct-chat-primary .right > .direct-chat-text::before {
  border-left-color: #3f6791;
}

.dark-mode .direct-chat-secondary .right > .direct-chat-text {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.dark-mode .direct-chat-secondary .right > .direct-chat-text::after, .dark-mode .direct-chat-secondary .right > .direct-chat-text::before {
  border-left-color: #6c757d;
}

.dark-mode .direct-chat-success .right > .direct-chat-text {
  background-color: #00bc8c;
  border-color: #00bc8c;
  color: #fff;
}

.dark-mode .direct-chat-success .right > .direct-chat-text::after, .dark-mode .direct-chat-success .right > .direct-chat-text::before {
  border-left-color: #00bc8c;
}

.dark-mode .direct-chat-info .right > .direct-chat-text {
  background-color: #3498db;
  border-color: #3498db;
  color: #fff;
}

.dark-mode .direct-chat-info .right > .direct-chat-text::after, .dark-mode .direct-chat-info .right > .direct-chat-text::before {
  border-left-color: #3498db;
}

.dark-mode .direct-chat-warning .right > .direct-chat-text {
  background-color: #f39c12;
  border-color: #f39c12;
  color: #1f2d3d;
}

.dark-mode .direct-chat-warning .right > .direct-chat-text::after, .dark-mode .direct-chat-warning .right > .direct-chat-text::before {
  border-left-color: #f39c12;
}

.dark-mode .direct-chat-danger .right > .direct-chat-text {
  background-color: #e74c3c;
  border-color: #e74c3c;
  color: #fff;
}

.dark-mode .direct-chat-danger .right > .direct-chat-text::after, .dark-mode .direct-chat-danger .right > .direct-chat-text::before {
  border-left-color: #e74c3c;
}

.dark-mode .direct-chat-light .right > .direct-chat-text {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #1f2d3d;
}

.dark-mode .direct-chat-light .right > .direct-chat-text::after, .dark-mode .direct-chat-light .right > .direct-chat-text::before {
  border-left-color: #f8f9fa;
}

.dark-mode .direct-chat-dark .right > .direct-chat-text {
  background-color: #343a40;
  border-color: #343a40;
  color: #fff;
}

.dark-mode .direct-chat-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-dark .right > .direct-chat-text::before {
  border-left-color: #343a40;
}

.dark-mode .direct-chat-lightblue .right > .direct-chat-text {
  background-color: #86bad8;
  border-color: #86bad8;
  color: #1f2d3d;
}

.dark-mode .direct-chat-lightblue .right > .direct-chat-text::after, .dark-mode .direct-chat-lightblue .right > .direct-chat-text::before {
  border-left-color: #86bad8;
}

.dark-mode .direct-chat-navy .right > .direct-chat-text {
  background-color: #002c59;
  border-color: #002c59;
  color: #fff;
}

.dark-mode .direct-chat-navy .right > .direct-chat-text::after, .dark-mode .direct-chat-navy .right > .direct-chat-text::before {
  border-left-color: #002c59;
}

.dark-mode .direct-chat-olive .right > .direct-chat-text {
  background-color: #74c8a3;
  border-color: #74c8a3;
  color: #1f2d3d;
}

.dark-mode .direct-chat-olive .right > .direct-chat-text::after, .dark-mode .direct-chat-olive .right > .direct-chat-text::before {
  border-left-color: #74c8a3;
}

.dark-mode .direct-chat-lime .right > .direct-chat-text {
  background-color: #67ffa9;
  border-color: #67ffa9;
  color: #1f2d3d;
}

.dark-mode .direct-chat-lime .right > .direct-chat-text::after, .dark-mode .direct-chat-lime .right > .direct-chat-text::before {
  border-left-color: #67ffa9;
}

.dark-mode .direct-chat-fuchsia .right > .direct-chat-text {
  background-color: #f672d8;
  border-color: #f672d8;
  color: #1f2d3d;
}

.dark-mode .direct-chat-fuchsia .right > .direct-chat-text::after, .dark-mode .direct-chat-fuchsia .right > .direct-chat-text::before {
  border-left-color: #f672d8;
}

.dark-mode .direct-chat-maroon .right > .direct-chat-text {
  background-color: #ed6c9b;
  border-color: #ed6c9b;
  color: #1f2d3d;
}

.dark-mode .direct-chat-maroon .right > .direct-chat-text::after, .dark-mode .direct-chat-maroon .right > .direct-chat-text::before {
  border-left-color: #ed6c9b;
}

.dark-mode .direct-chat-blue .right > .direct-chat-text {
  background-color: #3f6791;
  border-color: #3f6791;
  color: #fff;
}

.dark-mode .direct-chat-blue .right > .direct-chat-text::after, .dark-mode .direct-chat-blue .right > .direct-chat-text::before {
  border-left-color: #3f6791;
}

.dark-mode .direct-chat-indigo .right > .direct-chat-text {
  background-color: #6610f2;
  border-color: #6610f2;
  color: #fff;
}

.dark-mode .direct-chat-indigo .right > .direct-chat-text::after, .dark-mode .direct-chat-indigo .right > .direct-chat-text::before {
  border-left-color: #6610f2;
}

.dark-mode .direct-chat-purple .right > .direct-chat-text {
  background-color: #6f42c1;
  border-color: #6f42c1;
  color: #fff;
}

.dark-mode .direct-chat-purple .right > .direct-chat-text::after, .dark-mode .direct-chat-purple .right > .direct-chat-text::before {
  border-left-color: #6f42c1;
}

.dark-mode .direct-chat-pink .right > .direct-chat-text {
  background-color: #e83e8c;
  border-color: #e83e8c;
  color: #fff;
}

.dark-mode .direct-chat-pink .right > .direct-chat-text::after, .dark-mode .direct-chat-pink .right > .direct-chat-text::before {
  border-left-color: #e83e8c;
}

.dark-mode .direct-chat-red .right > .direct-chat-text {
  background-color: #e74c3c;
  border-color: #e74c3c;
  color: #fff;
}

.dark-mode .direct-chat-red .right > .direct-chat-text::after, .dark-mode .direct-chat-red .right > .direct-chat-text::before {
  border-left-color: #e74c3c;
}

.dark-mode .direct-chat-orange .right > .direct-chat-text {
  background-color: #fd7e14;
  border-color: #fd7e14;
  color: #1f2d3d;
}

.dark-mode .direct-chat-orange .right > .direct-chat-text::after, .dark-mode .direct-chat-orange .right > .direct-chat-text::before {
  border-left-color: #fd7e14;
}

.dark-mode .direct-chat-yellow .right > .direct-chat-text {
  background-color: #f39c12;
  border-color: #f39c12;
  color: #1f2d3d;
}

.dark-mode .direct-chat-yellow .right > .direct-chat-text::after, .dark-mode .direct-chat-yellow .right > .direct-chat-text::before {
  border-left-color: #f39c12;
}

.dark-mode .direct-chat-green .right > .direct-chat-text {
  background-color: #00bc8c;
  border-color: #00bc8c;
  color: #fff;
}

.dark-mode .direct-chat-green .right > .direct-chat-text::after, .dark-mode .direct-chat-green .right > .direct-chat-text::before {
  border-left-color: #00bc8c;
}

.dark-mode .direct-chat-teal .right > .direct-chat-text {
  background-color: #20c997;
  border-color: #20c997;
  color: #fff;
}

.dark-mode .direct-chat-teal .right > .direct-chat-text::after, .dark-mode .direct-chat-teal .right > .direct-chat-text::before {
  border-left-color: #20c997;
}

.dark-mode .direct-chat-cyan .right > .direct-chat-text {
  background-color: #3498db;
  border-color: #3498db;
  color: #fff;
}

.dark-mode .direct-chat-cyan .right > .direct-chat-text::after, .dark-mode .direct-chat-cyan .right > .direct-chat-text::before {
  border-left-color: #3498db;
}

.dark-mode .direct-chat-white .right > .direct-chat-text {
  background-color: #fff;
  border-color: #fff;
  color: #1f2d3d;
}

.dark-mode .direct-chat-white .right > .direct-chat-text::after, .dark-mode .direct-chat-white .right > .direct-chat-text::before {
  border-left-color: #fff;
}

.dark-mode .direct-chat-gray .right > .direct-chat-text {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.dark-mode .direct-chat-gray .right > .direct-chat-text::after, .dark-mode .direct-chat-gray .right > .direct-chat-text::before {
  border-left-color: #6c757d;
}

.dark-mode .direct-chat-gray-dark .right > .direct-chat-text {
  background-color: #343a40;
  border-color: #343a40;
  color: #fff;
}

.dark-mode .direct-chat-gray-dark .right > .direct-chat-text::after, .dark-mode .direct-chat-gray-dark .right > .direct-chat-text::before {
  border-left-color: #343a40;
}

.users-list {
  padding-left: 0;
  list-style: none;
}

.users-list > li {
  float: left;
  padding: 10px;
  text-align: center;
  width: 25%;
}

.users-list > li img {
  border-radius: 50%;
  height: auto;
  max-width: 100%;
}

.users-list > li > a:hover,
.users-list > li > a:hover .users-list-name {
  color: #999;
}

.users-list-name,
.users-list-date {
  display: block;
}

.users-list-name {
  color: #495057;
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.users-list-date {
  color: #748290;
  font-size: 12px;
}

.dark-mode .users-list-name {
  color: #ced4da;
}

.dark-mode .users-list-date {
  color: #adb5bd;
}

.card-widget {
  border: 0;
  position: relative;
}

.widget-user .widget-user-header {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  height: 135px;
  padding: 1rem;
  text-align: center;
}

.widget-user .widget-user-username {
  font-size: 25px;
  font-weight: 300;
  margin-bottom: 0;
  margin-top: 0;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.widget-user .widget-user-desc {
  margin-top: 0;
}

.widget-user .widget-user-image {
  left: 50%;
  margin-left: -45px;
  position: absolute;
  top: 80px;
}

.widget-user .widget-user-image > img {
  border: 3px solid #fff;
  height: auto;
  width: 90px;
}

.widget-user .card-footer {
  padding-top: 50px;
}

.widget-user-2 .widget-user-header {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  padding: 1rem;
}

.widget-user-2 .widget-user-username {
  font-size: 25px;
  font-weight: 300;
  margin-bottom: 5px;
  margin-top: 5px;
}

.widget-user-2 .widget-user-desc {
  margin-top: 0;
}

.widget-user-2 .widget-user-username,
.widget-user-2 .widget-user-desc {
  margin-left: 75px;
}

.widget-user-2 .widget-user-image > img {
  float: left;
  height: auto;
  width: 65px;
}
/*# sourceMappingURL=adminlte.extra-components.css.map */