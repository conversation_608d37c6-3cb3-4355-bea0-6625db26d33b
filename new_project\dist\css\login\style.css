body {
  font-family: "Poppins", sans-serif;
  background-color: #f8fafb; }

p {
  color: #b3b3b3;
  font-weight: 300; }

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: "Poppins", sans-serif; }

a {
  -webkit-transition: .3s all ease;
  -o-transition: .3s all ease;
  transition: .3s all ease; }
  a:hover {
    text-decoration: none !important; }

.content {
  padding: 4rem 0;
}

h2 {
  font-size: 20px; }

@media (max-width: 991.98px) {
  .content .bg {
    height: 500px; } }

.content .contents, .content .bg {
  width: 50%; }
  @media (max-width: 1199.98px) {
    .content .contents, .content .bg {
      width: 100%; } }
  .content .contents .form-group, .content .bg .form-group {
    overflow: hidden;
    margin-bottom: 0;
    padding: 15px 15px;
    border-bottom: none;
    position: relative;
    background: #edf2f5;
    border-bottom: 1px solid #e6edf1; }
    .content .contents .form-group label, .content .bg .form-group label {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      -webkit-transition: .3s all ease;
      -o-transition: .3s all ease;
      transition: .3s all ease; }
    .content .contents .form-group input, .content .bg .form-group input {
      background: transparent; }
    .content .contents .form-group.first, .content .bg .form-group.first {
      border-top-left-radius: 7px;
      border-top-right-radius: 7px; }
    .content .contents .form-group.last, .content .bg .form-group.last {
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px; }
    .content .contents .form-group label, .content .bg .form-group label {
      font-size: 12px;
      display: block;
      margin-bottom: 0;
      color: #b3b3b3; }
    .content .contents .form-group.focus, .content .bg .form-group.focus {
      background: #fff; }
    .content .contents .form-group.field--not-empty label, .content .bg .form-group.field--not-empty label {
      margin-top: -20px; }
  .content .contents .form-control, .content .bg .form-control {
    border: none;
    padding: 0;
    font-size: 20px;
    border-radius: 0; }
    .content .contents .form-control:active, .content .contents .form-control:focus, .content .bg .form-control:active, .content .bg .form-control:focus {
      outline: none;
      -webkit-box-shadow: none;
      box-shadow: none; }

.content .bg {
  background-size: cover;
  background-position: center; }

.content a {
  color: #888;
  text-decoration: underline; }

.content .btn {
  height: 54px;
  padding-left: 30px;
  padding-right: 30px; }

.content .forgot-pass {
  position: relative;
  top: 2px;
  font-size: 14px; }

.social-login a {
  text-decoration: none;
  position: relative;
  text-align: center;
  color: #fff;
  margin-bottom: 10px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: inline-block; }
  .social-login a span {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%); }
  .social-login a:hover {
    color: #fff; }
  .social-login a.facebook {
    background: #3b5998; }
    .social-login a.facebook:hover {
      background: #344e86; }
  .social-login a.twitter {
    background: #1da1f2; }
    .social-login a.twitter:hover {
      background: #0d95e8; }
  .social-login a.google {
    background: #ea4335; }
    .social-login a.google:hover {
      background: #e82e1e; }

.control {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 14px; }
  .control .caption {
    position: relative;
    top: .2rem;
    color: #888; }

.control input {
  position: absolute;
  z-index: -1;
  opacity: 0; }

.control__indicator {
  position: absolute;
  top: 2px;
  left: 0;
  height: 20px;
  width: 20px;
  background: #e6e6e6;
  border-radius: 4px; }

.control--radio .control__indicator {
  border-radius: 50%; }

.control:hover input ~ .control__indicator,
.control input:focus ~ .control__indicator {
  background: #ccc; }

.control input:checked ~ .control__indicator {
  background: #1C82E5; }

.control:hover input:not([disabled]):checked ~ .control__indicator,
.control input:checked:focus ~ .control__indicator {
  background: #847dff; }

.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.9;
  pointer-events: none; }

.control__indicator:after {
  font-family: 'icomoon';
  content: '\e5ca';
  position: absolute;
  display: none;
  font-size: 16px;
  -webkit-transition: .3s all ease;
  -o-transition: .3s all ease;
  transition: .3s all ease; }

.control input:checked ~ .control__indicator:after {
  display: block;
  color: #fff; }

.control--checkbox .control__indicator:after {
  top: 50%;
  left: 50%;
  margin-top: -1px;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%); }

.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b; }

.control--checkbox input:disabled:checked ~ .control__indicator {
  background-color: #7e0cf5;
  opacity: .2; }


/* Start User Login */
.user-login-sec{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  @media only screen and (min-width: 992px) {
    height: 100vh;
  }
}

.user-login-sec .row-wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: 992px) {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    border-radius: 18px;
  }
}

.user-login-sec .contents .top-head-wrap{
  margin-top: 20px;
  @media only screen and (min-width: 992px) {
    margin-top: 0px;
  }
}

.user-login-sec .contents .top-head-wrap .student-portal-wrap{
  background-color: #95BC2E;
  padding: 5px 0 3px 0;
  border-radius: 6px;
  margin: 5px 0 20px 0;
}

.user-login-sec .contents .top-head-wrap .student-portal-wrap .login-box-msg{
  font-weight: bold;
}

.user-login-sec .contents .login-form-wrap .form-group{
  padding: 0 10px;
  overflow: unset;
  margin-bottom: 11px;
  border-radius: 7px;
}

.user-login-sec .contents .login-form-wrap .form-group.field--not-empty label{
  margin-top: -15px;
  right: 0;
  background-color: #1C82E5;
  width: 100px;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.user-login-sec .contents .login-form-wrap .form-group input{
  font-size: 14px;
}

.user-login-sec .contents .login-form-wrap .forgot-pass-wrap{
  margin-bottom: 14px;
}

.user-login-sec .contents .login-form-wrap .forgot-pass-wrap .forgot-pass{
  text-decoration: none;
}

.user-login-sec .contents .login-form-wrap .forgot-pass-wrap .forgot-pass:hover{
  color: #1C82E5;
  text-decoration: underline !important;
}

.user-login-sec .contents .login-form-wrap .btn-custom{
  width: 100%;
  height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  color: #fff;
  background-color: #1C82E5;
  border: 2px solid #1C82E5;
  border-radius: 10px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.user-login-sec .contents .login-form-wrap .btn-custom:hover{
  background-color: #fff;
  color: #1C82E5;
}

.user-login-sec .contents .login-form-wrap .dont-have-acc-wrap{
  margin-top: 18px;
  display: flex;
  justify-content: center;
  text-align: center;
}

.user-login-sec .contents .login-form-wrap .dont-have-acc-wrap .forgot-pass{
  margin-left: 5px;
  color: #888;
  text-decoration: none;
}

.user-login-sec .contents .login-form-wrap .dont-have-acc-wrap .forgot-pass:hover{
  color: #1C82E5;
  text-decoration: underline !important;
}

.user-login-sec .img-wrap{
  margin-top: 20px;
  padding: 30px;
  background: #2EBCA4;
  border-top-left-radius: 18px;
  border-top-right-radius: 18px;
  @media only screen and (min-width: 992px) {
    margin-top: 0;
    padding: 80px;
    border-bottom-left-radius: 18px;
    border-bottom-right-radius: 18px;
  }
}
/* End User Login */

/* Start User SignUp */
.user-sign-up-sec{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  @media only screen and (min-width: 992px) {
    height: 100vh;
  }
}

.user-sign-up-sec .row-wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: 992px) {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    border-radius: 18px;
  }
}

.user-sign-up-sec .img-wrap{
  padding: 30px;
  background: #2EBCA4;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  @media only screen and (min-width: 992px) {
    padding: 80px;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
  }
}

.user-sign-up-sec .contents{
  margin-top: 20px;
  @media only screen and (min-width: 992px) {
    margin-top: 0;
  }
}

.user-sign-up-sec .contents .top-head-wrap .student-portal-wrap{
  background-color: #95BC2E;
  padding: 5px 0 3px 0;
  border-radius: 6px;
  margin: 5px 0 20px 0;
}

.user-sign-up-sec .contents .top-head-wrap .student-portal-wrap .login-box-msg{
  font-weight: bold;
  font-size: 15px;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
  color: #fff;
}

.user-sign-up-sec .contents .login-form-wrap .form-group{
  padding: 0 10px;
  overflow: unset;
  margin-bottom: 11px;
  border-radius: 7px;
}

.user-sign-up-sec .contents .login-form-wrap .form-group input{
  font-size: 14px;
}

.user-sign-up-sec .contents .login-form-wrap .form-group.field--not-empty label{
  margin-top: -15px;
  right: 0;
  background-color: #1C82E5;
  width: 100px;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.user-sign-up-sec .contents .login-form-wrap .mobile-wrap{
  display: flex;
  justify-content: space-between;
}

.user-sign-up-sec .contents .login-form-wrap .mobile-wrap .country-code-wrap{
  font-size: 14px;
  width:40%;
}

.user-sign-up-sec .contents .login-form-wrap .mobile-wrap .form-group{
  width: 59%;
}
.user-sign-up-sec .contents .login-form-wrap .mobile-wrap .form-group label{
  pointer-events: none;
}

.user-sign-up-sec .contents .login-form-wrap .btn-custom{
  width: 100%;
  height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  color: #fff;
  background-color: #1C82E5;
  border: 2px solid #1C82E5;
  border-radius: 10px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.user-sign-up-sec .contents .login-form-wrap .btn-custom:hover{
  background-color: #fff;
  color: #1C82E5;
}

.user-sign-up-sec .contents .login-form-wrap .have-acc-wrap{
  margin-top: 18px;
  display: flex;
  justify-content: center;
  text-align: center;
  margin-bottom: 20px;
  @media only screen and (min-width: 992px) {
    margin-bottom: 0;
  }
}

.user-sign-up-sec .contents .login-form-wrap .have-acc-wrap .forgot-pass{
  margin-left: 5px;
  color: #888;
  text-decoration: none;
}

.user-sign-up-sec .contents .login-form-wrap .have-acc-wrap .forgot-pass:hover{
  color: #1C82E5;
  text-decoration: underline !important;
}
/* End User SignUp */

/* Start Recovery */
.user-recovery-pass{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  @media only screen and (min-width: 992px) {
    height: 100vh;
  }
}
.user-recovery-pass .row-wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: 992px) {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    border-radius: 18px;
  }
}

.user-recovery-pass .img-wrap{
  padding: 30px;
  background: #2EBCA4;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  @media only screen and (min-width: 992px) {
    padding: 80px;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
  }
}

.user-recovery-pass .contents{
  margin-top: 20px;
  @media only screen and (min-width: 992px) {
    margin-top: 0;
  }
}

.user-recovery-pass .contents .top-head-wrap .student-portal-wrap{
  background-color: #95BC2E;
  padding: 5px 0 3px 0;
  border-radius: 6px;
  margin: 5px 0 20px 0;
}

.user-recovery-pass .contents .top-head-wrap .student-portal-wrap .login-box-msg{
  font-weight: bold;
  font-size: 15px;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
  color: #fff;
}

.user-recovery-pass .contents .login-form-wrap .form-group{
  padding: 0 10px;
  overflow: unset;
  margin-bottom: 11px;
  border-radius: 7px;
}

.user-recovery-pass .contents .login-form-wrap .form-group input{
  font-size: 14px;
}

.user-recovery-pass .contents .login-form-wrap .form-group.field--not-empty label{
  margin-top: -15px;
  right: 0;
  background-color: #1C82E5;
  width: 100px;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.user-recovery-pass .contents .login-form-wrap .form-group{
  width: 100%;
}
.user-recovery-pass .contents .login-form-wrap .form-group label{
  pointer-events: none;
}

.user-recovery-pass .contents .login-form-wrap .btn-custom{
  width: 100%;
  height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  color: #fff;
  background-color: #1C82E5;
  border: 2px solid #1C82E5;
  border-radius: 10px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.user-recovery-pass .contents .login-form-wrap .btn-custom:hover{
  background-color: #fff;
  color: #1C82E5;
}

.user-recovery-pass .contents .login-form-wrap .have-acc-wrap{
  margin-top: 18px;
  display: flex;
  justify-content: center;
  text-align: center;
  margin-bottom: 20px;
  @media only screen and (min-width: 992px) {
    margin-bottom: 0;
  }
}

.user-recovery-pass .contents .login-form-wrap .have-acc-wrap .forgot-pass{
  margin-left: 5px;
  color: #888;
  text-decoration: none;
}

.user-recovery-pass .contents .login-form-wrap .have-acc-wrap .forgot-pass:hover{
  color: #1C82E5;
  text-decoration: underline !important;
}
/* End Recovery */

/* Start Forgot Password */
.forgot-pass-sec{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  @media only screen and (min-width: 992px) {
    height: 100vh;
  }
}

.forgot-pass-sec .row-wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: 992px) {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    border-radius: 18px;
  }
}

.forgot-pass-sec .img-wrap{
  padding: 30px;
  background: #2EBCA4;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  @media only screen and (min-width: 992px) {
    padding: 80px;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
  }
}

.forgot-pass-sec .contents{
  margin-top: 20px;
  @media only screen and (min-width: 992px) {
    margin-top: 0;
  }
}

.forgot-pass-sec .contents .top-head-wrap .student-portal-wrap{
  background-color: #95BC2E;
  padding: 5px 0 3px 0;
  border-radius: 6px;
  margin: 5px 0 20px 0;
}

.forgot-pass-sec .contents .top-head-wrap .student-portal-wrap .login-box-msg{
  font-weight: bold;
  font-size: 15px;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
  color: #fff;
}

.forgot-pass-sec .contents .login-form-wrap .form-group{
  padding: 0 10px;
  overflow: unset;
  margin-bottom: 11px;
  border-radius: 7px;
}

.forgot-pass-sec .contents .login-form-wrap .form-group.field--not-empty label{
  margin-top: -15px;
  right: 0;
  background-color: #1C82E5;
  width: 100px;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.forgot-pass-sec .contents .login-form-wrap .form-group input{
  font-size: 14px;
}

.forgot-pass-sec .contents .login-form-wrap .btn-custom{
  width: 100%;
  height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  color: #fff;
  background-color: #1C82E5;
  border: 2px solid #1C82E5;
  border-radius: 10px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.forgot-pass-sec .contents .login-form-wrap .btn-custom:hover{
  background-color: #fff;
  color: #1C82E5;
}

.forgot-pass-sec .contents .login-form-wrap .have-acc-wrap{
  margin-top: 18px;
  display: flex;
  justify-content: center;
}

.forgot-pass-sec .contents .login-form-wrap .have-acc-wrap .forgot-pass{
  margin-left: 5px;
  color: #888;
  text-decoration: none;
}

.forgot-pass-sec .contents .login-form-wrap .have-acc-wrap .forgot-pass:hover{
  color: #1C82E5;
  text-decoration: underline !important;
}

.forgot-pass-sec .contents .login-form-wrap .have-acc-wrap .forgot-pass svg{
  margin-right: -5px;
}
/* End Forgot Password */

/* Start Agent Sign In */
.agent-sign-in-sec{
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  @media only screen and (min-width: 992px) {
    height: 100vh;
  }
}

.agent-sign-in-sec .row-wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  @media only screen and (min-width: 992px) {
    box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
    border-radius: 18px;
  }
}

.agent-sign-in-sec .img-wrap{
  padding: 30px;
  background: #2EBCA4;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  @media only screen and (min-width: 992px) {
    padding: 80px;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
  }
}

.agent-sign-in-sec .contents{
  margin-top: 20px;
  @media only screen and (min-width: 992px) {
    margin-top: 0;
  }
}

.agent-sign-in-sec .contents .top-head-wrap .agent-portal-wrap{
  background-color: #95BC2E;
  padding: 5px 0 3px 0;
  border-radius: 6px;
  margin: 5px 0 20px 0;
}

.agent-sign-in-sec .contents .top-head-wrap .agent-portal-wrap .login-box-msg{
  font-weight: bold;
  font-size: 15px;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
  color: #fff;
}

.agent-sign-in-sec .contents .login-form-wrap .form-group{
  padding: 0 10px;
  overflow: unset;
  margin-bottom: 11px;
  border-radius: 7px;
}

.agent-sign-in-sec .contents .login-form-wrap .form-group input{
  font-size: 14px;
}

.agent-sign-in-sec .contents .login-form-wrap .form-group.field--not-empty label{
  margin-top: -15px;
  right: 0;
  background-color: #1C82E5;
  width: 100px;
  color: #fff;
  text-align: center;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.agent-sign-in-sec .contents .login-form-wrap .btn-custom{
  width: 100%;
  height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  color: #fff;
  background-color: #1C82E5;
  border: 2px solid #1C82E5;
  border-radius: 10px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.agent-sign-in-sec .contents .login-form-wrap .btn-custom:hover{
  background-color: #fff;
  color: #1C82E5;
}
/* End Agent Sign In */