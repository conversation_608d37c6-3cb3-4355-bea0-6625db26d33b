$(document).ready(function () {

    //console.log("ready!");
    bsCustomFileInput.init();
    //Initialize Select2 Elements
    $('.select2bs4').select2({
        theme: 'bootstrap4'
    });

    $('.select2bs4').each(function(index) {
        if (!$(this).attr('id')) {
            $(this).attr('id', 'select2bs4-' + index);
        }
    });

    // Capture initial values from the select elements
    var initialValues = {};
    $('.select2bs4').each(function() {
        var id = $(this).attr('id');
        var initialValue = $(this).val();
        initialValues[id] = initialValue;
    });

    // Handle reset button click
    $('#reset-button').click(function() {       
        $('.select2bs4').each(function() {
            var id = $(this).attr('id');
            var initialValue = initialValues[id];
            $(this).val(initialValue).trigger('change');
        });
        $('#quickform').validate().resetForm();       
    });

    $('#mobile_no').on('keyup', function () {
        var mobile_no = $(this).val();
        $('#mobile').val("("+$('.country_phone_code :selected').text()+")"+ mobile_no);
    });



    /*Save staff*/
    $('#quickform').validate({
        rules: {
            first_name: {
                required: true,
            },
            country_id: {
                required: true,
            },
            last_name: {
                required: true,
            },
            email: {
                required: true,
            },
            mobile_no: {
                required: true,
            },
            user_type: {
                required: true,
            },
            staff_privilege_id: {
                required: true,
            }
        },
        messages: {

            first_name: {
                required: "Please Enter a value",
            },
            last_name: {
                required: "Please Enter a value",
            },
            email: {
                required: "Please Enter a value",
            },
            mobile_no: {
                required: "Please Enter a value",
            },
            user_type: {
                required: "Please Select One",
            },
            staff_privilege_id: {
                required: "Please Select One",
            },
        },
        errorElement: 'span',
        errorPlacement: function (error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function (form) {
            //console.log(new FormData(form));
            var formData = new FormData(form);
            formData.append('staffSave', '1');
            $.ajax({
                type: "POST",
                url: "controller/staff-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function (data) {
                    //console.log(data);
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        window.location.href = 'staff-list.php';
                        //   $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }
                    $buttons.prop('disabled', false); // Re-enable all buttons
                }
            });
        }
    });
    $('.staff_privilege_id').on('change', function () {
        $(this).valid();
    });

});