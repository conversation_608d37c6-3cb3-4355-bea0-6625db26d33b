//Student staff chat from student side

$(document).ready(function () {
  bsCustomFileInput.init();
  //Initialize Select2 Elements
  $(".select2bs4").select2({
    theme: "bootstrap4",
  });
  var application_id= document.querySelector('input[name="application_id"]').value;


  $("#quickform").validate({
    rules: {
      message: {
        required: false,
    },
    _files: {
        required: function () {
            return $('input[name="message"]').val().trim() === "";
        }
    }
  },
  messages: {
      _files: {
          required: "Please attach a document or type a message",
      }
  },
    errorElement: "span",
    errorPlacement: function (error, element) {
      error.addClass("invalid-feedback");
      element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
      $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
      // console.log($(form).serialize()+'&messagesSave=1');
      var $buttons = $(this).find('.btn-send');
      $buttons.prop('disabled', true);
      var formData = new FormData(form);
          formData.append('applicationMessagesSave', '1');
      $.ajax({
        type: "POST",
        url: "controller/message-controller.php",
        data: formData,
        contentType: false,
        processData: false,
        dataType: 'json',
        success: function (data) {              
          
          application_id = data.application_id;
          
            if (data.status === 'success') {
              $('input[name="message"]').val('');
              $('input[name="_files"]').val('');
              document.getElementById('file-name').textContent = '';
              document.getElementById('uploaded-file-div').style.display = 'none';                    
              console.log('Saved Successfully');
              $(form).trigger("reset");
              // getchatmessages(application_id);
          } else {
              console.log('Error:', data.message);
          }
          $buttons.prop('disabled', false); 
        },
        error: function (xhr, status, error) {
            console.log('Error while saving:', error);
            console.log('Server response:', xhr.responseText);
            $(form).trigger("reset");
            $('input[name="message"]').val('');
            $buttons.prop('disabled', false); 
        }
    });
    },
    
  });

  getchatmessages();

  setInterval(function () {
    getchatmessages();
  }, 15000);

  $("body").on("click", "#show2", function () {
    //console.log("this is working");
    setTimeout(function () {
      getchatmessages();
    }, 500);
  });

  function getchatmessages() {
    let student_id = $("#student_id").val();
    let agent_id = $("#agent_id").val();
    $.ajax({
      method: "post",
      url: "get-agent-messages-application.php",
      data:
        $("#displaychatdata").serialize() +
        "&agent_id=" +
        agent_id +
        "&student_id=" +
        student_id+
        "&application_id=" +
        application_id,
      dataType: "html",
      success: function (response) {
        $("#displaychatdata").html(response);
      },
    });
  }
});
