<!-- Navbar -->
<!-- Navbar Agent-->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <!-- Left navbar links -->
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>
    <form class="form-inline ml-0 ml-md-3" style="display: none;">
        <div class="input-group input-group-sm" >
            <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
            <div class="input-group-append">
                <button class="btn btn-navbar" type="submit">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">

        <!-- Messages Dropdown Menu -->
        <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
                <i class="fa fa-inbox"></i>
                <!-- <span class="badge badge-danger navbar-badge">3</span> -->
            </a>
            <!-- <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <a href="#" class="dropdown-item">
                    
                    <div class="media">
                        <div class="media-body">
                            <p><b>Kirshan Selvanayagam</b></p>
                            <p class="text-sm">Fleming College, Canada.</p>
                            <br>
                        </div>
                    </div>
                    <div class="media">
                        <img class="user-image img-circle elevation-2" src="dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.jpg" onerror="this.onerror=null;
               this.src='dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.png';
               this.onerror=null;
               this.src='dist/img/avatar.png';" alt="User Image">

                        <div class="media-body">
                            <h3 class="dropdown-item-title">
                                Mohammed
                                <span class="float-right text-sm text-success"><i class="fas fa-circle"></i></span>
                            </h3>
                            <p class="text-sm">LOA/OL</p>
                            <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 01-Mar-2022 04:20:38 PM</p>
                        </div>
                    </div>
                    
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item dropdown-footer">See All Applications</a>
            </div> -->
        </li>
        <!-- Notifications Dropdown Menu -->
        <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
                <i class="far fa-bell"></i>
                <!-- <span class="badge badge-warning navbar-badge">15</span> -->
            </a>
            <!-- <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <span class="dropdown-header">15 Notifications</span>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item">
                    <i class="fas fa-envelope mr-2"></i> 4 new messages
                    <span class="float-right text-muted text-sm">3 mins</span>
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item">
                    <i class="fas fa-users mr-2"></i> 8 requests
                    <span class="float-right text-muted text-sm">12 hours</span>
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item">
                    <i class="fas fa-file mr-2"></i> 3 new reports
                    <span class="float-right text-muted text-sm">2 days</span>
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
            </div> -->
            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <span class="dropdown-header">Notifications</span>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item dropdown-footer">No any Notifications!</a>
            </div>
        </li>
        <li class="nav-item dropdown user-menu">
            <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                <img class="user-image img-circle elevation-2" src="dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.jpg" onerror="this.onerror=null;
               this.src='dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.png';
               this.onerror=null;
               this.src='dist/img/avatar.png';" alt="User Image">


                <span class="d-none d-md-inline"><?php echo $_SESSION['user']['first_name'] ?></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right" style="left: inherit; right: 0px;">
                <!-- User image -->
                <li class="user-header bg-teal">
                    <img class="user-image img-circle elevation-2" src="dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.jpg" onerror="this.onerror=null;
               this.src='dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.png';
               this.onerror=null;
               this.src='dist/img/avatar.png';" alt="User Image">


                    <p>
                        <?php echo $_SESSION['user']['first_name'] ?>
                        <small>Agent</small>
                    </p>
                </li>
                <!-- Menu Body -->
                <li class="user-body">
                    <div class="row">
                        <div class="col-4 text-center">
                            <a href="#">Followers</a>
                        </div>
                        <div class="col-4 text-center">
                            <a href="#">Sales</a>
                        </div>
                        <div class="col-4 text-center">
                            <a href="#">Friends</a>
                        </div>
                    </div>
                    <!-- /.row -->
                </li>
                <!-- Menu Footer-->
                <li class="user-footer">
                    <a href="#" class="btn btn-default btn-flat">Profile</a>
                    <a href="logout.php" class="btn btn-default btn-flat float-right">Sign out</a>
                </li>
            </ul>
        </li>
    </ul>
</nav>
<!-- /.navbar -->

<style>
    .label-container {
        position: fixed;
        bottom: 32px;
        right: 95px;
        display: table;
        visibility: hidden;
        z-index: 1000;
    }

    .label-text {
        color: #FFF;
        background: rgba(51, 51, 51, 0.5);
        display: table-cell;
        vertical-align: middle;
        padding: 10px;
        border-radius: 3px;
        z-index: 1000;
    }

    .label-arrow {
        display: table-cell;
        vertical-align: middle;
        color: #333;
        opacity: 0.5;
        z-index: 1000;
    }

    .float {
        position: fixed;
        width: 60px;
        height: 60px;
        bottom: 35px;
        right: 30px;
        border-radius: 50px;
        text-align: center;
        box-shadow: 2px 2px 3px #999;
        z-index: 1000;
    }

    .my-float {
        font-size: 24px;
        margin-top: 18px;
    }

    a.float+div.label-container {
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.5s ease;
    }

    a.float:hover+div.label-container {
        visibility: visible;
        opacity: 1;
    }
</style>

<a href="manage-student-list-agent.php" class="float bg-teal message_a">
    <i class="fa fa-comments my-float"></i>
    <span class="badge badge-danger navbar-badge count_msg_cu">0</span>
</a>
<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<script>
    $(document).ready(function() {


        function getUnseenMsg() {
            $.ajax({
                method: "post",
                url: "get-unseen-msg.php",
                data: {
                    getunseenmsg: 1
                },
                dataType: "json",
                success: function(data) {
                    try {
                        var unreadCount = parseInt(data.data);

                        if (!isNaN(unreadCount)) {
                            var badgeElement = $('.count_msg_cu');

                            if (unreadCount > 0) {
                                badgeElement.text(unreadCount).show(); 
                            } else {
                                badgeElement.hide(); 
                            }
                        } else {
                            console.error('Invalid data format in JSON response.');
                        }
                    } catch (error) {
                        console.error('Error parsing JSON response:', error);
                    }
                },

                error: function(xhr, status, error) {
                    console.log('Error fetching unseen messages:', error);
                }
            });
        }

        setInterval(function() {
            getUnseenMsg();
            updatelastseen();
        }, 1000);

        var lastScrollTop = 0;
        $(window).scroll(function(event) {
            var st = $(this).scrollTop();
            if (st > lastScrollTop) {
                // downscroll code
                $('.float').hide('medium');
            } else {
                // upscroll code
                $('.float').show('medium');
            }
            lastScrollTop = st;

        });


        function updatelastseen() {
            $.ajax({
                method: "post",
                url: "update-last-seen.php",
                data: {
                    action: "UpdateLastSeen"
                },
            })
        }


    });
</script>