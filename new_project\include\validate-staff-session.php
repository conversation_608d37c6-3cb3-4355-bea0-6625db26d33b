<?php
//if (session_status() !== PHP_SESSION_ACTIVE) {session_start();}
//if(!isset($_SESSION['user'])){
//    session_start();
//}
if(session_status() !== PHP_SESSION_ACTIVE){
//    $sessionTime = 5 * 60 * 60;//365 * 24 * 60 * 60;
//    $sessionName = "my_session";
//    session_set_cookie_params($sessionTime);
//    session_name($sessionName);

	session_start();
}

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';


//echo "requesturi=".$_SERVER['REQUEST_URI'].""; //  /sldb/production/competition-list.php?competition=52
//echo "<br>querystrig=".$_SERVER['QUERY_STRING'];    // competition=52
//echo "<br>seld=".$_SERVER['PHP_SELF']; //  /sldb/production/competition-list.php
//die();

$host = filter_input(INPUT_SERVER, 'HTTP_HOST', FILTER_SANITIZE_URL);
//die("host=$host");
$redirect = filter_input(INPUT_SERVER, 'REQUEST_URI', FILTER_SANITIZE_URL);
$query_string = filter_input(INPUT_SERVER, 'QUERY_STRING', FILTER_SANITIZE_URL);

if($host=="localhost"){
    $full_redirect_url="http://".$host.$redirect;
}else{
    $full_redirect_url="https://".$host.$redirect;
}

if(isset($_SESSION['current_page_url']) && $query_string!="" && isset($_SESSION['previous_page_title']) && $_SESSION['previous_page_title']!=""){
    //show back button only if there is some parameters passed otherwise its referred form main menu so no back
    $_SESSION['previous_page_url']=$_SESSION['current_page_url'];
    $_SESSION['previous_page_title']=$_SESSION['current_page_title'];
}else{
    $_SESSION['previous_page_url']="";
    $_SESSION['previous_page_title']="";
}

$_SESSION['redirect']=$full_redirect_url;
$_SESSION['current_page_url']=$_SESSION['redirect'];


//echo "<br>full page=$full_redirect_url<br>";

$redirect_url=($_SESSION['redirect']!=""?$_SESSION['redirect']:"index.php?ref=3"); // filter_input(INPUT_GET, "redirect", FILTER_SANITIZE_STRING); //after loggedin, redirect.


if(!isset($_SESSION['user'])){
    header("location:login.php?ref=1&redirect=$redirect_url");
    die();
}

//check last activity time
if (isset($_SESSION['LAST_ACTIVITY']) && (time() - $_SESSION['LAST_ACTIVITY'] > 1800)) {
    // last request was more than 30 minutes ago = 1800 sec = 60sec * 30mins
    $last_act = $_SESSION['LAST_ACTIVITY'];
    session_unset();     // unset $_SESSION variable for the run-time
    session_destroy();   // destroy session data in storage
    header("location:staff/login.php");
    die();
}

//check last activity time
//if (isset($_SESSION['LAST_ACTIVITY']) && (time() - $_SESSION['LAST_ACTIVITY'] > 1800)) {
//    // last request was more than 30 minutes ago = 1800 sec = 60sec * 30mins
//    $last_act = $_SESSION['LAST_ACTIVITY'];
//    session_unset();     // unset $_SESSION variable for the run-time
//    session_destroy();   // destroy session data in storage
//    header("location:https://www.nbcourier.lk/");
//    die();
//}

$_SESSION['LAST_ACTIVITY'] = time(); // update last activity time stamp


//require_once $include_path . 'header-include.php'; //functions and class

if(!isset($_SESSION['redirect'])){
    $_SESSION['redirect'] = "index.php?ref=3&redirect=$redirect_url";
}

//require_once $include_path . 'validate-session.php';

/* 
 * This file will validate if the user logged in. otherwise will redirect to login page.
 */




if(isset($_SESSION['user']['user_id']) && $_SESSION['user']['user_id']>0){
    //die("loggedin");
}else{
    header("location:login.php?redirect=$redirect_url");
    die();
}

//validate the session key to makesure no forceful logout is initiated
if($_SESSION['user']['current_session_key']!=CURRENT_SESSION_KEY){
    header("location:login.php?ref=4&redirect=$redirect_url");
    die();
}