$(document).ready(function() {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });

    $('input[name="checkbox-new"]').on('change', function() {
        let active_yn = 'N';
        if (this.checked) {
            active_yn = 'Y';
        } else {
            active_yn = 'N';
        }

        let row_elem = $(this).attr('id');
        let remainder_id = $('input[name="remainder_id_' + row_elem + '"]').val();
        // console.log({row_elem});
        // console.log({name});
        //console.log({remainder_id});

        $.ajax({
            type: "POST",
            url: "controller/remainder-controller.php",
            data: {
                "remainder_id": remainder_id,
                "updateRemainder": 1,
                'active_yn': active_yn
            },
            dataType: 'json',
            success: function(data) {
                //console.log(data);
                if (data.data > 0) {
                    toastr.success('Updated');
                } else {
                    toastr.error('There is an error while saving.');
                }
            }
        });
    });



    $('.delete_btn').on('click', function() {
        var remainder_id = $(this).data('remainder_id');

        Swal.fire({
            title: 'Are you sure want to delete?',
            showCancelButton: true,
            confirmButtonText: `Delete`,
            confirmButtonColor: '#d33',
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {

                $.ajax({
                    type: "POST",
                    url: "controller/remainder-controller.php",
                    data: {
                        "remainder_id": remainder_id,
                        "deleteRemainder": 1
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);
                        if (data.data > 0) {
                            Swal.fire(
                                'Deleted!',
                                'Your file has been deleted.',
                                'success'
                            )
                            window.location.reload();
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });


            }
        })
    });

});