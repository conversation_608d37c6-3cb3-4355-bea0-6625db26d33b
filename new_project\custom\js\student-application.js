$(document).ready(function () {
    // $('#example2').DataTable({
    //     "paging": true,
    //     "lengthChange": true,
    //     "searching": true,
    //     "ordering": true,
    //     "info": true,
    //     "autoWidth": false,
    //     "responsive": true,
    // });
    var table = $("#example2").DataTable({
      paging: true,
      lengthChange: true,
      pageLength: 100,
      searching: true,
      ordering: true,
      info: true,
      autoWidth: false,
      responsive: true,
      initComplete: function () {
        // Apply the search
        this.api()
          .columns()
          .every(function () {
            var that = this;
  
            $("input", this.footer()).on("keyup change clear", function () {
              if (that.search() !== this.value) {
                that.search(this.value).draw();
              }
            });
          });
      },
    });

    
        $('.delete_btn').on('click',function(){        
        var student_application_id = $(this).data("student_application_id");    
        
        Swal.fire({
          title: "Are you sure want to delete?",
          showCancelButton: true,
          confirmButtonText: `Delete`,
          confirmButtonColor: "#d33",
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              type: "POST",
              url: "controller/application-controller.php",
              data: {
                delete_application_btn: 1, 
                student_application_id: student_application_id,
                },             
              dataType: "json",
              success: function (data) {
                if (data.data > 0) {
                  Swal.fire(
                    "Deleted!",
                    "Your Application has been deleted.",
                    "success"
                  );
                  window.location.reload();
                } else {
                  toastr.error(data.status);
                }
              },
            });
          }
        });
      });


  });