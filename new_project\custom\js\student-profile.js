$(document).ready(function() {


    $('.delete_student').on('click',function(){
        var student_id = $(this).data("student_id");      
        
        Swal.fire({
          title: "Are you sure want to delete?",
          showCancelButton: true,
          confirmButtonText: `Delete`,
          confirmButtonColor: "#d33",
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              type: "POST",
              url: "controller/student-controller.php",
              data: {
                delete_student: 1, 
                student_id: student_id,
                },             
              dataType: "json",
              success: function (data) {
                if (data.data > 0) {
                  Swal.fire(
                    "Deleted!",
                    "Your Application has been deleted.",
                    "success"
                  );                 
                  window.location.href = 'student-list.php';
                } else {
                  toastr.error(data.status);
                }
              },
            });
          }
        });
      });

});