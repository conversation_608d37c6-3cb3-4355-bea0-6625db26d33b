$(document).ready(function () {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });


    $('.delete_btn').on('click',function(){
      var agent_id = $(this).data('agent_id');
      
      Swal.fire({
              title: 'Are you sure want to delete?',
              showCancelButton: true,
              confirmButtonText: `Delete`,
              confirmButtonColor: '#d33',
          }).then((result) => {
              /* Read more about isConfirmed, isDenied below */
              if (result.isConfirmed) {

                $.ajax({
                    type:"POST",
                    url:"controller/agent-controller.php",
                    data:{"agent_id":agent_id,"deleteAgent":1},
                    dataType: 'json',
                    success:function(data){
                        //console.log(data);
                        if(data.data>0){
                            Swal.fire(
                              'Deleted!',
                              'Your file has been deleted.',
                              'success'
                            )
                            window.location.reload();
                        }else {
                            toastr.error(data.status);
                        }

                    }
                });

         
              }
          })
    });

    $('.enable_btn').on('click',function(){
        var agent_id = $(this).data('agent_id');
        console.log(agent_id);
        Swal.fire({
                
                title: 'Are you sure want to enable account?',
                showCancelButton: true,
                confirmButtonText: `Enable`,
                confirmButtonColor: '#00a65a',
            }).then((result) => {
                if (result.isConfirmed) {
  
                  $.ajax({
                      type:"POST",
                      url:"controller/agent-controller.php",
                      data:{"agent_id":agent_id,"enableAgent":1},
                      dataType: 'json',
                      success:function(data){
                          if(data.data>0){
                              Swal.fire(
                                'Disabled!',
                                'Account has been enabled.',
                                'success'
                              )
                              window.location.reload();
                          }else {
                              toastr.error(data.status);
                          }
                      }
                  });
  
           
                }
            })
      });

      $('.disable_btn').on('click',function(){
        var agent_id = $(this).data('agent_id');
        
        Swal.fire({
          title: 'Are you sure want to disable account?',
          showCancelButton: true,
          confirmButtonText: `Disable`,
          confirmButtonColor: '#d33',
            }).then((result) => {
                if (result.isConfirmed) {
  
                  $.ajax({
                      type:"POST",
                      url:"controller/agent-controller.php",
                      data:{"agent_id":agent_id,"disableAgent":1},
                      dataType: 'json',
                      success:function(data){
                          if(data.data>0){
                              Swal.fire(
                                'Disabled!',
                                'Account has been disabled.',
                                'success'
                              )
                              window.location.reload();
                          }else {
                              toastr.error(data.status);
                          }
                      }
                  });
  
           
                }
            })
      });

    
        $('.delete_application_btn').on('click',function(){
        var student_application_id = $(this).data("student_application_id");    
        
        Swal.fire({
          title: "Are you sure want to delete?",
          showCancelButton: true,
          confirmButtonText: `Delete`,
          confirmButtonColor: "#d33",
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              type: "POST",
              url: "controller/application-controller.php",
              data: {
                delete_application_btn: 1, 
                student_application_id: student_application_id,
                },             
              dataType: "json",
              success: function (data) {
                if (data.data > 0) {
                  Swal.fire(
                    "Deleted!",
                    "Your Application has been deleted.",
                    "success"
                  );
                  window.location.reload();
                } else {
                  toastr.error(data.status);
                }
              },
            });
          }
        });
      });


  });