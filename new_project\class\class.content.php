<?php

include_once 'connection.php';

class Content extends connection {

    public function __construct() {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Content() {
        self::__construct();
    }

    private $_conn;


    function getContentByName($content_name) {
        $conn = $this->_conn;
        $cond = '';

        if (isset($content_name) && strlen($content_name) > 0) {
            $content_name = mysqli_real_escape_string($conn, $content_name);
            $cond .= " and c.content_name = '$content_name' ";
        }else{
            $cond .= " and 1=2 ";
        }

        $query = "SELECT c.*
                    FROM ggportal_tbl_content c
                    WHERE 1=1  $cond;";
//        echo $query;
//        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }



    function saveContent($_details) {
        $conn = $this->_conn;
        $content_id = intval($_details['content_id']);

        //        print_R($driver_details);
        //        die();
        if ($content_id == 0) {
            $query = "INSERT INTO ggportal_tbl_content SET
                content_body = '" . mysqli_real_escape_string($conn, $_details['content_body']) . "'                         
               ;";
        } else {
            //update
            $query = "UPDATE `ggportal_tbl_content` SET
           content_body = '" . mysqli_real_escape_string($conn, $_details['content_body']) . "'   
            Where content_id = '$content_id'
            ";
        }
        // return $query;
        // die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($content_id == 0) {
                $content_id = mysqli_insert_id($conn);
            }
            return $content_id;

        } else {
            return mysqli_error($conn);
        }
    }


}