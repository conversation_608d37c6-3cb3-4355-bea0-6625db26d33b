<?php
if(session_status() !== PHP_SESSION_ACTIVE){
	session_start();
}

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
include_once $include_path . 'common_functions.php';
include_once $class_path . 'class.student.php';


$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

// check student details empty or not
// if empty then redirect to student-add.php

$cStudent = new Student();

$checkStudentDetails = $cStudent->getStudentByID($user_id);
//print_r($checkStudentDetails[0]);
if (
    $checkStudentDetails[0]['gender'] == '' &&
    $checkStudentDetails[0]['marital_status'] == '' &&
    $checkStudentDetails[0]['country'] == '' &&
    $checkStudentDetails[0]['emergency_contact_name'] == '' &&
    $checkStudentDetails[0]['emergency_contact_email'] == '' &&
    $checkStudentDetails[0]['emergency_contact_mobile'] == ''
    ) {
    errorRedirect("Please Fill Details to use the system", "./student-add.php",'W');
}



$education_files_list = $cStudent->getStudentDocuments($user_id, 'education');
$englishtest_files_list = $cStudent->getStudentDocuments($user_id, 'englishtest');
$travel_files_list = $cStudent->getStudentDocuments($user_id, 'travel');
$work_files_list = $cStudent->getStudentDocuments($user_id, 'work');
$other_files_list = $cStudent->getStudentDocuments($user_id, 'other');

$error_message = "";
if(count($education_files_list)==0){
    $error_message .= "Please Upload Education Documents<br>";
}
if(count($englishtest_files_list)==0){
    $error_message .= "Please Upload English Test Documents<br>";
}
if(count($travel_files_list)==0){
    $error_message .= "Please Upload Travel Documents<br>";
}
if(count($work_files_list)==0){
    $error_message .= "Please Upload Work Documents<br>";
}
if(count($other_files_list)==0){
    $error_message .= "Please Upload Other Documents<br>";
}
if($error_message!=""){
    // errorRedirect($error_message, "./student-add.php",'W');
}
