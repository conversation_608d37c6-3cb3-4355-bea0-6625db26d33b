$(document).ready(function() {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });


    $('.delete_btn').on('click', function() {
        var agent_sub_id = $(this).data('agent_sub_id');

        Swal.fire({
            title: 'Are you sure want to delete?',
            showCancelButton: true,
            confirmButtonText: `Delete`,
            confirmButtonColor: '#d33',
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {

                $.ajax({
                    type: "POST",
                    url: "controller/agent_sub-controller.php",
                    data: {
                        "agent_sub_id": agent_sub_id,
                        "deleteagent_sub": 1
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);
                        if (data.data > 0) {
                            Swal.fire(
                                'Deleted!',
                                'Your file has been deleted.',
                                'success'
                            )
                            //after 1 second reload the page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });


            }
        })
    });
    // })

});