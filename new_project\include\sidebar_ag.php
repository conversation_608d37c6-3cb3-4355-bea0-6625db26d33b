<!-- Main Sidebar Container -->
<aside class="main-sidebar elevation-4 sidebar-primary">
    <!-- Brand Logo -->
    <!-- <a href="dashboard.php" class="brand-link ctm-brand-link"> -->
    <a href="dash-agent.php" class="brand-link ctm-brand-link">
        <img src="dist/img/Icon.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">Edvios</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-child-indent  nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                <!-- Add icons to the links using the .nav-icon class
                     with font-awesome or any other icon font library -->
                <li class="nav-item">
                    <a href="dash-agent.php" class="nav-link">
                        <i class="nav-icon fas fa-th"></i>
                        <p>
                            Dashboard
                            <!-- <span class="right badge badge-danger">New</span> -->
                        </p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="program.php" class="nav-link">
                        <i class="nav-icon fas fa-book-open"></i>
                        <p>
                            Program Finder
                        </p>
                    </a>
                </li>

                <?php
                $user_type = $_SESSION['user']['user_type'];
                $cAgent = new Agent();
                $user_id = $_SESSION['user']['user_id'];
                $cAgreement = $cAgent->getAgentByID($user_id);
                $agent_agreement = $cAgreement[0]['agreement_signed_yn'];
                if ($user_type == 'AG' && $agent_agreement == 'Y') {
                ?>
                    <li class="nav-item">
                        <a href="student-list.php" class="nav-link">
                            <i class="nav-icon fas fa-graduation-cap"></i>
                            <p>
                                Students
                            </p>
                        </a>
                    </li>
                <?php } ?>
                <?php
                $user_type = $_SESSION['user']['user_type'];
                $cAgent = new Agent();
                $user_id = $_SESSION['user']['user_id'];
                $cAgreement = $cAgent->getAgentByID($user_id);
                $agent_agreement = $cAgreement[0]['agreement_signed_yn'];
                if ($user_type == 'AG' && $agent_agreement == 'Y') {
                ?>
                    <li class="nav-item">
                        <a href="student-application-view-all.php?$user_id=<?= $user_id ?>" class="nav-link">
                            <i class="nav-icon fas fa-book"></i>
                            <p>
                                Student Applications
                            </p>
                        </a>
                    </li>
                <?php } ?>
                

                <?php
                $user_type = $_SESSION['user']['user_type'];
                $cAgent = new Agent();
                $user_id = $_SESSION['user']['user_id'];
                $cAgreement = $cAgent->getAgentByID($user_id);
                $agent_agreement = $cAgreement[0]['agreement_signed_yn'];
                if ($user_type == 'AG' && $agent_agreement == 'Y') {
                ?>
                    <li class="nav-item">
                        <a href="staff-list.php" class="nav-link">
                            <i class="nav-icon fas fa-user-check"></i>
                            <p>
                                Staff
                            </p>
                        </a>
                    </li>
                <?php } ?>

                <li class="nav-item">
                    <a href="training-request-list.php" class="nav-link">
                        <i class="nav-icon fas fa-flag"></i>
                        <p>
                            Training Request
                        </p>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="agreement-content-add.php" class="nav-link">
                        <i class="nav-icon fas fa-file"></i>
                        <p>Agreement Contract</p>
                    </a>
                </li>

            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
<script>
    $(document).ready(function() {

        var url = window.location;
        // for single sidebar menu
        $('ul.nav-sidebar a').filter(function() {
            return this.href == url;
        }).addClass('active');

        // for sidebar menu and treeview
        $('ul.nav-treeview a').filter(function() {
                return this.href == url;
            }).parentsUntil(".nav-sidebar > .nav-treeview")
            .css({
                'display': 'block'
            })
            .addClass('menu-open').prev('a')
            .addClass('active');

    });
</script>