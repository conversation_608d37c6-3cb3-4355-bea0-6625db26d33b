<?php
/*
 * Copyright (c) 2021.  @aashif
 */

include_once 'connection.php';



class License extends connection {

    public function __construct() {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function License() {
        self::__construct();
    }

    private $_conn;

    function getLicenseByID($License_id) {
        $conn = $this->_conn;
        $License_id = intval($License_id);
        if($License_id>0){
            $cond="and a.License_id=$License_id ";
        }else{
            $cond=" and 1=2 ";
        }
        $query = "SELECT a.* FROM nbcourier_tbl_License a WHERE 1=1 $cond;";
//        echo $query;
//        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getLicense($criteria) {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['date']) && strlen($criteria['date']) > 0) {
            $cond .= " and  date_format(now(), '%Y-%m-%d') <= n.valid_to
and  date_format(now(), '%Y-%m-%d') >= n.valid_from";
        }

        if (isset($criteria['status_yn']) && strlen($criteria['status_yn']) > 0) {
            $status_yn = mysqli_real_escape_string($conn, $criteria['status_yn']);
            $cond .= " and  n.status_yn = '".$status_yn."'";
        }


        $query = "select n.* from nbcourier_tbl_license n where 1=1 $cond;";
//        echo $query;
//        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getNoticeByDate() {
        $conn = $this->_conn;

        // $query = "select n.* from nbcourier_tbl_notice n where n.is_active='Y' and n.date > DATE_SUB( NOW(), INTERVAL 24 HOUR) order by n.License_id;";
        $query = "select n.* from nbcourier_tbl_License n where n.is_active='Y' order by n.date DESC;";
        //        echo $query;
//        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function saveLicense($_details) {
        $conn = $this->_conn;
        $License_id = intval($_details['License_id']);
        
        if ($License_id == 0) {
            $query = "INSERT INTO nbcourier_tbl_License SET
                title = '" . mysqli_real_escape_string($conn, $_details['title']) . "'     
                ,description = '" . mysqli_real_escape_string($conn, $_details['description']) . "'     
                ,image_url = '" . mysqli_real_escape_string($conn, $_details['image_url']) . "'     
                ,goto_url = '" . mysqli_real_escape_string($conn, $_details['goto_url']) . "'     
                ,valid_from = '" . mysqli_real_escape_string($conn, $_details['valid_from']) . "'     
                ,valid_to = '" . mysqli_real_escape_string($conn, $_details['valid_to']) . "'     
                ,created_date = now()
                ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'                              
               ;";
        } else {
            //update
            $query = "UPDATE nbcourier_tbl_License SET 
                title = '" . mysqli_real_escape_string($conn, $_details['title']) . "'     
                ,description = '" . mysqli_real_escape_string($conn, $_details['description']) . "'     
                ,image_url = '" . mysqli_real_escape_string($conn, $_details['image_url']) . "'     
                ,goto_url = '" . mysqli_real_escape_string($conn, $_details['goto_url']) . "'     
                ,valid_from = '" . mysqli_real_escape_string($conn, $_details['valid_from']) . "'     
                ,valid_to = '" . mysqli_real_escape_string($conn, $_details['valid_to']) . "'   
                ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'         
                where License_id=$License_id ;";
        }
//        return $query;
//        die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($License_id == 0) {
                $License_id = mysqli_insert_id($conn);
            }
            return $License_id;
        } else {
            return 0;
        }
    }

}