$(document).ready(function () {


    $('.select2').select2();

    //Initialize Select2 Elements
    $('.select2bs4').select2({
        theme: 'bootstrap4'
    });

    $('#reservation').daterangepicker({
        locale: {
            format: 'YYYY-MM-DD'
        },
        singleDatePicker: true,
        showDropdowns: true,
    });

    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });

    // doucument .program_id change event
    $(document).on('change', '.program_id', function () {
        var program_id = $('.program_id').val();
        var institute_id = $('.institute_id').val();

        //console.log({course_id, institute_id});

        // if institute id is not selected then show error
        if (institute_id == '') {
            toastr.info('Please Select a College');
            $('.institute_id').addClass('is-invalid');
            $('.institute_id').focus();
            // remove selected multiple dropdown value
            $('.program_id').val('');
            return false;
        } else {
            //array to comma separated string
            var program_id_str = program_id.join(',');
            //console.log(course_id_str);

            $.ajax({
                type: "POST",
                url: "controller/program-controller.php",
                data: {
                    'program_id_str': program_id_str,
                    'getProgramMultipleDetails': 1
                },
                dataType: 'json',
                cache: false,
                success: function (data) {
                    //console.log(data);
                    var program_arr = data.data;
                    //for each course
                    var html = '';
                    program_arr.forEach(element => {
                        //console.log(element);
                        // if course name is empty then show course  inline if else

                        html += '<div><h3 class="profile-username">' + element[
                            'program_name'] + '</h3>' +
                            '<a href="' + element['program_web_url'] + '">' +
                            element['program_web_url'] + '</a>' +
                            '<div class="col-12 row p-2 bg-gray-light rounded">' +
                            '<div class="col-6 text-left">' +
                            '<h6 class="mb-0 ">Tuition Fee :<b>~' + element[
                            'currency_code'] + ' ' + element['tution_fee'] +
                            '</b></h3>' +
                            '</div><div class="col-6 text-left">' +
                            '<h6 class="mb-0 ">Application Fee :<b>~' + element[
                            'currency_code'] + ' ' + element[
                            'application_fee'] + '</b></h3>' +
                            '</div><div class="col-6 text-left">' +
                            '<h6 class="mb-0 ">Intake :<b>' + element[
                            'intake'] + '</b></h3>' +
                            '</div><div class="col-6 text-left">' +
                            '<h6 class="mb-0 ">Duration :<b> ' + element[
                            'duration'] + '</b></h3>' +
                            '</div></div></div>';

                    });

                    $('.course_list').html(html);

                }
            });
            $('.institute_id').removeClass('is-invalid');
        }

    });

    //   institute on change
    $('.institute_id').change(function () {
        var institute_id = $(this).val();
        //console.log(institute_id);
        $('.course_list').html('');

        //on institute is change get all course list
        $.ajax({
            type: "POST",
            url: "controller/program-controller.php",
            data: {
                'institute_id': institute_id,
                'program_id': 0,
                'getProgramMultipleCombo': 1
            },
            dataType: 'json',
            cache: false,
            success: function (data) {
                //console.log(data.data);
                $('.course_select_div').html(data.data);
                $('.program_id').select2();

            }
        });

        $.ajax({
            type: "POST",
            url: "controller/institute-controller.php",
            data: {
                'institute_id': institute_id,
                'getInstituteDetails': 1
            },
            dataType: 'json',
            cache: false,
            success: function (data) {
                $('.institute_name').text(data.data[0]['institute_name']);
                $('.institute_web_url').text(data.data[0]['web_url']);
                // change image src
                let logo_url = data.data[0]['logo_url'];
                if (logo_url == '') {
                    $('.institute_logo_url').attr('src', 'dist/img/building.png');
                } else {
                    $('.institute_logo_url').attr('src', data.data[0]['logo_url']);
                }
            }
        });

    });

    /*Save program*/
    // any id start with updateEstDate
    $('.updateLogForm').on('click', function () {

        var id = $(this).data('form_id');
        //console.log(id);

        $('#' + id).validate({
            rules: {
                new_due_date: {
                    required: true,
                },

            },
            messages: {

                new_due_date: {
                    required: "Please Select a date",
                },
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function (element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            },
            submitHandler: function (form) {
                // log form id to variable
                //var form_id = $(form).attr('id');
                //console.log(form_id);
                var formData = new FormData(form);
                formData.append('updateEstDate', '1');
                $.ajax({
                    type: "POST",
                    url: "controller/application-controller.php",
                    data: formData,
                    dataType: 'json',
                    mimeType: "multipart/form-data",
                    contentType: false,
                    processData: false,
                    cache: false,
                    success: function (data) {
                        //console.log(data);
                        if (data.data > 0) {
                            toastr.success('Saved Successfully');
                            //window.location.href = 'student-applications.php';
                            //$('#form').trigger("reset");
                            //   window.location.href = window.location.pathname + '?savedSuccess=1';
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });
            }
        });

        $('#' + id).submit();

    });


    /*Save program*/
    $('#quickform').validate({
        rules: {
            student_id: {
                required: true,
            },
            my_prefer_yn: {
                required: true,
            },
            institute_id: {
                required: true,
            },
            course_id: {
                required: true,
            },
            intake: {
                required: true,
            },
            year: {
                required: true,
            },

        },
        messages: {

            student_id: {
                required: "Please Enter a value",
            },
            my_prefer_yn: {
                required: "Please Select One",
            },
            institute_id: {
                required: "Please Select One",
            },
            course_id: {
                required: "Please Select One",
            },
            intake: {
                required: "Please Select One",
            },
            year: {
                required: "Please Select One",
            },
        },
        errorElement: 'span',
        errorPlacement: function (error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function (form) {
            console.log(new FormData(form));
            var program_id = $('.program_id').val();
            var program_id_str = program_id.join(',');
            var formData = new FormData(form);
            formData.append('studentApplicationUpdate', '1');
            formData.append('program_id_str', program_id_str);
            $.ajax({
                type: "POST",
                url: "controller/application-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function (data) {
                    console.log(data);
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        // window.location.href = 'student-applications.php';
                        //$('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });
        }
    });

});