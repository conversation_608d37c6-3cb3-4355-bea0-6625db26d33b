<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';
include '../form-submit-email.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cTraining = new Training();
$cagent = new Agent();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

// get Training  Details
if (isset($_POST['getTrainingDetails'])) {

    $training_request_id = filter_input(INPUT_POST, 'training_request_id');


    $result = $cTraining->getTrainingByID($training_request_id);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// save Training
if (isset($_POST['trainingSave'])) {

    $training_request_id = filter_input(INPUT_POST, 'training_request_id');
    $agent_id = filter_input(INPUT_POST, 'agent_id');
    $training_type = filter_input(INPUT_POST, 'training_type');
    $perfer_date_1 = filter_input(INPUT_POST, 'perfer_date_1');
    $perfer_date_2 = filter_input(INPUT_POST, 'perfer_date_2');
    $perfer_date_3 = filter_input(INPUT_POST, 'perfer_date_3');
    $active_yn = filter_input(INPUT_POST, 'active_yn');
    $send_yn = filter_input(INPUT_POST, 'send_yn');

    // format date mysql datetime format
    $perfer_date_1 = date('Y-m-d H:i:s', strtotime($perfer_date_1));
    $perfer_date_2 = date('Y-m-d H:i:s', strtotime($perfer_date_2));
    $perfer_date_3 = date('Y-m-d H:i:s', strtotime($perfer_date_3));

    //add to array 
    $criteria = array(
        'training_request_id' => $training_request_id,
        'training_type' => $training_type,
        'perfer_date_1' => $perfer_date_1,
        'perfer_date_2' => $perfer_date_2,
        'perfer_date_3' => $perfer_date_3,
        'active_yn' => $active_yn,
        'send_yn' => $send_yn,
        'user_id' => $agent_id,
        'user_type' => 'AG'
    );

    $result = $cTraining->saveTraining($criteria);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//update training
if (isset($_POST['trainingUpdate'])) {

    $training_request_id = filter_input(INPUT_POST, 'training_request_id');
    $meeting_link = filter_input(INPUT_POST, 'meeting_link');
    $selected_date = filter_input(INPUT_POST, 'prefer_date');
    $user_id = filter_input(INPUT_POST, 'user_id');

    $email = $cagent->getAgentByID($user_id);
    $email = $email[0]['email'];

    $details_list = $cTraining->getTrainingByID($training_request_id);
    $old_date = $details_list[0]['selected_date'];

    // format date mysql datetime format
    if ($selected_date != '') {
        $selected_date = date('Y-m-d H:i:s', strtotime($selected_date));
    } else {
        $selected_date = null;
    }


    //add to array 
    $criteria = array(
        'training_request_id' => $training_request_id,
        'selected_date' => $selected_date,
        'meeting_link' => $meeting_link,

    );

    $result = $cTraining->updateTrainingLink($criteria);

    if (($result > 0 && $meeting_link != '') || ($result > 0 && $selected_date != $old_date && $meeting_link != '')) {
        $token_id = uniqid();
        $address = $email;
        $subject = 'Edvios - Training Link';

        $body = '<!DOCTYPE html>
        <html>
        
        <head>
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
            <style>
                .header {
                    background-color: #293C4A;
                    padding: 20px;
                    font-size: 25px;
                    text-align: center;
                    font-weight: bolder;
                }
        
                .header h2 {
                    color: #fff;
                    margin-bottom: 0px;
                }
        
         
        
                .header p {
                    padding-top: 0px;
                    margin-top: 0px;
                    color: #fff;
                    font-size: 16px;
                }
        
            
            </style>
        </head>
        
        <body>
        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
        <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
          <tbody><tr>
            <td align="center" valign="top">
              <table width="650" border="0" cellspacing="0" cellpadding="0">
                <tbody><tr>
                  <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                    
                      <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                      
                          <tbody><tr>
                              <td class="header">
                                  <h2 style="font-weight: 700;">Edvios</h2>
                                  <p style="padding-top: 30px;">The Agent Portal</p>
                              </td>
                          </tr>
                      </tbody></table>
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tbody><tr>
                        <td>
                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                            <tbody><tr>
                              <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                  <tbody><tr>
                                    <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                      Hi Agent , </td>
                                  </tr>
                                  
                                </tbody></table>
                              </td>
                            </tr>
                          </tbody></table>
                        </td>
                      </tr>
                    </tbody></table>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody><tr>
                                  <td>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                      <tbody><tr>
                                        <td style="padding:0px 20px">
                                          <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                            <tbody><tr height="5px">
                                              <td width="100%">
                                                  &nbsp;                              
                                              </td>
                                            </tr>
                                            
                                            <tr height="30px">
                                               <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                               <p> You have been invited to schedule a online meeting on.</p> 
                                                <p> ' . $selected_date . ' .</p>                               
                                              </td>                             
                                            </tr>

                                            <tr height="25px">
                                               <td width="100%" style="color:#000000;text-align:left;font-weight:700;font-size:14px;line-height:20px">                             
                                               <a class="primary-btn" href="' . $meeting_link . '" target="_blank">Join Meeting</a>                            
                                              </td>                             
                                            </tr>
        
                                            <tr>
                                              <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                                <p>Thank you.<br> Kind Regards,<br> Edvios</p>
                                              </td>                             
                                            </tr> 
                                                <tr height="15px">
                                                    <td width="100%">&nbsp;</td>
                                                </tr>
                                                                 
                                                                       
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                                  </td>
                                </tr>
                              </tbody></table>
        
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tbody><tr>
                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                          <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody><tr>
                              <td align="center" style="padding-bottom:10px">
                                <table border="0" cellspacing="0" cellpadding="0">
                                  <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                  <tr>
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                    
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                  </tr>							
                                </tbody></table>
                              </td>
                            </tr>
                              
                          
                          </tbody></table>
                          
                        </td>
                      </tr>
                    </tbody></table>
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                      <tbody><tr>
                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                          <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                              <tbody><tr>
                              <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                  Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                            </tr>
                          </tbody></table>
                        </td>
                      </tr>
                    </tbody></table>
                    
                  </td>
                </tr>
              </tbody></table>
            </td>
          </tr>
        </tbody></table>
        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
        
        </body>
        
        </html>';

        send_mail($subject, $address, $body);
    }

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}


// delete Training
if (isset($_POST['deleteTraining'])) {

    $training_request_id = filter_input(INPUT_POST, 'training_request_id');

    $result = $cTraining->deleteTraining($training_request_id);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//update Training
if (isset($_POST['updateTraining'])) {

    $training_request_id = filter_input(INPUT_POST, 'training_request_id');
    $active_yn = filter_input(INPUT_POST, 'active_yn');

    $criteria = array(
        'training_request_id' => $training_request_id,
        'active_yn' => $active_yn
    );

    $result = $cTraining->updateTraining($criteria);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//get reminders
if (isset($_POST['getAllTrainings'])) {

    // $training_request_id = filter_input(INPUT_POST, 'training_request_id');

    $criteria = array(
        // 'user_id' => $user_id,
        // 'user_type' => $user_type,
        'active_yn' => 'Y'
    );

    $result = $cTraining->getTrainings($criteria);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}
