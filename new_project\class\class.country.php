<?php

include_once 'connection.php';

class Country extends connection
{

    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Country()
    {
        self::__construct();
    }

    private $_conn;

    function getLogin($username)
    {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $username) . "' ";
        $query = "SELECT u.* FROM ggportal_tbl_country u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    function randomPassword()
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $password = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < 8; $i++) {
            $n = rand(0, $alphaLength);
            $password[] = $alphabet[$n];
        }
        return implode($password); //turn the array into a string
    }

    function getCountryByID($country_id)
    {
        $conn = $this->_conn;
        $country_id = intval($country_id);
        if ($country_id > 0) {
            $cond = "and c.country_id=$country_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT * FROM ggportal_tbl_country c
                    WHERE 1=1  $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getCountryIDByName($country_name)
    {
        $conn = $this->_conn;

        $query = "SELECT c.country_id
        FROM ggportal_tbl_country c
        WHERE c.country_name LIKE '_$country_name' OR c.country_name LIKE '$country_name' LIMIT 1;";
        $res = mysqli_query($conn, $query);

        if (!$res) {
            return false;
        }

        $row = mysqli_fetch_assoc($res);
        if (!$row || !isset($row['country_id'])) {
            return false;
        }
        return $row['country_id'];
    }

    function getCountrys($criteria)
    {
        $conn = $this->_conn;
        $cond = "";



        $query = "select s.* from ggportal_tbl_country s
         where 1=1  $cond order by s.country_id DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }



    function saveCountry($_details)
    {
        $conn = $this->_conn;
        $country_id = intval($_details['country_id']);

        //        print_R($driver_details);
        //        die();
        if ($country_id == 0) {
            $query = "INSERT INTO ggportal_tbl_country SET                    
                country_name = '" . mysqli_real_escape_string($conn, $_details['country_name']) . "'                         
                ,country_code = '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'                    
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_country SET     
             country_name = '" . mysqli_real_escape_string($conn, $_details['country_name']) . "'                         
             ,country_code = '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'
            WHERE country_id = $country_id;  ";
        }
        // return $query;
        //    die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($country_id == 0) {
                $country_id = mysqli_insert_id($conn);
            }
            return $country_id;
        } else {
            return mysqli_error($conn);
        }
    }


    function deleteCountry($country_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_country` WHERE `country_id` = $country_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $country_id;
        } else {
            return mysqli_error($conn);
        }
    }

    //get country name by institute id
    function getCountryNameByInstituteID($institute_id)
    {
        $conn = $this->_conn;
        $institute_id = intval($institute_id);
        if ($institute_id > 0) {
            $cond = "and i.institute_id=$institute_id";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT c.country_code FROM ggportal_tbl_country c
                    INNER JOIN ggportal_tbl_institute i ON i.institute_country_id = c.country_id
                    WHERE 1=1  $cond;";
        $res = mysqli_query($conn, $query);

        //return as string
        $row = mysqli_fetch_assoc($res);
        if (!$row || !isset($row['country_code'])) {
            return false;
        }
        return $row['country_code'];
    }
    function getCountryNameByID($country_id)
    {
        $conn = $this->_conn;
        $country_id = intval($country_id);
        $country_name = ""; // Initialize country name variable
    
        if ($country_id > 0) {
            // Use prepared statement to prevent SQL injection
            $query = "SELECT country_name FROM ggportal_tbl_country WHERE country_id = ?";
            $stmt = mysqli_prepare($conn, $query);
    
            if ($stmt) {
                // Bind the country_id parameter
                mysqli_stmt_bind_param($stmt, "i", $country_id);
                
                // Execute the query
                mysqli_stmt_execute($stmt);
                
                // Bind the result
                mysqli_stmt_bind_result($stmt, $country_name);
                
                // Fetch the result
                mysqli_stmt_fetch($stmt);
                
                // Close the statement
                mysqli_stmt_close($stmt);
            } else {
                // Error handling for failed prepared statement
                $error_message = mysqli_error($conn);
                echo "Error creating prepared statement: $error_message";
                // You can log or handle this error as needed
            }
        }
        
        // Return the country name
        return $country_name;
    }
}
