<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'email_functions.php';

$redirect_url = filter_input(INPUT_GET, "redirect", FILTER_SANITIZE_STRING);
// if(isset($redirect_url)){
//   print_r($redirect_url);
//   die();//after loggedin, redirect.
// }

$_SESSION['user']['login_session'] = generate_random_characters(25);
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Edvios</title>

  <!-- Google Font: Source Sans Pro -->
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"> -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="../plugins/fontawesome-free/css/all.min.css">
  <!-- icheck bootstrap -->
  <link rel="stylesheet" href="../plugins/icheck-bootstrap/icheck-bootstrap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="../dist/css/adminlte.min.css">
  <!-- New Styles -->
  <link rel="stylesheet" href="../dist/css/new-styles.css">
</head>

<body class="hold-transition login-page">
  <div class="login-box">
    <!-- /.login-logo -->
    <div class="card card-outline card-teal newLoginCard">
      <div class="card-header text-center newCardHeader">
        <!-- <a href="#" class="h1"><b>GG</b>Portal</a> -->
        <a class="navbar-brand" href="index.php"><img src="../dist/img/login/edvious_logo.png" width="100%" height="90px" class="imgsize"></a>

      </div>
      <div class="card-body newCardBody">
        <div class="admin-portal-wrap">
          <p class="login-box-msg newCardTitle" style="margin-top: 0;">Admin Portal</p>
        </div>
        <?php include_once $include_path . 'display_message.php'; ?>
        <form name="frmLogin" action="../include/validate-login.php" method="post">

          <input type="hidden" value="<?php echo $redirect_url ?>" name='redirect' />
          <input type="hidden" value="<?php echo $_SESSION['user']['login_session'] ?>" name='session_variable' />
          <div class="input-group mb-2">
            <input type="email" class="form-control newFormControl" placeholder="Username" name="email" id="email" required="" value="">
          </div>
          <div class="input-group mb-2">
            <input type="password" class="form-control newFormControl" placeholder="Password" name="password" id="password" required="" value="">
          </div>
          <div class="row d-flex justify-content-center">
            <!--<div class="col-8">
            <div class="icheck-primary">
              <input type="checkbox" id="remember">
              <label for="remember">
                Remember Me
              </label>
            </div>
          </div>-->
            <!-- /.col -->
            <div class="col-6">
              <button type="submit" class="btn btn-block newActionButton">Sign In</button>
              <!--            <a href="../dashboard.php" class="btn btn-primary btn-block">Sign In</a>-->
            </div>
            <!-- /.col -->
          </div>
        </form>


        <p class="mb-1 d-flex justify-content-center">
          <!-- <a href="../forgot-password.php">I forgot my password</a> -->
        </p>
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.login-box -->

  <!-- jQuery -->
  <script src="../plugins/jquery/jquery.min.js"></script>
  <!-- Bootstrap 4 -->
  <script src="../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <!-- AdminLTE App -->
  <script src="../dist/js/adminlte.min.js"></script>
</body>

</html>