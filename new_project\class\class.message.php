<?php
/*
 * Copyright (c) 2021.  @aashif
 */

include_once 'connection.php';



class Messages extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    public function Messages()
    {
        self::__construct();
    }

    private $_conn;


    //function for return chats by using message ID
    //Codeaisys#Sithum#AdminView
    public function getStaffMessagesByID($message_id)
    {
        $conn = $this->_conn;

        $message_id = intval($message_id);
        if ($message_id > 0) {
            $cond = "and c.message_id=$message_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT * FROM ggportal_tbl_message_staff c WHERE 1=1 $cond;";        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    //Admin Student
    public function getmessagesByID($message_id)
    {
        $conn = $this->_conn;

        $message_id = intval($message_id);
        if ($message_id > 0) {
            $cond = "and c.message_id=$message_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT * FROM ggportal_tbl_message c WHERE 1=1 $cond;";        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }



    public function getmessagess($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['message']) && strlen($criteria['message']) > 0) {
            $message = mysqli_real_escape_string($conn, $criteria['message']);
            $cond .= " and r.message like '%$message%' ";
        }


        $query = "select c.* from ggportal_tbl_message c  where 1=1 $cond order by c.message_id;";        //        echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }



    #get student and admin chat from student side
    public function getStudentAdminChat($student_id)
    {//student side
        $conn = $this->_conn;
        $query = "
        SELECT m.message, m.message_id,m.user_id, m.is_read ,m.user_type,u.last_seen FROM `ggportal_tbl_message` m 
        JOIN `ggportal_tbl_user` u
            WHERE m.user_id = $student_id
            ORDER BY m.message_id DESC";
        $result = mysqli_query($conn, $query);
        return db_to_array($result);
        ;
    }

    //function for return chats to student by using student ID
    //Codeaisys#Sithum#Student Side
    public function getStudentStaffMemberChat($student_id)
    {

        $conn = $this->_conn;
        $query = "
        SELECT m.message, m.message_id, m.is_read,m.student_id ,m.user_type FROM `ggportal_tbl_message_staff` m 
        WHERE m.student_id = $student_id
        ORDER BY m.message_id DESC";
        $result = mysqli_query($conn, $query);

        return db_to_array($result);
    }

    //Save student admin messages
    // public function savemessages($_details)
    // {
    //     $conn = $this->_conn;

    //     $query = "INSERT INTO ggportal_tbl_message SET
    //             `message` = '" . mysqli_real_escape_string($conn, $_details['message']) . "'       
    //             ,`user_type` = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'  
    //             ,`user_id` = '" . mysqli_real_escape_string($conn, $_details['user_id']) . "'
    //             ,`is_read` = '" . mysqli_real_escape_string($conn, $_details['is_read']) . "';";

    //     $res = mysqli_query($conn, $query);
    //     return $res;
    // }
    public function savemessages($_details)
{
    $conn = $this->_conn;

    $query = "INSERT INTO ggportal_tbl_message SET
              `message` = '" . mysqli_real_escape_string($conn, $_details['message']) . "',
              `user_type` = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "',
              `user_id` = '" . mysqli_real_escape_string($conn, $_details['user_id']) . "',
              `is_read` = '" . mysqli_real_escape_string($conn, $_details['is_read']) . "';";
              
    $res = mysqli_query($conn, $query);
    return $res;
}

    

   


    //Sithum#save student staff message from student side
    public function saveMessagesStudentStaff($_details)
    {
        // die();
        $conn = $this->_conn;

    
            $query = "INSERT INTO ggportal_tbl_message_staff SET
                `message` = '" . mysqli_real_escape_string($conn, $_details['message']) . "'       
                ,`user_type` = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'  
                ,`student_id` = '" . mysqli_real_escape_string($conn, $_details['student_id']) . "'
                ,`staff_id` = '" . mysqli_real_escape_string($conn, $_details['staff_id']) . "'                                                  
               ";
    
        $res = mysqli_query($conn, $query);
        return $res;
    }

    //Sithum
    //#Codeaisys#Load Messages from staff member from his student
    public function getStudentsforStaffUser($staff_id)
    {
        $conn = $this->_conn;

        //select only his assign students
        $query = "SELECT
        stu.student_id,
        stu.first_name,
        stu.mobile,
        stu.email,
        stu.last_seen,
        stu.student_no,
        m.message AS message,
        (
        SELECT is_read 
        FROM ggportal_tbl_message_staff 
        WHERE message_id = max_message.last_message_id 
          AND user_type = 'ST'
    ) AS is_read  
    FROM
        ggportal_tbl_student stu
    LEFT JOIN (
        SELECT
            student_id,
            MAX(message_id) AS last_message_id
        FROM
            ggportal_tbl_message_staff
        GROUP BY
            student_id
    ) AS max_message ON stu.student_id = max_message.student_id
    LEFT JOIN
        ggportal_tbl_message_staff m ON max_message.last_message_id = m.message_id
    WHERE
        stu.assign_to_staff = $staff_id";

        $result = mysqli_query($conn, $query);
        if (!$result) {
            die('MySQL Error: ' . mysqli_error($conn));
        }

        return db_to_array($result);
    }



    //Codeaisys#Sithum#return staff stuents list  to staff
    public function getStudentsListToStaff($criteria)
    {
        $conn = $this->_conn;

        $query = "
    SELECT m.message, m.message_id, c.*, m.is_read
    FROM `ggportal_tbl_message` m
     JOIN `ggportal_tbl_student` c
    WHERE m.message_id IN (
        SELECT MAX(m.message_id)
        FROM `ggportal_tbl_message` m
        GROUP BY m.user_id
    )
    ORDER BY m.message_id DESC
";
        $result = mysqli_query($conn, $query);

        // echo($query);
        // die();
        return db_to_array($result);
        ;

        //    return db_to_array($result);

    }




    //return admin student message thread details
    //NO 1
    public function getStudents($criteria)
    {
        $conn = $this->_conn;

        #Sithum#Change to Join for retrive all the students
        $query = "
    SELECT m.message, m.message_id, c.*, m.is_read
    FROM `ggportal_tbl_message` m
     JOIN `ggportal_tbl_student` c
    WHERE m.message_id IN (
        SELECT MAX(m.message_id)
        FROM `ggportal_tbl_message` m
        GROUP BY m.user_id
    )
    ORDER BY m.message_id DESC
";
        $result = mysqli_query($conn, $query);

        return db_to_array($result);
        ;
    }

    // new function to  return admin side student list with messages if success then delete No 1
    public function getStudentsforadminsidewiththerechat($criteria)
    {
        $conn = $this->_conn;
        $query = "SELECT
        s.*,
        s.student_no AS student_no,
        IFNULL(m.message_id, '') AS message_id,
        IFNULL((SELECT is_read FROM ggportal_tbl_message WHERE message_id = m.message_id AND user_type = 'ST'), 0) AS is_read,
        IFNULL(m.message, '') AS message
    FROM
        ggportal_tbl_student s
    LEFT JOIN (
        SELECT
            user_id,
            MAX(message_id) AS message_id
        FROM
            ggportal_tbl_message
        GROUP BY
            user_id
    ) message ON s.student_id = message.user_id
    LEFT JOIN
        ggportal_tbl_message m ON message.user_id = m.user_id AND message.message_id = m.message_id;
    ";
        $result = mysqli_query($conn, $query);

        // echo($query);
        // die();
        return db_to_array($result);
        ;
    }

    //Codeaisys#Sithum#Save seen status student staff
    public function saveseenStaffStudent($messagedetails)
    {

        $conn = $this->_conn;
        $user_type = strtoupper($messagedetails['user_type']);
        $id_field = ($user_type === 'ST') ? 'student_id' : 'staff_id';
        $id_value = intval($messagedetails[$id_field]);

        $query = "UPDATE ggportal_tbl_message_staff 
          SET `is_read` = 'Y'
          WHERE `$id_field` = $id_value AND `user_type` = '$user_type'";

        $res = mysqli_query($conn, $query);

        if ($res) {
            return $id_value;
        } else {
            return 0;
        }

    }

    public function saveseen($messagedetails)
    {
        // die();
        $conn = $this->_conn;
        $student_id = intval($messagedetails['student_id']);

        $query = "UPDATE ggportal_tbl_message SET 
            `is_read` = 'Y'
            WHERE `user_id`=$student_id
            ;";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return ($student_id);
        } else {
            return 0;
        }
    }

    //Codeaisys#Sithum#Save seen status student Admin messages from student side

    public function saveseenstudentadmin($messagedetails)
    {
        // die();
        $conn = $this->_conn;
        $student_id = intval($messagedetails['student_id']);

        $query = "UPDATE ggportal_tbl_message SET 
            `is_read_cu` = 'Y'
            WHERE `user_id`=$student_id
            ;";

        $res = mysqli_query($conn, $query);
        if ($res) {
            return ($student_id);
        } else {
            return 0;
        }
    }



    public function getUnreadMsg($user_type, $user_id)
    {
        $conn = $this->_conn;


        if ($user_type == "RA") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message
            WHERE user_type = 'ST' AND is_read = 'N'";
        } else if ($user_type == "AG") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message_agent
            WHERE user_type = 'ST' AND agent_id = $user_id AND is_read = 'N'";
        } else if ($user_type == "SF") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message_staff
            WHERE user_type = 'ST' AND staff_id = $user_id AND is_read = 'N'
            ";
        } else if ($user_type == "ST") {
            $query = "SELECT SUM(CASE WHEN is_read = 'N' THEN 1 ELSE 0 END) AS count
            FROM (
                SELECT is_read FROM ggportal_tbl_message_staff WHERE user_type = 'SF' AND student_id = $user_id
                UNION ALL
                SELECT is_read FROM ggportal_tbl_message_agent WHERE user_type = 'AG' AND student_id = $user_id
                UNION ALL
                SELECT is_read FROM ggportal_tbl_message WHERE user_type = 'RA' AND user_id = $user_id
            ) AS is_read";
        }

        $result = mysqli_query($conn, $query);

        // Assuming db_to_array is a custom function
        return mysqli_fetch_all($result, MYSQLI_ASSOC);
    }
    

    //#Codeaisys#Sithum#23/09/02#Get Staff Unseen Messages
    public function getUnreadMsgStaff($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        $query = "select m.is_read,COUNT(case when m.is_read = 'N' then 1 else null end) count from ggportal_tbl_message_staff m  where 1=1 order by m.message_id;";

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    public function getUnreadCuMsg($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        $student_id = intval($criteria[0]);
        // print_r($student_id);
        // die();

        $query = "select m.is_read_cu,COUNT(case when m.is_read_cu = 'N' and m.user_id=$student_id then 1 else null end) count from ggportal_tbl_message m  where 1=1 order by m.message_id;";

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }


    // Student agent chat start
    public function getStudentAgentChat($student_id)
    {
//student side
        $conn = $this->_conn;
        $query = "
        SELECT
    m.message,
    m.message_id,
    m.is_read,
    m.student_id,
    m.user_type
    FROM
        ggportal_tbl_message_agent m
    WHERE
        m.student_id = $student_id
    ORDER BY
    m.message_id DESC";
        $result = mysqli_query($conn, $query);

        return db_to_array($result);
    }


    public function saveMessagesStudentAgent($_details)
    {
        $conn = $this->_conn;

        // Sanitize and escape input data


        $query = "INSERT INTO ggportal_tbl_message_agent SET
        `message` = '" . mysqli_real_escape_string($conn, $_details['message']) . "'       
        ,`user_type` = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'  
        ,`student_id` = '" . mysqli_real_escape_string($conn, $_details['student_id']) . "'
        ,`agent_id` = '" . mysqli_real_escape_string($conn, $_details['agent_id']) . "'                                                  
       ";
    
    $res = mysqli_query($conn, $query);

    return $res;
    }





    public function getStudentsforAgent($agent_id)
    {
        $conn = $this->_conn;

        //select only his assign students
        $query = "SELECT
        stu.student_id,
        stu.first_name,
        stu.mobile,
        stu.email,
        stu.last_seen,
        stu.student_no,
        m.message AS message,
        (
            SELECT is_read 
            FROM ggportal_tbl_message_agent 
            WHERE message_id = max_message.last_message_id 
              AND user_type = 'ST'
        ) AS is_read  
    FROM
        ggportal_tbl_student stu
    LEFT JOIN (
        SELECT
            student_id,
            MAX(message_id) AS last_message_id
        FROM
            ggportal_tbl_message_agent
        GROUP BY
            student_id
    ) AS max_message ON stu.student_id = max_message.student_id
    LEFT JOIN
        ggportal_tbl_message_agent m ON max_message.last_message_id = m.message_id
    WHERE
        stu.assign_to_agent = $agent_id";

        $result = mysqli_query($conn, $query);
        if (!$result) {
            die('MySQL Error: ' . mysqli_error($conn));
        }

        return db_to_array($result);
    }
    //Student agent chat end


    //Admin and student
    public function getChatMessages($student_id)
    {//student admin chat get from student side

        $query = "
        SELECT c.*, i.first_name AS name 
        FROM ggportal_tbl_message AS c
        INNER JOIN ggportal_tbl_student AS i ON c.user_id = i.student_id 
        WHERE c.user_id = $student_id 
        ORDER BY c.message_id";

        $result = mysqli_query($this->_conn, $query);

        $user_id = $_SESSION['user']['user_id'];
        //update student admin chat is_read 
        if ($_SESSION['user']['user_type'] == "RA") {
            $query_update_is_read = "UPDATE ggportal_tbl_message SET is_read = 'Y' WHERE user_id = $student_id AND user_type ='ST'";
            $result2 = mysqli_query($this->_conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student admin chat is_read from student side
            $query_update_is_read = "UPDATE ggportal_tbl_message SET is_read = 'Y' WHERE user_id = $user_id AND user_type ='RA'";
            $result2 = mysqli_query($this->_conn, $query_update_is_read);
        }

        if (!$result) {
            return [];
        }

        return db_to_array($result);
    }

    
    //function for return chats to student by using student ID and Staff Id
    //Codeaisys#Sithum#Student Side
    public function getStudentStaffChatMessages($student_id,$staff_id)
    {//staff side
        $conn = $this->_conn;
        $query = "
        SELECT ms.*, stu.first_name AS student_name,  stf.first_name As staff_name
        FROM ggportal_tbl_message_staff AS ms
        INNER JOIN ggportal_tbl_student  AS stu ON ms.student_id = stu.student_id 
        INNER JOIN ggportal_tbl_staff  AS stf ON ms.staff_id = stf.staff_id 
        WHERE ms.student_id = $student_id
        ORDER BY ms.message_id";

        $result = mysqli_query($conn, $query);

        //update student staff chat is_read 
        if ($_SESSION['user']['user_type'] == "SF") {

            $query_update_is_read = "UPDATE ggportal_tbl_message_staff SET is_read = 'Y' WHERE student_id = $student_id AND user_type ='ST'";

            $result2 = mysqli_query($conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student staff chat is_read from student side
            $query_update_is_read = "UPDATE ggportal_tbl_message_staff SET is_read = 'Y' WHERE staff_id = $staff_id AND user_type ='SF'";
            $result2 = mysqli_query($conn, $query_update_is_read);
        }
        return db_to_array($result);
    }

    public function getStudentAgentChatMessages($student_id)
    {
        $conn = $this->_conn;
        $query = "
        SELECT ma.*, stu.first_name AS student_name,  ag.first_name As agent_name
        FROM ggportal_tbl_message_agent AS ma
        INNER JOIN ggportal_tbl_student  AS stu ON ma.student_id = stu.student_id 
        INNER JOIN ggportal_tbl_agent  AS ag ON ma.agent_id = ag.agent_id 
        WHERE ma.student_id = $student_id
        ORDER BY ma.message_id
    ";
        $result = mysqli_query($conn, $query);
        
        //update student agent chat is_read 
        if ($_SESSION['user']['user_type'] == "AG") {
            $agent_id = $_SESSION['user']['user_id'];
            $query_update_is_read = "UPDATE ggportal_tbl_message_agent SET is_read = 'Y' WHERE agent_id = $agent_id AND user_type ='St'";
            $result2 = mysqli_query($conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student agent chat is_read from student side
            $query_update_is_read = "UPDATE ggportal_tbl_message_agent SET is_read = 'Y' WHERE student_id = $student_id AND user_type ='AG'";
            $result2 = mysqli_query($conn, $query_update_is_read);
        }
        return db_to_array($result);
    }

    public function initializeMesssege($student_id, $sender_id, $sender_type, $default_messege)
    {
        $default_messege = "Welcome to EDVIOS student portal, if you have anything to clarify please direct use this chat";
        $conn = $this->_conn;        
        
            if ($sender_type == "RA") {
                $query = "INSERT INTO ggportal_tbl_message SET
                message = '$default_messege',
                user_type = '$sender_type',
                user_id = '$student_id',
                is_read = 'N',
                is_status = 'Y'";
    
            $res = mysqli_query($conn, $query);
            return $res;
            


        } else if ($sender_type == "SF") {
            $query = "INSERT INTO ggportal_tbl_message_staff SET
                message = '$default_messege',       
                user_type = '$sender_type',
                student_id = '$student_id',
                staff_id = '$sender_id',
                is_read = 'N'                                                  
               ";

            $res = mysqli_query($conn, $query);
            return $res;

        } else if ($sender_type == "AG") {
             $query = "INSERT INTO ggportal_tbl_message_agent SET
                message = '$default_messege',       
                user_type = '$sender_type',
                student_id = '$student_id',
                agent_id = '$sender_id',
                is_read = 'N'                                                  
               ";
            $res = mysqli_query($conn, $query);
            return $res;

        } else {
            $query = "INSERT INTO ggportal_tbl_message SET
                message = '$default_messege',
                user_type = '$sender_type',
                user_id = '$student_id',
                is_read = 'N',
                is_status = 'Y'";
    
            $res = mysqli_query($conn, $query);
            return $res;
        }
    }
    
    //save messege application vise 
    public function applicationMessagesSave($_details)    
    {
    $conn = $this->_conn;

    $query = "INSERT INTO ggportal_tbl_message_application SET
              `message` = '" . mysqli_real_escape_string($conn, $_details['message']) . "',
              `user_type` = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "',
              `user_id` = '" . mysqli_real_escape_string($conn, $_details['user_id']) . "',
              `student_id` = '" . mysqli_real_escape_string($conn, $_details['student_id']) . "',
              `application_id` = '" . mysqli_real_escape_string($conn, $_details['application_id']) . "',
              `sender_type` = '" . mysqli_real_escape_string($conn, $_details['sender_type']) . "',
              `is_read` = '" . mysqli_real_escape_string($conn, $_details['is_read']) . "';";
              
    $res = mysqli_query($conn, $query);
    return $res;
}
//student  chat get from student side
public function getChatMessagesByApplication($student_id,$application_id)
    {//student admin chat get from student side        
       
        $query = "
        SELECT c.*, i.first_name AS name 
        FROM ggportal_tbl_message_application AS c
        INNER JOIN ggportal_tbl_student AS i ON c.student_id = i.student_id 
        WHERE c.student_id = $student_id and c.application_id = $application_id
        ORDER BY c.message_id";

        $result = mysqli_query($this->_conn, $query);

        $user_id = $_SESSION['user']['user_id'];
        //update student admin chat is_read 
        if ($_SESSION['user']['user_type'] == "RA") {
            $query_update_is_read = "UPDATE ggportal_tbl_message_application  SET is_read = 'Y' WHERE student_id = $student_id AND sender_type ='ST'";
            $result2 = mysqli_query($this->_conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student admin chat is_read from student side
            $query_update_is_read_SF = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE student_id = $user_id AND sender_type ='SF' AND application_id = $application_id";
            mysqli_query($this->_conn, $query_update_is_read_SF);

            $query_update_is_read_AG = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE student_id = $user_id AND sender_type ='AG' AND application_id = $application_id";
            mysqli_query($this->_conn, $query_update_is_read_AG);

            $query_update_is_read_RA = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE student_id = $user_id AND sender_type ='RA' AND application_id = $application_id";
            mysqli_query($this->_conn, $query_update_is_read_RA);
        } 

        if (!$result) {
            return [];
        }

        return db_to_array($result);
    }

    public function getStudentStaffChatMessagesByApplication($staff_id,$application_id,$student_id)
    {//staff side                
        $conn = $this->_conn;
        $query = "
        SELECT ms.*, stu.first_name AS student_name,  stf.first_name As staff_name
        FROM ggportal_tbl_message_application AS ms
        INNER JOIN ggportal_tbl_student  AS stu ON ms.student_id = stu.student_id 
        INNER JOIN ggportal_tbl_staff  AS stf ON ms.user_id = stf.staff_id 
        WHERE ms.user_id = $staff_id and ms.application_id = $application_id
        ORDER BY ms.message_id";

        $result = mysqli_query($conn, $query);

        //update student staff chat is_read 
        if ($_SESSION['user']['user_type'] == "SF") {
            $query_update_is_read = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE student_id = $student_id AND sender_type ='ST'";
            $result2 = mysqli_query($conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student staff chat is_read from student side
           // $query_update_is_read = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE staff_id = $staff_id AND user_type ='SF'";
            // $result2 = mysqli_query($conn, $query_update_is_read);
        }

        if (!$result) {
            return [];
        }
        return db_to_array($result);
    }

    public function getStudentAgentChatMessagesByApplication($student_id,$application_id)
    {//agent side
        $conn = $this->_conn;
        $query = "
        SELECT ma.*, stu.first_name AS student_name,  ag.first_name As agent_name
        FROM ggportal_tbl_message_application AS ma
        INNER JOIN ggportal_tbl_student  AS stu ON ma.student_id = stu.student_id 
        INNER JOIN ggportal_tbl_agent  AS ag ON ma.user_id = ag.agent_id 
        WHERE ma.user_id = $student_id and ma.application_id = $application_id
        ORDER BY ma.message_id
    ";
        $result = mysqli_query($conn, $query);
        
        //update student agent chat is_read 
        if ($_SESSION['user']['user_type'] == "AG") {
            $agent_id = $_SESSION['user']['user_id'];
            $query_update_is_read = "UPDATE ggportal_tbl_message_application SET is_read = 'Y' WHERE user_id = $student_id AND sender_type ='ST'";
            $result2 = mysqli_query($conn, $query_update_is_read);
        } else if ($_SESSION['user']['user_type'] == "ST") {
            //update student agent chat is_read from student side
            // $query_update_is_read = "UPDATE ggportal_tbl_message_agent SET is_read = 'Y' WHERE student_id = $student_id AND user_type ='AG'";
            // $result2 = mysqli_query($conn, $query_update_is_read);
        }
        return db_to_array($result);
    }

    public function getUnreadMsessageCountByApplication($user_type, $user_id, $application_id)
    {
        $conn = $this->_conn;

        if ($user_type == "RA") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message_application
            WHERE sender_type = 'ST' AND user_id = $user_id AND is_read = 'N' AND application_id = $application_id";
        } else if ($user_type == "AG") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message_application
            WHERE sender_type = 'ST' AND user_id = $user_id AND is_read = 'N' AND application_id = $application_id";
        } else if ($user_type == "SF") {
            $query = "SELECT COUNT(*) AS count
            FROM ggportal_tbl_message_application
            WHERE sender_type = 'ST' AND user_id = $user_id AND is_read = 'N' AND application_id = $application_id";
            
        } else if ($user_type == "ST") {            
            $query = "SELECT SUM(CASE WHEN is_read = 'N' THEN 1 ELSE 0 END) AS count
            FROM (
                SELECT is_read FROM ggportal_tbl_message_application WHERE sender_type = 'SF' AND student_id = $user_id AND application_id = $application_id
                UNION ALL
                SELECT is_read FROM ggportal_tbl_message_application WHERE sender_type = 'AG' AND student_id = $user_id AND application_id = $application_id
                UNION ALL
                SELECT is_read FROM ggportal_tbl_message_application WHERE sender_type = 'RA' AND student_id = $user_id  AND application_id = $application_id
            ) AS is_read";
        }

        $result = mysqli_query($conn, $query);

        //return value of count
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            return $row['count'];
        }

        // Assuming db_to_array is a custom function
        // return mysqli_fetch_all($result, MYSQLI_ASSOC);
    }

    

}
