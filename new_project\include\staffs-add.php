<?php
session_start();
//print_r($_SESSION);
require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$access_available_for = "RA";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$isEditing = isset($_GET['program_id']);
$isEditingName = isset($_GET['program_id']);

$buttonText = $isEditing ? "Edit" : "Add New";

$pageTitle = $isEditingName ? "Edit Application" : "Add a New Program";
$buttonText = $isEditingName ? "Update" : "Add New";

$cLoadCombo = new LoadCombo();
$cProgram = new Program();

$program_id = 0;
$country_id = 0;
$currency_id = 0;
$course_id = 0;
$institute_id = 0;
$program_name = '';
$commission = '';
$tution_fee = '';
$application_fee = '';
$duration = '';
$intake = '';
$intake2 = '';
$intake3 = '';
$intake4 = '';
$ets = '';
$requirements = '';
$program_web_url = '';
$deadline = '';
$deadline2 = '';
$deadline3 = '';
$deadline4 = '';
$city = '';
$english_requirements = '';

if ($deadline2 == '1970-01-01') {
    $deadline2 = '0000-00-00';
}

$course_type = '';

$id_pass = filter_input(INPUT_GET, 'program_id', FILTER_VALIDATE_INT);

if ($id_pass != 0) {
    $details_list = $cProgram->getProgramByID($id_pass);
    if (!empty($details_list)) {
        extract($details_list[0]);
        //echo $valid_date;
    }
}

$country_combo_dts = array(
    'id_match' => ($country_id > 0 ? $country_id : 0)
);
$institute_combo_dts = array(
    'id_match' => ($institute_id > 0 ? $institute_id : 0)
);
$course_combo_dts = array(
    'id_match' => ($course_id > 0 ? $course_id : 0)
);
$currency_combo_dts = array(
    'id_match' => ($currency_id > 0 ? $currency_id : 0)
);

$currency_combo = $cLoadCombo->getCurrencyCombo($currency_combo_dts);
$institute_combo = $cLoadCombo->getInstituteCombo($institute_combo_dts);
$course_combo = $cLoadCombo->getCourseCombo($course_combo_dts);
$country_combo = $cLoadCombo->getCountryCombo($country_combo_dts);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Edvios</title>

    <?php include_once('include/css_include.php') ?>
    <link rel="stylesheet" href="plugins/daterangepicker/daterangepicker.css">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- New Styles -->
    <link rel="stylesheet" href="./dist/css/new-styles.css">
</head>

<body class="sidebar-mini pace-flash-teal pace-center-atom-teal  sidebar-collapse">
    <!-- Site wrapper -->
    <div class="wrapper">
        <?php include_once('include/navbar.php') ?>
        <?php include_once('include/sidebar.php') ?>



        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            <!-- Content Header (Page header) -->
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="program-list.php">Program List</a></li>
                            </ol>
                        </div>
                    </div>
                </div><!-- /.container-fluid -->
            </section>

            <!-- Main content -->
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8 programForm">
                    <div class="text-center">
    <h2 class="display-4 newCardTitle"><b><?php echo $pageTitle; ?></b></h2>
</div>
                        <form id="quickform" role="form" novalidate="novalidate" data-parsley-validate name="quickform"
                            method="post" action="" enctype="multipart/form-data">
                            <input type="hidden" name="program_id" value="<?= $program_id ?>" />
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Program Name<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="program_name" value="<?= $program_name ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Course<span style="color:red;">*</span></label>
                                                <?= $course_combo ?>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>University<span style="color:red;">*</span></label>
                                                <?= $institute_combo ?>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Duration<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="duration" value="<?= $duration ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Country<span style="color:red;">*</span></label>
                                                <?= $country_combo ?>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>City<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="city" value="<?= $city ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Currency<span style="color:red;">*</span></label>
                                                <?= $currency_combo ?>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Commission<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="commission" value="<?= $commission ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Tution Fee<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="tution_fee" value="<?= $tution_fee ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Application Fee</label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="application_fee" value="<?= $application_fee ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>ETS<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent" name="ets"
                                                    value="<?= $ets ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Web Url<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="program_web_url" value="<?= $program_web_url ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Course Type<span style="color:red;">*</span></label>
                                                <select class="form-control" name="course_type" required>
                                                    <option value="select">----Select One----</option>
                                                    <option value="Foundation"
                                                        <?= ($course_type === 'Foundation') ? 'selected' : '' ?>>
                                                        Foundation</option>
                                                    <option value="Undergraduate"
                                                        <?= ($course_type === 'Undergraduate') ? 'selected' : '' ?>>
                                                        Undergraduate</option>
                                                    <option value="Postgraduate"
                                                        <?= ($course_type === 'Postgraduate') ? 'selected' : '' ?>>
                                                        Postgraduate</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-sm-6"></div>
                                        <!-- <div class="row container-fluid" id="div_intake"> -->
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Intake<span style="color:red;">*</span></label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="intake" value="<?= $intake ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Deadline for Registration<span
                                                        style="color:red;">*</span></label>
                                                <input type="date" class="form-control newFormControlStudent"
                                                    name="deadline"
                                                    value="<?= ($deadline === '1970-01-01') ? '' : $deadline ?>"
                                                    min="<?= date('Y-m-d') ?>">
                                            </div>
                                        </div>
                                        <!-- </div> -->
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Intake Two (Optional)</label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="intake2" value="<?= $intake2 ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Deadline Two for Registration</label>
                                                <input type="date" class="form-control newFormControlStudent"
                                                    name="deadline2"
                                                    value="<?= ($deadline2 === '1970-01-01') ? '' : $deadline2 ?>"
                                                    min="<?= date('Y-m-d') ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Intake Three (Optional)</label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="intake3" value="<?= $intake3 ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Deadline Three for Registration</label>
                                                <input type="date" class="form-control newFormControlStudent"
                                                    name="deadline3"
                                                    value="<?= ($deadline3 === '1970-01-01') ? '' : $deadline3 ?>"
                                                    min="<?= date('Y-m-d') ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Intake Four (Optional)</label>
                                                <input type="text" class="form-control newFormControlStudent"
                                                    name="intake4" value="<?= $intake4 ?>">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Deadline Four for Registration</label>
                                                <input type="date" class="form-control newFormControlStudent"
                                                    name="deadline4"
                                                    value="<?= ($deadline4 === '1970-01-01') ? '' : $deadline4 ?>"
                                                    min="<?= date('Y-m-d') ?>">
                                            </div>
                                        </div>

                                        <!-- <div class="col-sm-6">
                                        <a class="btn btn-sm btn-success mt-2 mb-2 font-weight-bolder add_intake">Add more +</a>
                                    </div> -->
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Academic Requirements<span style="color:red;">*</span></label>
                                                <textarea class="form-control newFormControlStudent" name="requirements"
                                                    style="height: 150px;"><?= $requirements ?></textarea>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>English Requirements<span style="color:red;">*</span></label>
                                                <textarea class="form-control newFormControlStudent"
                                                    name="english_requirements"
                                                    style="height: 150px;"><?= $english_requirements ?></textarea>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div>
                            <button type="submit" class="btn btn-block bg-gradient-primary btn-sm mx-auto w-50 rounded-pill">
        <?php echo $buttonText; ?>
    </button>
                            </div>
                            <div>
                                <button type="reset" class="btn btn-block bg-transparent btn-sm">Reset</button>
                            </div>


                        </form>
                    </div>
                </div>
            </div>


            <section class="content">

                <!-- Default box -->
                <div class="card">


                </div>
                <!-- /.card -->
            </section>
            <!-- /.content -->
        </div>
        <!-- /.content-wrapper -->

        <?php include_once('include/footer.php') ?>

        <!-- Control Sidebar -->
        <aside class="control-sidebar control-sidebar-dark">
            <!-- Control sidebar content goes here -->
        </aside>
        <!-- /.control-sidebar -->
    </div>
    <!-- ./wrapper -->

    <!-- jQuery -->
    <script src="plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="plugins/select2/js/select2.full.min.js"></script>
    <!-- AdminLTE App -->
    <!-- InputMask -->
    <script src="plugins/moment/moment.min.js"></script>
    <script src="plugins/inputmask/jquery.inputmask.min.js"></script>
    <script src="plugins/daterangepicker/daterangepicker.js"></script>
    <script src="dist/js/adminlte.min.js"></script>
    <!-- jquery-validation -->
    <script src="plugins/jquery-validation/jquery.validate.min.js"></script>
    <script src="plugins/jquery-validation/additional-methods.min.js"></script>
    <!-- bs-custom-file-input -->
    <script src="plugins/bs-custom-file-input/bs-custom-file-input.min.js"></script>
    <!-- Page specific script -->

    <!-- pace-progress -->

    <script src="plugins/sweetalert2/sweetalert2.min.js"></script>
    <!-- Toastr -->
    <script src="plugins/toastr/toastr.min.js"></script>
    <script src="plugins/pace-progress/pace.min.js"></script>
    <script type="text/javascript" src="./custom/js/program-add-file.js"></script>
</body>

</html>