$(document).ready(function () {
  //console.log("ready!");
  bsCustomFileInput.init();
  //Initialize Select2 Elements
  $(".select2bs4").select2({
    theme: "bootstrap4",
  });

  /*Save program*/
  $("#quickform").validate({
    rules: {
      program_name: {
        required: true,
      },
      program_type: {
        required: true,
      },
      country_id: {
        required: true,
      },
      state_id: {
        required: true,
      },
      currency_id: {
        required: true,
      },
      city: {
        required: true,
      },
      course_id: {
        required: true,
      },
      institute_id: {
        required: true,
      },
      duration: {
        required: true,
      },
      commission: {
        required: true,
      },
      tution_fee: {
        required: true,
      },
      ets: {
        required: true,
      },
      program_web_url: {
        required: true,
      },
      course_type: {
        required: true,
      },
      intake: {
        required: true,
      },
      deadline: {
        required: true,
      },
      requirements: {
        required: true,
      },
      english_requirements: {
        required: true,
      },
    },
    messages: {
      program_name: {
        required: "Please Enter a value",
      },
      course_id: {
        required: "Please Select One",
      },
      institute_id: {
        required: "Please Select One",
      },
      duration: {
        required: "Please Enter a value",
      },
      commission: {
        required: "Please Enter a value",
      },
      tution_fee: {
        required: "Please Enter a value",
      },
      course_type: {
        required: "Please Enter a value",
      },
      intake: {
        required: "Please Enter a value",
      },
      deadline: {
        required: "Please Enter a value",
      },
      requirements: {
        required: "Please Enter a value",
      },
      english_requirements: {
        required: "Please Enter a value",
      },
      program_web_url: {
        required: "Please Enter a value",
      },
      ets: {
        required: "Please Enter a value",
      },
      program_type: {
        required: "Please Enter a value",
      },
      city: {
        required: "Please Enter a value",
      },
      country_id: {
        required: "Please Select One",
      },
      currency_id: {
        required: "Please Select One",
      },
      state_id: {
        required: "Please Select One",
      },
    },
    errorElement: "span",
    errorPlacement: function (error, element) {
      error.addClass("invalid-feedback");
      element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
      $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
      //console.log(new FormData(form));
      var formData = new FormData(form);
      formData.append("programSave", "1");
      $.ajax({
        type: "POST",
        url: "controller/program-controller.php",
        data: formData,
        dataType: "json",
        mimeType: "multipart/form-data",
        contentType: false,
        processData: false,
        cache: false,
        success: function (data) {
          console.log(data);
          if (data.data > 0) {
            toastr.success("Saved Successfully");
            window.location.href = "program-list.php";
            $("#form").trigger("reset");
            //   window.location.href = window.location.pathname + '?savedSuccess=1';
          } else {
            toastr.error(data.status);
          }
        },
      });
    },
  });

  /* Add intake */
//   $(".add_intake").on("click", function () {
//     var currentDate = new Date().toISOString().split("T")[0];
//     $("#div_intake").append(
//       '<div class="col-sm-6">' +
//         '<div class="form-group">' +
//         '<label>Intake<span style="color:red;">*</span></label>' +
//         '<input type="text" class="form-control" name="intake">' +
//         "</div>" +
//         "</div>" +
//         '<div class="col-sm-6">' +
//         '<div class="form-group">' +
//         '<label>Deadline for Registration<span style="color:red;">*</span></label>' +
//         '<input type="date" class="form-control" name="deadline" min="' +
//         currentDate +
//         '">' +
//         "</div>" +
//         "</div>"
//     );
//   });
});
