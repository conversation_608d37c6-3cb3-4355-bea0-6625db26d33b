<?php

session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';
include '../form-submit-email.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
  //will automatically redirect to login page
  die("No Access");
}

$cStaff = new Staff();
$cStudent = new Student();


// assign student to staff
if (isset($_POST['staffAssignSave'])) {

  $staff_id = filter_input(INPUT_POST, 'staff_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $staff_type= $cStaff->getStaffByID($staff_id)[0]['user_type'];


  $result = $cStudent->updateStudentStaff($student_id, $staff_id, $staff_type);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}

if (isset($_POST['staffAssignDelete'])) {

  $student_id = filter_input(INPUT_POST, 'student_id');

  $result = $cStudent->deleteStudentStaff($student_id);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
  );
  echo json_encode($json);
  die();

}

// get Staff Details
if (isset($_POST['getStaffDetails'])) {

  $staff_id = filter_input(INPUT_POST, 'staff_id');


  $result = $cStaff->getStaffByID($staff_id);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
  );
  echo json_encode($json);
  die();
}


// save Staff
if (isset($_POST['staffSave'])) {

  $staff_id = filter_input(INPUT_POST, 'staff_id');
  $first_name = filter_input(INPUT_POST, 'first_name');
  $last_name = filter_input(INPUT_POST, 'last_name');
  $password = filter_input(INPUT_POST, 'password');
  $profile_picture = filter_input(INPUT_POST, 'profile_picture');
  $email = filter_input(INPUT_POST, 'email');
  $mobile = filter_input(INPUT_POST, 'mobile');
  $parent_user_id = filter_input(INPUT_POST, 'parent_user_id');
  $parent_user_type = filter_input(INPUT_POST, 'parent_user_type');
  $country_id = filter_input(INPUT_POST, 'country_id');
  $user_type = "SF";
  $email_validate_yn = filter_input(INPUT_POST, 'email_validate_yn');

  if ($parent_user_type == 'AG') {
    $user_privilege_id = 8;
  } else {
    $user_privilege_id = filter_input(INPUT_POST, 'staff_privilege_id');
  }


  //check email already exist
  if ($staff_id > 0) {
    $staff_deatils = $cStaff->getStaffByID($staff_id);

    if ($staff_deatils[0]['email'] != $email) {
      $check_email = $cStaff->checkEmail($email, $staff_id);
      if (count($check_email) > 0) {
        $json = array(
          "data" => 0, "status" => "Email already exist"
        );
        echo json_encode($json);
        die();
      }
    }
  } else {
    $res_email = $cStaff->getLogin($email);
    if (count($res_email)) {
      $json = array(
        "data" => 0, "status" => 'Email already exist'
      );
      echo json_encode($json);
      die();
    }
  }



  //profile picture upload
  if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['name'] != "") {
    $file_name = $_FILES['profile_picture']['name'];
    $file_size = $_FILES['profile_picture']['size'];
    $file_tmp = $_FILES['profile_picture']['tmp_name'];
    $file_type = $_FILES['profile_picture']['type'];

    $exp = explode('.', $_FILES['profile_picture']['name']);
    $file_ext = strtolower(end($exp));

    // timestamp
    $time = time();

    //remove a file already exists
    if ($profile_picture != "") {
      if (file_exists("../" . $profile_picture)) {
        unlink("../" . $profile_picture);
      }
    }

    //upload the file
    if (move_uploaded_file($file_tmp, "../dist/uploads/staff/" . $time . '.' . $file_ext)) {
      $profile_picture = "dist/uploads/staff/" . $time . '.' . $file_ext;
    }
  }


  if ($profile_picture == "") {
    $profile_picture = 'dist/img/user2-160x160.jpg';
  }

  //if is empty then generate 8 digit passwod
  if (empty($password)) {
    $password1 = substr(rand(1, 99999999), 0, 8);
    //encrypt password
    $password = convert_string('encrypt', $password1);
  } else {
    $old = $cStaff->getStaffByID($staff_id);
    $oldPwd = $old[0]['password'];
    if ($oldPwd != $password) {
      $pwdChanged = 1;
      $password1 = $password;
      $password = convert_string('encrypt', $password);
    } else {
      $pwdChanged = 0;
      $password = $oldPwd;
    }
  }

  //add to array
  $criteria = array(
    'staff_id' => $staff_id,
    'first_name' => $first_name,
    'last_name' => $last_name,
    'username' => $email,
    'email' => $email,
    'mobile' => $mobile,
    'category' => '',
    'gender' => '',
    'date_of_birth' => '2000-02-02',
    'marital_status' => '',
    'country_id' => $country_id,
    'state' => '0',
    'city' => '0',
    'password' => $password,
    'password_salt' => '',
    'user_type' => $user_type,
    'user_privilege_id' => $user_privilege_id,
    'profile_picture' => $profile_picture,
    'email_validate_yn' => $email_validate_yn,
    'user_active_yn' => 'Y',
    'created_by' => $user_id,
    'updated_by' => $user_id,
    'parent_user_type' => $parent_user_type,
    'parent_user_id' => $parent_user_id
  );




  $result = $cStaff->saveStaff($criteria);

  if ($result > 0) {
    if ($staff_id < 1  || $pwdChanged == 1) {
      $token_id = uniqid();
      $address = $email;
      $subject = 'Activate your account';

      $body = '<!DOCTYPE html>
        <html>
        
        <head>
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
            <style>
                .header {
                    background-color: #293C4A;
                    padding: 20px;
                    font-size: 25px;
                    text-align: center;
                    font-weight: bolder;
                }
        
                .header h2 {
                    color: #fff;
                    margin-bottom: 0px;
                }
        
         
        
                .header p {
                    padding-top: 0px;
                    margin-top: 0px;
                    color: #fff;
                    font-size: 16px;
                }
        
            
            </style>
        </head>
        
        <body>
        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
        <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
          <tbody><tr>
            <td align="center" valign="top">
              <table width="650" border="0" cellspacing="0" cellpadding="0">
                <tbody><tr>
                  <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                    
                      <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                      
                          <tbody><tr>
                              <td class="header">
                                  <h2 style="font-weight: 700;">Edvios</h2>
                                  <p style="padding-top: 30px;">The Staff Portal</p>
                              </td>
                          </tr>
                      </tbody></table>
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tbody><tr>
                        <td>
                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                            <tbody><tr>
                              <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                  <tbody><tr>
                                    <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                      Hi ' . $last_name . '</td>
                                  </tr>
                                  
                                </tbody></table>
                              </td>
                            </tr>
                          </tbody></table>
                        </td>
                      </tr>
                    </tbody></table>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody><tr>
                                  <td>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                      <tbody><tr>
                                        <td style="padding:0px 20px">
                                          <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                            <tbody><tr height="5px">
                                              <td width="100%">
                                                  &nbsp;                              
                                              </td>
                                            </tr>
                                            
                                            <tr height="30px">
                                               <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                               <p> You have been registered to the Edvios by a system admin. Please click on the link below to verify your account.</p> 
                                                <p> If you do not need to register, please ignore this email. .</p>                               
                                              </td>                             
                                            </tr>
    
                                            <tr height="30px">
                                               <td width="100%" style="color:#000000;text-align:left;font-weight:600;font-size:12px;line-height:20px">                             
                                                 <p>Use below credentials to login to your account</p>
                                                 <p>Username : ' . $email . ' <br> 
                                                   Password : ' . $password1 . '
                                                 </p>                             
                                              </td>                             
                                            </tr>
    
    
                                            <tr height="25px">
                                               <td width="100%" style="color:#000000;text-align:left;font-weight:700;font-size:14px;line-height:20px">                             
                                               <a class="primary-btn" href="' . $base_url . 'staff-activate-account.php?token_id=' . $token_id . '&st=' . $result . '">Verify Account</a>                            
                                              </td>                             
                                            </tr>
        
                                            <tr>
                                              <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                                <p>Thank you.<br> Kind Regards,<br> Edvios</p>
                                              </td>                             
                                            </tr> 
                                                <tr height="15px">
                                                    <td width="100%">&nbsp;</td>
                                                </tr>
                                                                 
                                                                       
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                                  </td>
                                </tr>
                              </tbody></table>
        
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tbody><tr>
                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                          <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody><tr>
                              <td align="center" style="padding-bottom:10px">
                                <table border="0" cellspacing="0" cellpadding="0">
                                  <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                  <tr>
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                    
                                    <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                      <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                  </tr>							
                                </tbody></table>
                              </td>
                            </tr>
                              
                          
                          </tbody></table>
                          
                        </td>
                      </tr>
                    </tbody></table>
                    
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                      <tbody><tr>
                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                          <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                              <tbody><tr>
                              <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                  Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                            </tr>
                           
                          
                          </tbody></table>
                          
                        </td>
                      </tr>
                    </tbody></table>
                    
                  </td>
                </tr>
              </tbody></table>
            </td>
          </tr>
        </tbody></table>
        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
        
        </body>
        
        </html>';

      send_mail($subject, $address, $body);
    }
  }

  $json = array(
    "data" => $result, "status" => $result
  );
  echo json_encode($json);
  die();
}

// delete Staff and remove assignment
if (isset($_POST['deleteStaff'])) {

  $staff_id = filter_input(INPUT_POST, 'staff_id');

  $result = $cStaff->deleteStaff($staff_id);
  
  if ($result > 0) {
    $cStaff->removeAssignment($staff_id);
  }

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}

if (isset($_POST['action']) && $_POST['action'] === 'getLastSeen') {

  $staff_id = filter_input(INPUT_POST, 'staff_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $user_type = filter_input(INPUT_POST, 'user_type');
  $result = null;
  $last_seen = null;
  $last_seen_status = null;
  $first_name = null;


  if ($user_type === "ST") {
    $result = $cStaff->calStaffOnlineStatus($staff_id, $student_id);
    $first_name = $result['first_name'];
    $last_seen = $result['last_seen'];
    $last_seen_status = last_seen($last_seen);
  } else if ($user_type === "SF") {
    $result = $cStudent->calStudentOnlineStatus($student_id, $staff_id);
    $first_name = $result['first_name'];
    $last_seen = $result['last_seen'];
    $last_seen_status = last_seen($last_seen);
  }



  $response = array(
    'first_name' => $first_name,
    'last_seen_status' => $last_seen_status,
    'last_seen' => $last_seen
  );

  echo json_encode($response);
  exit;
}
