<?php

class Sms
{

    public function send_sms($message, $phone_number)
    {
        
        if (substr($phone_number, 0, 5) == '(+94)') {
            
            //(+94)719997732
            preg_match('/\(\+(\d+)\)(\d+)/', $phone_number, $matches);
            $dial_code = '+' . $matches[1]; //(+94)
            $phone_number = $matches[2]; //719997732
            $phone_number = '0' . $phone_number; //0719997732
            
            // $phone_number = str_replace(' ', '', $phone_number);        
            // if (substr($phone_number, 0, 3) != '+94') {                
            //   $phone_number = '+94' . $phone_number;
            // } else {          
        
            //   $phone_number = str_replace("+94", "0", $phone_number);
            
            // }

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.smshub.lk/api/v1/send/single',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode(array(
                    "message" => $message,
                    "phoneNumber" => $phone_number
                )),
                CURLOPT_HTTPHEADER => array(
                    'Authorization: bMOQJncTR2UqmHbEKq6bK250MMUoKfNqI16Nhi2dVqiYU7b6vtn7FKV2vCazqMm8kQHrnhV4PJf8PNfim93XlinYQFvf86JO3x6qqHlkQlI6Njca862NKaUu5tAZaPA8JgCCePYx2qlTpnJRFfkdl1jmx7yRaGHgtnKsks7DfeqYxLJG1jZiLBjFDIv2NiIXs1itMiWHoV3nVBOTmGcK88TFfXyTtWx0lbldw7LtmKMoJDVTZ6SCwnLl7mB3rVfnwTi0cH9WxJkV8qgSWL6UTF5Smk2DvAJZjdCsULKif6iDRqHaFDLuy8HHxLaPMuOlM3qYlFUmzOfeXTkMFFkAD18s0BJSiUH4YxIbB7js39ptpzgKMEmc9SiksFs8VZrzDSZl94yY1cDndpIrV0QkSXi9llOpE0Y5Vh54IvtqOmdLPxLwVcLZHuh5667qzj1Zel9Ox857Aq8xii4cjFs6A29a5OWkWaj6CkfrG03ya0I0LY0M7VwDFPIK6Q6uodzzBY6wdv9fpD9yGcQDhXROSO8lY6kIJu1FCP4qSwySU2KBr2D9yQObCkyrnXtlbTdNculktP0IHliZF3PizeuwDOcxYWPlygsX5AP3eiFCnLOAnhIW674bGh2GqJEeGYFQk3T2Z8ygGH3ryUoXtBrla1ve1qLlqIx2dEauCiC9Be9nWBWmFvMYkTCwc9DWE7LxcsYaEEn748r8UxFqmg4fPaI1Ve7hFxEOstoxsqDDaBfkuXotN5XGPtBGgcDxMFpGrge5NxrPujGsEuIh1d81YtvSHxFij4cEvXi18n8PbDVSxJ2nAPhMz6QMm3KFrJ3gQCLM4xLhfcjTpVuHd1tpi5HUp6y2OdKIpKW5R1HkxmTYEb3Cs4cuBDxLuQcV8AqsLZyumuet6vEyH3TZ4rwSNvCqaoqghzfrUYhqSE2IrZP9tOF5Liz5F6VjjSZJlkB7Z8ss5S2a8HbI2gCL3fvS6njDOP81Z0SkCxUb4xiVV8GEtV00nwe8YoujcrTCf78a',
                    'Content-Type: application/json'
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }else{
            // Invalid Mobile
            return "Invalid Mobile";
        }
    }

   
}
