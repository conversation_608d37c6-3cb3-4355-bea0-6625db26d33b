<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cStudent = new Student();

// $json = array(
//    "success" =>true
//    ,"data" =>$_POST
//    ,"datae" =>$_FILES
   
// );
// echo json_encode($json);
// die();


// delete Student Document
if(isset($_POST['deleteStudentDocument'])){

    $student_document_id = filter_input(INPUT_POST,'student_document_id');
    $doc_url = filter_input(INPUT_POST,'doc_url');
    
    if($doc_url != ""){
        if(file_exists("../".$doc_url)) {
            unlink("../".$doc_url);
        }
    }

    $result = $cStudent-> deleteStudentDocument($student_document_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Delete')
    );
    echo json_encode($json);
    die();
}

// save Student Document
if(isset($_POST['action']) &&  $_POST['action'] == 'FileUploader'){

    $doc_url = '';
    $student_id = filter_input(INPUT_POST, 'student_id');
    $doc_name = filter_input(INPUT_POST, 'file_name');

    if(isset($_FILES['_files']) && $_FILES['_files']['name'] != "") {
        $file_name = $_FILES['_files']['name'];
        $file_size = $_FILES['_files']['size'];
        $file_tmp = $_FILES['_files']['tmp_name'];
        $file_type = $_FILES['_files']['type'];
        
        $exp = explode('.', $_FILES['_files']['name']);
        $file_ext = strtolower(end($exp));
    }
    if(isset($_FILES['_files2']) && $_FILES['_files2']['name'] != "") {
        $file_name = $_FILES['_files2']['name'];
        $file_size = $_FILES['_files2']['size'];
        $file_tmp = $_FILES['_files2']['tmp_name'];
        $file_type = $_FILES['_files2']['type'];
        
        $exp = explode('.', $_FILES['_files2']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files3']) && $_FILES['_files3']['name'] != "") {
        $file_name = $_FILES['_files3']['name'];
        $file_size = $_FILES['_files3']['size'];
        $file_tmp = $_FILES['_files3']['tmp_name'];
        $file_type = $_FILES['_files3']['type'];
        
        $exp = explode('.', $_FILES['_files3']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files4']) && $_FILES['_files4']['name'] != "") {
        $file_name = $_FILES['_files4']['name'];
        $file_size = $_FILES['_files4']['size'];
        $file_tmp = $_FILES['_files4']['tmp_name'];
        $file_type = $_FILES['_files4']['type'];
        
        $exp = explode('.', $_FILES['_files4']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files5']) && $_FILES['_files5']['name'] != "") {
        $file_name = $_FILES['_files5']['name'];
        $file_size = $_FILES['_files5']['size'];
        $file_tmp = $_FILES['_files5']['tmp_name'];
        $file_type = $_FILES['_files5']['type'];
        
        $exp = explode('.', $_FILES['_files5']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files6']) && $_FILES['_files6']['name'] != "") {
        $file_name = $_FILES['_files6']['name'];
        $file_size = $_FILES['_files6']['size'];
        $file_tmp = $_FILES['_files6']['tmp_name'];
        $file_type = $_FILES['_files6']['type'];
        
        $exp = explode('.', $_FILES['_files6']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files7']) && $_FILES['_files7']['name'] != "") {
        $file_name = $_FILES['_files7']['name'];
        $file_size = $_FILES['_files7']['size'];
        $file_tmp = $_FILES['_files7']['tmp_name'];
        $file_type = $_FILES['_files7']['type'];
        
        $exp = explode('.', $_FILES['_files7']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files8']) && $_FILES['_files8']['name'] != "") {
        $file_name = $_FILES['_files8']['name'];
        $file_size = $_FILES['_files8']['size'];
        $file_tmp = $_FILES['_files8']['tmp_name'];
        $file_type = $_FILES['_files8']['type'];
        
        $exp = explode('.', $_FILES['_files8']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files9']) && $_FILES['_files9']['name'] != "") {
        $file_name = $_FILES['_files9']['name'];
        $file_size = $_FILES['_files9']['size'];
        $file_tmp = $_FILES['_files9']['tmp_name'];
        $file_type = $_FILES['_files9']['type'];
        
        $exp = explode('.', $_FILES['_files9']['name']);
        $file_ext = strtolower(end($exp));
    }else if(isset($_FILES['_files10']) && $_FILES['_files10']['name'] != "") {
        $file_name = $_FILES['_files10']['name'];
        $file_size = $_FILES['_files10']['size'];
        $file_tmp = $_FILES['_files10']['tmp_name'];
        $file_type = $_FILES['_files10']['type'];
        
        $exp = explode('.', $_FILES['_files10']['name']);
        $file_ext = strtolower(end($exp));
    }
    else{
        $json = array(
            "success" => false,
            "error" => "No File to Uploaded",
            "errorcode" => "785846",
        );
    }
        // timestamp
        $time = time();
        
        //remove a file already exists
        if($doc_url != ""){
            if(file_exists("../".$doc_url)) {
                unlink("../".$doc_url);
            }
        }
        //upload the file
        if( move_uploaded_file($file_tmp,"../dist/uploads/documents/".$time."-".$file_name)){
            $doc_url = "dist/uploads/documents/".$time."-".$file_name;
            $json = array(
                "success" =>true
                ,"data" =>$doc_url
             );

        }else{
            $json = array(
                "success" => false,
                "error" => "Not Uploaded",
                "errorcode" => "785846",
            );

        }

    //add to array 
    // $criteria = array(
    //     'student_document_id' => 0,
    //     'student_id' => $student_id,
    //     'doc_url' => $doc_url,
    //     'file_name' => $doc_name
    // );

    // $result = $cStudent->saveStudentDocument($criteria);
    

    // if($result>0){
   
    //     $json = array(
    //         "success" =>true
    //         ,"data" =>$result
    //      );
    // }else{
    //     $json = array(
    //         "success" => false,
    //         "error" => "Not Uploaded",
	//         "errorcode" => "785846",
    //         "data" =>$result
    //     );
    // }

    echo json_encode($json);
    die();
}