<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cRemainder = new Remainder();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

// get Remainder  Details
if(isset($_POST['getRemainderDetails'])){
    
    $remainder_id = filter_input(INPUT_POST, 'remainder_id');
    
    
    $result = $cRemainder->getRemainderByID($remainder_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// save Remainder
if(isset($_POST['remainderSave'])){

    $remainder_id = filter_input(INPUT_POST, 'remainder_id');
    $title = filter_input(INPUT_POST, 'title');
    $remainder_date = filter_input(INPUT_POST, 'remainder_date');
    $active_yn = filter_input(INPUT_POST, 'active_yn');
    $note = filter_input(INPUT_POST, 'note');

    // format date mysql datetime format
    $remainder_date = date('Y-m-d H:i:s', strtotime($remainder_date));

//     $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();
    //add to array 
    $criteria = array(
        'remainder_id' => $remainder_id,
        'user_id' => $user_id,
        'title' => $title,
        'user_type' => $user_type,
        'remainder_date' => $remainder_date,
        'note' => $note,
        'active_yn' => $active_yn
        
    );

    $result = $cRemainder->saveRemainder($criteria);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}


// delete Remainder
if(isset($_POST['deleteRemainder'])){

    $remainder_id = filter_input(INPUT_POST,'remainder_id');
    
    $result = $cRemainder-> deleteRemainder($remainder_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//update Remainder
if(isset($_POST['updateRemainder'])){

    $remainder_id = filter_input(INPUT_POST,'remainder_id');
    $active_yn = filter_input(INPUT_POST,'active_yn');
    // $active_yn = 'N';
    
    $criteria = array(
        'remainder_id' => $remainder_id,
        'active_yn' => $active_yn
    );
    
    $result = $cRemainder->updateRemainder($criteria);
    
    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//get reminders
if(isset($_POST['getAllremainders'])){
    
    // $remainder_id = filter_input(INPUT_POST, 'remainder_id');
    
    $criteria = array(
        'user_id' => $user_id,
        'user_type' => $user_type,
        'active_yn' => 'Y'
    );
    
    $result = $cRemainder->getRemainders($criteria);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}