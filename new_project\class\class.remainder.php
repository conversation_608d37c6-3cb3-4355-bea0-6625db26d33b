<?php

include_once 'connection.php';

class Remainder extends connection {

    public function __construct() {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Remainder() {
        self::__construct();
    }

    private $_conn;


    function getRemainderByID($remainder_id) {
        $conn = $this->_conn;
        $remainder_id = intval($remainder_id);
        if($remainder_id>0){
            $cond="and c.remainder_id=$remainder_id ";
        }else{
            $cond=" and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_remainder c
                    WHERE 1=1 $cond;";
//        echo $query;
//        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    function getRemainders($criteria) {
        $conn = $this->_conn;
        $cond = "";


        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond .= " and s.user_type = '$user_type' ";
        }

        if (isset($criteria['active_yn']) && strlen($criteria['active_yn']) > 0) {
            $active_yn = mysqli_real_escape_string($conn, $criteria['active_yn']);
            $cond .= " and s.active_yn = '$active_yn' ";
        }

        if (isset($criteria['today_date']) && strlen($criteria['today_date']) > 0) {
            $today_date = mysqli_real_escape_string($conn, $criteria['today_date']);
            $cond .= " and s.remainder_date <= '$today_date' ";
        }


        if (isset($criteria['user_id']) && strlen($criteria['user_id']) > 0 ) {
            $user_id = intval( $criteria['user_id']);
            $cond .= " and s.user_id  = $user_id ";
        }

        $query = "select * from ggportal_tbl_remainder s
         where 1=1  $cond order by s.remainder_id DESC;";
    //    echo $query;
//        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getCommissions($criteria) {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['customer_id']) && strlen($criteria['customer_id']) > 0 ) {
            $customer_id = intval( $criteria['customer_id']);
            $cond .= " and c.customer_id  = $customer_id ";
        }

        $query = "select * from ggportal_tbl_remainder 
         where 1=1  $cond ";
    //    echo $query;
//        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function saveRemainder($_details) {
        $conn = $this->_conn;
        $remainder_id = intval($_details['remainder_id']);
      
     
        if ($remainder_id == 0) {
            
            $query = "INSERT INTO ggportal_tbl_remainder SET
                  title = '" . mysqli_real_escape_string($conn, $_details['title']) . "'               
                    ,user_id = " . mysqli_real_escape_string($conn, $_details['user_id']) . "          
                    ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                    ,remainder_date = '" . mysqli_real_escape_string($conn, $_details['remainder_date']) . "'  
                    ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'     
                    ,note = '" . mysqli_real_escape_string($conn, $_details['note']) . "'            
               ;";
        } else {
            //update
            $query="update ggportal_tbl_remainder SET     
            title = '" . mysqli_real_escape_string($conn, $_details['title']) . "'               
            ,user_id = " . mysqli_real_escape_string($conn, $_details['user_id']) . "          
            ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
            ,remainder_date = '" . mysqli_real_escape_string($conn, $_details['remainder_date']) . "' 
            ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'  
            ,note = '" . mysqli_real_escape_string($conn, $_details['note']) . "'  
            WHERE remainder_id = $remainder_id;        
            ";
           
        }
        
        // return $query;
        // die();
       
        $res = mysqli_query($conn, $query);
        
        if ($res) {
            if ($remainder_id == 0) {
                $remainder_id = mysqli_insert_id($conn);
            }
            return $remainder_id;

        } else {
            return mysqli_error($conn);
        }
    }

    //update remainder
    function updateRemainder($_details) {
        $conn = $this->_conn;
        $remainder_id = intval($_details['remainder_id']);
        $query = "update ggportal_tbl_remainder SET      
        active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'  
            WHERE remainder_id = $remainder_id;        
            ";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $remainder_id;
        } else {
            return mysqli_error($conn);
        }
    }


    function deleteRemainder($remainder_id) {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_remainder` WHERE `remainder_id` = $remainder_id;";
     
        $res = mysqli_query($conn, $query);
        // return $query;
        // die();
     

        if ($res) {
            return $remainder_id;
        } else {
            return mysqli_error($conn);
        }
    }



}