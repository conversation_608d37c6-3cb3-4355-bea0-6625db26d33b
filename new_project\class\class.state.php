<?php

include_once 'connection.php';

class State extends connection {

    public function __construct() {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function State() {
        self::__construct();
    }

    private $_conn;

    function getLogin($username) {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond=" and u.email = '". mysqli_real_escape_string($conn, $username)."' ";
        $query = "SELECT u.* FROM ggportal_tbl_state u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);

    }

    function randomPassword() {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $password = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < 8; $i++) {
            $n = rand(0, $alphaLength);
            $password[] = $alphabet[$n];
        }
        return implode($password); //turn the array into a string
    }

    function getCustomerByID($customer_id) {
        $conn = $this->_conn;
        $customer_id = intval($customer_id);
        if($customer_id>0){
            $cond="and c.customer_id=$customer_id ";
        }else{
            $cond=" and 1=2 ";
        }
        $query = "SELECT c.*,concat(ca.address ,' ',a.area_name) as address,concat(ca.address) as address_main ,ca.customer_address_id,ca.latitude,ca.longitude,ct.customer_type,ca.area_id
                    FROM ggportal_tbl_state c
                    left join ggportal_tbl_state_address ca on ca.customer_id = c.customer_id
                    left join ggportal_tbl_state_type ct  on ct.customer_type_id = c.customer_type_id
                     join nbcourier_tbl_area a on ca.area_id = a.area_id
                    WHERE 1=1 and ca.label='main' $cond;";
//        echo $query;
//        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    function getStates($criteria) {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['customer_id']) && strlen($criteria['customer_id']) > 0 ) {
            $customer_id = intval( $criteria['customer_id']);
            $cond .= " and c.customer_id  = $customer_id ";
        }

        $query = "select s.* from ggportal_tbl_state s
         where 1=1  $cond order by s.state_id DESC;";
    //    echo $query;
//        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    

    function saveState($_details) {
        $conn = $this->_conn;
        $state_id = intval($_details['state_id']);

//        print_R($driver_details);
//        die();
        if ($state_id == 0) {
            $query = "INSERT INTO ggportal_tbl_state SET
                first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'              
                ,category = '" . mysqli_real_escape_string($conn, $_details['category']) . "'              
                ,gender = '" . mysqli_real_escape_string($conn, $_details['gender']) . "'              
                ,date_of_birth = '" . mysqli_real_escape_string($conn, $_details['date_of_birth']) . "'              
                ,marital_status = '" . mysqli_real_escape_string($conn, $_details['marital_status']) . "'              
                ,state_id = '" . mysqli_real_escape_string($conn, $_details['state_id']) . "'              
                ,state = '" . mysqli_real_escape_string($conn, $_details['state']) . "'              
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'              
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,user_active_yn = '" . mysqli_real_escape_string($conn, $_details['user_active_yn']) . "'              
                ,created_by =  '" . intval($_details['user_id']) . "'
                ,created_at = now()      
                ,updated_by =  '" . intval($_details['user_id']) . "'
                ,updated_at = now()                         
               ;";
        } else {
            //update
          
        }
        // return $query;
//        die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($state_id == 0) {
                $state_id = mysqli_insert_id($conn);
            }
            return $state_id;

        } else {
            return mysqli_error($conn);
        }
    }


}