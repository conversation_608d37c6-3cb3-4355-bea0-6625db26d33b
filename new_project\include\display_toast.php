<?php


if (isset($_SESSION['message_toast']) && $_SESSION['message_toast'] != "") {
    $session_message_toast=$_SESSION['message_toast'];
    ?>
    <script type="text/javascript">
        $(function(){
            toastr.success(' <?php echo $session_message_toast; ?>');
        });
    </script>
    <?php
    //die("<br>displayed");
    $_SESSION['message_toast'] = "";
    $_SESSION['message'] = "";
    $_SESSION['error'] = "";
}

if (isset($_SESSION['error_toast']) && $_SESSION['error_toast'] != "") {
    $session_error_toast=$_SESSION['error_toast'];
    ?>
    <script type="text/javascript">
        $(function(){
            toastr.error(' <?php echo $session_error_toast; ?>');
        });
    </script>
    <?php
    //die("<br>displayed");
    $_SESSION['error_toast'] = "";
    $_SESSION['error'] = "";
    $_SESSION['error'] = "";
}

unset($_SESSION['message_toast']);
unset($_SESSION['message']);
unset($_SESSION['error']);
?>
