<?php
/**
 * This funciton will send push notification to the mobile app.
 * @param type $title
 * @param type $body
 * @param type $token
 */
function ip_info($ip = null, $purpose = "location", $deep_detect = true)
{
    $output = null;
    if (filter_var($ip, FILTER_VALIDATE_IP) === false) {
        $ip = $_SERVER["REMOTE_ADDR"];
        if ($deep_detect) {
            if (filter_var(@$_SERVER['HTTP_X_FORWARDED_FOR'], FILTER_VALIDATE_IP)) {
                $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            }
            if (filter_var(@$_SERVER['HTTP_CLIENT_IP'], FILTER_VALIDATE_IP)) {
                $ip = $_SERVER['HTTP_CLIENT_IP'];
            }
        }
    }
    $purpose    = str_replace(array("name", "\n", "\t", " ", "-", "_"), null, strtolower(trim($purpose)));
    $support    = array("country", "countrycode", "state", "region", "city", "location", "address");
    $continents = array(
        "AF" => "Africa",
        "AN" => "Antarctica",
        "AS" => "Asia",
        "EU" => "Europe",
        "OC" => "Australia (Oceania)",
        "NA" => "North America",
        "SA" => "South America"
    );
    if (filter_var($ip, FILTER_VALIDATE_IP) && in_array($purpose, $support)) {
        $ipdat = @json_decode(file_get_contents("http://www.geoplugin.net/json.gp?ip=" . $ip));
        if (@strlen(trim($ipdat->geoplugin_countryCode)) == 2) {
            switch ($purpose) {
                case "location":
                    $output = array(
                        "city"           => @$ipdat->geoplugin_city,
                        "state"          => @$ipdat->geoplugin_regionName,
                        "country"        => @$ipdat->geoplugin_countryName,
                        "country_code"   => @$ipdat->geoplugin_countryCode,
                        "continent"      => @$continents[strtoupper($ipdat->geoplugin_continentCode)],
                        "continent_code" => @$ipdat->geoplugin_continentCode
                    );
                    break;
                case "address":
                    $address = array($ipdat->geoplugin_countryName);
                    if (@strlen($ipdat->geoplugin_regionName) >= 1) {
                        $address[] = $ipdat->geoplugin_regionName;
                    }
                    if (@strlen($ipdat->geoplugin_city) >= 1) {
                        $address[] = $ipdat->geoplugin_city;
                    }
                    $output = implode(", ", array_reverse($address));
                    break;
                case "city":
                    $output = @$ipdat->geoplugin_city;
                    break;
                case "state":
                    $output = @$ipdat->geoplugin_regionName;
                    break;
                case "region":
                    $output = @$ipdat->geoplugin_regionName;
                    break;
                case "country":
                    $output = @$ipdat->geoplugin_countryName;
                    break;
                case "countrycode":
                    $output = @$ipdat->geoplugin_countryCode;
                    break;
            }
        }
    }
    return $output;
}
function send_sms($message, $phoneNumber)
{
    $arrayToSend = array('message' => $message, 'phoneNumber' => $phoneNumber);
    $data  = json_encode($arrayToSend);
    $url = 'https://smsapi.bitshifttech.com/api/v1/send/single';
    $server_key = 'bMOQJncTR2UqmHbEKq6bK250MMUoKfNqI16Nhi2dVqiYU7b6vtn7FKV2vCazqMm8kQHrnhV4PJf8PNfim93XlinYQFvf86JO3x6qqHlkQlI6Njca862NKaUu5tAZaPA8JgCCePYx2qlTpnJRFfkdl1jmx7yRaGHgtnKsks7DfeqYxLJG1jZiLBjFDIv2NiIXs1itMiWHoV3nVBOTmGcK88TFfXyTtWx0lbldw7LtmKMoJDVTZ6SCwnLl7mB3rVfnwTi0cH9WxJkV8qgSWL6UTF5Smk2DvAJZjdCsULKif6iDRqHaFDLuy8HHxLaPMuOlM3qYlFUmzOfeXTkMFFkAD18s0BJSiUH4YxIbB7js39ptpzgKMEmc9SiksFs8VZrzDSZl94yY1cDndpIrV0QkSXi9llOpE0Y5Vh54IvtqOmdLPxLwVcLZHuh5667qzj1Zel9Ox857Aq8xii4cjFs6A29a5OWkWaj6CkfrG03ya0I0LY0M7VwDFPIK6Q6uodzzBY6wdv9fpD9yGcQDhXROSO8lY6kIJu1FCP4qSwySU2KBr2D9yQObCkyrnXtlbTdNculktP0IHliZF3PizeuwDOcxYWPlygsX5AP3eiFCnLOAnhIW674bGh2GqJEeGYFQk3T2Z8ygGH3ryUoXtBrla1ve1qLlqIx2dEauCiC9Be9nWBWmFvMYkTCwc9DWE7LxcsYaEEn748r8UxFqmg4fPaI1Ve7hFxEOstoxsqDDaBfkuXotN5XGPtBGgcDxMFpGrge5NxrPujGsEuIh1d81YtvSHxFij4cEvXi18n8PbDVSxJ2nAPhMz6QMm3KFrJ3gQCLM4xLhfcjTpVuHd1tpi5HUp6y2OdKIpKW5R1HkxmTYEb3Cs4cuBDxLuQcV8AqsLZyumuet6vEyH3TZ4rwSNvCqaoqghzfrUYhqSE2IrZP9tOF5Liz5F6VjjSZJlkB7Z8ss5S2a8HbI2gCL3fvS6njDOP81Z0SkCxUb4xiVV8GEtV00nwe8YoujcrTCf78a';
    $headers = array(
        'Authorization:' . $server_key,
        'Content-Type:application/json'
    );
    //CURL request to route notification to FCM connection server (provided by Google)
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $result = curl_exec($ch);
    curl_close($ch);
    $result  = json_decode($result);
    return $result;
}

function send_otp($otp, $phoneNumber)
{
    $arrayToSend = array('message' => $otp, 'phoneNumber' => $phoneNumber);
    $data  = json_encode($arrayToSend);
    $url = 'https://smsapi.bitshifttech.com/api/v1/send/single';
    $server_key = 'bMOQJncTR2UqmHbEKq6bK250MMUoKfNqI16Nhi2dVqiYU7b6vtn7FKV2vCazqMm8kQHrnhV4PJf8PNfim93XlinYQFvf86JO3x6qqHlkQlI6Njca862NKaUu5tAZaPA8JgCCePYx2qlTpnJRFfkdl1jmx7yRaGHgtnKsks7DfeqYxLJG1jZiLBjFDIv2NiIXs1itMiWHoV3nVBOTmGcK88TFfXyTtWx0lbldw7LtmKMoJDVTZ6SCwnLl7mB3rVfnwTi0cH9WxJkV8qgSWL6UTF5Smk2DvAJZjdCsULKif6iDRqHaFDLuy8HHxLaPMuOlM3qYlFUmzOfeXTkMFFkAD18s0BJSiUH4YxIbB7js39ptpzgKMEmc9SiksFs8VZrzDSZl94yY1cDndpIrV0QkSXi9llOpE0Y5Vh54IvtqOmdLPxLwVcLZHuh5667qzj1Zel9Ox857Aq8xii4cjFs6A29a5OWkWaj6CkfrG03ya0I0LY0M7VwDFPIK6Q6uodzzBY6wdv9fpD9yGcQDhXROSO8lY6kIJu1FCP4qSwySU2KBr2D9yQObCkyrnXtlbTdNculktP0IHliZF3PizeuwDOcxYWPlygsX5AP3eiFCnLOAnhIW674bGh2GqJEeGYFQk3T2Z8ygGH3ryUoXtBrla1ve1qLlqIx2dEauCiC9Be9nWBWmFvMYkTCwc9DWE7LxcsYaEEn748r8UxFqmg4fPaI1Ve7hFxEOstoxsqDDaBfkuXotN5XGPtBGgcDxMFpGrge5NxrPujGsEuIh1d81YtvSHxFij4cEvXi18n8PbDVSxJ2nAPhMz6QMm3KFrJ3gQCLM4xLhfcjTpVuHd1tpi5HUp6y2OdKIpKW5R1HkxmTYEb3Cs4cuBDxLuQcV8AqsLZyumuet6vEyH3TZ4rwSNvCqaoqghzfrUYhqSE2IrZP9tOF5Liz5F6VjjSZJlkB7Z8ss5S2a8HbI2gCL3fvS6njDOP81Z0SkCxUb4xiVV8GEtV00nwe8YoujcrTCf78a';
    $headers = array(
        'Authorization:' . $server_key,
        'Content-Type:application/json'
    );
    //CURL request to route notification to FCM connection server (provided by Google)
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $result = curl_exec($ch);
    curl_close($ch);
    $result  = json_decode($result);
    return $result;
}
function send_notification($title, $body, $token)
{
    $notification = array('title' => $title, 'body' => $body, 'sound' => 'default', 'badge' => '1');
    $arrayToSend = array('to' => $token, 'notification' => $notification, 'priority' => 'high');
    $data  = json_encode($arrayToSend);
    //FCM API end-point
    $url = 'https://fcm.googleapis.com/fcm/send';
    //api_key in Firebase Console -> Project Settings -> CLOUD MESSAGING -> Legacy Server key
    $server_key = 'AAAAI56xu14:APA91bEArkgOFRy4V8NAfbUhKAp2AQwZyO4qhaY9pE9txXEGS1mXaqeuU3xNTpHdRNCEvjEV4_YF5kPLgFzz3l6Giup6SPKWRKttUHIEJA7XVmBD1pA4E2zweOSNPZ0O-WjEupy17TDI';
    //header with content_type api key
    $headers = array(
        'Content-Type:application/json',
        'Authorization:key=' . $server_key
    );
    //CURL request to route notification to FCM connection server (provided by Google)
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $result = curl_exec($ch);
    //print_r($result);
    if ($result === false) {
        $json_data['message-notify'] = 'Oops! FCM Send Error: ' . curl_error($ch);
        die('Oops! FCM Send Error: ' . curl_error($ch));
    }
    curl_close($ch);
    //        $result  = json_decode($result);
}
function create_csv($list, $title_csv)
{
    $year = date("Y");
    $filename_short = "YDC-$year-" . generate_random_characters() . ".csv";
    $filename = "temp_csv/$filename_short";
    //echo "<br>$filename<br>";
    $fp = fopen($filename, 'w');
    if (count($list) > 0) {
        foreach ($list[0] as $key => $value) {
            // echo "key=$key value=$value<br/>";
            $key2[] = utf8_decode($key);
        }
        $key1[] = $title_csv;
        fputcsv($fp, $key1);
        fputcsv($fp, $key2);
        //die();
        foreach ($list as $key => $value) {
            $line2 = implode(",", $value); //. "<br/>";
            //echo "value=$line2<br/>";
            fputcsv($fp, $value);
            //fputcsv($fp,preg_split(',',$line2));
        }
        $total = count($list);
    }
    fclose($fp);
    return $filename;
    //$download_link = "<a href='$filename' style='WIDTH: 150PX;WIDTH: 150PX; FLOAT: RIGHT; MARGIN-right: 100px; margin-top: 30px; margin-bottom: 20px;padding: 8px; color: green; background-color: yellow; font-size: 16px; text-align: center; border: 1px solid green;font-weight: 700;' Title='Download CSV' target='_blank'>Download CSV ($total Candidates)</a>";
    //echo "<script>document.getElementById('csv_area').innerHTML =(\"".$download_link."\"); </script>";
}
/**
 * @param type $access_valid_for
 * @param string $redirect_url
 * @param type $error_message
 * @return boolean
 */
function validate_page_access($access_valid_for, $redirect_url = "index.php", $error_message = "Access Denied!")
{
    $checking_user_type = $_SESSION['user']['user_type'];
    $valid = false;
    if ($checking_user_type == "") {
        $valid = false;
        $redirect_url = "login.php";
    } else {
        if (strpos(".." . $access_valid_for, $checking_user_type) > 0) {
            //usertype is included in this access list.
            $valid = true;
        } else {
            $valid = false;
        }
    }
    //echo "valid=$valid";
    if ($valid) {
        return true;
    } else {
        if ($redirect_url == "") {
            return false;
        } else {
            errorRedirect($error_message, $redirect_url);
        }
    }
}
/**
 * This funciton will read the GET or POST data and store the value in the session variable.
 * It is handy when using to retrive an incompleted form submission.
 * @param type $field_name as the $_POST['field_name']
 * @param type $session_variable_name as $_SESSION['user']['cookie'][$session_variable_name][$field_name]
 * @param type $type as 'S'tring, 'I'
 * @param type $get_post
 * @return type
 */
function input_filter($field_name, $session_variable_name, $type = 'S', $get_post = 'P')
{
    $gp = ($get_post == "P" ? INPUT_POST : INPUT_GET);
    $filtertype = ($type == "B" ? FILTER_VALIDATE_BOOLEAN : ($type == "F" ? FILTER_VALIDATE_FLOAT : ($type == "NF" ? FILTER_SANITIZE_NUMBER_FLOAT : ($type == "I" ? FILTER_VALIDATE_INT : ($type == "E" ? FILTER_VALIDATE_EMAIL : ($type == "SP" ? FILTER_SANITIZE_FULL_SPECIAL_CHARS : FILTER_SANITIZE_STRING))))));
    $newvalue = filter_input($gp, $field_name, $filtertype);
    if ($type == "NF" || $type == "I" || $type == "F") {
        //echo "<bR>type=$type value=$newvalue <br>";
        if ($newvalue == "" || is_null($newvalue)) {
            $newvalue = 0;
        }
    }
    //filter_input(INPUT_GET,"a", FILTER_SANITIZE_SPECIAL_CHARS);
    //echo "<br>$field_name = $newvalue :: $filtertype ";
    if ($session_variable_name != "") {
        $_SESSION['user']['cookie'][$session_variable_name][$field_name] = $newvalue;
    }
    return $newvalue;
}
/**
 * This function will read the session variable value stored during the form submission.
 * @param type $field_name
 * @param type $session_variable_name
 * @return type
 */
function read_session_value($field_name, $session_variable_name, $default_value = "")
{
    $return_value = (isset($_SESSION['user']['cookie'][$session_variable_name][$field_name]) ? $_SESSION['user']['cookie'][$session_variable_name][$field_name] : "");
    if ($default_value != "" && $return_value == "") {
        $return_value = $default_value;
    }
    return $return_value;
}
/**
 * this will produce number of pages in numerical format. useful to display when there are multiple
 * pages of records available. user can click on a page to go to specific page
 * @param <type> $total_rows = total number of rows
 * @param <type> $current_page_no = which page user clicked
 * @param <type> $page_reload = when click on a page number which page to reload
 * @param <type> $rows_per_page = no of rows in each page
 * @return <type>
 */
function paginateBootstrap($total_rows, $current_page_no, $page_reload, $rows_per_page)
{
    if ($total_rows == 0) {
        $total_rows = 1; //default 1 to display a page
    }
    $rows_per_page_1 = "";
    if ($rows_per_page == $total_rows) { //'ALL'){
        //still need to show page numbers, but dont show ALL at the end
        $rows_per_page = $total_rows;
        $rows_per_page_1 = "ALL";
    } elseif ($rows_per_page == 0) {
        $rows_per_page = $total_rows;
    }
    $total_pages = number_format($total_rows / $rows_per_page, 1); //need to know if theer is a decimal
    //total_pages will be xx.x one decimal place
    $total_prev = intval($total_pages); // number_format($total_pages,0);
    if (($total_pages - $total_prev) > 0) {
        $total_pages = $total_prev + 1;
    }
    //echo "Total_rows=$total_rows Total_prev=$total_prev  total_pages=$total_pages rows_per_page=$rows_per_page";
    //die();
    // <span class="first"><a href="" title="First page">&#171; First</a></span>
    // <span class="first"><a href="" title="Previous page">&lt; Previous</a></span>
    // <span class="first"><a href="" title="Page 1">1</a> <a href="" title="page 2">2</a> <a href="" title="page 3">3</a> etc</span>
    // <span class="first"><a href="" title="Next page">Next &gt;</a></span>
    // <span class="first"><a href="" title="Last page">Last &#187;</a></span>
    $result = "";
    if ($total_pages > 0) {
        if ($total_pages > 3 && $current_page_no > 1) {
            $result .= "<span class='first'><a href='$page_reload&page=1&rows_per_page=$rows_per_page' title='First page'>&#171; </a></span>  ";
            $result .= "<span class='previous'><a href='" . $page_reload . "&page=" . ($current_page_no - 1) . "&rows_per_page=$rows_per_page' title='Previous page'>&#139; </a></span>";
        }
        /* we will always display 7 pages for any
         * |< < ... 12 13 (14) 15 16 17 ... > >|  -- you are in page 14 and you have more than 17 pages
         * |< < ... 12 14 (14) 15 16            -- you are in page 14 and you have only 16 pages
         *
         */
        $ps = 1; //page start
        $p_to_add = 0;
        if (($current_page_no - 3) > 1 && $total_pages > 15) { // 5-3 = 2
            $result .= " ... ";
            if (($total_pages - $current_page_no) < 3) { //11-10 = 1 we are in page 10
                $p_to_add = 3 - ($total_pages - $current_page_no);
            } else {
                $p_to_add = 0;
            }
            $ps = $current_page_no - 3 - $p_to_add; //2
        } else {
            $ps = 1;
        }
        if ($total_pages > 15) {
            if ($total_pages > ($current_page_no + 3)) { // 11 > (10+3)
                $loop = $ps + 14; //number of pages to display
            } else {
                $loop = $ps + 3 + ($total_pages - $current_page_no) + $p_to_add; //
            }
        } else {
            $loop = $total_pages;
            $ps = 1;
        }
        for ($i = $ps; $i <= $loop; $i++) {
            if ($i == $current_page_no) {
                $result .= " <b>$i</b> ";
            } else {
                //$p=$i-1;
                $result .= " <a class='.pagination' href='$page_reload&page=$i&rows_per_page=$rows_per_page'/>$i</a> ";
            }
        }
        if ($i <= $total_pages) {
            $result .= " ... ";
        }
        if ($total_pages > 3 && $current_page_no < $total_pages) {
            $result .= "<span class='next'><a href='" . $page_reload . "&page=" . ($current_page_no + 1) . "&rows_per_page=$rows_per_page' title='Next page'>&#155;</a></span>";
            // if($i<$total_pages){
            $result .= "<span class='last'><a href='$page_reload&page=$total_pages&rows_per_page=$rows_per_page' title='Last page'>&#187; </a></span>";
            // }
        }
        if ($total_pages == $current_page_no) {
        }
        //include an option to show ALL
        //$result.="<span class='all'><select name='pp' onClick='$page_reload&page=&rows_per_page=0'><option value=12>12</option><option value='all'>ALL</option></select></span>" ;
        $options = "";
        $margin = 12;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 20;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 30;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 60;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 100;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        if ($rows_per_page_1 != 'ALL') {
            //$result.="<span class='all'><a href='$page_reload&page=0&rows_per_page=ALL' title='Show All'>ALL</a> </span>" ;
            $result .= " <select name='pp' id='pp' onchange=\"reloadPage('title',this.value,1);\">
                $options
                <option value='$total_rows'>Show All</option>
                </select>
                ";
        } else {
            //give an option to paginate
            // $result="<span class='recordsInPage'><a href='$page_reload&page=1&rows_per_page=".$_SESSION['rows_per_page']."' title='Paginate'>Paginate</a> </span>" ;
            $result = "<span class='recordsInPage'> <select name='pp' id='pp' onchange=\"reloadPage('title',this.value,1);\">
                $options
                <option value='$total_rows' Selected >Show All</option>
                </select>
                </span>";
        }
    }
    return $result;
}
/**
 * this will produce number of pages in numerical format. useful to display when there are multiple
 * pages of records available. user can click on a page to go to specific page
 * @param <type> $total_rows = total number of rows
 * @param <type> $current_page_no = which page user clicked
 * @param <type> $page_reload = when click on a page number which page to reload
 * @param <type> $rows_per_page = no of rows in each page
 * @return <type>
 */
function paginate($total_rows, $current_page_no, $page_reload, $rows_per_page)
{
    if ($total_rows == 0) {
        $total_rows = 1; //default 1 to display a page
    }
    $rows_per_page_1 = "";
    if ($rows_per_page == $total_rows) { //'ALL'){
        //still need to show page numbers, but dont show ALL at the end
        $rows_per_page = $total_rows;
        $rows_per_page_1 = "ALL";
    } elseif ($rows_per_page == 0) {
        $rows_per_page = $total_rows;
    }
    $total_pages = number_format($total_rows / $rows_per_page, 1); //need to know if theer is a decimal
    //total_pages will be xx.x one decimal place
    $total_prev = intval($total_pages); // number_format($total_pages,0);
    if (($total_pages - $total_prev) > 0) {
        $total_pages = $total_prev + 1;
    }
    //echo "Total_rows=$total_rows Total_prev=$total_prev  total_pages=$total_pages rows_per_page=$rows_per_page";
    //die();
    // <span class="first"><a href="" title="First page">&#171; First</a></span>
    // <span class="first"><a href="" title="Previous page">&lt; Previous</a></span>
    // <span class="first"><a href="" title="Page 1">1</a> <a href="" title="page 2">2</a> <a href="" title="page 3">3</a> etc</span>
    // <span class="first"><a href="" title="Next page">Next &gt;</a></span>
    // <span class="first"><a href="" title="Last page">Last &#187;</a></span>
    $result = "";
    if ($total_pages > 0) {
        if ($total_pages > 3 && $current_page_no > 1) {
            $result .= "<span class='first'><a href='$page_reload&page=1&rows_per_page=$rows_per_page' title='First page'>&#171; </a></span>  ";
            $result .= "<span class='previous'><a href='" . $page_reload . "&page=" . ($current_page_no - 1) . "&rows_per_page=$rows_per_page' title='Previous page'>&#139; </a></span>";
        }
        /* we will always display 7 pages for any
         * |< < ... 12 13 (14) 15 16 17 ... > >|  -- you are in page 14 and you have more than 17 pages
         * |< < ... 12 14 (14) 15 16            -- you are in page 14 and you have only 16 pages
         *
         */
        $ps = 1; //page start
        $p_to_add = 0;
        if (($current_page_no - 3) > 1 && $total_pages > 15) { // 5-3 = 2
            $result .= " ... ";
            if (($total_pages - $current_page_no) < 3) { //11-10 = 1 we are in page 10
                $p_to_add = 3 - ($total_pages - $current_page_no);
            } else {
                $p_to_add = 0;
            }
            $ps = $current_page_no - 3 - $p_to_add; //2
        } else {
            $ps = 1;
        }
        if ($total_pages > 15) {
            if ($total_pages > ($current_page_no + 3)) { // 11 > (10+3)
                $loop = $ps + 14; //number of pages to display
            } else {
                $loop = $ps + 3 + ($total_pages - $current_page_no) + $p_to_add; //
            }
        } else {
            $loop = $total_pages;
            $ps = 1;
        }
        for ($i = $ps; $i <= $loop; $i++) {
            if ($i == $current_page_no) {
                $result .= " <b>$i</b> ";
            } else {
                //$p=$i-1;
                $result .= " <a href='$page_reload&page=$i&rows_per_page=$rows_per_page'/>$i</a> ";
            }
        }
        if ($i <= $total_pages) {
            $result .= " ... ";
        }
        if ($total_pages > 3 && $current_page_no < $total_pages) {
            $result .= "<span class='next'><a href='" . $page_reload . "&page=" . ($current_page_no + 1) . "&rows_per_page=$rows_per_page' title='Next page'>&#155;</a></span>";
            // if($i<$total_pages){
            $result .= "<span class='last'><a href='$page_reload&page=$total_pages&rows_per_page=$rows_per_page' title='Last page'>&#187; </a></span>";
            // }
        }
        if ($total_pages == $current_page_no) {
        }
        //include an option to show ALL
        //$result.="<span class='all'><select name='pp' onClick='$page_reload&page=&rows_per_page=0'><option value=12>12</option><option value='all'>ALL</option></select></span>" ;
        $options = "";
        $margin = 12;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 20;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 30;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 60;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        $margin = 100;
        if ($total_rows > $margin) {
            $options .= "<option value=$margin " . ($rows_per_page == $margin ? " selected " : "") . ">$margin</option>";
        }
        if ($rows_per_page_1 != 'ALL') {
            //$result.="<span class='all'><a href='$page_reload&page=0&rows_per_page=ALL' title='Show All'>ALL</a> </span>" ;
            $result .= " <select name='pp' id='pp' onchange=\"reloadPage('title',this.value,1);\">
                $options
                <option value='$total_rows'>Show All</option>
                </select>
                ";
        } else {
            //give an option to paginate
            // $result="<span class='recordsInPage'><a href='$page_reload&page=1&rows_per_page=".$_SESSION['rows_per_page']."' title='Paginate'>Paginate</a> </span>" ;
            $result = "<span class='recordsInPage'> <select name='pp' id='pp' onchange=\"reloadPage('title',this.value,1);\">
                $options
                <option value='$total_rows' Selected >Show All</option>
                </select>
                </span>";
        }
    }
    return $result;
}
/**
 * this function will check for form post/get variables
 * @param <type> $field = name of the field eg. txtname/chkactive
 * @param <type> $mandatory ='Y' or 'N'
 * @param <type> $redirect = if field not passed but its mandatory so cant continue the operation, redirect to this link
 * @param <type> $errorstring = what should be displayed when this field not present
 * @param <type> $allowEmpty = can the field be empty eg: username cannot be empty
 * @param <type> $emptyRedirect = if empty then redirect
 * @param <type> $maxLength = max num of characters allowed. this is to prevent firebug editings
 * @param <type> $dataType = just extra checking to convert if integer
 * @return <type>
 */
function checkdata($field, $mandatory = 'Y', $redirect = "", $maxLength = 0, $errorstring = 'incorrect access!', $allowEmpty = '', $emptyRedirect = 'index.php', $dataType = 'S')
{
    //echo "<br/>data=".$_REQUEST[$field];
    //die();
    if (isset($_POST[$field])) {
        $data = $_POST[$field];
    } elseif (isset($_GET[$field])) {
        $data = $_GET[$field];
    }
    if (isset($data)) {
        $data = trim($data);
        if ($allowEmpty == "N" && $data == "") {
            $errortext = "error=$field cannot be empty!";
            $url = redirectHeader($emptyRedirect, $errortext);
            header("location:" . $url);
            die();
        }
        if ($maxLength != 0 && strlen($data) > $maxLength) {
            $errortext = "error=$field cannot exceed maximum length!";
            $url = redirectHeader($emptyRedirect, $errortext);
            header("location:" . $url);
            die();
        }
        if ($data != "") {
            if ($dataType != 'S') {
                if ($dataType == 'I') { //convert integer
                    $data = intval($data); //conver to integer http://php.net/manual/en/function.intval.php
                }
            }
        }
        if (strpos($data, ' --') > 0) {
            //sql comment lines not accepted!!
            $errortext = "error=$field with incorrect value!";
            $url = redirectHeader($emptyRedirect, $errortext);
            header("location:" . $url);
            die();
        }
        if (strpos($data, 'shop_tbl') > 0 || strpos($data, 'forum_') > 0) {
            $errortext = "error=$field with unacceptable value!";
            $url = redirectHeader($emptyRedirect, $errortext);
            header("location:" . $url);
            die();
        }
        //write_to_file($field.' = '.$data);
        $field = "export[$field]";
        if ($field != "username" && $field != "password") {
            setcookie($field, $data);
        }
        return $data;
    } elseif ($mandatory != 'N') {
        $errortext = "error=$errorstring($field))";
        if ($redirect == '') {
            $redirect = "index.php";
        }
        $url = redirectHeader($redirect, $errortext);
        header("location:" . $url);
        die();
    }
}
function checkCookie($cookiename)
{
    if (isset($_COOKIE['export'][$cookiename])) {
        return $_COOKIE['export'][$cookiename];
    } else {
        return "";
    }
}
function removeCookie($cookiename = "")
{
    foreach ($_COOKIE['export'] as $key => $value) {
        $field = "export[$key]";
        setcookie($field, "");
    }
}
function displayAlert($alert, $alerttype = 'A')
{
    //  echo "<script type='text/javascript' language='javascript'>var a='$alert'; alert(a);</script>";
    if ($alerttype == "A") {
        echo "<script type='text/javascript' language='javascript'>alert(\"$alert\");</script>";
    } else {
        //message, should change the icon
        echo "<script type='text/javascript' language='javascript'>alert(\"$alert\");</script>";
    }
    // $_SESSION['alert'] = ""; //reset the alert
    // $_SESSION['error'] = ""; //reset the alert
    // $_SESSION['message'] = ""; //reset the alert
}
/**
 * This function will merge the new added parameter with a url, mostly used to send the error messages back to the originator
 * @param <type> $url ='add_product.php?pid=1'
 * @param <type> $parameter = 'error=invalid product'
 * @return <type> return='add_product.php?pid=1&error=invalid product'
 */
function redirectHeader($url, $parameter)
{
    $url = removeErrorInURL($url); //remove the error='' parameter if already exists
    if (strpos($url, "?") > 0) {
        $url .= "&";
    } else {
        $url .= "?";
    }
    $url .= $parameter;
    return $url;
}
/**
 * This function will remove the pre-existing error='' from the url. because multiple error messages generated in url when saved again and again.
 * @param <type> $url
 * @return <type>
 */
function removeErrorInURL($url)
{
    $pos = strpos($url, 'error=');
    if ($pos > 0) {
        //this url already has an error parameter message, so remove the error message.
        // in most cases the error='' is the last parameter in the url
        // this will remove everything after this error message
        $url = substr($url, 0, $pos - 1);
    }
    $pos = strpos($url, 'message=');
    if ($pos > 0) {
        $url = substr($url, 0, $pos - 1);
    }
    return $url;
}
function displayMessages()
{
    if (isset($_GET['error'])) {
        //  echo "error found";
        echo displayAlert($_GET['error']);
    }
    if (isset($_GET['message'])) {
        echo displayAlert($_GET['message'], 'M');
    }
    if (isset($_SESSION['message']) && $_SESSION['message'] != "") {
        echo displayAlert($_SESSION['message'], 'M');
    }
    if (isset($_SESSION['error']) && $_SESSION['error'] != "") {
        echo displayAlert($_SESSION['error'], 'A');
    }
}
function write_to_file($msg)
{
    $fl = fopen("log_error.txt", "a"); //to write
    fwrite($fl, $msg . "; DATE:" . date("r") . "\r\n");
    fclose($fl);
}
function errorRedirect($error, $redirect_url, $message_type = "A", $die = true)
{
    $_SESSION['error'] = "";
    $_SESSION['message'] = "";
    if ($error != "") {
        if ($message_type == "A") {
            $_SESSION['error'] = $error;
        } elseif ($message_type == "W") {
            $_SESSION['warning'] = $error;
        } else {
            $_SESSION['message'] = $error;
        }
    }
    header("Location:$redirect_url");
    if ($die == true) {
        die();
    }
}
function generate_random_characters($no_of_letters = 6)
{
    $p = ""; //random password
    for ($i = 0; $i < $no_of_letters; $i++) {
        $d = rand(1, 30) % 2;
        if ($d == 1) {
            $p .= chr(rand(65, 90));
        } else {
            $p .= chr(rand(48, 57));
        }
    }
    return $p;
}
function getUserIpAddr()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        //ip from share internet
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        //ip pass from proxy
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}
function getIP()
{
    $ip;
    if (getenv("HTTP_CLIENT_IP")) {
        $ip = getenv("HTTP_CLIENT_IP");
    } elseif (getenv("HTTP_X_FORWARDED_FOR")) {
        $ip = getenv("HTTP_X_FORWARDED_FOR");
    } elseif (getenv("REMOTE_ADDR")) {
        $ip = getenv("REMOTE_ADDR");
    } else {
        $ip = "UNKNOWN";
    }
    return $ip;
}
function findYouTubeImage($source, $videoid = "")
{
    if ($videoid != "") {
        $vid = "http://img.youtube.com/vi/$videoid/hqdefault.jpg";
    } else {
        $src_main = $source;    //...value="http://www.youtube.com/v/D9sMrqkrVDs?.....
        $search = "/v/";
        $pos1 = strpos($src_main, $search);
        if ($pos1 == 0) {
            $search = "/p/";
            $pos1 = strpos($src_main, $search);
        }
        if ($pos1 == 0) {
            $search = "/embed/";
            $pos1 = strpos($src_main, $search);
        }
        if ($pos1 > 0) {
            //width found, now get the parameter value
            $pos2 = strpos($src_main, "?", $pos1);
            $vid = substr($src_main, $pos1 + strlen($search), $pos2 - ($pos1 + strlen($search))); //pic the 5 chars so will return "425" (for safe side
            $vid = "http://i.ytimg.com/vi/$vid/hqdefault.jpg";
        } else {
            $vid = "not found";
        }
    }
    return $vid;
}
function checkFileExtension($filename)
{
    $info = pathinfo($filename);
    $extension = $info['extension'];
    if (strpos(' exe php asp cs js html htm htaccess', $extension) > 0) {
        //do not allow the file to upload
        $_SESSION['error'] = "The file  extension $extension is prohibited to upload!";
        return false;
    } else {
        return true;
    }
}
/**
 * This funciton will format the yyyy-mm-dd hh:mm:ss date to dd/mm/yyyy format
 * Month should be always in the middle eg: 2011-03-21 , 21-03-2011
 * strtotime('Y-M-d','11/01/12') will return -> 2011-Jan-12
 * <br/>1 = m/d/Y
 * <br/>2 = m/d/Y H:i
 * <br/>3 = Y-m-d H:i:s
 * <br/>0 = m/d/Y H:i:s
 * @param <type> $date = in yyyy-mm-dd hh:ii:ss format (original mysql date)
 * @param <type> $option
 * @return <type>
 */
function format_date($date, $option = 2, $default = 'Today')
{
    $option = intval($option);
    $date = str_replace("/", "-", $date); //because strtotime works for $date field '-' as dd/mm/yyyy and '/' as mm/dd/yyyy
    $date = str_replace(".", "-", $date);
    $date = trim($date);
    if ($option == 1) {
        $format = "d/m/Y";
    } elseif ($option == 2) {
        $format = "d/m/Y H:i";
    } elseif ($option == 3) {
        $format = "Y-m-d H:i:s"; //to save to database
    } elseif ($option == 4) {
        $format = "d F Y";
    } elseif ($option == 5) {
        $format = "Y-m-d";
    } else {
        $format = "d/m/Y H:i:s";
    }
    //echo "<br/>date=$date<br/>";
    if ($date != "") {
        return date($format, strtotime($date));
    } else {
        if ($default == 'Today') {
            return date($format);
        } else {
            return "";
        }
    }
}
/**
 * Find the different between date1 and date2 (date2-date1) in days
 * @param <type> $date1 : string 'yyyy-mm-dd' format "2005-11-20"
 * @param <type> $date2 : string 'yyyy-mm-dd' format
 * @param String $difference: what difference eg: y - year d - days, H - hours
 * @return days
 */
function dateDiff2($date1, $date2, $difference = "d")
{
    //date("Y-m-d")
    if ($difference == 'd') {
        $return = round((strtotime($date2) - strtotime($date1)) / (60 * 60 * 24), 0);
    } elseif ($difference == 'H') {
        //return hours
        $return = round((strtotime($date2) - strtotime($date1)) / (60 * 60), 0);
    } elseif ($difference == 'i') {
        //return mins
        $return = round((strtotime($date2) - strtotime($date1)) / (60), 0);
    } elseif ($difference == "y") {
        $return = round((strtotime($date2) - strtotime($date1)) / (60 * 60 * 24 * 365), 0);
    }
    return $return;
}
/**
 * Add no of given days/months/year to the given date
 * @param <type> $type = 'd'=days ; 'm'=month; 'y'=year
 * @param <int> $value = No of days/months/year to add or substract
 * @param <type> $date = date
 * @return <type> new date.
 */
function dateAdd($type, $value, $date)
{
    if ($type == "d") {
        $type = "days";
    } elseif ($type == "m") {
        $type = "month";
    } elseif ($type == "y") {
        $type = "year";
    }
    $date = strtotime($date);
    $new_date = strtotime($value . " " . $type, $date); //time());
    //messageAlert("",date("Y-m-d", $new_date));
    return date("Y-m-d", $new_date);
}
function isDate($date)
{
    $x = strtotime($date);
    if ($x == false || $x == -1) {
        return false;
    } else {
        return true;
    }
}
function setYN($value, $change = true)
{
    if ($value == "" || $value == "N") {
        return "N";
    } elseif ($change) {
        return "Y";
    }
}
function db_to_array($res)
{
    //print_r($res);
    $recordList = array();
    //if (count($res) > 0) {
    while ($row = mysqli_fetch_assoc($res)) {
        foreach ($row as $field => $value) {
            $field = strtolower($field);
            $record[$field] = $value;
        }
        array_push($recordList, $record);
    }
    //}
    return $recordList;
}
function db_to_combo_new_add($id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    $combo_return = "<select name='$combo_name' class='form-control select2bs4 $combo_name' $required $function style='width: 100%;'> ";
    if ($choose) {
        $combo_return .= "<option  selected disabled hidden> </option>";
    }
    $combo_return .= "<option value='$combo_name' > New" . ($placeholder_text == "" ? "One" : $placeholder_text) . "</option>";
    while ($row = mysqli_fetch_array($res)) {
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo_select2_multiple($id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    $combo_return = "
    <select name='$combo_name' class='select2 select2-hidden-accessible  $combo_name'  $required $function multiple='' data-dropdown-css-class='select2-teal' data-placeholder='" . $placeholder_text . "' style='width: 100%;' data-select2-id='7' tabindex='-1' aria-hidden='true'>";
    while ($row = mysqli_fetch_array($res)) {
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option data-select2-id='$id_data' value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    $combo_return .= " </select>
";
    return $combo_return;
}
function db_to_combo_bs4($id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    $combo_return = "<select name='$combo_name' class='form-control select2bs4 $combo_name' $required $function style='width: 100%;'> ";
    if ($choose) {
        $combo_return .= "<option value='' selected> Choose" . ($placeholder_text == "" ? "One" : $placeholder_text) . "</option>";
    }
    while ($row = mysqli_fetch_array($res)) {
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo_bs4_json($all_function, $id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    $all = false;
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    if ($all_function == 'all') {
        $all = true;
    }
    $combo_return = "<select name='$combo_name' class='form-control select2bs4 $combo_name' $required $function style='width: 100%;'> ";
    if ($choose) {
        $combo_return .= "<option value='' > Choose" . ($placeholder_text == "" ? "One" : $placeholder_text) . "</option>";
    }
    if ($all) {
        $combo_return .= "<option value='all' " . ('all' == $id_match ? 'selected' : "") . "> All </option>";
    }
    foreach ($res as $key => $value) {
        # code...
        $combo_return .= "<option value='" . $value['name'] . "' " . ($value['name'] == $id_match ? 'selected' : "") . " >" . $value['name'] . "</option>";
    }
    // while ($row = mysqli_fetch_array($res)) {
    //     $id_data = $row[0];
    //     $value_data = $row[1];
    // }
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo_bs4_json_code($id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    $combo_return = "<select name='$combo_name' class='form-control select2bs4 $combo_name ' $required $function style='padding-left:30px;width: 100%;'> ";
    // if ($choose) {
    //     $combo_return .= "<option value='' > Choose" . ($placeholder_text == "" ? "One" : $placeholder_text) . "</option>";
    // }
    foreach ($res as $key => $value) {
        # code...
        $combo_return .= "<option value='" . $value['code'] . "' " . ($value['code'] == $id_match ? 'selected' : "") . " >" . $value['dial_code'] . "</option>";
    }
    // while ($row = mysqli_fetch_array($res)) {
    //     $id_data = $row[0];
    //     $value_data = $row[1];
    // }
    $combo_return .= "</select>";
    return $combo_return;
}

function db_to_combo_bs4_first_disabled($id_match, $res, $combo_name, $function_to_call = "", $required = "", $placeholder_text = "", $choose)
{
    if ($function_to_call == "readonly") {
        $function = "readonly";
    } elseif ($function_to_call == "disabled") {
        $function = "disabled";
    } else {
        $function = "";
    }
    $combo_return = "<select name='$combo_name' class='form-control select2bs4 $combo_name' $required $function style='width: 100%;'> ";
    if ($choose) {
        $combo_return .= "<option value=''> Choose" . ($placeholder_text == "" ? "One" : $placeholder_text) . "</option>";
    }
    while ($row = mysqli_fetch_array($res)) {
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo($id_match, $res, $combo_name, $function_to_call = "", $required = "required", $placeholder_text = "")
{
    if ($function_to_call != "") {
        $function = "$function_to_call()";
    } else {
        $function = "";
    }
    $combo_return = "<select style='padding-left:42px;' name='$combo_name' id='$combo_name' class='form-control has-feedback-left' $required ><option value=''> --- Choose " . ($placeholder_text == "" ? $combo_name : $placeholder_text) . " --- </option>";
    //$combo_return = "<select name='$combo_name' id='$combo_name' class='form-control' $required placeholder='Choose'><option> </option>";
    //if ( count($res) > 0) {
    while ($row = mysqli_fetch_array($res)) {
        //            print_r($row);
        //            echo "<br>";
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    //}
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo_group($id_match, $res, $combo_name, $function_to_call = "", $required = "required", $placeholder_text = "")
{
    if ($function_to_call != "") {
        $function = "$function_to_call()";
    } else {
        $function = "";
    }
    $combo_return = "<select name='$combo_name' id='$combo_name' class='form-control' $required ><option value=''>Choose " . ($placeholder_text == "" ? $combo_name : $placeholder_text) . " </option>";
    //if (count($res) > 0) {
    while ($row = mysqli_fetch_array($res)) {
        //print_r($row);
        //echo "<br>";
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    //}
    $combo_return .= "</select>";
    return $combo_return;
}
function db_to_combo_with_option($id_match, $res, $combo_name, $option, $function_to_call = "", $required = "required", $placeholder_text = "")
{
    if ($function_to_call != "") {
        $function = "$function_to_call()";
    } else {
        $function = "";
    }
    $combo_return = "<select style='padding-left:42px;' name='$combo_name' id='$combo_name' class='form-control has-feedback-left' $required >"
        . "<option value='-1' " . (-1 == $id_match ? "selected" : "") . "> --- Choose " . ($placeholder_text == "" ? $combo_name : $placeholder_text) . " --- </option>";
    if ($option == 0) {
        $combo_return .= " <option value='$option'  " . ($option == $id_match ? "selected" : "") . " > Mr Solution </option>";
    }
    while ($row = mysqli_fetch_array($res)) {
        $id_data = $row[0];
        $value_data = $row[1];
        $combo_return .= "<option value='$id_data' " . ($id_data == $id_match ? "selected" : "") . " >$value_data</option>";
    }
    $combo_return .= "</select>";
    return $combo_return;
}
function validateUserACL($acl_required, $message = '', $redirect_url = "")
{
    //echo $_SESSION['admin']['user_acl'];
    //die();
    if ($_SESSION['admin']['user_acl'] >= $acl_required) {
        return true;
    } else {
        if ($redirect_url != "") {
            errorRedirect("Access Denied. $message", $redirect_url);
        }
        return false;
    }
}
function convertTimeToSeconds($timeString)
{
    $timeExploded1 = explode(':', $timeString);
    $timeExploded = array_reverse($timeExploded1);
    $return_value = 0;
    $hour = $mins = $secons = $milliseconds = 0;
    if (isset($timeExploded[2])) { //hrs
        $hour = sprintf('%02d', $timeExploded[2]);
        $return_value += $hour * 3600;
    }
    if (isset($timeExploded[1])) { //mins
        $mins = sprintf('%02d', $timeExploded[1]);
        $return_value += intval($mins) * 60;
    }
    //now find the part of seconds
    $secondsExploded = explode(".", $timeExploded[0]);
    //$secondsExploded = array_reverse($secondsExploded1);
    //print_R($secondsExploded);
    if (isset($secondsExploded[0])) { //hrs
        $seconds = sprintf('%02d', $secondsExploded[0]);
        $return_value += $seconds;
    }
    if (isset($secondsExploded[1])) { //hrs
        $milliseconds = sprintf('%03d', $secondsExploded[1]);
        $return_value = $return_value . '.' . $milliseconds;
    }
    //echo "<br>time=$hour:$mins:$seconds.$milliseconds<br>returnvalue=$return_value";
    return array('seconds' => $return_value, 'string' => "$mins:$seconds.$milliseconds");
}
function validateReCaptcha($response)
{
    //your site secret key
    //$secret = "6LdlcBIUAAAAALSfKvV5VYFhmFKMDGgXkD-zvo0V"; //kishorraheem.co.uk
    $secret = "6LdzgjMUAAAAALxGerRJ_q6cHeubJdi8-jAOTPws"; //sldb.youcandothecube.com
    //get verify response data
    $verifyResponse = file_get_contents('https://www.google.com/recaptcha/api/siteverify?secret=' . $secret . '&response=' . $response);
    $responseData = json_decode($verifyResponse);
    if ($responseData->success) {
        return true;
    } else {
        return false;
    }
}

//This function for the userlogin Encryption Code
function convert_string($action, $string)
{
    $output = '';
    $encrypt_method = "AES-256-CBC";
    $secret_key = 'eaiYYkYTysia2lnHiw0N0vx7t7a3kEJVLfbTKoQIx5o=';
    $secret_iv = 'eaiYYkYTysia2lnHiw0N0';
    // hash
    $key = hash('sha256', $secret_key);
    $initialization_vector = substr(hash('sha256', $secret_iv), 0, 16);
    if ($string != '') {
        if ($action == 'encrypt') {
            $output = openssl_encrypt($string, $encrypt_method, $key, 0, $initialization_vector);
            $output = base64_encode($output);
        }
        if ($action == 'decrypt') {
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $initialization_vector);
        }
    }
    return $output;
}

define('TIMEZONE', 'Asia/Colombo');
date_default_timezone_set(TIMEZONE);

function last_seen($date_time)
{

    $timestamp = strtotime($date_time);

    $strTime = array("second", "minute", "hour", "day", "month", "year");
    $length = array("60","60","24","30","12","10");

    $currentTime = time();
    if($currentTime >= $timestamp) {
        $diff     = time()- $timestamp;
        for($i = 0; $diff >= $length[$i] && $i < count($length)-1; $i++) {
            $diff = $diff / $length[$i];
        }

        $diff = round($diff);
        if ($diff < 59 && $strTime[$i] == "second") {
            return 'Online';
        } else {
            return $diff . " " . $strTime[$i] . "(s) ago ";
        }

    }
}
