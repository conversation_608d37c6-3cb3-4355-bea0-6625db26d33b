<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';
include '../form-submit-email.php';
require_once __DIR__ . '/../class/class.sms.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cStudent = new Student();
$cSms = new Sms();
$cmessages = new Messages();

// save Student
if (isset($_POST['studentSave'])) {

    // $json = array(
    //     "data" => 0, 
    //     "status" => $_REQUEST
    // );  
    // echo json_encode($json);
    // die();

    

    $student_id = filter_input(INPUT_POST, 'student_id');
    $first_name = filter_input(INPUT_POST, 'first_name');
    $last_name = filter_input(INPUT_POST, 'last_name');
    $nic = filter_input(INPUT_POST, 'nic');
    $address = filter_input(INPUT_POST, 'address');
    $province = filter_input(INPUT_POST, 'province');
    $postal_code = filter_input(INPUT_POST, 'postal_code');
    $username = filter_input(INPUT_POST, 'email');
    $mobile = filter_input(INPUT_POST, 'mobile');
    $gender = filter_input(INPUT_POST, 'gender');
    $email = filter_input(INPUT_POST, 'email');

    //check email already exist
    if ($student_id > 0) {
        $student_deatils = $cStudent->getStudentByID($student_id);
        $passport_details = $cStudent->getPassportDetailsByStudentId($student_id); // get passport_details using student_id

        if ($student_deatils[0]['email'] != $email) {
            $check_email = $cStudent->checkEmail($email, $student_id);
            if (count($check_email) > 0) {
                $json = array(
                    "data" => 0,
                    "status" => "Email already exist! Please go to the first page and try with different email."
                );
                echo json_encode($json);
                die();
            }
        }

        //check nic already exist when update
        if ($student_deatils[0]['nic'] != $nic) {
            $check_nic = $cStudent->checkNicAlreadyExists($nic, $student_id);
            if (count($check_nic) > 0) {
                $json = array(
                    "data" => 0,
                    "status" => "NIC already exist! Please go to the first page and check again."
                );
                echo json_encode($json);
                die();
            }
        }

        //check passport_number already exist when update
        if ($passport_details[0]['passport_number'] != $_POST['passport_number'][0]) {
            $check_passport = $cStudent->checkPassportAlreadyExists($_POST['passport_number'][0], $student_id);
            if (count($check_passport) > 0) {
                $json = array(
                    "data" => 0,
                    "status" => "Passport Number already exist! Please go to the Passport & Travel History page and check again."
                );
                echo json_encode($json);
                die();
            }
        }

    } else {
        $res_email = $cStudent->getLogin($email);
        if (count($res_email)) {
            $json = array(
                "data" => 0,
                "status" => 'Email already exist! Please go to the first page and try with different email.'
            );
            echo json_encode($json);
            die();
        }
        
        //check nic already exist when save
        $check_nic = $cStudent->checkNicAlreadyExists($nic);
        if (count($check_nic) > 0) {
            $json = array(
                "data" => 0,
                "status" => "NIC already exist! Please go to the first page and check again."
            );
            echo json_encode($json);
            die();
        }

        //check passport_number already exist when save
        $check_passport = $cStudent->checkPassportAlreadyExists($_POST['passport_number'][0]);
        if (count($check_passport) > 0) {
            $json = array(
                "data" => 0,
                "status" => "Passport Number already exist! Please go to the Passport & Travel History page and check again."
            );
            echo json_encode($json);
            die();
        }

    }


    //format data 
    $date_of_birth = filter_input(INPUT_POST, 'date_of_birth');
    $marital_status = filter_input(INPUT_POST, 'marital_status');
    $country = filter_input(INPUT_POST, 'country');
    $state = filter_input(INPUT_POST, 'state');
    $city = filter_input(INPUT_POST, 'city');
    $user_type = 'ST';
    $profile_picture = filter_input(INPUT_POST, 'profile_picture');
    $email_validate_yn = 'N';
    $user_active_yn = filter_input(INPUT_POST, 'user_active_yn');
    $password = filter_input(INPUT_POST, 'password');
    $password_salt = '';

    //if is empty then generate 8 digit passwod
    if (empty($password)) {
        $password1 = substr(rand(1, 99999999), 0, 8);
        //encrypt password
        $password = convert_string('encrypt', $password1);
    } else {
        $old = $cStudent->getStudentByID($student_id);
        $oldPwd = $old[0]['password'];
        if ($oldPwd != $password) {
            $pwdChanged = 1;
            $password1 = $password;
            $password = convert_string('encrypt', $password);
        } else {
            $pwdChanged = 0;
            $password = $oldPwd;
        }
    }

    //get files urls
    $education_files_input = filter_input(INPUT_POST, 'education_files_input');
    $englishtest_files_input = filter_input(INPUT_POST, 'englishtest_files_input');
    $travel_files_input = filter_input(INPUT_POST, 'travel_files_input');
    $work_files_input = filter_input(INPUT_POST, 'work_files_input');
    $service_files_input = filter_input(INPUT_POST, 'service_files_input');
    // $other_files_input = filter_input(INPUT_POST, 'other_files_input');
    $cv_input = filter_input(INPUT_POST, 'cv_input');
    $sop_input = filter_input(INPUT_POST, 'sop_input');
    $lor_input = filter_input(INPUT_POST, 'lor_input');
    $moi_input = filter_input(INPUT_POST, 'moi_input');
    $refusal_input = filter_input(INPUT_POST, 'refusal_input'); // not required
    $medical_input = filter_input(INPUT_POST, 'medical_input');
    $offer_input = filter_input(INPUT_POST, 'offer_input');
    $cas_input = filter_input(INPUT_POST, 'cas_input');
    $loa_input = filter_input(INPUT_POST, 'loa_input');
    $receipt_input = filter_input(INPUT_POST, 'receipt_input');

    //emergency_contact 
    $emergency_contact_name = filter_input(INPUT_POST, 'emergency_contact_name');
    $emergency_contact_mobile = filter_input(INPUT_POST, 'emergency_contact_mobile');
    $emergency_contact_relation = filter_input(INPUT_POST, 'emergency_contact_relation');
    $remarks = filter_input(INPUT_POST, 'remarks');
    $real_time_status = filter_input(INPUT_POST, 'real_time_status');
    $assign_to_staff = filter_input(INPUT_POST, 'assign_to_staff');
    $assign_to_agent = filter_input(INPUT_POST, 'assign_to_agent');
    $assign_to_type = filter_input(INPUT_POST, 'assign_to_type');
    $category = filter_input(INPUT_POST, 'category');

    



    if (isset($_POST['travel_history_id'])) {
        $travel_history_id = $_POST['travel_history_id'];
        $travel_country_id = $_POST['travel_country_id'];
        $travel_departure = $_POST['travel_departure'];
        $travel_arrival = $_POST['travel_arrival'];
        $travel_reason = $_POST['travel_reason'];
    }
    if (isset($_POST['visa_refusal_id'])) {
        $visa_country_id = $_POST['visa_country_id'];
        $visa_refusal_id = $_POST['visa_refusal_id'];
        $visa_date = $_POST['visa_date'];
    }

    //passport details
    if (isset($_POST['passport_id'])) {
        $passport_id = $_POST['passport_id'];
        $passport_number = $_POST['passport_number'];
        $issued_date = $_POST['issued_date'];
        $expiry_date = $_POST['expiry_date'];
        $nationality = $_POST['nationality'];
        $passport_country_id = $_POST['passport_country_id'];
        $birth_country_id = $_POST['birth_country_id'];
    }

    //dependence details
    if (isset($_POST['dependence_id'])) {
        $dependence_id = $_POST['dependence_id'];
        $dependence_type = $_POST['dependence_type'];
        $dependence_age = $_POST['dependence_age'];
    }

    //disability details
    if (isset($_POST['disability_id'])) {
        $disability_id = $_POST['disability_id'];
        $description = $_POST['description'];
    }

    //education details
    if (isset($_POST['education_id'])) {
        $education_id = $_POST['education_id'];
        $education_country_id = $_POST['education_country_id'];
        $education_institute = $_POST['institute'];
        $education_program = $_POST['program'];
        $starting_date = $_POST['starting_date'];
        $end_date = $_POST['end_date'];
        $degree_type = $_POST['degree_type'];        
        $education_level = $_POST['education_level'];
        $study_language = $_POST['language'];
    }

    //english test details
    if (isset($_POST['english_test_id'])) {
        $english_test_id = $_POST['english_test_id'];
        $test_name = $_POST['tests_type'];
        $test_status = $_POST['test_status'];
        $test_date = $_POST['test_date'];
        $speaking = $_POST['speaking'];
        $reading = $_POST['reading'];
        $listening = $_POST['listening'];
        $writing = $_POST['writing'];
        $test_description = $_POST['test_description'];
    }

    //work details
    if (isset($_POST['work_id'])) {
        $work_id = $_POST['work_id'];
        $company_name = $_POST['companyname'];
        $position = $_POST['position'];
        $work_starting_date = $_POST['work_starting_date'];
        $work_end_date = $_POST['work_end_date'];
    }


    //profile picture upload
    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['name'] != "") {
        $file_name = $_FILES['profile_picture']['name'];
        $file_size = $_FILES['profile_picture']['size'];
        $file_tmp = $_FILES['profile_picture']['tmp_name'];
        $file_type = $_FILES['profile_picture']['type'];

        $exp = explode('.', $_FILES['profile_picture']['name']);
        $file_ext = strtolower(end($exp));

        // timestamp
        $time = time();

        //remove a file already exists
        if ($profile_picture != "") {
            if (file_exists("../" . $profile_picture)) {
                unlink("../" . $profile_picture);
            }
        }

        //upload the file
        if (move_uploaded_file($file_tmp, "../dist/uploads/student/" . $time . '.' . $file_ext)) {
            $profile_picture = "dist/uploads/student/" . $time . '.' . $file_ext;
        }
    }


    if ($profile_picture == "") {
        $profile_picture = 'dist/img/user2-160x160.jpg';
    }

    // Check if files and user data are empty then return error json

    if($student_id){
        // User update validate required fields
        if (
            empty($first_name) ||
            empty($last_name)||
            empty($nic)||
            empty($address)||
            empty($province)||
            empty($postal_code)||
            empty($mobile)||
            empty($gender)||
            empty($date_of_birth)||
            empty($marital_status)||
            empty($country)||
            empty($city)||
            empty($email) ||
            empty($emergency_contact_name) ||
            empty($emergency_contact_mobile) ||
            empty($emergency_contact_relation) ||
            empty($passport_number) ||
            empty($issued_date) ||
            empty($expiry_date) ||
            empty($nationality) ||
            empty($passport_country_id) ||
            empty($birth_country_id) ||
            empty($education_country_id) ||
            empty($education_institute) ||
            empty($education_program) ||
            empty($starting_date) ||
            empty($end_date) ||
            empty($degree_type) ||
            empty($education_level) ||
            empty($study_language) ||
            empty($test_name) ||
            empty($test_status)
        ) {
            $json = array(
                "data" => 0,
                "status" => "You must fill all the required fields",
            );
            // $json = array(
            //     "data" => 2,
            //     "status" => $_REQUEST,
            // );
            echo json_encode($json);
            die();
        }

    } else {
        // User create validate required fields
        if (
            empty($first_name) ||
            empty($last_name)||
            empty($nic)||
            empty($address)||
            empty($province)||
            empty($postal_code)||
            empty($mobile)||
            empty($gender)||
            empty($date_of_birth)||
            empty($marital_status)||
            empty($country)||
            empty($city)||
            empty($email) ||
            empty($emergency_contact_name) ||
            empty($emergency_contact_mobile) ||
            empty($emergency_contact_relation) ||
            empty($education_files_input) || 
            empty($englishtest_files_input) ||
            empty($travel_files_input) ||
            empty($cv_input) ||
            empty($sop_input) ||
            empty($lor_input) ||
            empty($moi_input) ||
            empty($medical_input) ||
            empty($offer_input) ||
            empty($cas_input) ||
            empty($loa_input) ||
            empty($receipt_input) ||
            empty($passport_number) ||
            empty($issued_date) ||
            empty($expiry_date) ||
            empty($nationality) ||
            empty($passport_country_id) ||
            empty($birth_country_id) ||
            empty($education_country_id) ||
            empty($education_institute) ||
            empty($education_program) ||
            empty($starting_date) ||
            empty($end_date) ||
            empty($degree_type) ||
            empty($education_level) ||
            empty($study_language) ||
            empty($test_name) ||
            empty($test_status)
        ) {
            $json = array(
                "data" => 0,
                "status" => "You must fill all the required fields",
            );
            // $json = array(
            //     "data" => 2,
            //     "status" => $_REQUEST,
            // );
            echo json_encode($json);
            die();
        }

    }

    // $json = array(
    //     "data" => 2,
    //     "status" => $_REQUEST,
    // );
    // echo json_encode($json);
    // die();

    

    //add to array 
    $criteria = array(
        'student_id' => $student_id,
        'first_name' => $first_name,
        'last_name' => $last_name,
        'nic' => $nic,
        'username' => $username,
        'email' => $email,
        'mobile' => $mobile,
        'category' => $category,
        'gender' => $gender,
        'date_of_birth' => $date_of_birth,
        'marital_status' => $marital_status,
        'address' => $address,
        'country' => $country,
        'province' => $province,
        'state' => $state,
        'city' => $city,
        'postal_code' => $postal_code,
        'password' => $password,
        'password_salt' => $password_salt,
        'user_type' => $user_type,
        'email_validate_yn' => $email_validate_yn,
        'profile_picture' => $profile_picture,
        'user_active_yn' => $user_active_yn,
        'user_id' => $user_id,
        'emergency_contact_name' => $emergency_contact_name,
        'emergency_contact_mobile' => $emergency_contact_mobile,
        'emergency_contact_relation' => $emergency_contact_relation,
        'remarks' => $remarks,
        'real_time_status' => $real_time_status,
        'assign_to_staff' => $assign_to_staff,
        'assign_to_type' => $assign_to_type,
        'assign_to_agent' => $assign_to_agent
    );

    $result = $cStudent->saveStudent($criteria);

    if ($result > 0) {

        // save docs
        //comma separated string to array
        $education_files_array = explode(',', $education_files_input);
        $englishtest_files_array = explode(',', $englishtest_files_input);
        $travel_files_array = explode(',', $travel_files_input);
        $work_files_array = explode(',', $work_files_input);
        $service_files_array = explode(',', $service_files_input);
        $cv_array = explode(',', $cv_input);
        $sop_array = explode(',', $sop_input);
        $lor_array = explode(',', $lor_input);
        $moi_array = explode(',', $moi_input);
        $refusal_array = explode(',', $refusal_input);
        $medical_array = explode(',', $medical_input);
        $offer_array = explode(',', $offer_input);
        $cas_array = explode(',', $cas_input);
        $loa_array = explode(',', $loa_input);
        $receipt_array = explode(',', $receipt_input);


        //save education docs
        if ($education_files_input != "") {
            foreach ($education_files_array as $key => $value) {
                $criteria_education = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'education'
                );

                $res = $cStudent->saveStudentDocument($criteria_education);
            }
        }

        //save englishtest docs
        if ($englishtest_files_input != "") {
            foreach ($englishtest_files_array as $key => $value) {
                $criteria_englishtest = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'englishtest'
                );

                $res = $cStudent->saveStudentDocument($criteria_englishtest);
            }
        }

        //save travel docs
        if ($travel_files_input != "") {
            foreach ($travel_files_array as $key => $value) {
                $criteria_travel = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'travel'
                );

                $res = $cStudent->saveStudentDocument($criteria_travel);
            }
        }

        //save work docs
        if ($work_files_input != "") {
            foreach ($work_files_array as $key => $value) {
                $criteria_work = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'work'
                );

                $res = $cStudent->saveStudentDocument($criteria_work);
            }
        }

        //save service docs
        if ($service_files_input != "") {
            foreach ($service_files_array as $key => $value) {
                $criteria_service = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'service'
                );

                $res = $cStudent->saveStudentDocument($criteria_service);
            }
        }

        //save cv
        if ($cv_input != "") {
            foreach ($cv_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'cv'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($sop_input != "") {
            foreach ($sop_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'sop'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($lor_input != "") {
            foreach ($lor_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'lor'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($moi_input != "") {
            foreach ($moi_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'moi'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($refusal_input != "") {
            foreach ($refusal_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'refusal'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($medical_input != "") {
            foreach ($medical_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'medical'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($offer_input != "") {
            foreach ($offer_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'offer'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($cas_input != "") {
            foreach ($cas_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'cas'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($loa_input != "") {
            foreach ($loa_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'loa'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        if ($receipt_input != "") {
            foreach ($receipt_array as $key => $value) {
                $criteria_other = array(
                    'student_document_id' => 0,
                    'student_id' => $result,
                    'doc_url' => $value,
                    'file_name' => 'receipt'
                );

                $res = $cStudent->saveStudentDocument($criteria_other);
            }
        }

        // save visa details
        if (isset($_POST['visa_refusal_id'])) {
            for ($i = 0; $i < count($visa_country_id); $i++) {
                $criteria_visa = array(
                    'visa_refusal_id' => $visa_refusal_id[$i],
                    'student_id' => $result,
                    'country' => $visa_country_id[$i],
                    'date' => format_date(date($visa_date[$i]), 3)
                );
                if ($criteria_visa['country'] != '') {
                    $r1 = $cStudent->saveStudentVisaDetails($criteria_visa);
                }
            }
        }

        // save travel details
        if (isset($_POST['travel_country_id'])) {
            for ($j = 0; $j < count($travel_country_id); $j++) {
                $criteria_travel_details = array(
                    'travel_history_id' => $travel_history_id[$j],
                    'student_id' => $result,
                    'country' => $travel_country_id[$j],
                    'travel_departure' => format_date(date($travel_departure[$j]), 3),
                    'travel_arrival' => format_date(date($travel_arrival[$j]), 3),
                    'travel_reason' => $travel_reason[$j]
                );
                $r2 = $cStudent->saveStudentTravelDetails($criteria_travel_details);
            }
        }

        //save education details
        if (isset($_POST['education_id'])) {
            for ($l = 0; $l < count($education_id); $l++) {
                $criteria_education = array(
                    'education_id' => $education_id[$l],
                    'student_id' => $result,
                    'education_country' => $education_country_id[$l],
                    'education_institute' => $education_institute[$l],
                    'education_program' => $education_program[$l],
                    'starting_date' => $starting_date[$l],
                    'end_date' => $end_date[$l],
                    'degree_type' => $degree_type[$l],
                    'education_level' => $education_level[$l],
                    'study_language' => $study_language[$l]
                );
                $r5 = $cStudent->saveStudentEducationDetails($criteria_education);
            }
        }

        //save passport details
        if (isset($_POST['passport_id'])) {
            for ($k = 0; $k < count($passport_country_id); $k++) {
                $criteria_passport = array(
                    'passport_id' => $passport_id[$k],
                    'student_id' => $result,
                    'passport_number' => $passport_number[$k],
                    'issued_country' => $passport_country_id[$k],
                    'passport_issue_date' => format_date(date($issued_date[$k]), 3),
                    'passport_expiry_date' => format_date(date($expiry_date[$k]), 3),
                    'nationality' => $nationality,
                    'birth_country' => $birth_country_id[$k],
                );

                $r3 = $cStudent->saveStudentPassportDetails($criteria_passport);
            }
        }


        if (isset($_POST['dependence_id'])) {
            for ($k = 0; $k < count($dependence_id); $k++) {
                $criteria_dependence = array(
                    'dependence_id' => $dependence_id[$k],
                    'student_id' => $result,
                    'dependence_type' => $dependence_type[$k],
                    'dependence_age' => $dependence_age[$k],
                );
                if ($criteria_dependence['dependence_type'] != 'select' && $criteria_dependence['dependence_age'] > 0) {
                    $r4 = $cStudent->saveStudentDependenceDetails($criteria_dependence);
                }
            }
        }

        //save disability details
        if (isset($_POST['disability_id'])) {
            for ($m = 0; $m < count($disability_id); $m++) {
                $criteria_disability = array(
                    'disability_id' => $disability_id[$m],
                    'student_id' => $result,
                    'description' => $description[$m]
                );
                if ($criteria_disability['description'] != '') {
                    $r6 = $cStudent->saveStudentDisabilityDetails($criteria_disability);
                }
            }
        }


        //save work details
        if (isset($_POST['work_id'])) {
            for ($n = 0; $n < count($work_id); $n++) {
                $criteria_work = array(
                    'work_id' => $work_id[$n],
                    'student_id' => $result,
                    'company_name' => $company_name[$n],
                    'position' => $position[$n],
                    'work_starting_date' => $work_starting_date[$n],
                    'work_end_date' => $work_end_date[$n]
                );
                if ($criteria_work['company_name'] != '') {
                    $r7 = $cStudent->saveStudentWorkDetails($criteria_work);
                }
            }
        }

        //save english test details
        if (isset($_POST['english_test_id'])) {
            for ($o = 0; $o < count($english_test_id); $o++) {
                $criteria_english_test = array(
                    'english_test_id' => $english_test_id[$o],
                    'student_id' => $result,
                    'test_name' => $test_name[$o],
                    'test_status' => $test_status[$o],
                    'reading' => ($test_status[$o] == 'taken' ? $reading[$o] : null),
                    'writing' => ($test_status[$o] == 'taken' ? $writing[$o] : null),
                    'listening' => ($test_status[$o] == 'taken' ? $listening[$o] : null),
                    'speaking' => ($test_status[$o] == 'taken' ? $speaking[$o] : null),
                    'test_date' => ($test_status[$o] == 'will_be_taking' ? $test_date[$o] : null),
                    'test_description' => ($test_name[$o] == 'other' ? $test_description[$o] : ''),
                );
                if ($criteria_english_test['test_name'] != '') {
                    $r8 = $cStudent->saveStudentEnglishTestDetails($criteria_english_test);
                }
            }
        }

        if ($student_id < 1 || $pwdChanged == 1) {
            $token_id = uniqid();
            $address = $email;
            $subject = 'Activate your account';

            $body = '<!DOCTYPE html>
                <html>
                
                <head>
                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
                    <style>
                        .header {
                            background-color: #293C4A;
                            padding: 20px;
                            font-size: 25px;
                            text-align: center;
                            font-weight: bolder;
                        }
                
                        .header h2 {
                            color: #fff;
                            margin-bottom: 0px;
                        }
                
                
                
                        .header p {
                            padding-top: 0px;
                            margin-top: 0px;
                            color: #fff;
                            font-size: 16px;
                        }
                
                    
                    </style>
                </head>
                
                <body>
                <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
                <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
                <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
                <tbody><tr>
                    <td align="center" valign="top">
                    <table width="650" border="0" cellspacing="0" cellpadding="0">
                        <tbody><tr>
                        <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                            
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                            
                                <tbody><tr>
                                    <td class="header">
                                        <h2 style="font-weight: 700;">Edvios</h2>
                                        <p style="padding-top: 30px;">The Student Portal</p>
                                    </td>
                                </tr>
                            </tbody></table>
                            
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody><tr>
                                <td>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                    <tbody><tr>
                                    <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tbody><tr>
                                            <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                            Hi ' . $last_name . '</td>
                                        </tr>
                                        
                                        </tbody></table>
                                    </td>
                                    </tr>
                                </tbody></table>
                                </td>
                            </tr>
                            </tbody></table>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tbody><tr>
                                        <td>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                            <tbody><tr>
                                                <td style="padding:0px 20px">
                                                <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                    <tbody><tr height="5px">
                                                    <td width="100%">
                                                        &nbsp;                              
                                                    </td>
                                                    </tr>
                                                    
                                                    <tr height="30px">
                                                    <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                                    <p> You have been registered to the Edvios by a system admin. Please click on the link below to verify your account.</p> 
                                                        <p> If you do not need to register, please ignore this email. .</p>                               
                                                    </td>                             
                                                    </tr>
            
                                                    <tr height="30px">
                                                    <td width="100%" style="color:#000000;text-align:left;font-weight:600;font-size:12px;line-height:20px">                             
                                                        <p>Use below credentials to login to your account</p>
                                                        <p>Username : ' . $email . ' <br> 
                                                        Password : ' . $password1 . '
                                                        </p>                             
                                                    </td>                             
                                                    </tr>
            
            
                                                    <tr height="25px">
                                                    <td width="100%" style="color:#000000;text-align:left;font-weight:700;font-size:14px;line-height:20px">                             
                                                    <a class="primary-btn" href="' . $base_url . 'student-activate-account.php?token_id=' . $token_id . '&st=' . $result . '">Verify Account</a>                            
                                                    </td>                             
                                                    </tr>
                
                                                    <tr>
                                                    <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                                        <p>Thank you.<br> Kind Regards,<br> Edvios</p>
                                                    </td>                             
                                                    </tr> 
                                                        <tr height="15px">
                                                            <td width="100%">&nbsp;</td>
                                                        </tr>
                                                                        
                                                                            
                                                </tbody></table>
                                                </td>
                                            </tr>
                                            </tbody></table>
                                        </td>
                                        </tr>
                                    </tbody></table>
                
                            
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tbody><tr>
                                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr>
                                    <td align="center" style="padding-bottom:10px">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                        <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                        <tr>
                                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                            <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                            <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                            
                                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                            <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                        </tr>							
                                        </tbody></table>
                                    </td>
                                    </tr>
                                    
                                
                                </tbody></table>
                                
                                </td>
                            </tr>
                            </tbody></table>
                            
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                            <tbody><tr>
                                <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                                    <tbody><tr>
                                    <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                        Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                                    </tr>
                                
                                
                                </tbody></table>
                                
                                </td>
                            </tr>
                            </tbody></table>
                            
                        </td>
                        </tr>
                    </tbody></table>
                    </td>
                </tr>
                </tbody></table>
                <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
                
                </body>
                
                </html>';


            send_mail($subject, $address, $body); // send mail

            // sms to student
            $message = "Your profile setup is complete. You're all set to proceed!.";       
            $student_phone_number = (string)$mobile;
            $cSms->send_sms($message, $student_phone_number); // send sms

            //messege to student           
            $sender_id=$user_id;
            $sender_type=$_SESSION['user']['user_type'];
            $default_messege="Welcome to Edvios";
            $cmessages->initializeMesssege($result, $sender_id, $sender_type, $default_messege);
        
        }
    }
 
    $json = array(
        "data" => $result,
        "status" => $result
    );
    echo json_encode($json);
    die();
}


// delete Student
if (isset($_POST['delete_student'])) {

    $student_id = filter_input(INPUT_POST, 'student_id');

    $result = $cStudent->deleteStudent($student_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteVisa'])) {

    $visa_refusal_id = filter_input(INPUT_POST, 'visa_refusal_id');

    $result = $cStudent->deleteVisa($visa_refusal_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteDependence'])) {

    $dependence_id = filter_input(INPUT_POST, 'dependence_id');

    $result = $cStudent->deleteDependence($dependence_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteDisability'])) {

    $disability_id = filter_input(INPUT_POST, 'disability_id');

    $result = $cStudent->deleteDisability($disability_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteTravel'])) {

    $travel_history_id = filter_input(INPUT_POST, 'travel_history_id');

    $result = $cStudent->deleteTravel($travel_history_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteEducation'])) {

    $education_id = filter_input(INPUT_POST, 'education_id');

    $result = $cStudent->deleteEducation($education_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['deleteWork'])) {

    $work_id = filter_input(INPUT_POST, 'work_id');

    $result = $cStudent->deleteWork($work_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Deleting')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['updateTimelineSave'])) {

    $student_id = filter_input(INPUT_POST, 'student_id');
    $timeline_id = filter_input(INPUT_POST, 'timeline_id');

    $result = $cStudent->updateStudentTimeline($student_id, $timeline_id);

    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}


if (isset($_POST['action']) && $_POST['action'] === 'getLastSeen') {
    $user = new user();
    $student_id = filter_input(INPUT_POST, 'student_id');
    $user_type = filter_input(INPUT_POST, 'user_type');
    $result = null;
    $last_seen = null;
    $last_seen_status = null;
    $first_name = null;


    if ($user_type === "ST") {
        $result = $user->calAdmiOnlineStatus();
        $last_seen = $result['last_seen'];
        $first_name = "Admin";
        $last_seen_status = last_seen($last_seen);
    } else {
        $result = $cStudent->calStudentOnlineStatus($student_id);
        $first_name = $result['first_name'];
        $last_seen = $result['last_seen'];
        $last_seen_status = last_seen($last_seen);
    }

    $response = array(
        'first_name' => $first_name,
        'last_seen_status' => $last_seen_status,
        'last_seen' => $last_seen
    );

    echo json_encode($response);
    exit;
}


if (isset($_POST['action']) && $_POST['action'] === 'getCountryRelatedProvince') {
    $country_id = filter_input(INPUT_POST, 'country_id', FILTER_SANITIZE_STRING);
    $student = new Student();
    $result = $student->getCountryRelatedProvince($country_id);
   // Output the data as JSON
    //header("Content-Type: application/json");
    echo json_encode($result);
    exit; // Terminate the script after sending the response
}

