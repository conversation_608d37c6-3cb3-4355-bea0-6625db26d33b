<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
// if(isset($_SESSION['error'])){
//     print_r($_SESSION['error']);
//     die();
// }
?>

<?php
//show back button
if(isset($_SESSION['previous_page_url']) && $_SESSION['previous_page_url']!="" && $_SESSION['previous_page_title']!=""){ ?>
    <div class="x_title" >
        <div  role="alert" >            
            <div> <a href='<?=$_SESSION['previous_page_url'];?>' class="btn btn-dark">Return to <?=$_SESSION['previous_page_title']?></a>

            </div>
        </div>
    </div>
<?php }

if (isset($_SESSION['message_toast']) && $_SESSION['message_toast'] != "") {
    $session_message_toast=$_SESSION['message_toast'];
    ?>
    <script type="text/javascript">
        $(function(){
            toastr.success(' <?php echo $session_message_toast; ?>');
        });
    </script>
    <?php
    //die("<br>displayed");
    $_SESSION['message_toast'] = "";
    $_SESSION['message'] = "";
    $_SESSION['error'] = "";
}elseif (isset($_SESSION['message']) && $_SESSION['message'] != "") {
    $session_message=$_SESSION['message'];
    ?>
    <div class="alert alert-success alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        <i class="icon fas fa-check"></i> <?php echo $session_message; ?>
    </div>
    <?php
    //die("<br>displayed");
    $_SESSION['message_toast'] = "";
    $_SESSION['message'] = "";
    $_SESSION['error'] = "";
}elseif (isset($_SESSION['warning']) && $_SESSION['warning'] != "") {
    $session_message=$_SESSION['warning'];
    ?>
    <div class="alert alert-warning alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        <i class="icon fas fa-exclamation-circle"></i> <?php echo $session_message; ?>
    </div>
    <?php
    //die("<br>displayed");
    $_SESSION['message_toast'] = "";
    $_SESSION['warning'] = "";
    $_SESSION['message'] = "";
    $_SESSION['error'] = "";
}elseif (isset($_SESSION['error']) && $_SESSION['error'] != "") {
    $session_error=$_SESSION['error'];
    ?>
    <div class="alert alert-danger alert-dismissible">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
        <i class="icon fas fa-ban"></i>
        <?php echo $session_error; ?>
    </div>

    <?php
    //die("<br>displayed");
    $_SESSION['message_toast'] = "";
    $_SESSION['message'] = "";
    $_SESSION['error'] = "";
}


unset($_SESSION['message_toast']);
unset($_SESSION['message']);
unset($_SESSION['error']);
?>
