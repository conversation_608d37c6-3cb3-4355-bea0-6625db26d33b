<!-- Navbar -->
<!-- Navbar Admin-->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <!-- Left navbar links -->
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">

        <!-- Messages Dropdown Menu -->
        <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
                <i class="fa fa-inbox"></i>
                <span class="badge badge-danger navbar-badge count_trainings"></span>
            </a>
            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <a href="#" class="dropdown-item trainings_div">

                    <!-- Message End -->
                </a>
                <div class="dropdown-divider"></div>
                <a href="training-request-list.php" class="dropdown-item dropdown-footer">See All</a>
            </div>
        </li>
        <!-- Notifications Dropdown Menu -->
        <li class="nav-item dropdown">
            <a class="nav-link" data-toggle="dropdown" href="#">
                <i class="far fa-bell"></i>
                <span class="badge badge-danger navbar-badge notification_count"></span>
            </a>
            <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right top_notification_bar">
                <span class="dropdown-header"><span class="notification_count"></span> Notifications</span>
                <div class="dropdown-divider notification_div"></div>
                <a href="#" class="dropdown-item  notification_div">
                    <i class="fas fa-bell mr-2"></i> No Notifications <br>
                    <span class=" text-muted text-sm pl-4"></span>
                </a>

            </div>
        </li>
        <li class="nav-item dropdown user-menu">
            <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="false">

                <img class="user-image img-circle elevation-2" src="dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.jpg" onerror="this.onerror=null;
               this.src='dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.png';
               this.onerror=null;
               this.src='dist/img/avatar.png';" alt="User Image">



                <span class="d-none d-md-inline"><?php echo $_SESSION['user']['first_name'] ?></span>
            </a>
            <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right" style="left: inherit; right: 0px;">
                <!-- User image -->
                <li class="user-header bg-teal">

                    <img class="user-image img-circle elevation-2" src="dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.jpg" onerror="this.onerror=null;
               this.src='dist/img/<?= htmlspecialchars($_SESSION['user']['first_name']) ?>.png';
               this.onerror=null;
               this.src='dist/img/avatar.png';" alt="User Image">




                    <p>
                        <?php echo $_SESSION['user']['first_name'] ?>
                        <small>Admin</small>
                    </p>
                </li>
                <!-- Menu Body -->

                <!-- Menu Footer-->
                <li class="user-footer text-center">
                    <!-- <a href="#" class="btn btn-default btn-flat">Profile</a> -->
                    <a href="logout.php" class="btn btn-default btn-flat">Sign out</a>
                </li>
            </ul>
        </li>
    </ul>
</nav>
<!-- /.navbar -->

<style>
    .label-container {
        position: fixed;
        bottom: 32px;
        right: 95px;
        display: table;
        visibility: hidden;
        z-index: 1000;
    }

    .label-text {
        color: #FFF;
        background: rgba(51, 51, 51, 0.5);
        display: table-cell;
        vertical-align: middle;
        padding: 10px;
        border-radius: 3px;
        z-index: 1000;
    }

    .label-arrow {
        display: table-cell;
        vertical-align: middle;
        color: #333;
        opacity: 0.5;
        z-index: 1000;
    }

    .float {
        position: fixed;
        width: 60px;
        height: 60px;
        bottom: 35px;
        right: 30px;
        border-radius: 50px;
        text-align: center;
        box-shadow: 2px 2px 3px #999;
        z-index: 1000;
    }

    .my-float {
        font-size: 24px;
        margin-top: 18px;
    }

    a.float+div.label-container {
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.5s ease;
    }

    a.float:hover+div.label-container {
        visibility: visible;
        opacity: 1;
    }
</style>
<!-- set widget URL -->
<a href="message-student-list.php" class="float bg-teal message_a">
    <i class="fa fa-comments my-float"></i>
    <span class="badge badge-danger navbar-badge count_msg_cu">0</span>
</a>
<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/toastr/toastr.min.js"></script>
<script>
    $(document).ready(function() {

        const notification_count = '';

        // string contain url
        var url = window.location;
        var pathname = url.pathname;
        var str_match = pathname.match(/message-student-list.php/);
        if (str_match) {
            //    console.log(str_match);
            $(".message_a").hide('slow');
        }


        var lastScrollTop = 0;
        $(window).scroll(function(event) {
            if (str_match) {} else {
                var st = $(this).scrollTop();
                if (st > lastScrollTop) {
                    // downscroll code
                    $('.float').hide('medium');
                } else {
                    // upscroll code
                    $('.float').show('medium');
                }
                lastScrollTop = st;

            }

        });

        getRemainders();
        getTrainings();

        //getCountCustomerRequest();

        setInterval(function() {
            getUnseenMsg();
            //getCountCustomerRequest();
            getRemainders();
            updatelastseen();
        }, 1000);

        function updatelastseen() {
            $.ajax({
                method: "post",
                url: "update-last-seen.php",
                data: {
                    action: "UpdateLastSeen"
                },
            })
        }

        function getUnseenMsg() {
            $.ajax({
                method: "post",
                url: "get-unseen-msg.php",
                data: {
                    getunseenmsg: 1
                },
                dataType: "json",
                success: function(data) {
                    // console.log('Response from server:', data);

                    try {
                        var unreadCount = parseInt(data.data); 

                        if (!isNaN(unreadCount)) {
                            var badgeElement = $('.count_msg_cu');

                            if (unreadCount > 0) {
                                badgeElement.show('medium').text(unreadCount); 
                            } else {
                                badgeElement.hide();
                            }
                        } else {
                            console.error('Invalid data format in JSON response.');
                        }
                    } catch (error) {
                        console.error('Error parsing JSON response:', error);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error fetching unseen messages:', error);
                }
            });
        }

        $(document).on('click', '.remainder_id', function() {
            var remainder_id = $(this).data('remainder_id');
            var title_name = $(this).data('title');
            var note = $(this).data('note');
            //console.log(remainder_id);

            Swal.fire({
                title: "" + title_name + "",
                text: "" + note + "",
                showDenyButton: false,
                showCancelButton: false,
                confirmButtonText: 'Close',
            }).then((result) => {
                /* Read more about isConfirmed, isDenied below */
                $.ajax({
                    type: "POST",
                    url: "controller/remainder-controller.php",
                    data: {
                        "updateRemainder": 1,
                        "active_yn": 'N',
                        "remainder_id": remainder_id
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);

                    }
                });
            })

        });

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": false,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": false,
        }

        $(document).on('click', '.training_div', function() {
            console.log('awe');
            window.location.href = "training-request-list.php";
        });

        function getTrainings() {
            $.ajax({
                type: "POST",
                url: "controller/training-controller.php",
                data: {
                    "getAllTrainings": 1
                },
                dataType: 'json',
                success: function(data) {
                    var trainings = data.data;
                    //if training has data for each training
                    $('.trainings_div').html('');
                    if (trainings.length > 0) {
                        //$('.notification_count').html(trainings.length);

                        var count_trainings = 0;
                        //$('.training_div').addClass('d-none');
                        trainings.forEach(element => {
                            // element.training_date
                            //check datetime if passed then training show
                            var active_yn = element.active_yn;

                            if (active_yn == 'Y') {
                                //console.log(element);
                                count_trainings++;
                                $('.trainings_div').append(
                                    ' <div class="media training_div"> ' +
                                    '<div class="media-body">' +
                                    '<p><b>' + element.name + '</b></p>' +
                                    '<p class = "text-sm" >' + element.training_type +
                                    ' training request.</p>' +
                                    '<br>' +
                                    '</div>' +
                                    '</div>'
                                );
                            }


                        });

                        if (count_trainings > 0) {
                            $('.count_trainings').html(count_trainings);
                        } else {
                            $('.count_trainings').html('');
                        }
                        //console.log(data.data);

                    } else {

                    }

                }
            });
        }

        function getRemainders() {
            $.ajax({
                type: "POST",
                url: "controller/remainder-controller.php",
                data: {
                    "getAllremainders": 1
                },
                dataType: 'json',
                success: function(data) {
                    // console.log(data);
                    var remainders = data.data;
                    //if remainder has data for each remainder
                    if (remainders.length > 0) {
                        //$('.notification_count').html(remainders.length);

                        var count_remainders = 0;
                        $('.notification_div').addClass('d-none');
                        remainders.forEach(element => {
                            // element.remainder_date
                            //check datetime if passed then remainder show
                            var remainder_date = new Date(element.remainder_date);
                            var current_date = new Date();
                            var diff = current_date - remainder_date;
                            //console.log(diff);
                            if (parseInt(diff) < 0 && parseInt(diff) > -1000) {

                                toastr.info(element.title);


                            } else if (remainder_date > current_date) {

                            } else {

                                count_remainders = parseInt(count_remainders) + 1;
                                $('.top_notification_bar').append(
                                    '<div class="dropdown-dividernotification_div"></div>' +
                                    '<a class="dropdown-item  notification_div remainder_id " data-remainder_id="' +
                                    element.remainder_id + '" data-title="' + element
                                    .title + '" data-note="' + element.note + '" ' +
                                    '>' +
                                    '<i class="fas fa-bell mr-2"></i> ' +
                                    element.title +
                                    '' +
                                    '<br><span class="text-muted text-sm pl-4">' +
                                    element
                                    .remainder_date + '</span>' +
                                    '</a>'
                                )
                                //console.log(element.remainder_date);

                            }
                        });

                        if (count_remainders > 0) {
                            $('.notification_count').html(count_remainders);
                        } else {
                            $('.notification_count').html('');
                        }
                        //console.log(data.data);

                    } else {

                    }

                }
            });
        }
    });
</script>