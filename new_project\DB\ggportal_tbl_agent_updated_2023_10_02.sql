-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 02, 2023 at 06:06 AM
-- Server version: 10.4.28-MariaDB
-- PHP Version: 8.2.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ggportal_tbl_agent`
--

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_agent`
--

CREATE TABLE `ggportal_tbl_agent` (
  `agent_id` int(11) NOT NULL,
  `parent_agent_id` int(11) NOT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(20) NOT NULL,
  `country` varchar(200) NOT NULL,
  `city` varchar(100) NOT NULL,
  `br_document` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `profile_picture` text NOT NULL,
  `user_access_level` int(11) NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `active_yn` varchar(1) NOT NULL,
  `agreement_signed_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `commission_rate` int(11) NOT NULL,
  `last_seen` datetime DEFAULT NULL,
  `online_status` int(1) NOT NULL DEFAULT 0,
  `ag_document` varchar(255) NOT NULL,
  `assigned_staff` int(11) NOT NULL DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_agent`
--

INSERT INTO `ggportal_tbl_agent` (`agent_id`, `parent_agent_id`, `first_name`, `last_name`, `username`, `email`, `mobile`, `country`, `city`, `br_document`, `password`, `password_salt`, `user_type`, `profile_picture`, `user_access_level`, `email_validate_yn`, `active_yn`, `agreement_signed_yn`, `commission_rate`, `last_seen`, `online_status`, `ag_document`, `assigned_staff`) VALUES
(35, 0, 'AGED', '2', '<EMAIL>', '<EMAIL>', ' Choose Country78777', 'Sri Lanka', 'Colombo', '', 'eGlDUGNxbkVSYm5tNlB2SjhMdHVGZz09', '', 'AG', 'dist/uploads/agent/1692776919.jpg', 100, 'Y', 'Y', 'Y', 0, '2023-09-28 17:35:39', 0, 'dist/uploads/agent/1695186218.pdf', 56),
(39, 0, 'we', 'ecc', '<EMAIL>', '<EMAIL>', ' Choose Country46865', 'American Samoa', 'fdsd', '', 'VWpkZ2Rmak8zamc5RGlydTNuT2Vwdz09', '', 'AG', 'dist/uploads/agent/1692776903.jpg', 100, 'Y', 'Y', 'Y', 0, NULL, 0, '', 56);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_application_log`
--

CREATE TABLE `ggportal_tbl_application_log` (
  `application_log_id` int(11) NOT NULL,
  `student_application_id` int(11) NOT NULL,
  `status_id` int(11) NOT NULL,
  `updated_date` datetime DEFAULT NULL,
  `remarks` varchar(120) NOT NULL,
  `due_date` datetime NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'N',
  `special_note` varchar(100) DEFAULT NULL,
  `date` date NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_application_log`
--

INSERT INTO `ggportal_tbl_application_log` (`application_log_id`, `student_application_id`, `status_id`, `updated_date`, `remarks`, `due_date`, `active_yn`, `special_note`, `date`) VALUES
(209, 57, 11, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(208, 57, 10, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(207, 57, 9, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(206, 57, 8, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(205, 57, 7, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(204, 57, 6, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(203, 57, 5, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(202, 57, 1, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(201, 57, 2, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(200, 57, 3, '2023-08-23 13:17:15', '', '2022-12-31 20:06:11', 'Y', NULL, '0000-00-00'),
(199, 57, 4, NULL, '', '2022-12-31 20:06:11', 'N', NULL, '0000-00-00'),
(198, 56, 11, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(197, 56, 10, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(196, 56, 9, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(195, 56, 8, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(194, 56, 7, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(193, 56, 6, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(192, 56, 5, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(191, 56, 1, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(190, 56, 2, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(189, 56, 3, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(187, 55, 11, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(188, 56, 4, NULL, '', '2022-12-31 19:46:49', 'N', NULL, '0000-00-00'),
(186, 55, 10, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(185, 55, 9, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(184, 55, 8, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(183, 55, 7, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(182, 55, 6, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(181, 55, 5, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(180, 55, 1, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(179, 55, 2, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(178, 55, 3, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(177, 55, 4, NULL, '', '2022-12-22 02:07:41', 'N', NULL, '0000-00-00'),
(176, 54, 11, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(175, 54, 10, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(174, 54, 9, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(173, 54, 8, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(172, 54, 7, '2023-08-02 15:26:26', '', '2022-10-17 10:13:29', 'Y', NULL, '0000-00-00'),
(171, 54, 6, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(170, 54, 5, '2023-08-02 15:26:07', '', '2022-10-17 10:13:29', 'Y', NULL, '0000-00-00'),
(169, 54, 1, '2022-10-17 10:13:47', '', '2022-10-17 10:13:29', 'Y', NULL, '0000-00-00'),
(168, 54, 2, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(167, 54, 3, '2022-12-21 17:13:20', 'Sample note', '2022-10-17 10:13:29', 'Y', 'Test note', '0000-00-00'),
(166, 54, 4, NULL, '', '2022-10-17 10:13:29', 'N', NULL, '0000-00-00'),
(165, 53, 11, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(164, 53, 10, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(163, 53, 9, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(162, 53, 8, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(161, 53, 7, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(160, 53, 6, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(159, 53, 5, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(158, 53, 1, '2022-10-17 08:42:58', '', '2022-10-17 00:00:00', 'Y', 'Upload your SOP\r\nUpdate your CV', '0000-00-00'),
(157, 53, 2, '2022-10-17 08:40:37', 'Top up your existing qualifications to an honours degree', '2022-10-17 00:00:00', 'Y', '', '0000-00-00'),
(156, 53, 3, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(155, 53, 4, NULL, '', '2022-10-17 08:40:09', 'N', NULL, '0000-00-00'),
(210, 58, 4, '2023-09-22 09:47:03', 'CO', '2023-08-02 16:09:47', 'Y', NULL, '2023-12-01'),
(211, 58, 3, '2023-09-22 15:34:28', '', '2023-08-02 16:09:47', 'Y', NULL, '2023-09-21'),
(212, 58, 2, '2023-08-23 13:17:57', '', '2023-08-02 16:09:47', 'Y', NULL, '0000-00-00'),
(213, 58, 1, '2023-09-22 13:52:38', '', '2023-08-02 16:09:47', 'Y', NULL, '2023-10-02'),
(214, 58, 5, '2023-09-22 15:35:01', '', '2023-08-02 16:09:47', 'Y', NULL, '2023-08-09'),
(215, 58, 6, '2023-09-11 09:54:46', '', '2023-08-02 16:09:47', 'Y', NULL, '0000-00-00'),
(216, 58, 7, '2023-09-22 09:41:23', 'cl', '2023-08-02 16:09:47', 'Y', NULL, '2023-10-01'),
(217, 58, 8, NULL, '', '2023-08-02 16:09:47', 'N', NULL, '0000-00-00'),
(218, 58, 9, NULL, '', '2023-08-02 16:09:47', 'N', NULL, '0000-00-00'),
(219, 58, 10, NULL, '', '2023-08-02 16:09:47', 'N', NULL, '0000-00-00'),
(220, 58, 11, NULL, '', '2023-08-02 16:09:47', 'N', NULL, '0000-00-00');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_application_status`
--

CREATE TABLE `ggportal_tbl_application_status` (
  `application_status_id` int(11) NOT NULL,
  `status_name` varchar(45) NOT NULL,
  `status_code` varchar(45) NOT NULL,
  `date_count` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_application_status`
--

INSERT INTO `ggportal_tbl_application_status` (`application_status_id`, `status_name`, `status_code`, `date_count`) VALUES
(1, 'Application Received', ' AR', 30),
(2, 'Application Undereviewed ', ' AU', 23),
(3, 'Program Apply', ' PA', 14),
(4, 'Conditional Offer', ' CO', 7),
(5, 'Unconditional Offer', ' UO', 25),
(7, 'CAS/LOA Issued', ' CL', 30),
(8, 'VISA Granted', ' VG', 32),
(9, 'University Enrolled', ' UE', 32),
(10, 'Commission', ' C', 42),
(11, 'Refund', ' Rd', 42),
(12, 'Reappeal', ' Rl', 42),
(13, 'Differment', ' D', 10),
(14, ' Processing Completed', 'PC', 30),
(6, 'University Payment', 'UP', 25);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_bank`
--

CREATE TABLE `ggportal_tbl_bank` (
  `bank_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(255) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `account_no` varchar(100) NOT NULL,
  `bank_name` varchar(150) NOT NULL,
  `branch_name` varchar(150) NOT NULL,
  `branch_code` varchar(10) NOT NULL,
  `swift_code` varchar(15) NOT NULL,
  `bank_code` varchar(50) NOT NULL,
  `iban` varchar(50) NOT NULL,
  `is_active` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_bank`
--

INSERT INTO `ggportal_tbl_bank` (`bank_id`, `user_id`, `user_type`, `account_name`, `account_no`, `bank_name`, `branch_name`, `branch_code`, `swift_code`, `bank_code`, `iban`, `is_active`) VALUES
(6, 3, 'AG', 'N/A', 'N/A', 'N/A', 'N/A', '', '', 'N/A', '', 'Y'),
(7, 9, 'AG', 'rr', '545665', 'ff', 'rdtft', '554', '45454', '566', '4343', 'Y'),
(8, 10, 'AG', 'feef', '333', 'ddfs', 'werw', '2343', '234', '3423', '234', 'Y'),
(9, 12, 'AG', '4t', '443', 'fdfg', 'frefe', '23243', '34534', '3453', '43543', 'Y'),
(16, 19, 'AG', 'AD', '356869', 'EG', 'EGG', '242', '54', '53', '24', 'Y'),
(17, 20, 'AG', 'adw', '243523', 'fge', 'Ee', '3545', '435346', '345', '436346', 'Y'),
(18, 21, 'AG', 't4et', '3353', 'rr', 'dfgt', '35', '345', '4343', '436', 'Y'),
(19, 22, 'AG', 'tre', '45646', 'gdfgd', 'gr', '434', '4534', '545', '34', 'Y'),
(20, 23, 'AG', 'W', '46758', 'WE', 'frefe', '', '', '', '', 'Y'),
(21, 24, 'AG', 'ww', '324234', 'fdfg', 'C', '546', '', '', '', 'Y'),
(22, 25, 'AG', 'ww', '443', 'fdfg', 'frefe', '234', '22432', '234234', '', 'Y'),
(23, 26, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(24, 27, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(25, 28, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(26, 29, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(27, 30, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(28, 30, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(29, 30, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(30, 30, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(31, 30, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(32, 31, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(33, 31, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(34, 31, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(35, 31, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(36, 31, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(37, 32, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(38, 32, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(39, 32, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(40, 32, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(41, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(42, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(43, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(44, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(45, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(46, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(47, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(48, 33, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(49, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(50, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(51, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(52, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(53, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(54, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(55, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(56, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(57, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(58, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(59, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(60, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(61, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(62, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(63, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(64, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(65, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(66, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(67, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(68, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(69, 34, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(70, 35, 'AG', 'ww', '443', 'B', 'C', '234', '22432', '234234', '', 'Y'),
(71, 29, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(72, 36, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(73, 36, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(74, 37, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(75, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(76, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(77, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(78, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(79, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(80, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(81, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(82, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(83, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(84, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(85, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(86, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(87, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(88, 38, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(89, 36, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(90, 36, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(91, 36, 'AG', '', '', '', '', '', '', '', '', 'Y'),
(92, 39, 'AG', 'ef', '7554', 'fdfg', 'frefe', '23243', '22432', '3453', 'rt57', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_city`
--

CREATE TABLE `ggportal_tbl_city` (
  `city_id` int(11) NOT NULL,
  `state_id` int(11) NOT NULL,
  `city_name` varchar(45) NOT NULL,
  `city_code` varchar(45) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_commission`
--

CREATE TABLE `ggportal_tbl_commission` (
  `commission_id` int(11) NOT NULL,
  `institute_id` int(11) NOT NULL,
  `rate` varchar(45) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_commission`
--

INSERT INTO `ggportal_tbl_commission` (`commission_id`, `institute_id`, `rate`) VALUES
(3, 3, 'AUD 3780'),
(4, 4, '0.1'),
(5, 5, '0.09'),
(6, 6, '0.11'),
(7, 7, '0.09'),
(8, 8, '0.09'),
(9, 9, '0.09'),
(10, 10, '0.09'),
(11, 11, '0.09'),
(12, 12, '0.13'),
(13, 13, '0.18'),
(14, 14, '0.09'),
(15, 15, '0.09'),
(16, 16, '0.13'),
(17, 17, '0.09'),
(18, 18, '0.1'),
(19, 19, '0.0875'),
(20, 20, '0.13'),
(21, 21, '0.13'),
(22, 22, '0.16'),
(23, 23, '0.1'),
(24, 24, '0.09'),
(25, 25, '0.13'),
(26, 26, '0.09'),
(27, 27, '0.0875'),
(29, 29, '0.105'),
(30, 30, '0.09'),
(31, 31, '0.13'),
(32, 32, '0.13'),
(33, 33, '0.09'),
(34, 34, '0.13'),
(35, 35, '0.09'),
(36, 36, '0.1'),
(37, 37, '0.1'),
(38, 38, '0.18'),
(40, 40, '0.1'),
(41, 41, '0.1'),
(42, 42, '0.07'),
(43, 43, '0.13'),
(44, 44, '0.13'),
(45, 45, '0.09'),
(46, 46, '0.09'),
(47, 47, '0.09'),
(48, 48, '0.22'),
(49, 49, '0.21'),
(50, 50, '0.07'),
(51, 51, '0.09'),
(52, 52, '0.1'),
(53, 53, '0.18'),
(54, 54, '0.18'),
(55, 55, '0.18'),
(56, 56, '0.09'),
(57, 57, '0.13'),
(58, 58, '0.09'),
(59, 59, '0.105'),
(60, 60, '0.13'),
(61, 61, '0.13'),
(62, 62, '0.13'),
(63, 63, '0.13'),
(64, 64, '0.09'),
(65, 65, '0.1'),
(66, 66, '0.09'),
(67, 67, '0.0875'),
(68, 68, '0.09'),
(69, 69, '0.13'),
(70, 70, '0.13'),
(71, 71, '0.09'),
(72, 72, '0.09'),
(73, 73, '0.09'),
(74, 74, '0.1025'),
(75, 75, '0.09'),
(76, 76, '0.105'),
(77, 77, '0.13'),
(78, 78, '0.09'),
(79, 79, '0.09'),
(81, 81, '0.13'),
(82, 82, '0.09'),
(83, 83, '0.09'),
(84, 84, '0.09'),
(85, 85, '0.13'),
(86, 86, '0.13'),
(87, 87, '0.13'),
(88, 88, '0.13'),
(89, 89, '0.13'),
(90, 90, '0.13'),
(91, 91, '0.14'),
(92, 92, '0.18'),
(93, 93, '0.1'),
(94, 94, '0.18'),
(95, 95, '0.18'),
(96, 96, 'USD 600'),
(97, 97, '0.18'),
(98, 98, '0.18'),
(99, 99, '0.18'),
(101, 101, '0.13'),
(102, 102, '0.16'),
(103, 103, 'CAD 2800'),
(104, 104, '0.16'),
(105, 105, 'CAD 1260'),
(106, 106, '0.12'),
(107, 107, '0.12'),
(108, 108, '0.13'),
(109, 109, '0.2'),
(110, 110, '0.12'),
(111, 111, '0.12'),
(112, 112, 'CAD 520'),
(113, 113, 'CAD 1800'),
(114, 114, 'CAD 1800'),
(115, 115, '0.16'),
(116, 116, '0.19'),
(117, 117, '0.16'),
(118, 118, '0.15'),
(119, 119, '0.14'),
(120, 120, '0.14'),
(121, 121, '0.18'),
(122, 122, '0.15'),
(123, 123, '0.16'),
(124, 124, 'CAD 1380'),
(125, 125, '0.16'),
(126, 126, '0.14'),
(127, 127, '0.2'),
(128, 128, 'CAD 2925'),
(129, 129, '0.2'),
(130, 130, 'CAD 1600'),
(131, 131, '0.16'),
(132, 132, 'CAD 1600'),
(133, 133, 'CAD 1600'),
(134, 134, '0.12'),
(135, 135, '0.12'),
(136, 136, '0.12'),
(137, 137, '0.2'),
(138, 138, 'USD 2000'),
(139, 139, '0.12'),
(140, 140, '0.12'),
(141, 141, '0.16'),
(142, 142, '0.12'),
(143, 143, '0.12'),
(144, 144, '0.13'),
(145, 145, 'CAD 1200'),
(146, 146, '0.12'),
(147, 147, '0.1'),
(148, 148, '0.16'),
(149, 149, '0.12'),
(150, 150, 'CAD 1920'),
(151, 151, '0.2'),
(152, 152, '0.084'),
(153, 153, '0.096'),
(154, 154, 'CAD 1500'),
(155, 155, 'CAD 1600'),
(156, 156, '0.15'),
(157, 157, '0.14'),
(158, 158, 'CAD 1800'),
(159, 159, '0.15'),
(160, 160, '0.12'),
(161, 161, 'CAD 1680'),
(162, 162, '0.2'),
(163, 163, '0.12'),
(164, 164, '0.144'),
(165, 165, '0.15'),
(166, 166, '0.15'),
(167, 167, 'CAD 800'),
(168, 168, '0.08'),
(169, 169, 'CAD 1400'),
(170, 170, '0.15'),
(171, 171, '0.176'),
(172, 172, '0.176'),
(173, 173, '0.12'),
(174, 174, '0.12'),
(175, 175, '0.1'),
(176, 176, '0.12'),
(177, 177, '0.12'),
(178, 178, '0.12'),
(179, 179, 'CAD 2400'),
(180, 180, '0.12'),
(181, 181, 'CAD 1800'),
(182, 182, '0.16'),
(183, 183, 'CAD 1600'),
(184, 184, '0.14'),
(185, 185, '0.12'),
(186, 186, '0.12'),
(187, 187, '0.16'),
(188, 188, '0.16'),
(189, 189, 'CAD 1456'),
(190, 190, '0.12'),
(191, 191, 'CAD 2000'),
(192, 192, '0.056'),
(193, 193, 'CAD 2400'),
(194, 194, '0.12'),
(195, 195, '0.12'),
(196, 196, 'CAD 2800'),
(197, 197, '0.12'),
(198, 198, '0.096'),
(199, 199, '0.096'),
(200, 200, '0.15'),
(201, 201, '0.08'),
(202, 202, '0.16'),
(203, 203, '0.11'),
(204, 204, '0.15'),
(205, 205, '0.12'),
(206, 206, '0.16'),
(207, 207, '0.12'),
(208, 208, '0.12'),
(209, 209, 'CAD 1600'),
(210, 210, '0.12'),
(211, 211, 'CAD 1200'),
(212, 212, '0.2'),
(213, 213, 'CAD 900'),
(214, 214, '0.1'),
(215, 215, '0.1'),
(216, 216, '0.144'),
(217, 217, '0.1'),
(218, 218, '0.1'),
(219, 219, 'CAD 882'),
(220, 220, '0.16'),
(221, 221, 'CAD 1900'),
(222, 222, '0.1'),
(223, 223, '0.12'),
(224, 224, '0.12'),
(225, 225, '0.1'),
(226, 226, '0.12'),
(227, 227, 'CAD 1500'),
(228, 228, '0.12'),
(229, 229, 'CAD 2300'),
(230, 230, '0.11'),
(231, 231, '0.12'),
(232, 232, '0.12'),
(233, 233, '0.2'),
(234, 234, '0.22'),
(235, 235, 'CAD 4000'),
(236, 236, '0.18'),
(237, 237, 'CAD 2500'),
(238, 238, '0.16'),
(239, 239, '0.12'),
(240, 240, '0.2'),
(241, 241, 'USD 740'),
(242, 242, '0.12'),
(243, 243, '0.15'),
(244, 244, '0.12'),
(245, 245, 'USD 2500'),
(246, 246, '0.16'),
(247, 247, '0.08'),
(248, 248, '0.07'),
(249, 249, '0.1'),
(250, 250, '0.07'),
(251, 251, '0.07'),
(252, 252, '0.1'),
(253, 253, '0.08'),
(254, 254, '0.12'),
(255, 255, '0.15'),
(256, 256, '0.16'),
(257, 257, '0.1'),
(258, 258, 'EUR 1800'),
(259, 259, '0.12'),
(260, 260, 'EUR 840'),
(261, 261, '0.17'),
(262, 262, '0.15'),
(263, 263, '0.16'),
(264, 264, '0.14'),
(265, 265, '0.105'),
(266, 266, '0.105'),
(267, 267, '0.14'),
(268, 268, '0.105'),
(269, 269, '0.105'),
(270, 270, '0.15'),
(271, 271, '0.07'),
(272, 272, '0.07'),
(273, 273, '0.07'),
(274, 274, '0.105'),
(275, 275, '0.07'),
(276, 276, '0.1225'),
(277, 277, '0.105'),
(278, 278, '0.105'),
(279, 279, '0.105'),
(280, 280, '0.105'),
(281, 281, '0.105'),
(282, 282, 'EUR 350'),
(283, 283, 'USD 2500'),
(284, 284, '0.1'),
(285, 285, '0.15'),
(286, 286, '0.1'),
(287, 287, '0.1'),
(288, 288, 'USD 800'),
(289, 289, '0.12'),
(290, 290, '0.19'),
(291, 291, '0.13'),
(292, 292, '0.09'),
(293, 293, '0.15'),
(294, 294, '0.18'),
(295, 295, '0.0875'),
(296, 296, '0.15'),
(297, 297, '0.09'),
(298, 298, '0.25'),
(299, 299, '0.14'),
(300, 300, '0.0875'),
(301, 301, '0.18'),
(302, 302, '0.18'),
(303, 303, '0.15'),
(304, 304, '0.1'),
(305, 305, '0.13'),
(306, 306, '0.12'),
(307, 307, '0.18'),
(308, 308, '0.12'),
(309, 309, '0.18'),
(310, 310, '0.15'),
(311, 311, '0.14'),
(312, 312, '0.105'),
(313, 313, '0.091'),
(314, 314, '0.12'),
(315, 315, '0.15'),
(316, 316, '0.03'),
(317, 317, '0.12'),
(318, 318, '0.1'),
(319, 319, '0.18'),
(320, 320, '0.1'),
(321, 321, '0.09'),
(322, 322, '0.13'),
(323, 323, '0.11'),
(324, 324, '0.1'),
(325, 325, '0.13'),
(326, 326, '0.22'),
(327, 327, '0.22'),
(328, 328, '0.22'),
(329, 329, '0.22'),
(330, 330, '0.22'),
(331, 331, '0.22'),
(332, 332, '0.13'),
(333, 333, '0.13'),
(334, 334, '0.18'),
(335, 335, '0.13'),
(336, 336, 'NZD 2400'),
(337, 337, '0.12'),
(338, 338, 'USD 1500'),
(339, 339, 'USD 1500'),
(340, 340, 'USD 1500'),
(341, 341, 'USD 1500'),
(342, 342, 'USD 1500'),
(343, 343, 'USD 1500'),
(344, 344, 'USD 1500'),
(345, 345, 'USD 1500'),
(346, 346, 'USD 1500'),
(347, 347, 'USD 1500'),
(348, 348, 'USD 1500'),
(349, 349, 'USD 1500'),
(350, 350, 'USD 1500'),
(351, 351, 'USD 1500'),
(352, 352, 'USD 1500'),
(353, 353, 'USD 1500'),
(354, 354, 'USD 1500'),
(355, 355, 'USD 1500'),
(356, 356, 'USD 1500'),
(357, 357, 'USD 1500'),
(358, 358, '0.12'),
(359, 359, '0.08'),
(360, 360, 'USD 1500'),
(361, 361, '0.16'),
(362, 362, '0.16'),
(363, 363, '0.16'),
(364, 364, '0.16'),
(365, 365, '0.16'),
(366, 366, '0.15'),
(367, 367, '0.12'),
(368, 368, '0.15'),
(369, 369, '0.095'),
(370, 370, '0.09'),
(371, 371, '0.16'),
(372, 372, '0.15'),
(373, 373, '0.12'),
(374, 374, '0.12'),
(375, 375, '0.15'),
(376, 376, '0.16'),
(377, 377, '0.15'),
(378, 378, '0.15'),
(379, 379, '0.1'),
(380, 380, '0.15'),
(381, 381, '0.12'),
(382, 382, '0.15'),
(383, 383, '0.16'),
(384, 384, '0.08'),
(385, 385, '0.12'),
(386, 386, '0.08'),
(387, 387, '0.12'),
(388, 388, '0.06'),
(389, 389, '0.06'),
(390, 390, 'GBP 441'),
(391, 391, '0.03'),
(392, 392, 'GBP 1800'),
(393, 393, '0.12'),
(394, 394, '0.09'),
(395, 395, '0.09'),
(396, 396, '0.15'),
(397, 397, '0.12'),
(398, 398, '0.09'),
(399, 399, '0.09'),
(400, 400, '0.09'),
(401, 401, '0.09'),
(402, 402, '0.09'),
(403, 403, '0.09'),
(404, 404, '0.09'),
(405, 405, '0.09'),
(406, 406, '0.09'),
(407, 407, '0.09'),
(408, 408, '0.09'),
(409, 409, '0.15'),
(410, 410, '0.06'),
(411, 411, '0.15'),
(412, 412, '0.12'),
(413, 413, '0.13'),
(414, 414, '0.12'),
(415, 415, '0.12'),
(416, 416, '0.15'),
(417, 417, '0.13'),
(418, 418, '0.09'),
(419, 419, '0.09'),
(420, 420, '0.09'),
(421, 421, '0.09'),
(422, 422, '0.09'),
(423, 423, '0.12'),
(424, 424, '0.09'),
(425, 425, '0.09'),
(426, 426, '0.08'),
(427, 427, '0.12'),
(428, 428, '0.09'),
(429, 429, '0.15'),
(430, 430, '0.09'),
(431, 431, '0.09'),
(432, 432, '0.14'),
(433, 433, '0.09'),
(434, 434, '0.09'),
(435, 435, '0.09'),
(436, 436, '0.09'),
(437, 437, '0.15'),
(438, 438, '0.09'),
(439, 439, '0.16'),
(440, 440, '0.09'),
(441, 441, '0.06'),
(442, 442, '0.144'),
(443, 443, '0.15'),
(444, 444, '0.16'),
(445, 445, '0.09'),
(446, 446, '0.12'),
(447, 447, '0.12'),
(448, 448, '0.15'),
(449, 449, '0.15'),
(450, 450, '0.15'),
(451, 451, '0.12'),
(452, 452, '0.15'),
(453, 453, '0.15'),
(454, 454, '0.1'),
(455, 455, '0.15'),
(456, 456, '0.15'),
(457, 457, '0.12'),
(458, 458, '0.14'),
(459, 459, '0.09'),
(460, 460, '0.15'),
(461, 461, '0.15'),
(462, 462, '0.15'),
(463, 463, '0.12'),
(464, 464, '0.09'),
(465, 465, '0.15'),
(466, 466, '0.15'),
(467, 467, '0.12'),
(468, 468, '0.12'),
(469, 469, '0.09'),
(470, 470, '0.11'),
(471, 471, '0.12'),
(472, 472, '0.08'),
(473, 473, '0.08'),
(474, 474, '0.12'),
(475, 475, '0.176'),
(476, 476, '0.176'),
(477, 477, '0.08'),
(478, 478, '0.09'),
(479, 479, '0.15'),
(480, 480, '0.14'),
(481, 481, '0.09'),
(482, 482, '0.15'),
(483, 483, '0.1'),
(484, 484, '0.15'),
(485, 485, '0.12'),
(486, 486, '0.12'),
(487, 487, '0.12'),
(488, 488, '0.08'),
(489, 489, '0.12'),
(490, 490, '0.16'),
(491, 491, '0.15'),
(492, 492, '0.09'),
(493, 493, '0.15'),
(494, 494, '0.14'),
(495, 495, '0.12'),
(496, 496, '0.15'),
(497, 497, '0.12'),
(498, 498, '0.12'),
(499, 499, '0.12'),
(500, 500, '0.2'),
(501, 501, '0.08'),
(502, 502, '0.06'),
(503, 503, '0.08'),
(504, 504, '0.09'),
(505, 505, '0.15'),
(506, 506, '0.15'),
(507, 507, '0.16'),
(508, 508, '0.15'),
(509, 509, '0.12'),
(510, 510, '0.12'),
(511, 511, '0.1'),
(512, 512, 'EUR 2800'),
(513, 513, 'USD 2500'),
(514, 514, 'USD 2500'),
(515, 515, '0.12'),
(516, 516, '0.12'),
(517, 517, '0.096'),
(518, 518, '0.096'),
(519, 519, '0.08'),
(520, 520, '0.16'),
(521, 521, 'USD 1830'),
(522, 522, '0.078'),
(523, 523, '0.078'),
(524, 524, 'USD 2210'),
(525, 525, 'USD 2080'),
(526, 526, 'USD 2065'),
(527, 527, 'USD 1655'),
(528, 528, 'USD 2405'),
(529, 529, '0.078'),
(530, 530, '0.078'),
(531, 531, 'USD 2500'),
(532, 532, 'USD 2500'),
(533, 533, '0.078'),
(534, 534, 'USD 1790'),
(535, 535, 'USD 1750'),
(536, 536, '0.078'),
(537, 537, '0.078'),
(538, 538, '0.063'),
(539, 539, '0.07'),
(540, 540, 'USD 630'),
(541, 541, '0.063'),
(542, 542, 'USD 1890'),
(543, 543, 'USD 2500'),
(544, 544, '0.094'),
(545, 545, '0.126'),
(546, 546, 'USD 155'),
(547, 547, '0.094'),
(548, 548, 'USD 2000'),
(549, 549, 'USD 945'),
(550, 550, '0.094'),
(551, 551, '0.094'),
(552, 552, '0.063'),
(553, 553, 'USD 470'),
(554, 554, 'USD 1765'),
(555, 555, '0.063'),
(556, 556, '0.063'),
(557, 557, '0.063'),
(558, 558, '0.063'),
(559, 559, '0.063'),
(560, 560, 'USD 2205'),
(561, 561, '0.063'),
(562, 562, '0.063'),
(563, 563, 'USD 945'),
(564, 564, '0.063'),
(565, 565, 'USD 945'),
(566, 566, 'USD 945'),
(567, 567, '0.125'),
(568, 568, '0.063'),
(569, 569, '0.063'),
(570, 570, '0.0945'),
(571, 571, 'USD 440'),
(572, 572, 'USD 3780'),
(573, 573, '0.063'),
(574, 574, '0.125'),
(575, 575, 'USD 2835'),
(576, 576, 'USD 315'),
(577, 577, 'USD 1385'),
(578, 578, 'USD 2500'),
(579, 579, 'USD 630'),
(580, 580, '0.0945'),
(581, 581, '0.0945'),
(582, 582, '0.125'),
(583, 583, 'USD 315'),
(584, 584, 'USD 315'),
(585, 585, '0.0945'),
(586, 586, 'USD 1575'),
(587, 587, 'USD 1450'),
(588, 588, '0.063'),
(589, 589, '0.0945'),
(590, 590, 'USD 945'),
(591, 591, '0.063'),
(592, 592, 'USD 945'),
(593, 593, 'USD 630'),
(594, 594, '0.0945'),
(595, 595, 'USD 630'),
(596, 596, 'USD 945'),
(597, 597, 'USD 630'),
(598, 598, 'USD 945'),
(599, 599, 'USD 945'),
(600, 600, 'USD 12.5'),
(601, 601, 'USD 13.5'),
(602, 602, 'USD 945'),
(603, 603, '0.063'),
(604, 604, 'USD 1575'),
(605, 605, 'USD 945'),
(606, 606, 'USD 945'),
(607, 607, 'USD 1575'),
(608, 608, 'USD 945'),
(610, 610, 'USD 1260'),
(611, 611, 'USD 945'),
(612, 612, '0.094'),
(613, 613, 'USD 1070'),
(614, 614, 'USD 315'),
(615, 615, '0.063'),
(616, 616, '0.063'),
(617, 617, '0.063'),
(618, 618, '0.094'),
(619, 619, '0.094'),
(620, 620, 'USD 1385'),
(621, 621, 'USD 1575'),
(622, 622, '0.094'),
(623, 623, '0.063'),
(624, 624, 'USD 1575'),
(625, 625, 'USD 630'),
(626, 626, 'USD 1730'),
(627, 627, 'USD 440'),
(628, 628, 'USD 945'),
(629, 629, 'USD 9.4'),
(630, 630, '0.063'),
(631, 631, 'USD 630'),
(632, 632, '0.094'),
(633, 633, '0.094'),
(634, 634, '0.105'),
(635, 635, '0.063'),
(636, 636, 'USD 630'),
(637, 637, '0.094'),
(638, 638, 'USD 785'),
(639, 639, 'USD 1260'),
(640, 640, 'USD 1260'),
(641, 641, '0.05'),
(642, 642, '0.063'),
(643, 643, 'USD 630'),
(644, 644, 'USD 755'),
(645, 645, 'USD 1260'),
(646, 646, 'USD 945'),
(647, 647, '0.094'),
(648, 648, '0.075'),
(649, 649, 'USD 945'),
(650, 650, '0.125'),
(651, 651, '0.063'),
(652, 652, '0.063'),
(653, 653, '0.094'),
(654, 654, 'USD 1575'),
(655, 655, '0.03'),
(656, 656, '0.063'),
(657, 657, '0.063'),
(658, 658, 'USD 2500'),
(659, 659, 'USD 630'),
(660, 660, '0.094'),
(661, 661, '0.094'),
(662, 662, 'USD 2500'),
(663, 663, 'USD 785'),
(664, 664, 'USD 2700'),
(665, 665, 'USD 630'),
(666, 666, '0.063'),
(667, 667, '0.063'),
(668, 668, 'USD 630'),
(669, 669, 'USD 1890'),
(670, 670, '0.094'),
(671, 671, 'USD 630'),
(672, 672, 'USD 470'),
(673, 673, '0.063'),
(674, 674, '0.063'),
(675, 675, 'USD 945'),
(676, 676, 'USD 630'),
(677, 677, '0.063'),
(678, 678, '0.125'),
(679, 679, 'USD 375'),
(680, 680, 'USD 785'),
(681, 681, 'USD 945'),
(682, 682, 'USD 1575'),
(683, 683, 'USD 2500'),
(684, 684, 'USD 315'),
(685, 685, '0.094'),
(686, 686, 'USD 2500'),
(687, 687, 'USD 315'),
(688, 688, '0.063'),
(689, 689, 'USD 315'),
(690, 690, 'USD 880'),
(691, 691, 'USD 2500'),
(692, 692, '0.063'),
(693, 693, '0.063'),
(694, 694, '0.063'),
(695, 695, '0.063'),
(696, 696, '0.063'),
(697, 697, '0.063'),
(698, 698, '0.078'),
(699, 699, '0.063'),
(700, 700, '0.063'),
(701, 701, '0.094'),
(702, 702, '0.094'),
(703, 703, 'USD 785'),
(704, 704, '0.094'),
(705, 705, '0.094'),
(706, 706, '0.094'),
(707, 707, 'USD 2500'),
(708, 708, 'USD 2500'),
(709, 709, 'USD 2500'),
(710, 710, 'USD 2500'),
(711, 711, 'USD 2500'),
(712, 712, '0.094'),
(713, 713, '0.094'),
(714, 714, '0.094'),
(715, 715, 'USD 0'),
(716, 716, '0.094'),
(717, 717, '0.047'),
(718, 718, 'USD 2500'),
(719, 719, 'USD 2500'),
(720, 720, 'USD 2500'),
(721, 721, '0.094'),
(722, 722, 'USD 2500'),
(723, 723, 'USD 2500'),
(724, 724, 'USD 2500'),
(725, 725, 'USD 2500'),
(726, 726, 'USD 2500'),
(727, 727, 'USD 2500'),
(728, 728, '0.094'),
(729, 729, '0.094'),
(730, 730, 'USD 2500'),
(731, 731, 'USD 2500'),
(732, 732, 'USD 2500'),
(733, 733, 'USD 2500'),
(734, 734, '0.1'),
(735, 735, '0.07'),
(736, 736, 'USD 1700'),
(737, 737, 'USD 800'),
(738, 738, '0.1'),
(739, 739, 'USD 2500'),
(740, 740, 'USD 2500'),
(741, 741, 'USD 2500'),
(742, 742, 'USD 2500'),
(743, 743, 'USD 2500'),
(744, 744, 'USD 2500'),
(745, 745, 'USD 2500'),
(746, 746, 'USD 2500'),
(747, 747, 'USD 2500'),
(748, 748, 'USD 2500'),
(749, 749, 'USD 2500'),
(750, 750, 'USD 2500'),
(751, 751, 'USD 2500'),
(752, 752, 'USD 2500'),
(753, 753, 'USD 2500'),
(754, 754, 'USD 2500'),
(755, 755, 'USD 2500'),
(756, 756, 'USD 2500'),
(771, 474, '5%'),
(772, 498, '8%');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_content`
--

CREATE TABLE `ggportal_tbl_content` (
  `content_id` int(11) NOT NULL,
  `content_name` varchar(50) NOT NULL,
  `content_body` text NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_content`
--

INSERT INTO `ggportal_tbl_content` (`content_id`, `content_name`, `content_body`) VALUES
(1, 'agreement', '                                                                            Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in, viverra quis, feugiat a, tellus. Phasellus viverra nulla ut metus varius laoreet. Quisque rutrum. Aenean imperdiet. Etiam ultricies nisi vel augue. Curabitur ullamcorper ultricies nisi. Nam eget dui. Etiam rhoncus. Maecenas tempus, tellus eget condimentum rhoncus, sem quam semper libero, sit amet adipiscing sem neque sed ipsum. Nam quam nunc, blandit vel, luctus pulvinar, hendrerit id, lorem. Maecenas nec odio et ante tincidunt tempus. Donec vitae sapien ut libero venenatis faucibus. Nullam quis ante. Etiam sit amet orci eget eros faucibus tincidunt. Duis leo. Sed fringilla mauris sit amet nibh. Donec sodales sagittis magna. Sed consequat, leo eget bibendum sodales, augue velit cursus');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_country`
--

CREATE TABLE `ggportal_tbl_country` (
  `country_id` int(11) NOT NULL,
  `country_name` varchar(45) NOT NULL,
  `country_code` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_country`
--

INSERT INTO `ggportal_tbl_country` (`country_id`, `country_name`, `country_code`) VALUES
(1, ' Australia', 'AU'),
(2, ' Canada', 'CA'),
(3, ' France', 'FRA'),
(4, ' Georgia', 'GA'),
(5, ' Germany', 'DE'),
(6, ' Ireland', 'IRL'),
(7, ' Italy', 'ITA'),
(8, ' Latvia', 'LV'),
(9, ' Lithuania', 'LT'),
(10, ' Malta', 'MT'),
(11, ' Mauritius', 'MU'),
(12, ' Netherlands', 'NL'),
(13, ' New Zealand', 'NZ'),
(14, ' Poland', 'PL'),
(15, ' Russia', 'RU'),
(16, ' Singapore', 'SG'),
(17, ' Switzerland', 'CH'),
(18, ' Ukraine', 'UA'),
(19, ' United Arab Emirates', 'UAE'),
(20, ' United Kingdom', 'UK'),
(21, ' United States', 'USA'),
(22, 'Sri Lanka', 'SL');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_course`
--

CREATE TABLE `ggportal_tbl_course` (
  `course_id` int(11) NOT NULL,
  `course_name` varchar(450) NOT NULL,
  `course_code` varchar(20) NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_course`
--

INSERT INTO `ggportal_tbl_course` (`course_id`, `course_name`, `course_code`, `active_yn`) VALUES
(1, 'Arts', 'tc', 'Y'),
(2, 'Law, Politics, Social, Community Service and Teaching', '', 'Y'),
(3, 'Health Sciences, Medicine, Nursing, Paramedic and Kinesiology', '', 'Y'),
(4, 'Sciences', '', 'Y'),
(5, 'Business, Management and Economics', '', 'Y'),
(6, 'Engineering and Technology', '', 'Y'),
(7, 'English for Academic Studies', '', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_currency`
--

CREATE TABLE `ggportal_tbl_currency` (
  `currency_id` int(11) NOT NULL,
  `currency_name` varchar(45) NOT NULL,
  `currency_code` varchar(20) NOT NULL,
  `currency_symbol` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_currency`
--

INSERT INTO `ggportal_tbl_currency` (`currency_id`, `currency_name`, `currency_code`, `currency_symbol`) VALUES
(1, 'USD', 'USD $', '$'),
(2, 'CAD', 'CAD $', '$'),
(3, 'GBP', 'GBP £', '£'),
(4, 'AUD ', 'AUD $', '$'),
(5, 'NZD', 'NZD $', '$'),
(6, 'EUR', 'EUR €', '€');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_institute`
--

CREATE TABLE `ggportal_tbl_institute` (
  `institute_id` int(11) NOT NULL,
  `institute_name` varchar(180) NOT NULL,
  `institute_country_id` int(11) NOT NULL,
  `institute_type` varchar(180) NOT NULL,
  `institute_state` varchar(180) NOT NULL,
  `institute_city` varchar(45) NOT NULL,
  `web_url` varchar(450) NOT NULL,
  `logo_url` varchar(450) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_institute`
--

INSERT INTO `ggportal_tbl_institute` (`institute_id`, `institute_name`, `institute_country_id`, `institute_type`, `institute_state`, `institute_city`, `web_url`, `logo_url`, `email`, `mobile`) VALUES
(1, ' Academies Australasia Polytechnic', 1, 'college', 'Victoria', '', '', '', '', ''),
(2, ' Academy of Information Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(3, ' Airways Aviation', 1, 'college', 'Queensland', '', '', '', '', ''),
(4, ' Amber Aviation Academy', 1, 'college', 'Victoria', '', '', '', '', ''),
(5, ' APM College of Business and Communication', 1, 'college', 'New South Wales', '', '', '', '', ''),
(6, ' Australian College of Applied Psychology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(7, ' Australian Institute of Higher Education', 1, 'college', 'New South Wales', '', '', '', '', ''),
(8, ' Australian National University', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(9, ' Australian National University College', 1, 'college', 'Australian Capital Territory', '', '', '', '', ''),
(10, ' Bedford College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(11, ' Billy Blue College of Design', 1, 'college', 'New South Wales', '', '', '', '', ''),
(12, ' Blue Mountains International Hotel Management School', 1, 'college', 'New South Wales', '', '', '', '', ''),
(13, ' Bond University', 1, 'university', 'Queensland', '', '', '', '', ''),
(14, ' Box Hill Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(15, ' Cambridge International College', 1, 'college', 'Victoria', '', '', '', '', ''),
(16, ' Curtin College', 1, 'college', 'Western Australia', '', '', '', '', ''),
(17, ' Curtin University', 1, 'university', 'Western Australia', '', '', '', '', ''),
(18, ' Deakin College', 1, 'college', 'Victoria', '', '', '', '', ''),
(19, ' Deakin University', 1, 'university', 'Victoria', '', '', '', '', ''),
(20, ' Edith Cowan College', 1, 'college', 'Western Australia', '', '', '', '', ''),
(21, ' Edith Cowan University', 1, 'university', 'Western Australia', '', '', '', '', ''),
(22, ' Excelsia College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(23, ' Eynesbury College', 1, 'college', 'South Australia', '', '', '', '', ''),
(24, ' Federation University (ATMC College)', 1, 'college', 'Victoria', '', '', '', '', ''),
(25, ' Federation University', 1, 'university', 'Victoria', '', '', '', '', ''),
(26, ' Flinders International Study Centre', 1, 'college', 'South Australia', '', '', '', '', ''),
(27, ' Flinders University', 1, 'university', 'South Australia', '', '', '', '', ''),
(28, ' Griffith College', 1, 'college', 'Queensland', '', '', '', '', ''),
(29, ' Griffith University', 1, 'university', 'Queensland', '', '', '', '', ''),
(30, ' Global Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(31, ' Holmes Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(32, ' Holmesglen Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(33, ' IES College', 1, 'college', 'Queensland', '', '', '', '', ''),
(34, ' International College of Management Sydney.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(35, ' International Institute of Business & Information Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(36, ' James Cook University', 1, ' university', 'Queensland', '', '', '', '', ''),
(37, ' James Cook University', 1, ' university', 'Queensland', '', '', '', '', ''),
(38, ' King\'s Own Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(39, ' Laneway International College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(40, ' La Trobe College', 1, 'college', 'Victoria', '', '', '', '', ''),
(41, ' La Trobe University', 1, 'university', 'New South Wales/Victoria', '', '', '', '', ''),
(42, ' Macquarie University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(43, ' Melbourne Polytechnic', 1, 'college', 'Victoria', '', '', '', '', ''),
(44, ' Melbourne Institute of Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(45, ' Monash University', 1, 'university', 'Victoria', '', '', '', '', ''),
(46, ' Monash College', 1, 'college', 'Victoria', '', '', '', '', ''),
(47, ' Murdoch Institute of Technology', 1, 'college', 'Western Australia', '', '', '', '', ''),
(48, ' Queensland International Institute', 1, 'college', 'Queensland', '', '', '', '', ''),
(49, ' Queensland University of Technology', 1, 'university', 'Queensland', '', '', '', '', ''),
(50, ' RMIT University', 1, 'university', 'Victoria', '', '', '', '', ''),
(51, ' Russo Business School', 1, 'college', 'Queensland', '', '', '', '', ''),
(52, ' SAE creative media institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(53, ' Sarina Russo Institute', 1, 'college', 'Queensland', '', '', '', '', ''),
(54, ' Stott\'s College', 1, 'college', 'Victoria', '', '', '', '', ''),
(55, ' SIBT', 1, 'college', 'New South Wales', '', '', '', '', ''),
(56, ' South Australia Institute of Business & Technology', 1, 'college', 'South Australia', '', '', '', '', ''),
(57, ' Southern Cross University', 1, 'university', 'Queensland', '', '', '', '', ''),
(58, ' Stratfield College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(59, ' Swinburne University of Technology', 1, 'university', 'Victoria', '', '', '', '', ''),
(60, ' TAFE', 1, 'college', 'Queensland', '', '', '', '', ''),
(61, ' TAFE TASMANIA', 1, 'college', 'Tasmania', '', '', '', '', ''),
(62, ' TAFE SOUTH AUSTRALIA', 1, 'college', 'South Australia', '', '', '', '', ''),
(63, ' Taylors College', 1, ' perth college', 'New South Wales/Western Australia', '', '', '', '', ''),
(64, ' Trinity College', 1, 'college', 'Victoria', '', '', '', '', ''),
(65, ' The Hotel School', 1, 'college', 'New South Wales', '', '', '', '', ''),
(66, ' Torrens University Australia', 1, 'university', 'South Australia', '', '', '', '', ''),
(67, ' University of Adelaide', 1, 'university', 'South Australia', '', '', '', '', ''),
(68, ' University of Adelaide College', 1, 'college', 'South Australia', '', '', '', '', ''),
(69, ' University of Canberra', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(70, ' University of Canberra College', 1, 'college', 'Australian Capital Territory', '', '', '', '', ''),
(71, ' University of Melbourne', 1, 'university', 'Victoria', '', '', '', '', ''),
(72, ' University of New England', 1, 'university', 'New South Wales', '', '', '', '', ''),
(73, ' University of New South Wales', 1, 'university', 'New South Wales', '', '', '', '', ''),
(74, ' University of Newcastle', 1, 'university', 'New South Wales', '', '', '', '', ''),
(75, ' University of Queensland', 1, 'university', 'Queensland', '', '', '', '', ''),
(76, ' University of South Australia', 1, 'university', 'South Australia', '', '', '', '', ''),
(77, ' University of Southern Queensland', 1, 'university', 'New South Wales', '', '', '', '', ''),
(78, ' University of Sunshine Coast (ATMC College)', 1, 'university', 'Victoria', '', '', '', '', ''),
(79, ' University of Sydney', 1, 'university', 'New South Wales', '', '', '', '', ''),
(80, ' University of Tasmania', 1, 'university', 'Tasmania', '', '', '', '', ''),
(81, ' University of Technology', 1, 'university', 'New South Wales', '', '', '', '', ''),
(82, ' University of the Sunshine Coast', 1, 'university', 'New South Wales', '', '', '', '', ''),
(83, ' University of Western Australia', 1, 'university', 'Western Australia', '', '', '', '', ''),
(84, ' University of Wollongong', 1, 'university', 'New South Wales', '', '', '', '', ''),
(85, ' UTS Insearch', 1, 'college', 'New South Wales', '', '', '', '', ''),
(86, ' Victoria University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(87, ' Victoria University', 1, 'university', 'Victoria', '', '', '', '', ''),
(88, ' Western Sydney University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(89, ' Western Sydney University International College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(90, ' William Angliss Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(91, ' Le Cordon Bleu', 1, 'college', 'South Australia', '', '', '', '', ''),
(92, ' Newcastle International College.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(93, ' Sydney Institute of Business & Technology.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(94, ' TAFE International Western Australia.', 1, 'college', 'Western Australia', '', '', '', '', ''),
(95, ' Taylors College', 1, ' college', 'New South Wales', '', '', '', '', ''),
(96, ' Asia Pacific International College', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(97, ' Australian Catholic University', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(98, ' Charles Sturt University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(99, ' Central Queensland University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(100, ' Engineering Institute of Technology.', 1, 'college', 'Western Australia', '', '', '', '', ''),
(101, ' Acadia University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(102, ' Acsenda School of Management', 2, 'college', 'British Columbia', '', '', '', '', ''),
(103, ' Adler University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(104, ' Alexander College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(105, ' Algonquin College', 2, 'college', 'Ontario', '', '', '', '', ''),
(106, ' Assiniboine Community College', 2, 'college', 'Manitoba', '', '', '', '', ''),
(107, ' Avalon College', 2, 'college', 'Quebec', '', '', '', '', ''),
(108, ' Aviron Technical Institute', 2, 'college', 'Quebec', '', '', '', '', ''),
(109, ' Brighton College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(110, ' British Columbia Institute of Technology', 2, 'college', 'British Columbia', '', '', '', '', ''),
(111, ' Brock University', 2, 'university', 'Ontario', '', '', '', '', ''),
(112, ' Bow Valley College', 2, 'college', 'Alberta', '', '', '', '', ''),
(113, ' Cambrian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(114, ' Cambrian College at Hanson', 2, 'college', 'Ontario', '', '', '', '', ''),
(115, ' Canada College', 2, 'college', 'Quebec', '', '', '', '', ''),
(116, ' Canadian College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(117, ' Canadore College', 2, 'college', 'Ontario', '', '', '', '', ''),
(118, ' Cape Breton University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(119, ' Capilano University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(120, ' CCSQ', 2, 'college', 'Quebec', '', '', '', '', ''),
(121, ' CDI College', 2, 'college', 'Quebec', '', '', '', '', ''),
(122, ' Cegep de la Gaspesie et des Iles', 2, 'college', 'Quebec', '', '', '', '', ''),
(123, ' Cegep Marie Victorin', 2, 'college', 'Quebec', '', '', '', '', ''),
(124, ' Centennial College', 2, 'college', 'Ontario', '', '', '', '', ''),
(125, ' College of New Caledonia', 2, 'college', 'British Columbia', '', '', '', '', ''),
(126, ' College of the Rockies', 2, 'college', 'British Columbia', '', '', '', '', ''),
(127, ' College Multihexa', 2, 'college', 'Quebec', '', '', '', '', ''),
(128, ' College St. Michel', 2, 'college', 'Quebec', '', '', '', '', ''),
(129, ' College Universel', 2, 'college', 'Quebec', '', '', '', '', ''),
(130, ' Columbia College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(131, ' Conestoga College', 2, 'college', 'Ontario', '', '', '', '', ''),
(132, ' Confederation College', 2, 'college', 'Ontario', '', '', '', '', ''),
(133, ' Crandall University', 2, 'university', 'NewBrunswick', '', '', '', '', ''),
(134, ' Dalhousie University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(135, ' Douglas College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(136, ' Durham College', 2, 'college', 'Ontario', '', '', '', '', ''),
(137, ' Evergreen College', 2, 'college', 'Ontario', '', '', '', '', ''),
(138, ' Fairleigh Dickinson University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(139, ' Fanshawe College', 2, 'college', 'Ontario', '', '', '', '', ''),
(140, ' Fleming College', 2, 'college', 'Ontario', '', '', '', '', ''),
(141, ' Focus College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(142, ' George Brown College', 2, 'college', 'Ontario', '', '', '', '', ''),
(143, ' Georgian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(144, ' Great Plains College', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(145, ' Humber College', 2, 'college', 'Ontario', '', '', '', '', ''),
(146, ' Kwantlen Polytechnic University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(147, ' Langara College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(148, ' Lakehead University', 2, 'university', 'Ontario', '', '', '', '', ''),
(149, ' Lakeland College', 2, 'college', 'Alberta', '', '', '', '', ''),
(150, ' Lambton College', 2, 'college', 'Ontario', '', '', '', '', ''),
(151, ' M College', 2, 'college', 'Quebec', '', '', '', '', ''),
(152, ' MacEwan University', 2, 'university', 'Alberta', '', '', '', '', ''),
(153, ' Manitoba Institute of Trades and Technology', 2, 'college', 'Manitoba', '', '', '', '', ''),
(154, ' Medicine Hat College', 2, 'university', 'Alberta', '', '', '', '', ''),
(155, ' Memorial University of Newfoundland', 2, 'university', 'New Foundland', '', '', '', '', ''),
(156, ' Mount Allison University', 2, 'university', 'NewBrunswick', '', '', '', '', ''),
(157, ' Mount Saint Vincent University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(158, ' New York Institute of Technology', 2, 'college', 'British Columbia', '', '', '', '', ''),
(159, ' Niagara College', 2, 'college', 'Ontario', '', '', '', '', ''),
(160, ' Nipissing University', 2, 'university', 'Ontario', '', '', '', '', ''),
(161, ' NorQuest College', 2, 'college', 'Alberta', '', '', '', '', ''),
(162, ' North Island College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(163, ' Northern College', 2, 'college', 'Ontario', '', '', '', '', ''),
(164, ' Northern College at Pures-Toronto', 2, 'college', 'Ontario', '', '', '', '', ''),
(165, ' Northern Lights College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(166, ' Parkland College', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(167, ' Royal Roads University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(168, ' Saskatchewan Polytechnic', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(169, ' Sault College', 2, 'college', 'Ontario', '', '', '', '', ''),
(170, ' Selkirk College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(171, ' Seneca College', 2, 'college', 'Ontario', '', '', '', '', ''),
(172, ' Seneca International Academy', 2, 'college', 'Ontario', '', '', '', '', ''),
(173, ' Sheridan College', 2, 'college', 'Ontario', '', '', '', '', ''),
(174, ' Simon Fraser University through Fraser International College', 2, 'university', 'British Columbia', '', '', '', '', ''),
(175, ' Sprott Shaw College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(176, ' St. Lawrence College', 2, 'college', 'Ontario', '', '', '', '', ''),
(177, ' Stenberg College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(178, ' Thompson Rivers University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(179, ' Toronto Film School', 2, 'university', 'Ontario', '', '', '', '', ''),
(180, ' Trent University', 2, 'university', 'Ontario', '', '', '', '', ''),
(181, ' Trinity Western University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(182, ' University Canada West', 2, 'university', 'British Columbia', '', '', '', '', ''),
(183, ' University of Guelph', 2, 'university', 'Ontario', '', '', '', '', ''),
(184, ' University of Lethbridge', 2, 'university', 'Alberta', '', '', '', '', ''),
(185, ' University of Manitoba', 2, 'university', 'Manitoba', '', '', '', '', ''),
(186, ' University of Manitoba through International College of Manitoba', 2, 'college', 'Manitoba', '', '', '', '', ''),
(187, ' University of Prince Edward Island', 2, 'university', 'Prince Edward', '', '', '', '', ''),
(188, ' University of Regina', 2, 'university', 'Saskatchewan', '', '', '', '', ''),
(189, ' University of Saskatchewan', 2, 'university', 'Saskatchewan', '', '', '', '', ''),
(190, ' University of the Fraser Valley', 2, 'university', 'British Columbia', '', '', '', '', ''),
(191, ' University of Victoria', 2, 'university', 'British Columbia', '', '', '', '', ''),
(192, ' University of Waterloo', 2, 'university', 'Ontario', '', '', '', '', ''),
(193, ' University of Windsor', 2, 'university', 'Ontario', '', '', '', '', ''),
(194, ' University of Winnipeg', 2, 'university', 'Manitoba', '', '', '', '', ''),
(195, ' Vancouver Community College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(196, ' Vancouver Film School', 2, 'college', 'British Columbia', '', '', '', '', ''),
(197, ' Vancouver Island University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(198, ' Wilfrid Laurier University', 2, 'university', 'Ontario', '', '', '', '', ''),
(199, ' York University', 2, 'university', 'Ontario', '', '', '', '', ''),
(200, ' Yukon University', 2, 'university', 'Yukon', '', '', '', '', ''),
(201, ' Brandon University', 2, 'university', 'Manitoba', '', '', '', '', ''),
(202, ' Coquitlam College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(203, ' Academy of Art University at Langara College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(204, ' CDE College', 2, 'college', 'Quebec', '', '', '', '', ''),
(205, ' Eton College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(206, ' ISI College', 2, 'college', 'Quebec', '', '', '', '', ''),
(207, ' Kings College (University of Western Ontario)', 2, 'university', 'Ontario', '', '', '', '', ''),
(208, ' Lakehead University through Georgian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(209, ' LaSalle College', 2, 'college', 'Quebec/British Columbia', '', '', '', '', ''),
(210, ' Le Cordon Bleu', 2, 'college', 'Ontario', '', '', '', '', ''),
(211, ' Loyalist College', 2, 'college', 'Ontario', '', '', '', '', ''),
(212, ' Matrix College', 2, 'college', 'Quebec', '', '', '', '', ''),
(213, ' Montreal College of Information Technology', 2, 'college', 'Quebec', '', '', '', '', ''),
(214, ' Oulton College', 2, 'college', 'New Brunswick', '', '', '', '', ''),
(215, ' Pacific Link College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(216, ' Queens College of Business', 2, '  college', 'Ontario', '', '', '', '', ''),
(217, ' St. Thomas University', 2, 'university', 'New Brunswick', '', '', '', '', ''),
(218, ' The Kings University', 2, 'university', 'Alberta', '', '', '', '', ''),
(219, ' University of New Brunswick', 2, 'university', 'New Brunswick', '', '', '', '', ''),
(220, ' University of Northern British Columbia', 2, 'university', 'British Columbia', '', '', '', '', ''),
(221, ' Vancouver Film School', 2, 'college', 'British Columbia', '', '', '', '', ''),
(222, ' Yukon College', 2, 'college', 'Yukon', '', '', '', '', ''),
(223, ' Coast Mountain College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(224, ' CodeCore Collge', 2, 'college', 'British Columbia', '', '', '', '', ''),
(225, ' SAIT College', 2, 'college', 'Alberta', '', '', '', '', ''),
(226, ' St. Clair College', 2, 'college', 'Ontario', '', '', '', '', ''),
(227, ' St.Lawrence Alpha', 2, 'college', 'Ontario', '', '', '', '', ''),
(228, ' Okanagan College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(229, ' St.Francis Xavier university', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(230, ' Red River College', 2, 'college', 'Manitoba', '', '', '', '', ''),
(231, ' ST.MARY UNIVERSITY', 2, 'university', 'Alberta', '', '', '', '', ''),
(232, ' SUNVIEW COLLEGE', 2, 'college', 'Ontario', '', '', '', '', ''),
(233, ' College National of Science & Technology', 2, 'college', 'Quebec', '', '', '', '', ''),
(234, ' Toronto School of Management', 2, 'college', 'Ontario', '', '', '', '', ''),
(235, ' Trebas Institute', 2, 'college', 'Quebec', '', '', '', '', ''),
(236, ' Kensley college', 2, 'college', 'Montreal', '', '', '', '', ''),
(237, ' Insignia College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(238, ' Kensley College', 2, 'college', 'Quebec', '', '', '', '', ''),
(239, ' Keyano College', 2, 'college', 'Alberta', '', '', '', '', ''),
(240, ' Cambria College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(241, ' Northeastern University', 2, ' toronto university', 'Ontario', '', '', '', '', ''),
(242, ' Red Deer College', 2, 'college', 'Alberta', '', '', '', '', ''),
(243, ' CDC Pont-Viau', 2, 'college', 'Quebec', '', '', '', '', ''),
(244, ' Q College', 2, 'college', 'Washington', '', '', '', '', ''),
(245, ' University of Leithbridge', 2, 'university', 'Alberta', '', '', '', '', ''),
(246, ' Niagara College Toronto', 2, 'college', 'Ontario', '', '', '', '', ''),
(247, ' ISC-Business School', 3, 'college', 'Paris', '', '', '', '', ''),
(248, ' Neoma Business School', 3, ' rouen college', 'Paris', '', '', '', '', ''),
(249, ' ESC Clermont Business School', 3, 'college', 'Puy-de-Dome', '', '', '', '', ''),
(250, ' Leonard de Vinci', 3, 'university', 'Puy-de-Dome', '', '', '', '', ''),
(251, ' Skema Business School', 3, 'college', 'North Carolina', '', '', '', '', ''),
(252, ' IPAG Business School', 3, 'college', 'Paris', '', '', '', '', ''),
(253, ' Ilia state University', 4, 'university', 'Tbilisi', '', '', '', '', ''),
(254, ' ISET - International School of Economics at TSU', 4, 'college', 'Tbilisi', '', '', '', '', ''),
(255, ' University of applied science', 5, 'university', 'Hamburg', '', '', '', '', ''),
(256, ' BSBI', 5, 'college', 'Berlin', '', '', '', '', ''),
(257, ' GISMA Business School', 5, 'college', 'Hannover', '', '', '', '', ''),
(258, ' IUBH University of Applied Sciences', 5, 'university', 'Bavaria', '', '', '', '', ''),
(259, ' Steinbeis University Steinbeis School Of Management And Innovation', 5, 'college', 'Berlin', '', '', '', '', ''),
(260, ' SRH Hochschule University', 5, 'university', 'Berlin', '', '', '', '', ''),
(261, ' University of Europe for Applied Sciences', 5, 'university', 'Hamburg', '', '', '', '', ''),
(262, ' Arden University', 5, 'university', 'Berlin', '', '', '', '', ''),
(263, ' LYIT', 6, 'university', 'Ulster', '', '', '', '', ''),
(264, ' Dublin Business School', 6, 'college', 'Dublin', '', '', '', '', ''),
(265, ' Athlone Institute of Technology (AIT)', 6, 'college', 'Westmeath', '', '', '', '', ''),
(266, ' National College of Ireland', 6, 'college', 'Dublin', '', '', '', '', ''),
(267, ' Griffith College', 6, 'college', 'Dublin', '', '', '', '', ''),
(268, ' Maynooth University', 6, 'university', 'Maynooth', '', '', '', '', ''),
(269, ' Trinity College Dublin', 6, 'college', 'Dublin', '', '', '', '', ''),
(270, ' University College Dublin', 6, 'college', 'Dublin', '', '', '', '', ''),
(271, ' National University of Ireland Galway', 6, 'university', 'Galway', '', '', '', '', ''),
(272, ' University College Cork', 6, 'university', 'Cork', '', '', '', '', ''),
(273, ' Dublin City University', 6, 'university', 'Dublin', '', '', '', '', ''),
(274, ' University of Limerick', 6, 'university', 'Limerick', '', '', '', '', ''),
(275, ' Technological University Dublin', 6, 'university', 'Dublin', '', '', '', '', ''),
(276, ' Dundalk Institute of Technology', 6, 'college', 'Donegal', '', '', '', '', ''),
(277, ' Institute of Technology Carlow', 6, 'college', 'Carlow', '', '', '', '', ''),
(278, ' Limerick Institute of Technology', 6, 'college', 'Limerick', '', '', '', '', ''),
(279, ' Letterkenny Institute of Technology', 6, 'college', 'Kilkenny', '', '', '', '', ''),
(280, ' Waterford Institute of Technology', 6, 'college', 'Waterford', '', '', '', '', ''),
(281, ' Cork Institute of Technology', 6, 'college', 'Cork', '', '', '', '', ''),
(282, ' University of Padua', 7, 'university', 'Padova', '', '', '', '', ''),
(283, ' Transport and Telecommunication Institute', 8, 'college', 'Riga', '', '', '', '', ''),
(284, ' Kazimieras Simonavicius University(KSU)', 9, 'university', 'Vilniaus', '', '', '', '', ''),
(285, ' EIE', 10, 'college', 'Valletta', '', '', '', '', ''),
(286, ' College of Logistics and Management Studies', 10, 'college', 'Western', '', '', '', '', ''),
(287, ' St Edward\'s College', 10, 'college', 'Valletta', '', '', '', '', ''),
(288, ' Stephen Business School', 11, 'college', 'Port Louis', '', '', '', '', ''),
(289, ' Holland International Study Centre', 12, 'college', 'Noord-Holland', '', '', '', '', ''),
(290, ' Cornell Institute of Business & Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(291, ' New Zealand School of Education (NZSE)', 13, 'college', 'Auckland', '', '', '', '', ''),
(292, ' Pacific International Hotel Management School', 13, 'college', 'New Plymouth', '', '', '', '', ''),
(293, ' UUNZ Institute of Business', 13, 'college', 'Auckland', '', '', '', '', ''),
(294, ' Ara Institute of Canterbury', 13, 'college', 'Christchurch', '', '', '', '', ''),
(295, ' Auckland University of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(296, ' Lincoln University', 13, 'university', 'Christchurch', '', '', '', '', ''),
(297, ' Western Institute of Technology at Taranaki (WITT)', 13, 'college', 'New Plymouth', '', '', '', '', ''),
(298, ' EDENZ Colleges', 13, 'college', 'Auckland', '', '', '', '', ''),
(299, ' Otago Polytechnic', 13, 'college', 'Auckland/Dunedin', '', '', '', '', ''),
(300, ' University of Canterbury', 13, 'university', 'Christchurch', '', '', '', '', ''),
(301, ' Avonmore Tertiary Institute', 13, 'college', 'Christchurch/ Auckland', '', '', '', '', ''),
(302, ' Manukau Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(303, ' Aspire2International Group', 13, 'college', 'Auckland', '', '', '', '', ''),
(304, ' Toi Ohomai Institute of Technology', 13, 'college', 'North Island', '', '', '', '', ''),
(305, ' Wellington Institute of Technology (WelTec)', 13, 'college', 'Wellington', '', '', '', '', ''),
(306, ' North Tec', 13, 'college', 'Auckland', '', '', '', '', ''),
(307, ' Nelson & Marlborough Institute of Technology', 13, 'college', 'Nelson/ Richmond/Blenheim', '', '', '', '', ''),
(308, ' Whitireia New Zealand', 13, 'college', 'Porirua/Aucklan', '', '', '', '', ''),
(309, ' Southern Institute of Technology', 13, 'college', 'Invercargill/ Queenstown/ Christchurch/ Gore/ Auckland/ Otanomono', '', '', '', '', ''),
(310, ' Auckland Institute of Studies', 13, 'college', 'Auckland', '', '', '', '', ''),
(311, ' Eastern Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(312, ' University of Waikato', 13, 'university', 'Hamilton', '', '', '', '', ''),
(313, ' Victoria University of Wellington', 13, 'university', 'Wellington', '', '', '', '', ''),
(314, ' Whitecliff College of Arts and Design/Fashion and Sustainability /Technology and Innovation', 13, 'college', 'Auckland', '', '', '', '', ''),
(315, ' ATMC NZ- Nelson Marlborough Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(316, ' New Zealand Airline Academy', 13, 'college', 'Oamaru', '', '', '', '', ''),
(317, ' Kauri Academy', 13, 'college', 'Auckland', '', '', '', '', ''),
(318, ' The University of AUCKLAND', 13, 'university', 'Auckland', '', '', '', '', ''),
(319, ' AUT International House', 13, 'college', 'Auckland', '', '', '', '', ''),
(320, ' AUT University', 13, 'university', 'Auckland', '', '', '', '', ''),
(321, ' Massey University', 13, 'university', 'Auckland', '', '', '', '', ''),
(322, ' Waikato University', 13, 'university', 'Hamilton', '', '', '', '', ''),
(323, ' Waikato Institute of Technology', 13, 'college', 'Hamilton', '', '', '', '', ''),
(324, ' University of Canterbury International College', 13, 'college', 'Christchurch', '', '', '', '', ''),
(325, ' Kaplan International English', 13, 'college', 'Auckland', '', '', '', '', ''),
(326, ' Yoobee Colleges Limited', 13, 'college', 'Auckland', '', '', '', '', ''),
(327, ' New Zealand Management Academies (NZMA)', 13, 'college', 'Auckland', '', '', '', '', ''),
(328, ' New Zealand School of Tourism', 13, 'college', 'Auckland', '', '', '', '', ''),
(329, ' North Shore International Academy', 13, 'college', 'Auckland', '', '', '', '', ''),
(330, ' New Zealand Institute of Sport', 13, 'college', 'Auckland', '', '', '', '', ''),
(331, ' The New Zealand College of Massage', 13, 'college', 'Auckland', '', '', '', '', ''),
(332, ' Otago University', 13, 'university', 'Dunedin', '', '', '', '', ''),
(333, ' Unitec Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(334, ' Northland Polytechnic', 13, 'college', 'Whangarei', '', '', '', '', ''),
(335, ' New Zealand Tertiary College', 13, 'college', 'Christchurch/ Auckland', '', '', '', '', ''),
(336, ' Newton College of Business and Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(337, ' Lazarski University', 14, 'university', 'Warszawa', '', '', '', '', ''),
(338, ' Dagestan State Medical University', 15, 'university', 'Adygeja', '', '', '', '', ''),
(339, ' Pirogov Russian National Research Medical University', 15, 'university', 'Moscow', '', '', '', '', ''),
(340, ' Chechen state Medical university', 15, 'university', 'Chechenija', '', '', '', '', ''),
(341, ' Kabardino-Balkarian State University', 15, 'university', 'Kabardino-Balkarija', '', '', '', '', ''),
(342, ' Peoples\' Friendship University of Russia', 15, 'university', 'Moscow', '', '', '', '', ''),
(343, ' First Moscow State Medical University', 15, 'university', 'Moscow', '', '', '', '', ''),
(344, ' First Moscow State Medical University', 15, 'university', 'Tatarstan', '', '', '', '', ''),
(345, ' Pskov State University', 15, 'university', 'Pskov', '', '', '', '', ''),
(346, ' Saint Petersburg State University', 15, 'university', 'Sankt-Peterburg', '', '', '', '', ''),
(347, ' Tula State Pedagogical University', 15, 'university', 'Tula', '', '', '', '', ''),
(348, ' Rybinsk State Aviation Technical University', 15, 'university', 'Jaroslavl', '', '', '', '', ''),
(349, ' Kazan National Research Technical University named after A.N.Tupolev', 15, 'university', 'Tatarstan', '', '', '', '', ''),
(350, ' Grozny State Oil Technical University named after M.D. Millionshikova', 15, 'university', 'Chechenija', '', '', '', '', ''),
(351, ' Tomsk Polytechnic University', 15, 'university', 'Tomsk', '', '', '', '', ''),
(352, ' Moscow Aviation Institute', 15, 'university', 'Idaho', '', '', '', '', ''),
(353, ' Moscow Institute of Physics and Technology', 15, 'university', 'Idaho', '', '', '', '', ''),
(354, ' Saint Petersburg State University of Industrial Technologies and Design', 15, 'university', 'Sankt-Peterburg', '', '', '', '', ''),
(355, ' Ural State University of Economics', 15, 'university', 'Sverdlovsk', '', '', '', '', ''),
(356, ' Admiral Ushakov State Maritime University', 15, 'university', 'Krasnodar', '', '', '', '', ''),
(357, ' International European University', 15, 'university', 'Kyyiv', '', '', '', '', ''),
(358, ' London School of Business & Finance', 16, 'singapore college', 'Singapore', '', '', '', '', ''),
(359, ' HTMI', 17, 'college', 'Lucerne', '', '', '', '', ''),
(360, ' South Ukrainian National Pedagogical University named after K.D. Ushynsky', 18, 'university', 'Odessa', '', '', '', '', ''),
(361, ' Kharkiv National Medical University', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(362, ' V.N Karazin Kharkiv National University', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(363, ' Kiev Medical University', 18, 'university', 'Kyyiv', '', '', '', '', ''),
(364, ' Poltava state Medical University', 18, 'university', 'Poltavs\'ka', '', '', '', '', ''),
(365, ' Kharkiv university of airforce', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(366, ' Heriot Watt University', 19, 'university', 'Dubai', '', '', '', '', ''),
(367, ' Aberystwyth University', 20, 'university', 'Wales', '', '', '', '', ''),
(368, ' Anglia Ruskin University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(369, ' Anglia Ruskin University College', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(370, ' Aston University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(371, ' Bangor University', 20, 'university', 'Wales', '', '', '', '', ''),
(372, ' Birmingham City University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(373, ' Bournemouth University', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(374, ' BPP University', 20, 'university', 'London', '', '', '', '', ''),
(375, ' Brunel University London', 20, 'university', 'London', '', '', '', '', ''),
(376, ' Cardiff Metropolitan University', 20, 'university', 'Wales', '', '', '', '', ''),
(377, ' Cardiff University International Study Centre', 20, 'college', 'Wales', '', '', '', '', ''),
(378, ' Coventry University London International Study Centre', 20, 'college', 'London', '', '', '', '', ''),
(379, ' Coventry University Coventry Campus', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(380, ' Coventry University London Campus', 20, 'university', 'London', '', '', '', '', ''),
(381, ' Cranfield University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(382, ' De MontFort University', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(383, ' Edinburgh Napier University', 20, 'university', 'Scotland', '', '', '', '', ''),
(384, ' Glasgow Caledonian University', 20, 'university', 'Scotland', '', '', '', '', ''),
(385, ' Heriot Watt University', 20, 'university', 'Scotland', '', '', '', '', ''),
(386, ' Hult International Business School', 20, 'college', 'London', '', '', '', '', ''),
(387, ' Instituto Marangoni', 20, 'college', 'London', '', '', '', '', ''),
(388, ' INTO Glasgow Caledonian University', 20, 'university', 'Scotland', '', '', '', '', ''),
(389, ' INTO City University', 20, 'university', 'London', '', '', '', '', ''),
(390, ' INTO London', 20, 'university', 'London', '', '', '', '', ''),
(391, ' INTO Manchester Metropolitan University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(392, ' INTO Newcastle University', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(393, ' INTO Queen\'s University Belfast', 20, 'university', 'Northern Ireland', '', '', '', '', ''),
(394, ' INTO University of East of Anglia', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(395, ' INTO University of Exeter', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(396, ' INTO University of Gloucestershire', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(397, ' INTO University of Manchester', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(398, ' Kaplan- Bournemouth University', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(399, ' Kaplan International College London', 20, 'college', 'London', '', '', '', '', ''),
(400, ' Kaplan- Nottingham Trent International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(401, ' Kaplan- Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(402, ' Kaplan- University of Essex International College', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(403, ' Kaplan- University of Glasgow International College', 20, 'college', 'Scotland', '', '', '', '', ''),
(404, ' Kaplan- University of Liverpool International College', 20, 'college', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(405, ' Kaplan- University of Nottingham International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(406, ' Kaplan- UWE Bristol', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(407, ' Kaplan-University of Brighton', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(408, ' Kaplan-University of York', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(409, ' Kingston University', 20, 'university', 'London', '', '', '', '', ''),
(410, ' Le - Cordon Bleu', 20, 'college', 'London', '', '', '', '', ''),
(411, ' Leeds Beckett University', 20, 'university', 'WEST YORKSHIRE ENGLAND', '', '', '', '', ''),
(412, ' Liverpool Hope University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(413, ' London Metropolitan University', 20, 'university', 'London', '', '', '', '', ''),
(414, ' London South Bank University', 20, 'university', 'London', '', '', '', '', ''),
(415, ' Manchester Metropolitan University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(416, ' Middlesex University', 20, 'university', 'London', '', '', '', '', ''),
(417, ' Navitas University of Leicester Global Study Centre', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(418, ' Navitas Birmingham City University- International College', 20, 'college', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(419, ' Navitas Hertfordshire International Colleage', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(420, ' Navitas Lancaster University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(421, ' Navitas London Brunel International College', 20, 'college', 'London', '', '', '', '', ''),
(422, ' Navitas- Robert Gordon University', 20, 'university', 'Scotland', '', '', '', '', ''),
(423, ' Navitas University of International College Portsmouth', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(424, ' Navitas University of Northampton International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(425, ' Navitas University of Plymouth International College', 20, 'college', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(426, ' Newcastle University', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(427, ' Northumbria University London Campus', 20, 'university', 'London', '', '', '', '', ''),
(428, ' Northumbria University Newcastle Campus', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(429, ' Nottingham Trent University', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(430, ' OnCampus Coventry University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(431, ' OnCampus Goldsmiths University of London', 20, 'university', 'London', '', '', '', '', ''),
(432, ' OnCampus University of Central Lancashire', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(433, ' OnCampus University of Hull', 20, 'university', 'YORKSHIRE AND THE HUMBER ENGLA', '', '', '', '', ''),
(434, ' OnCampus University of Reading', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(435, ' OnCampus Birkbeck University of London', 20, 'university', 'London', '', '', '', '', ''),
(436, ' Oncampus Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(437, ' Oxford Brookes University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(438, ' QAHE - Northumbria University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(439, ' QAHE - Solent University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(440, ' Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(441, ' Queen\'s University Belfast', 20, 'university', 'Northern Ireland', '', '', '', '', ''),
(442, ' Regent\'s University London', 20, 'university', 'London', '', '', '', '', ''),
(443, ' Royal Holloway University of London', 20, 'university', 'London', '', '', '', '', ''),
(444, ' Sheffield Hallam University', 20, 'university', 'SOUTH YORKSHIRE ENGLAND', '', '', '', '', ''),
(445, ' Solent University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(446, ' Staffordshire University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(447, ' Study Group - University of Surrey International Study Centre', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(448, ' Study Group- Bellerbys College Brighton', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(449, ' Study Group- Durham University Durham', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(450, ' Study Group- Lancaster University Lancaster', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(451, ' Study Group- Leeds Beckett University Leeds', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(452, ' Study Group- Liverpool John Moores University Liverpool', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(453, ' Study Group- The University of Sheffield International College', 20, 'college', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(454, ' Study Group University of Huddersfield', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(455, ' Study Group- University of Lincoln Lincoln', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(456, ' Study Group- University of Sussex Falmer', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(457, ' Swansea University', 20, 'university', 'Wales', '', '', '', '', ''),
(458, ' Teesside University', 20, 'university', 'NORTH YORKSHIRE ENGLAND', '', '', '', '', ''),
(459, ' The College Swansea University', 20, 'university', 'Wales', '', '', '', '', ''),
(460, ' The University of Huddersfield', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(461, ' The University of Huddersfield London Campus', 20, 'university', 'London', '', '', '', '', ''),
(462, ' The University of Northampton', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(463, ' Ulster University Birmingham Campus', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(464, ' Ulster University London Campus', 20, 'university', 'WEST MIDLANDS ENGLAND AND LOND', '', '', '', '', ''),
(465, ' University for the Creative Arts', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(466, ' University of Aberdeen - ISC', 20, 'university', 'Scotland', '', '', '', '', ''),
(467, ' University of Bedfordshire', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(468, ' University of Birmingham', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(469, ' University of Bolton', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(470, ' University of Bradford', 20, 'university', 'WEST YORKSHIRE ENGLAND', '', '', '', '', ''),
(471, ' University of Brighton', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(472, ' University ', 20, 'university', 'SOUTH WEST ENGLAND', 'ENGLAND', '', '', '', ''),
(473, ' University of Chester', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(474, 'University of Dundee', 20, 'university', 'Scotland', 'Dundee', '', 'dist/uploads/institute/1665571461.jpg', '', ''),
(475, ' University of East London', 20, 'university', 'London', '', '', '', '', ''),
(476, ' University of East London - ISC', 20, 'university', 'London', '', '', '', '', ''),
(477, ' University of Essex', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(478, ' University of Glasgow', 20, 'university', 'Scotland', '', '', '', '', ''),
(479, ' University of Greenwich', 20, 'university', 'Scotland', '', '', '', '', ''),
(480, ' University of Hertfordshire', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(481, ' University of Kingston - ISC', 20, 'university', 'London', '', '', '', '', ''),
(482, ' University of Law', 20, 'university', 'London', '', '', '', '', ''),
(483, ' University of Leicester', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(484, ' University of Liverpool', 20, 'university', 'NORTH WEST AND LONDON', '', '', '', '', ''),
(485, ' University of Plymouth', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(486, ' University of Portsmouth', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(487, ' University of Roehampton', 20, 'university', 'London', '', '', '', '', ''),
(488, ' University of Salford', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(489, ' University of South Wales', 20, 'university', 'Wales', '', '', '', '', ''),
(490, ' University of Stirling', 20, 'university', 'Scotland', '', '', '', '', ''),
(491, ' University of Strathclyde', 20, 'university', 'Scotland', '', '', '', '', ''),
(492, ' University of Strathclyde International Study Centre', 20, 'college', 'Scotland', '', '', '', '', ''),
(493, ' University of Suffolk', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(494, ' University of Sunderland London Campus', 20, 'university', 'London', '', '', '', '', ''),
(495, ' University of Sunderland Sunderland Campus', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(496, ' University of Surrey', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(497, ' University of the Creative Arts', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(498, 'UWE Bristol', 20, 'university', 'SOUTH WEST ENGLAND', 'Bristol', '', 'dist/uploads/institute/1665570639.jpeg', '', ''),
(499, ' University of West London', 20, 'university', 'London', '', '', '', '', ''),
(500, ' University of West of Scotland', 20, 'university', 'Scotland', '', '', '', '', ''),
(501, ' University of Westminster', 20, 'university', 'London', '', '', '', '', ''),
(502, ' University of Wolverhampton', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(503, ' University of York', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(504, ' University of East Anglia', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(505, ' Wrexham Glyndwr University', 20, 'university', 'Wrexham', '', '', '', '', ''),
(506, ' University of Wales trinity saint david', 20, 'university', 'Birmingham/London/Lampter/Swansea/Tileyard London/Carmarthen', '', '', '', '', ''),
(507, ' wrexham glyndwr university', 20, 'university', 'Wales', '', '', '', '', ''),
(508, ' St Andrew College Cambridge', 20, 'college', 'Tierra del Fuego', '', '', '', '', ''),
(509, ' Canterbury Christ Church University', 20, 'university', 'Pitcairn Island', '', '', '', '', ''),
(510, ' university of kingston international', 20, 'university', 'London', '', '', '', '', ''),
(511, ' City University of London', 20, 'university', 'England', '', '', '', '', ''),
(512, ' Buckinghamshire New University', 20, 'university', 'England', '', '', '', '', ''),
(513, ' St Clares Oxford', 20, 'college', 'England', '', '', '', '', ''),
(514, ' St Patricks College', 20, 'college', 'England', '', '', '', '', ''),
(515, ' University of Leeds', 20, 'university', 'England', '', '', '', '', ''),
(516, ' University College Birmingham', 20, 'university', 'England', '', '', '', '', ''),
(517, ' Durham University', 20, 'university', 'England', '', '', '', '', ''),
(518, ' University of Nottingham', 20, 'university', 'England', '', '', '', '', ''),
(519, ' DMU International College (DMUIC)', 20, 'college', 'England', '', '', '', '', ''),
(520, ' University of Gloucestershire', 20, 'university', 'England', '', '', '', '', ''),
(521, ' George Mason University', 21, 'university', 'Virginia', '', '', '', '', ''),
(522, ' Hofstra Universiy', 21, 'university', 'New York', '', '', '', '', ''),
(523, ' Washington State University', 21, 'university', 'Washington', '', '', '', '', ''),
(524, ' Oregon State University - (Tier I Research University) : Public', 21, 'university', 'Oregon', '', '', '', '', ''),
(525, ' University of South Florida - (-Tier I Research University) : Public', 21, 'university', 'Florida', '', '', '', '', ''),
(526, ' Saint Louis University ( Private research University) : Private', 21, 'university', 'Missouri', '', '', '', '', ''),
(527, ' Marshall University : Public', 21, 'university', 'West Virginia', '', '', '', '', ''),
(528, ' Suffolk University', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(529, ' The University of Alabama at Birmingham', 21, 'university', 'Alabama', '', '', '', '', ''),
(530, ' Jefferson University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(531, ' Long Island University Brooklyn', 21, 'university', 'New York', '', '', '', '', ''),
(532, ' Long Island University Post', 21, 'university', 'New York', '', '', '', '', ''),
(533, ' Mercer University', 21, 'university', 'Georgia', '', '', '', '', ''),
(534, ' Illinois State University', 21, 'university', 'Illinois', '', '', '', '', ''),
(535, ' Drew University', 21, 'university', 'New Jersey', '', '', '', '', ''),
(536, ' New England College', 21, 'college', 'New Hampshire', '', '', '', '', ''),
(537, ' Quinnipiac University', 21, 'university', 'Connecticut', '', '', '', '', ''),
(538, ' Navitas- Virginia Commonwealth University', 21, ' public university (ug  pg pathway and direct entry) universit', 'Virginia', '', '', '', '', ''),
(539, ' Colorado State University (Tier I Research University): Public', 21, 'university', 'Colorado', '', '', '', '', ''),
(540, ' University of Idaho', 21, 'university', 'Idaho', '', '', '', '', ''),
(541, ' Seattle Pacific University', 21, 'university', 'Washington', '', '', '', '', ''),
(542, ' University of Wisconsin--Milwaukee', 21, 'university', 'Milwaukee', '', '', '', '', ''),
(543, ' Cleveland State University', 21, 'university', 'Ohio', '', '', '', '', ''),
(544, ' University of Nevada', 21, ' reno university', 'Nevada', '', '', '', '', ''),
(545, ' Ohio University (Only UG) : Public', 21, 'university', 'Ohio', '', '', '', '', ''),
(546, ' University of Arizona- (Well Spring)', 21, 'university', 'Arizona', '', '', '', '', ''),
(547, ' Virginia Tech Language and Culture Institute (Only UG Pathways)', 21, 'college', 'Virginia', '', '', '', '', ''),
(548, ' Arizona State University', 21, 'university', 'Arizona', '', '', '', '', ''),
(549, ' University of Tampa', 21, 'university', 'Florida', '', '', '', '', ''),
(550, ' University at Albany (SUNY)', 21, 'university', 'New York', '', '', '', '', ''),
(551, ' The State University of New York at Geneseo (SUNY Geneseo)', 21, 'university', 'New York', '', '', '', '', ''),
(552, ' The State University of New York Polytechnic Institute (SUNY Polytechnic Institute)', 21, 'college', 'New York', '', '', '', '', ''),
(553, ' University of Colorado Denver', 21, 'university', 'DENVER', '', '', '', '', ''),
(554, ' University of Cincinnati', 21, ' ohio. : public university', 'Ohio', '', '', '', '', ''),
(555, ' University of Maryland', 21, ' baltimore county  the graduate school at umbc, university', 'Maryland', '', '', '', '', ''),
(556, ' University of Nebraska Lincoln', 21, 'university', 'Nebraska', '', '', '', '', ''),
(557, ' University of Nebraska at Kearney (Only UG)', 21, 'university', 'Nebraska', '', '', '', '', ''),
(558, ' Nebraska Wesleyan University', 21, 'university', 'Nebraska', '', '', '', '', '');
INSERT INTO `ggportal_tbl_institute` (`institute_id`, `institute_name`, `institute_country_id`, `institute_type`, `institute_state`, `institute_city`, `web_url`, `logo_url`, `email`, `mobile`) VALUES
(559, ' MCPHS University (Massachusetts College of Pharmacy and Health Sciences)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(560, ' University of Massachusetts Lowell', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(561, ' New Jersey Institute of Technology (NJIT)', 21, 'college', 'New Jersey', '', '', '', '', ''),
(562, ' Duquesne University (Private University)', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(563, ' San Jose State University', 21, 'university', 'California', '', '', '', '', ''),
(564, ' Montana State University : Public', 21, 'university', 'Michigan', '', '', '', '', ''),
(565, ' Ashland University', 21, 'university', 'Ohio', '', '', '', '', ''),
(566, ' Indiana State University', 21, 'university', 'Indiana', '', '', '', '', ''),
(567, ' Kent State University : Public', 21, 'university', 'Ohio', '', '', '', '', ''),
(568, ' The State University of New York at New Paltz (SUNY New Paltz)', 21, 'university', 'New York', '', '', '', '', ''),
(569, ' State University of New York at Fredonia (UG and PG Programs)', 21, 'university', 'New York', '', '', '', '', ''),
(570, ' Indiana University of Pennsylvania', 21, 'university', 'Indiana', '', '', '', '', ''),
(571, ' Wichita State University : Public', 21, 'university', 'Kansas', '', '', '', '', ''),
(572, ' Gannon University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(573, ' The University of Memphis', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(574, ' Tennessee Tech University : Public', 21, 'university', 'Tennessee', '', '', '', '', ''),
(575, ' Florida Institute Of Technology', 21, 'university', 'Florida', '', '', '', '', ''),
(576, ' Midwestern State University : Public', 21, 'university', 'Texas', '', '', '', '', ''),
(577, ' Southeast Missouri State University : Public', 21, 'university', 'Missouri', '', '', '', '', ''),
(578, ' University of Missouri', 21, 'university', 'Missouri', '', '', '', '', ''),
(579, ' Youngstown State University of Youngstown', 21, 'university', 'Ohio', '', '', '', '', ''),
(580, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(581, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(582, ' California State University', 21, ' dominguez hills - public university', 'California', '', '', '', '', ''),
(583, ' California State University', 21, ' stanislaus university', 'California', '', '', '', '', ''),
(584, ' California Baptist University', 21, 'university', 'California', '', '', '', '', ''),
(585, ' Humboldt State University : Public', 21, 'university', 'California', '', '', '', '', ''),
(586, ' Western Kentucky University : Public', 21, 'university', 'Kentucky', '', '', '', '', ''),
(587, ' Texas State University', 21, ' san marcos university', 'Texas', '', '', '', '', ''),
(588, ' Dakota State University', 21, 'university', 'Medison', '', '', '', '', ''),
(589, ' Pittsburg State University', 21, 'university', 'Kansas', '', '', '', '', ''),
(590, ' University Of Central Oklahoma', 21, 'university', 'Oklahoma', '', '', '', '', ''),
(591, ' Northwest Missourie State University : Public', 21, 'university', 'Missouri', '', '', '', '', ''),
(592, ' Missouri State University', 21, 'university', 'Missouri', '', '', '', '', ''),
(593, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(594, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(595, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(596, ' University of New Hampshire', 21, 'university', 'DENVER', '', '', '', '', ''),
(597, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(598, ' University of New Haven', 21, 'university', 'California', '', '', '', '', ''),
(599, ' University of New Haven', 21, 'private university', 'Connecticut', '', '', '', '', ''),
(600, ' University of Bridgeport : Private', 21, 'university', 'Connecticut', '', '', '', '', ''),
(601, ' Pacific Lutheran University (International Pathways and UG/PG Programs)', 21, 'university', 'Washington', '', '', '', '', ''),
(602, ' California Lutheran University', 21, 'university', 'California', '', '', '', '', ''),
(603, ' Murray State University. : Public', 21, 'university', 'Kentucky', '', '', '', '', ''),
(604, ' Altantis University', 21, 'university', 'Florida', '', '', '', '', ''),
(605, ' Rider University', 21, 'university', 'New Jersy', '', '', '', '', ''),
(606, ' NY Tech (NYIT) New York : Private', 21, 'college', 'New York', '', '', '', '', ''),
(607, ' Marist College', 21, 'college', 'New York', '', '', '', '', ''),
(608, ' Golden Gate University', 21, 'university', 'California', '', '', '', '', ''),
(609, ' University of Wisconsin-Eau Claire.', 21, 'university', 'Wisconsin', '', '', '', '', ''),
(610, ' University of WisconsinLa Crosse', 21, 'university', 'Wisconsin', '', '', '', '', ''),
(611, ' State University of New York at Plattsburgh (SUNY Plattsburgh)', 21, 'university', 'New York', '', '', '', '', ''),
(612, ' State University of New York College at Old Westbury', 21, 'university', 'New York', '', '', '', '', ''),
(613, ' University of Mary Hardin-Baylor', 21, 'university', 'Texas', '', '', '', '', ''),
(614, ' Mississippi College : Private', 21, 'college', 'Mississippi', '', '', '', '', ''),
(615, ' Marshall University', 21, 'university', 'West Virginia', '', '', '', '', ''),
(616, ' Troy University : Public', 21, 'university', 'Alabama', '', '', '', '', ''),
(617, ' Texas Wesleyan University', 21, 'university', 'Texas', '', '', '', '', ''),
(618, ' Canisius College', 21, 'college', 'New York', '', '', '', '', ''),
(619, ' Oklahoma city University ', 21, 'private university', 'Oklahoma', '', '', '', '', ''),
(620, ' Dallas Baptist University', 21, 'university', 'Texas', '', '', '', '', ''),
(621, ' The University of Findlay', 21, 'university', 'Ohio', '', '', '', '', ''),
(622, ' Western New England University', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(623, ' Southern New Hampshire', 21, 'college', 'New Hampshire', '', '', '', '', ''),
(624, ' Rivier University', 21, 'university', 'New Hampshire', '', '', '', '', ''),
(625, ' Northwood University', 21, 'university', 'Michigan', '', '', '', '', ''),
(626, ' New England Institute of Technology: Private', 21, 'college', '', '', '', '', '', ''),
(627, ' Monroe College', 21, ' new york : private university', 'New York', '', '', '', '', ''),
(628, ' University of California', 21, 'university', 'California', '', '', '', '', ''),
(629, ' University of California Riverside Extension', 21, ' california (for international education program) university', 'California', '', '', '', '', ''),
(630, ' University of California', 21, ' irvine division of continuing education university', 'California', '', '', '', '', ''),
(631, ' Drexel University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(632, ' California State University', 21, ' bakersfield university', 'California', '', '', '', '', ''),
(633, ' California State University', 21, ' los angeles (elp programs at ug/pg level) university', 'California', '', '', '', '', ''),
(634, ' Academy of Art University', 21, 'university', 'California', '', '', '', '', ''),
(635, ' Full Sail University', 21, 'university', 'Florida', '', '', '', '', ''),
(636, ' Cleary University', 21, 'university', 'Michigan', '', '', '', '', ''),
(637, ' New York Film Academy', 21, ' new york city : private', 'New York', '', '', '', '', ''),
(638, ' Rocky Mountain College of Art and Design (RMCAD)', 21, 'college', 'Colorado', '', '', '', '', ''),
(639, ' Upper Iowa University : Private', 21, 'university', 'Iowa', '', '', '', '', ''),
(640, ' Briar Cliff University', 21, 'university', 'Iowa', '', '', '', '', ''),
(641, ' California Flight Center', 21, 'college', 'California', '', '', '', '', ''),
(642, ' San Francisco State University', 21, 'university', 'California', '', '', '', '', ''),
(643, ' Fisher College', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(644, ' Northeastern University in Boston - The College of Professional Studies (CPS)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(645, ' Pace University', 21, 'university', 'New York', '', '', '', '', ''),
(646, ' University of North Texas', 21, ' texas public university', 'Texas', '', '', '', '', ''),
(647, ' University of New Mexico', 21, 'university', 'New Mexico', '', '', '', '', ''),
(648, ' Arkansas State University', 21, 'university', 'Arkansas', '', '', '', '', ''),
(649, ' Southern Illinois University', 21, 'university', 'Illinois', '', '', '', '', ''),
(650, ' Tiffin University', 21, 'university', 'Ohio', '', '', '', '', ''),
(651, ' Alvernia University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(652, ' Point Park University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(653, ' Bellevue College', 21, 'united states washington', 'Washington', '', '', '', '', ''),
(654, ' St. Thomas Aquinas College', 21, 'college', 'New York', '', '', '', '', ''),
(655, ' Slippery Rock University of Pennsylvania', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(656, ' Hult International Business School Boston & San Francisco', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(657, ' Charleston Southern University', 21, 'university', 'South Carolina', '', '', '', '', ''),
(658, ' University of Charleston', 21, 'university', 'West Virginia', '', '', '', '', ''),
(659, ' DeVry University', 21, 'university', 'Illinois', '', '', '', '', ''),
(660, ' Schiller International University', 21, 'united states florida', 'Florida', '', '', '', '', ''),
(661, ' National University', 21, 'united states california', 'California', '', '', '', '', ''),
(662, ' Concordia University Chicago', 21, 'university', 'Illinois', '', '', '', '', ''),
(663, ' Agnes Scott College', 21, 'college', 'Georgia', '', '', '', '', ''),
(664, ' Webster University', 21, 'university', 'Missouri', '', '', '', '', ''),
(665, ' Milwaukee School of Engineering', 21, 'college', 'Wisconsin', '', '', '', '', ''),
(666, ' West Virginia State University', 21, 'university', 'West Virginia', '', '', '', '', ''),
(667, ' Virginia Wesleyan University', 21, 'university', 'Virginia', '', '', '', '', ''),
(668, ' Saginaw Valley State University', 21, ' public university', 'Michigan', '', '', '', '', ''),
(669, ' University of Tulsa ( Direct entry only for UG - Through Kaplan )', 21, 'university', 'Oklahoma', '', '', '', '', ''),
(670, ' Lawrence Technological University - LTU', 21, 'university', 'Michigan', '', '', '', '', ''),
(671, ' Saint Leo University', 21, 'university', 'Florida', '', '', '', '', ''),
(672, ' Johnson and Wales University', 21, 'university', 'Rhode Island', '', '', '', '', ''),
(673, ' Park University', 21, ' missouri university', 'Missouri', '', '', '', '', ''),
(674, ' Le Cordon Bleu Paris(Hong Kong) Ltd', 21, 'college', '', '', '', '', '', ''),
(675, ' Valparaiso University', 21, 'united states indiana', 'Indiana', '', '', '', '', ''),
(676, ' Trine University', 21, 'university', 'Indiana', '', '', '', '', ''),
(677, ' Aviation Institute of Maintenance', 21, 'college', 'Virginia', '', '', '', '', ''),
(678, ' Northern Arizona University', 21, ' usa university', 'Arizona', '', '', '', '', ''),
(679, ' Summer Discovery. Discovery Internship and Jr. Internship.', 21, 'college', '', '', '', '', '', ''),
(680, ' Manhattan College', 21, ' new york (well spring) college', 'New York', '', '', '', '', ''),
(681, ' UMass Amherst (ECE MS 1+1 Programs)', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(682, ' On Campus Boston- Curry College (UG transfer Program)', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(683, ' Florida Polytechnic University', 21, 'university', 'Florida', '', '', '', '', ''),
(684, ' Mississippi State University', 21, 'university', 'Mississippi', '', '', '', '', ''),
(685, ' Seattle Central Community College : Public', 21, 'college', 'Washington', '', '', '', '', ''),
(686, ' Shoreline Community College', 21, ' washington college', 'Washington', '', '', '', '', ''),
(687, ' Green River College', 21, 'college', 'Washington', '', '', '', '', ''),
(688, ' San Mateo Colleges of Silicon Valley (Canada College', 21, 'united states california', 'California', '', '', '', '', ''),
(689, ' Santa Ana College', 21, 'united states california', 'California', '', '', '', '', ''),
(690, ' Hillsborough Community College', 21, 'college', 'Florida', '', '', '', '', ''),
(691, ' University of Dayton', 21, 'university', 'Ohio', '', '', '', '', ''),
(692, ' INTO - George Mason University UG Pathways & PG pathways.. : Public', 21, 'university', 'Virginia', '', '', '', '', ''),
(693, ' INTO -Hofstra University (UG', 21, ' pg pathway and direct entry)', 'New York', '', '', '', '', ''),
(694, ' INTO - Oregon State University - (Tier I Research University) UG Pathways & PG pathways.. : Public', 21, 'university', 'Oregon', '', '', '', '', ''),
(695, ' INTO - Saint Louis University ( Private research University) UG Pathways: Private', 21, 'university', 'Missouri', '', '', '', '', ''),
(696, ' INTO - Marshall University UG Pathways & PG pathways. : Public', 21, 'university', 'West Virginia', '', '', '', '', ''),
(697, ' INTO- Suffolk University (Private University)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(698, ' INTO New York at Drew University Undergraduate 2 year Standard Pathway', 21, 'university', 'New Jersey', '', '', '', '', ''),
(699, ' Pace University', 21, ' new york city. (kaplan - global pathway programs) u.g pathways & pg pathways.: private', 'New York', '', '', '', '', ''),
(700, ' Arizona State University (Kaplan- University Pathway)Global Launch Programs', 21, 'university', 'Arizona', '', '', '', '', ''),
(701, ' University of Massachusetts - MA UG pathways', 21, ' pg pathways and : public', 'Massachusetts', '', '', '', '', ''),
(702, ' University of Massachusetts- MA. (UG Pathways & PG Pathways) : Public', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(703, ' Loyola University New Orleans', 21, 'university', 'Louisiana', '', '', '', '', ''),
(704, ' Navitas- Florida Atlantic University (UG Pathways): Public', 21, 'university', 'Florida', '', '', '', '', ''),
(705, ' Navitas -Richard Bland College of William & Mary', 21, 'college', 'Virginia', '', '', '', '', ''),
(706, ' Navitas- Queens College of the City University of New York', 21, 'college', 'New York', '', '', '', '', ''),
(707, ' University of Hartford', 21, 'university', 'Connecticut', '', '', '', '', ''),
(708, ' DePaul University', 21, 'university', 'Illinois', '', '', '', '', ''),
(709, ' Louisiana State University', 21, 'university', 'Louisiana', '', '', '', '', ''),
(710, ' American University Washington D.C', 21, 'university', 'Washington', '', '', '', '', ''),
(711, ' Adelphi University', 21, 'university', 'New York', '', '', '', '', ''),
(712, ' Kings College - Canisius College', 21, 'college', 'New York', '', '', '', '', ''),
(713, ' Kings College - Marymount College', 21, ' los angeles. (undergraduate pathways) : private college', 'California', '', '', '', '', ''),
(714, ' Kings College - Pine Manor', 21, ' boston. (undergraduate pathways) : private college', 'Massachusetts', '', '', '', '', ''),
(715, ' American Honors (National Transter Network)', 21, 'college', 'Washington', '', '', '', '', ''),
(716, ' CATS Academy', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(717, ' Kaplan Test Prep', 21, 'college', 'California', '', '', '', '', ''),
(718, ' University of Central Florida', 21, 'university', 'Florida', '', '', '', '', ''),
(719, ' Auburn University', 21, 'university', 'Alabama', '', '', '', '', ''),
(720, ' Auburn University at Montgomery', 21, 'university', 'Montgomery', '', '', '', '', ''),
(721, ' Lynn University', 21, 'university', 'Florida', '', '', '', '', ''),
(722, ' The University of Kansas', 21, 'university', 'Kansas', '', '', '', '', ''),
(723, ' Florida International University (FIU)', 21, 'university', 'Florida', '', '', '', '', ''),
(724, ' University of South Carolina', 21, 'university', 'South Carolina', '', '', '', '', ''),
(725, ' University of the Pacific', 21, 'university', 'California', '', '', '', '', ''),
(726, ' University of Utah', 21, 'university', 'Utah', '', '', '', '', ''),
(727, ' James Madison University', 21, 'university', 'Virginia', '', '', '', '', ''),
(728, ' University of Vermont', 21, 'university', 'Vermont', '', '', '', '', ''),
(729, ' Oglethorpe University (Only UG - Both Pathway and Direct entry)', 21, 'university', 'Georgia', '', '', '', '', ''),
(730, ' Baylor University', 21, 'university', 'Texas', '', '', '', '', ''),
(731, ' Lipscomb University', 21, 'university', 'Tennessee', '', '', '', '', ''),
(732, ' Western Washington University', 21, 'university', 'Washington', '', '', '', '', ''),
(733, ' Texas A & M University', 21, 'university', 'Texas', '', '', '', '', ''),
(734, ' Academy of Art University', 21, 'college', 'California', '', '', '', '', ''),
(735, ' University of Illinois at Chicago', 21, 'university', 'Illinois', '', '', '', '', ''),
(736, ' Wright State University', 21, 'university', 'Ohio', '', '', '', '', ''),
(737, ' International American University', 21, 'university', 'California', '', '', '', '', ''),
(738, ' Mercy College', 21, 'college', 'New York', '', '', '', '', ''),
(739, ' AMERICAN COLLEGIATE LA (ACLA)', 21, 'university', 'New York', '', '', '', '', ''),
(740, ' AMERICAN COLLEGIATE DC (ACDC)', 21, 'university', 'New York', '', '', '', '', ''),
(741, ' GONZAGA UNIVERSITY (GU)', 21, 'university', 'New York', '', '', '', '', ''),
(742, ' UNIVERSITY OF MASSACHUSETTS BOSTON (UMB)', 21, 'university', 'New York', '', '', '', '', ''),
(743, ' UNIVERSITY OF MASSACHUSETTS AMHERST (UMA)', 21, 'university', 'New York', '', '', '', '', ''),
(744, ' UNIVERSITY OF MISSISSIPPI (OMI)', 21, 'university', 'New York', '', '', '', '', ''),
(745, ' Community Colleges of Spokane', 21, 'college', 'Washington', '', '', '', '', ''),
(746, ' Edgewood College', 21, 'college', 'Wisconsin', '', '', '', '', ''),
(747, ' Montana State University Billings', 21, 'university', 'Montana', '', '', '', '', ''),
(748, ' Hilbert College', 21, 'college', 'New York', '', '', '', '', ''),
(749, ' Bay Atlantic University', 21, 'university', 'Washington', '', '', '', '', ''),
(750, ' Castleton University', 21, 'university', 'Vermont', '', '', '', '', ''),
(751, ' Hawai?i Pacific University', 21, 'university', 'Hawaii', '', '', '', '', ''),
(752, ' Paul Smith\'s College', 21, 'college', 'New York', '', '', '', '', ''),
(753, ' Nichols College', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(754, ' Hartwick College', 21, 'college', 'New York', '', '', '', '', ''),
(755, ' Los Angeles Mission College', 21, 'college', 'California', '', '', '', '', ''),
(756, ' Presentation College', 21, 'college', 'South Dakota', '', '', '', '', ''),
(757, ' Seattle University', 21, 'university', 'Washington', '', '', '', '', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message`
--

CREATE TABLE `ggportal_tbl_message` (
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_status` varchar(1) NOT NULL DEFAULT 'Y',
  `is_read` varchar(1) NOT NULL DEFAULT 'N',
  `is_read_cu` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message`
--

INSERT INTO `ggportal_tbl_message` (`message_id`, `user_id`, `user_type`, `message`, `datetime`, `is_status`, `is_read`, `is_read_cu`) VALUES
(11, 25, 'RA', 'hi', '2022-12-31 19:45:35', 'Y', 'Y', 'Y'),
(12, 25, 'ST', 'hi', '2022-12-31 20:06:19', 'Y', 'N', 'Y'),
(13, 53, 'RA', 'hi', '2023-08-03 14:12:36', 'Y', 'Y', 'N'),
(14, 52, 'RA', 'kj', '2023-08-03 14:12:58', 'Y', 'Y', 'N'),
(15, 52, 'RA', 'u', '2023-08-03 15:38:51', 'Y', 'Y', 'N'),
(16, 95, 'ST', 'Good Morning', '2023-09-13 12:15:32', 'Y', 'Y', 'Y'),
(17, 95, 'RA', 'Good Morning! lad', '2023-09-13 12:21:18', 'Y', 'Y', 'Y'),
(18, 95, 'ST', 'what happned to my documents', '2023-09-13 14:40:47', 'Y', 'Y', 'Y'),
(19, 95, 'ST', 'working on that', '2023-09-14 11:14:16', 'Y', 'Y', 'Y'),
(20, 95, 'RA', 'no still not', '2023-09-14 11:14:43', 'Y', 'Y', 'Y'),
(21, 95, 'RA', 'are you there student', '2023-09-14 14:36:47', 'Y', 'Y', 'Y'),
(22, 95, 'ST', 'yes admin', '2023-09-14 14:45:08', 'Y', 'Y', 'Y'),
(23, 95, 'RA', 'very well', '2023-09-14 14:45:22', 'Y', 'Y', 'Y'),
(24, 95, 'ST', 'this is a test message by sithum', '2023-09-15 14:02:25', 'Y', 'Y', 'Y'),
(25, 96, 'RA', 'dsf', '2023-09-25 10:33:05', 'Y', 'Y', 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message_agent`
--

CREATE TABLE `ggportal_tbl_message_agent` (
  `message_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `agent_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message_agent`
--

INSERT INTO `ggportal_tbl_message_agent` (`message_id`, `student_id`, `agent_id`, `user_type`, `message`, `datetime`, `is_read`) VALUES
(1, 95, 35, 'AG', 'hi from agent', '2022-12-31 19:45:35', 'Y'),
(2, 95, 35, 'ST', 'hi from student', '2022-12-31 20:06:19', 'Y'),
(5, 52, 35, 'AG', 'how are you by agent', '2023-08-03 15:38:51', 'Y'),
(7, 95, 35, 'ST', 'im fine thanks from student', '2023-01-01 23:06:19', 'Y'),
(11, 25, 35, 'AG', 'did you submit ag', '2023-08-03 15:38:51', 'Y'),
(12, 95, 35, 'ST', 'Test Start st', '2023-09-15 16:29:35', 'Y'),
(13, 95, 35, 'AG', 'agent second message', '2023-09-16 14:40:04', 'Y'),
(14, 95, 35, 'ST', 'Malaka Second message to agent', '2023-09-16 14:40:42', 'Y'),
(15, 95, 35, 'AG', 'agent Second message', '2023-09-18 14:03:32', 'Y'),
(16, 95, 35, 'ST', 'are you there', '2023-09-18 14:13:59', 'Y'),
(17, 95, 35, 'ST', 'Malaka Thired Message', '2023-09-18 14:25:39', 'Y'),
(18, 95, 35, 'AG', 'agent Thired Message', '2023-09-18 14:26:01', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message_staff`
--

CREATE TABLE `ggportal_tbl_message_staff` (
  `message_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message_staff`
--

INSERT INTO `ggportal_tbl_message_staff` (`message_id`, `student_id`, `staff_id`, `user_type`, `message`, `datetime`, `is_read`) VALUES
(1, 95, 56, 'SF', 'hi from staff', '2022-12-31 19:45:35', 'Y'),
(2, 95, 56, 'ST', 'hi from student', '2022-12-31 20:06:19', 'Y'),
(5, 52, 54, 'RA', 'u', '2023-08-03 15:38:51', 'Y'),
(7, 95, 56, 'ST', 'what\'s the progress', '2023-01-01 23:06:19', 'Y'),
(11, 25, 54, 'RA', 'did you submit', '2023-08-03 15:38:51', 'Y'),
(12, 95, 56, 'ST', 'Test Start', '2023-09-15 16:29:35', 'Y'),
(13, 95, 56, 'SF', 'staff second message', '2023-09-16 14:40:04', 'Y'),
(14, 95, 56, 'ST', 'Malaka Second message to Nilaksha', '2023-09-16 14:40:42', 'Y'),
(15, 95, 56, 'SF', 'Nilaksha Second message', '2023-09-18 14:03:32', 'Y'),
(16, 95, 56, 'ST', 'are you there', '2023-09-18 14:13:59', 'Y'),
(17, 95, 56, 'ST', 'Malaka Thired Message', '2023-09-18 14:25:39', 'Y'),
(18, 95, 56, 'SF', 'Nilaksha Thired Message', '2023-09-18 14:26:01', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_program`
--

CREATE TABLE `ggportal_tbl_program` (
  `program_id` int(11) NOT NULL,
  `program_name` varchar(450) NOT NULL,
  `course_type` varchar(50) NOT NULL,
  `course_id` varchar(45) NOT NULL,
  `country_id` int(11) NOT NULL,
  `city` varchar(50) DEFAULT NULL,
  `institute_id` int(11) NOT NULL,
  `deadline` date NOT NULL,
  `currency_id` int(11) NOT NULL,
  `commission` decimal(10,2) NOT NULL,
  `tution_fee` decimal(10,2) NOT NULL,
  `application_fee` decimal(10,2) DEFAULT NULL,
  `intake` varchar(200) NOT NULL,
  `duration` varchar(90) NOT NULL,
  `ets` varchar(45) NOT NULL,
  `requirements` varchar(450) NOT NULL,
  `english_requirements` varchar(450) NOT NULL,
  `program_web_url` varchar(500) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_program`
--

INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`) VALUES
(9, 'Accounting and Finance (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 9250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 UK credit points (120 ECTS credits)) in Accounting and Finance.', '', 'https://courses.uwe.ac.uk/N30H/accounting-and-finance-top-up'),
(10, 'Accounting and Business Management - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'Three years full-time; Four years sandwich', 'https://www.uwe.ac.uk/courses/applying/underg', 'Don\'t Meet the Entry Requirements', '', 'https://courses.uwe.ac.uk/N4NB/accounting-and-business-management'),
(11, 'Business and Management (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed the equivalent of 240 UK credit points (120 ECTS credits) in a related subject discipline.', '', 'https://courses.uwe.ac.uk/N12T/business-and-management-top-up'),
(12, 'Banking and Finance (Top-up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 9250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed the equivalent of 240 UK credit points (120 ECTS credits) in a related subject discipline.', '', 'https://courses.uwe.ac.uk/N310/banking-and-finance-top-up'),
(13, 'Business and Events Management (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 9250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a Business Management related subject.', '', 'https://courses.uwe.ac.uk/NN2G/business-and-events-management-top-up'),
(14, 'Business and Human Resource Management (top-up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 9250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N1NY/business-and-human-resource-management-top-up'),
(15, 'Business Management and Economics (top-up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N1LH/business-management-and-economics-top-up'),
(16, 'Business Management with Marketing (top-up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N1NW/business-management-with-marketing-top-up'),
(17, 'International Business Communication (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N1P9/international-business-communication-top-up'),
(18, 'International Business Management (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N12U/international-business-management-top-up'),
(19, 'Marketing (Top Up)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/N50J/marketing-top-up'),
(20, 'Fine Art (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Fine Art.', '', 'https://courses.uwe.ac.uk/W10F/fine-art-with-foundation-year'),
(21, 'Illustration (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '', '', 'https://courses.uwe.ac.uk/W20F/illustration-with-foundation-year'),
(26, 'Accounting (with Foundation Year) ', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 0.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Accounting.', '', 'https://courses.uwe.ac.uk/N4KF/accounting-with-foundation-year'),
(22, 'Fine Art - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '', '', 'https://courses.uwe.ac.uk/W101/fine-art'),
(23, 'Illustration - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Illustration (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W224/illustration'),
(24, 'Curating - MA/MFA', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND DEC', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We are looking for highly motivated, creative students with some prior experience in curating or a related area', '', 'https://courses.uwe.ac.uk/W90L12/curating'),
(25, 'Multi-Disciplinary Printmaking - MA', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, ' SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We are looking for highly motivated, creative students with some prior experience in curating or a related area', '', 'https://courses.uwe.ac.uk/W90L12/curating'),
(27, 'Accounting and Finance (with Foundation Year)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Accounting.', '', 'https://courses.uwe.ac.uk/N4PF/accounting-and-finance-with-foundation-year'),
(28, 'Banking and Finance (with Foundation Year) - BSc(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Accounting.', '', 'https://courses.uwe.ac.uk/N30F/banking-and-finance-with-foundation-year'),
(29, 'Accounting - BSc(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 0.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Accounting.', '', 'https://courses.uwe.ac.uk/N40K/accounting'),
(30, 'Accounting and Business Management', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Accounting and Business Management (with Foundation Year).', '', 'https://courses.uwe.ac.uk/N4NB/accounting-and-business-management'),
(31, 'Accounting and Finance - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', ' honours degree of 2:1 or above in Economics, Mathematics or a Business related subject.', '', 'https://courses.uwe.ac.uk/N34012/accounting-and-finance'),
(32, 'Finance and Investment - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', ' honours degree of 2:2 or above in Accounting, Finance, Economics, Business, Business-related subjects, Mathematics or another quantitative subject.', '', 'https://courses.uwe.ac.uk/N39012/finance-and-investment'),
(33, 'Financial Technology (FinTech) - MSc', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'honours degree of 2:2 or above.', '', 'https://courses.uwe.ac.uk/N3I212/financial-technology-fintech'),
(34, 'Risk Management and Insurance - MSc', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'honours degree of 2:2 or above in any subject.', '', 'https://courses.uwe.ac.uk/N32012/risk-management-and-insurance'),
(35, 'Architectural Technology and Design (with Foundation Year) - BSc(Hons', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/K26F/architectural-technology-and-design-with-foundation-year'),
(36, 'Architecture and Environmental Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 0.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/KH1F/architecture-and-environmental-engineering-with-foundation-year'),
(37, 'Architecture and Environmental Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'HND or Foundation Degree (or equivalent), or have successfully completed a course which is equivalent to the first two years of a UK honours degree (240 credit points (120 ECTS credits)) in a related subject.', '', 'https://courses.uwe.ac.uk/KH1F/architecture-and-environmental-engineering-with-foundation-year'),
(38, 'https://www.uwe.ac.uk/courses/applying/international-applications/english-language-requirements', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Architecture and Planning.', '', 'https://courses.uwe.ac.uk/KK1F/architecture-and-planning-with-foundation-year'),
(39, 'Interior Architecture (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Architecture and Planning.', '', 'https://courses.uwe.ac.uk/K12F/interior-architecture-with-foundation-year'),
(40, 'Architectural Technology and Design - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Architecture and Planning.', '', 'https://courses.uwe.ac.uk/K236/architectural-technology-and-design'),
(41, 'Architecture', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 15500.00, 0.00, 'JAN AND SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We will usually make an assesment based on your UCAS application and we do not require portfolios.', '', 'https://courses.uwe.ac.uk/K100/architecture'),
(42, 'Architecture and Environmental Engineering - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Architecture and Environmental Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/KH12/architecture-and-environmental-engineering'),
(43, 'Architecture and Planning - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Architecture and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/KK14/architecture-and-planning'),
(44, 'Interior Architecture - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Architecture and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/K120/interior-architecture'),
(45, 'Architecture - March', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 15500.00, 0.00, 'JAN AND SEP', '2 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'architecture degree of 2:2 plus a minimum of 60% in the final year\'s design studio module (or equivalent module) that is recognised by ARB, NAAB, RIBA, CCA or EAAE.', '', 'https://courses.uwe.ac.uk/K10B1/architecture'),
(46, 'Computational Architecture - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', '', '', 'https://courses.uwe.ac.uk/K19A12/computational-architecture'),
(68, 'Cyber Security and Digital Forensics (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '\"If you exceed the entry requirements you may be eligible for BSc(Hons) Cyber Security and Digital Forensics.  \"', '', 'https://courses.uwe.ac.uk/G4HF/cyber-security-and-digital-forensics-with-foundation-year'),
(47, 'Facade Engineering - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 18000.00, 0.00, ' SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'A honours degree of 2:1 or above in an engineering, built environment or architecture related subject.', '', 'https://courses.uwe.ac.uk/K90D1/facade-engineering'),
(48, 'Professional Practice and Management in Architecture - Postgraduate Certificate', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 2100.00, 0.00, 'JAN AND DEC', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', ' normally require Part 1 and Part 2 qualifications prescribed by the Architects Registration Board or graduates of courses recognised for exemption from the RIBA Part 1 and Part 2 examination*.', '', 'https://courses.uwe.ac.uk/K10A/professional-practice-and-management-in-architecture'),
(49, 'Audio and Music Technology - BSc(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you are an international student and do not meet the academic requirements to study this course, you can qualify by completing preparatory study at our International College.', '', 'https://courses.uwe.ac.uk/J932/audio-and-music-technology'),
(50, 'Creative Music Technology - BSc(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/WJ39/creative-music-technology'),
(51, 'Biological Sciences (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Biological Sciences.', '', 'https://courses.uwe.ac.uk/C1MF/biological-sciences-with-foundation-year'),
(52, 'Biological Sciences (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Biological Sciences.', '', 'https://courses.uwe.ac.uk/C11F/biological-sciences-with-foundation-year'),
(53, 'Biomedical Science (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Biomedical Science.', '', 'https://courses.uwe.ac.uk/C98F/biomedical-science-with-foundation-year'),
(54, 'Biomedical Science (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Biomedical Science.', '', 'https://courses.uwe.ac.uk/C9MF/biomedical-science-with-foundation-year'),
(55, 'Biological Sciences - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'you may be eligible for BSc(Hons) Biological Sciences (with Foundation Year).', '', 'https://courses.uwe.ac.uk/C110/biological-sciences'),
(56, 'Biological Sciences - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'you may be eligible for BSc(Hons) Biological Sciences (with Foundation Year).', '', 'https://courses.uwe.ac.uk/C11M/biological-sciences'),
(57, 'Applied Sciences - Masters in Research (MRes)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, ' JAN AND SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'An applicant will be expected to have a minimum of a 2:1 in a relevant first degree or equivalent.', '', 'https://courses.uwe.ac.uk/C99K1/applied-sciences'),
(58, 'Applied Transfusion and Transplantation Science - MSc', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'An undergraduate degree (at least 2:2, or equivalent) in a relevant subject.', '', 'https://courses.uwe.ac.uk/C99S12/applied-transfusion-and-transplantation-science'),
(59, 'Business Computing (with Foundation Year) - BSc(Hons)', 'Undergraduate', '0', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '\"If you exceed the entry requirements you may be eligible for BSc(Hons) Cyber Security and Digital Forensics.  \"', '', 'https://courses.uwe.ac.uk/N1IF/business-computing-with-foundation-year'),
(60, 'Aerospace Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Aerospace Engineering.', '', 'https://courses.uwe.ac.uk/H43F/aerospace-engineering-with-foundation-year'),
(61, 'Aerospace Engineering with Pilot Studies (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Aerospace Engineering with Pilot Studies.', '', 'https://courses.uwe.ac.uk/H45F/aerospace-engineering-with-pilot-studies-with-foundation-year'),
(62, 'Architecture and Environmental Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'fIVE years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Architecture and Environmental Engineering.', '', 'https://courses.uwe.ac.uk/KH1F/architecture-and-environmental-engineering-with-foundation-year'),
(63, 'Automotive Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Automotive Engineering.', '', 'https://courses.uwe.ac.uk/H31F/automotive-engineering-with-foundation-year'),
(64, 'Civil Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Civil Engineering.', '', 'https://courses.uwe.ac.uk/H29F/civil-engineering-with-foundation-year'),
(65, 'Electronic Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Electronic Engineering.', '', 'https://courses.uwe.ac.uk/H6DF/electronic-engineering-with-foundation-year'),
(66, 'Mechanical Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Mechanical Engineering.', '', 'https://courses.uwe.ac.uk/H3FF/mechanical-engineering-with-foundation-year'),
(67, 'Robotics (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Robotics.', '', 'https://courses.uwe.ac.uk/H67F/robotics-with-foundation-year'),
(69, 'Digital Media (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Digital Media.', '', 'https://courses.uwe.ac.uk/G45F/digital-media-with-foundation-year'),
(70, 'Games Technology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Games Technology.', '', 'https://courses.uwe.ac.uk/G61F/games-technology-with-foundation-year'),
(71, 'Software Engineering for Business (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Software Engineering for Business', '', 'https://courses.uwe.ac.uk/6F3F/software-engineering-for-business-with-foundation-year'),
(72, 'Information Technology - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'Applicants are required to have an HND or Foundation Degree in Computer Studies, Information Systems or Information Technology (or similar), with an average of two thirds Merits in Final year.', '', 'https://courses.uwe.ac.uk/G560/information-technology'),
(73, 'Information Technology Management for Business (ITMB) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you are applying to study at UWE Bristol and require additional support to meet our English language requirements, you may be able to attend one of our pre-sessional English courses. Read more about our Pre-Sessional English Programme.', '', 'https://courses.uwe.ac.uk/GN52/information-technology-management-for-business-itmb'),
(74, 'Artificial Intelligence - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 16000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'certificate of completion of a recognised and relevant online course', '', 'https://courses.uwe.ac.uk/I4001/artificial-intelligence'),
(75, 'Financial Technology (FinTech) - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'an honours degree of 2:2 or above.', '', 'https://courses.uwe.ac.uk/N3I212/financial-technology-fintech'),
(76, 'Building Surveying (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Building Surveying.', '', 'https://courses.uwe.ac.uk/K23F/building-surveying-with-foundation-year'),
(77, 'Construction Project Management (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Construction Project Management.', '', 'https://courses.uwe.ac.uk/K25F/construction-project-management-with-foundation-year'),
(78, 'Property Development and Planning (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Property Development and Planning.', '', 'https://courses.uwe.ac.uk/K43F/property-development-and-planning-with-foundation-year'),
(79, 'Quantity Surveying and Commercial Management (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Quantity Surveying and Commercial Management.', '', 'https://courses.uwe.ac.uk/KN2F/quantity-surveying-and-commercial-management-with-foundation-year'),
(80, 'Real Estate (with Foundation Year) - BSc(Hons)', '', '6', 20, '', 100, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Real Estate.', '', 'https://courses.uwe.ac.uk/K44F/real-estate-with-foundation-year'),
(81, 'Building Surveying - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Property Development and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/K230/building-surveying'),
(82, 'Property Development and Planning - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Property Development and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/K430/property-development-and-planning'),
(83, 'Facade Engineering - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 18000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'A honours degree of 2:1 or above in an engineering, built environment or architecture related subject.', '', 'https://courses.uwe.ac.uk/K90D1/facade-engineering'),
(84, 'Real Estate Management (Distance Learning) - MSc', '', '6', 20, '', 0, '0000-00-00', 3, 5.00, 18000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require an honours degree of 2.2 or above', '', 'https://courses.uwe.ac.uk/KN4A6/real-estate-management-distance-learning'),
(85, 'Real Estate Management (Distance Learning) - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 18000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require an honours degree of 2.2 or above', '', 'https://courses.uwe.ac.uk/KN4A6/real-estate-management-distance-learning'),
(86, 'Early Childhood (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Early Childhood.', '', 'https://courses.uwe.ac.uk/X31F/early-childhood-with-foundation-year'),
(87, 'Education (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Education.', '', 'https://courses.uwe.ac.uk/X34F/education-with-foundation-year'),
(88, 'Early Childhood - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Early Childhood (with Foundation Year).', '', 'https://courses.uwe.ac.uk/X312/early-childhood'),
(89, 'Primary Education (ITE) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'The Primary ITE course includes a high level of placement-based learning and working with children.', '', 'https://courses.uwe.ac.uk/X123/primary-education-ite'),
(90, 'Education (Early Years) - MA', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'An undergraduate degree (or equivalent) comprising at least 320 credits.', '', 'https://courses.uwe.ac.uk/X0084/education-early-years'),
(91, 'Education (Early Years) - MA', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'An undergraduate degree (or equivalent) comprising at least 320 credits.', '', 'https://courses.uwe.ac.uk/X0084/education-early-years'),
(92, 'Graphic Design (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W22F/graphic-design-with-foundation-year'),
(93, 'Interior Design (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/2C3F/interior-design-with-foundation-year'),
(94, 'Product Design (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Product Design.', '', 'https://courses.uwe.ac.uk/W41F/product-design-with-foundation-year'),
(95, 'Product Design Technology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Product Design Technology.', '', 'https://courses.uwe.ac.uk/W24F/product-design-technology-with-foundation-year'),
(96, 'Graphic Design - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Graphic Design (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W211/graphic-design'),
(97, 'Interior Design - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Interior Design (with Foundation Year).', '', 'https://courses.uwe.ac.uk/2C3W/interior-design'),
(98, 'Product Design - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Product Design (with Foundation Year)', '', 'https://courses.uwe.ac.uk/W241/product-design'),
(99, 'Product Design Technology - BSc(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BSc(Hons) Product Design Technology (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W240/product-design-technology'),
(100, 'Graphic Arts - MA', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements, please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W21D12/graphic-arts'),
(101, 'Drama - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W400/drama'),
(102, 'Drama and Acting - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W490/drama-and-acting'),
(103, 'Aerospace Engineering - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Aerospace Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H403/aerospace-engineering'),
(104, 'Civil Engineering - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Civil Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H290/civil-engineering'),
(105, 'Civil Engineering - Meng', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Civil Engineering or BEng(Hons) Civil Engineering (with Foundation Year)', '', 'https://courses.uwe.ac.uk/H29C/civil-engineering'),
(106, 'Electronic Engineering - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Electronic Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H61D/electronic-engineering'),
(107, 'Engineering - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'Applicants are required to have an HND or Foundation Degree (or equivalent) in an Engineering discipline, with content meeting the pre-requisites of the modules on the programme', '', 'https://courses.uwe.ac.uk/H110/engineering'),
(108, 'Mechanical Engineering - Meng', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Mechanical Engineering or BEng(Hons) Mechanical Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H301/mechanical-engineering'),
(109, 'Mechanical Engineering - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Mechanical Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H300/mechanical-engineering'),
(110, 'Robotics - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Robotics (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H671/robotics'),
(111, 'Civil Engineering - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in Civil Engineering or a related discipline', '', 'https://courses.uwe.ac.uk/H20H1/civil-engineering'),
(112, 'Engineering Management - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in an engineering or business related discipline.', '', 'https://courses.uwe.ac.uk/H9N21/engineering-management'),
(113, 'Health Technology - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'Applicants should have an undergraduate degree in a relevant subject such as science, engineering, computer science or healthcare.', '', 'https://courses.uwe.ac.uk/L5901/health-technology'),
(114, 'Transport Engineering and Planning - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', '', '', 'https://courses.uwe.ac.uk/K46D1/transport-engineering-and-planning'),
(115, 'Creative and Professional Writing (with Foundation Year) - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Creative and Professional Writing.', '', 'https://courses.uwe.ac.uk/W81F/creative-and-professional-writing-with-foundation-year'),
(116, 'English Language and Linguistics (with Foundation Year) - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) English Language and Linguistics.', '', 'https://courses.uwe.ac.uk/QQ3F/english-language-and-linguistics-with-foundation-year'),
(117, 'English Literature (with Foundation Year) - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) English Literature.', '', 'https://courses.uwe.ac.uk/Q30F/english-literature-with-foundation-year'),
(118, 'Film Studies (with Foundation Year) - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Film Studies.', '', 'https://courses.uwe.ac.uk/P3AF/film-studies-with-foundation-year'),
(119, 'History (with Foundation Year) - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) History.', '', 'https://courses.uwe.ac.uk/V10F/history-with-foundation-year'),
(120, 'Creative and Professional Writing - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Creative and Professional Writing (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W810/creative-and-professional-writing'),
(121, 'English Language and Linguistics - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) English Language and Linguistics (with Foundation Year).', '', 'https://courses.uwe.ac.uk/QQ3C/english-language-and-linguistics'),
(122, 'English Literature with Writing - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) English Literature with Writing (with Foundation Year).', '', 'https://courses.uwe.ac.uk/0PC3/english-literature-with-writing'),
(123, 'Film Studies - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Film Studies (with Foundation Year).', '', 'https://courses.uwe.ac.uk/P30A/film-studies'),
(124, 'History - BA(Hons)', 'Undergraduate', '7', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) History (with Foundation Year).', '', 'https://courses.uwe.ac.uk/V100/history'),
(126, 'Biological Sciences (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Biological Sciences.', '', 'https://courses.uwe.ac.uk/C1MF/biological-sciences-with-foundation-year'),
(127, 'Biological Sciences (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Biological Sciences.', '', 'https://courses.uwe.ac.uk/C11F/biological-sciences-with-foundation-year'),
(128, 'Environmental Science (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Environmental Science.', '', 'https://courses.uwe.ac.uk/F90F/environmental-science-with-foundation-year'),
(129, 'Environmental Science (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Environmental Science.', '', 'https://courses.uwe.ac.uk/F9MF/environmental-science-with-foundation-year'),
(130, 'Wildlife Ecology and Conservation Science (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Wildlife Ecology and Conservation Science.', '', 'https://courses.uwe.ac.uk/45MF/wildlife-ecology-and-conservation-science-with-foundation-year'),
(131, 'Wildlife Ecology and Conservation Science (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Wildlife Ecology and Conservation Science.', '', 'https://courses.uwe.ac.uk/45FF/wildlife-ecology-and-conservation-science-with-foundation-year'),
(132, 'Biological Sciences - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BSc(Hons) Biological Sciences (with Foundation Year).', '', 'https://courses.uwe.ac.uk/C110/biological-sciences'),
(133, 'Biological Sciences - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for MSci Biological Sciences (with Foundation Year).', '', 'https://courses.uwe.ac.uk/C11M/biological-sciences'),
(134, 'Environmental Science - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for MSci Environmental Science (with Foundation Year).', '', 'https://courses.uwe.ac.uk/F90M/environmental-science'),
(135, 'Integrated Wildlife Conservation (Top Up) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'GCSE subjects: Grade C/4 in English Literature or Language and Mathematics, or equivalent. We do not accept Level 2 Key Skills, Functional Skills or Certificates in Adult Numeracy and Literacy as alternatives to GCSEs.', '', 'https://courses.uwe.ac.uk/F75A/integrated-wildlife-conservation-top-up');
INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`) VALUES
(136, 'Wildlife Ecology and Conservation Science - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '\"If you don\'t meet the entry requirements, you may be eligible for BSc(Hons) Wildlife Ecology and Conservation Science (with Foundation Year).  \"', '', 'https://courses.uwe.ac.uk/45MN/wildlife-ecology-and-conservation-science'),
(137, 'Wildlife Ecology and Conservation Science - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for MSci Wildlife Ecology and Conservation Science (with Foundation Year).', '', 'https://courses.uwe.ac.uk/45MM/wildlife-ecology-and-conservation-science'),
(138, 'Advanced Wildlife Conservation in Practice - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'a good honours degree (minimum 2:2) in a subject which contains a core grounding in conservation, ecology and environmental scienc', '', 'https://courses.uwe.ac.uk/C1841/advanced-wildlife-conservation-in-practice'),
(139, 'Integrated Wildlife Conservation (Top Up) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'GCSE subjects: Grade C/4 in English Literature or Language and Mathematics, or equivalent. We do not accept Level 2 Key Skills, Functional Skills or Certificates in Adult Numeracy and Literacy as alternatives to GCSEs.', '', 'https://courses.uwe.ac.uk/F75A/integrated-wildlife-conservation-top-up'),
(140, 'Wildlife Ecology and Conservation Science - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', '', '', 'https://courses.uwe.ac.uk/45MN/wildlife-ecology-and-conservation-science'),
(141, 'Wildlife Ecology and Conservation Science - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for MSci Wildlife Ecology and Conservation Science (with Foundation Year).', '', 'https://courses.uwe.ac.uk/45MM/wildlife-ecology-and-conservation-science'),
(142, 'Advanced Wildlife Conservation in Practice - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'a good honours degree (minimum 2:2) in a subject which contains a core grounding in conservation, ecology and environmental scienc', '', 'https://courses.uwe.ac.uk/C1841/advanced-wildlife-conservation-in-practice'),
(143, 'Applied Sciences - Masters in Research (MRes)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'An applicant will be expected to have a minimum of a 2:1 in a relevant first degree or equivalent. ', '', 'https://courses.uwe.ac.uk/C99K1/applied-sciences'),
(144, 'Environmental Health - MSc/Postgraduate Diploma', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'hold a first degree (minimum 2:2 honours) in a science based discipline', '', 'https://courses.uwe.ac.uk/B90032/environmental-health'),
(145, 'Environmental Health - MSc/Postgraduate Diploma', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'hold a first degree (minimum 2:2 honours) in a science based discipline', '', 'https://courses.uwe.ac.uk/B90032/environmental-health'),
(146, 'Public Health - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'GCSE Grade C or above in English Language and Mathematics, or equivalents are required.', '', 'https://courses.uwe.ac.uk/BL9412/public-health'),
(147, 'Science Communication - MSc/Postgraduate Diploma', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '18 Months', 'https://www.uwe.ac.uk/courses/applying/intern', 'Applicants normally have an honours degree awarded by a UK institute of higher education of at least lower second status, in a relevant subject.', '', 'https://courses.uwe.ac.uk/P90012/science-communication'),
(148, 'Fashion Communication (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W2PF/fashion-communication-with-foundation-year'),
(149, 'Fashion Textiles (with Foundation Year) - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W23F/fashion-textiles-with-foundation-year'),
(150, 'Fashion Communication - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Fashion Communication (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W2P2/fashion-communication'),
(151, 'Fashion Textiles - BA(Hons)', 'Undergraduate', '1', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'Three years full-time; Four years sandwich', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Fashion Textiles (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W23A/fashion-textiles'),
(152, 'Animation (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/W61F/animation-with-foundation-year'),
(153, 'Film Studies (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Film Studies.', '', 'https://courses.uwe.ac.uk/P3AF/film-studies-with-foundation-year'),
(154, 'Photography (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Photography.', '', 'https://courses.uwe.ac.uk/W64F/photography-with-foundation-year'),
(155, 'Animation - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Animation (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W615/animation'),
(156, 'Film Studies - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 0.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Film Studies (with Foundation Year).', '', 'https://courses.uwe.ac.uk/P30A/film-studies'),
(157, 'Photography - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Photography (with Foundation Year).', '', 'https://courses.uwe.ac.uk/W640/photography'),
(158, 'Creative and Cultural Leadership - MA', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 13250.00, 0.00, ' JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We are looking for highly motivated applicants with skills and experience in creative, cultural, arts and heritage sectors.', '', 'https://courses.uwe.ac.uk/W9N91/creative-and-cultural-leadership'),
(159, 'Film and Television Industries - MA', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'International and EU applicants are required to have a minimum overall IELTS (Academic) score of 6.5 with 5.5 in each component (or approved equivalent*).', '', 'https://courses.uwe.ac.uk/P31L12/film-and-television-industries'),
(160, 'Screen Production (Documentary) - MA', 'Undergraduate', '6', 19, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements, please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/P31M12/screen-production-documentary'),
(161, 'Screen Production (Screenwriting) - MA', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements, please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/P31N12/screen-production-screenwriting'),
(162, 'Wildlife Filmmaking - MA', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the country information pages.', '', 'https://courses.uwe.ac.uk/D4P31/wildlife-filmmaking'),
(163, 'Forensic Science (with Foundation Year) - Msci', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Forensic Science.', '', 'https://courses.uwe.ac.uk/F4MF/forensic-science-with-foundation-year'),
(164, 'Forensic Science (with Foundation Year) - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for MSci Forensic Science.', '', 'https://courses.uwe.ac.uk/F41F/forensic-science-with-foundation-year'),
(165, 'MSci Forensic Science', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for MSci Forensic Science (with Foundation Year).', '', 'https://courses.uwe.ac.uk/F41M/forensic-science'),
(166, 'Forensic Science - BSc(Hons)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BSc(Hons) Forensic Science (with Foundation Year).', '', 'https://courses.uwe.ac.uk/F410/forensic-science'),
(167, 'Applied Sciences - Masters in Research (MRes)', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'An applicant will be expected to have a minimum of a 2:1 in a relevant first degree or equivalent. ', '', 'https://courses.uwe.ac.uk/C99K1/applied-sciences'),
(168, 'Forensic Science - MSc', 'Undergraduate', '4', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', '\"An honours degree awarded by a UK institute of higher education (at least 2:2), in a relevant applied or social science subject. \"', '', 'https://courses.uwe.ac.uk/F41G12/forensic-science'),
(169, 'Civil Engineering (with Foundation Year) - BEng(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BEng(Hons) Civil Engineering.', '', 'https://courses.uwe.ac.uk/H29F/civil-engineering-with-foundation-year'),
(170, '\"Civil Engineering - BEng(Hons) \"', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BEng(Hons) Civil Engineering (with Foundation Year).', '', 'https://courses.uwe.ac.uk/H290/civil-engineering'),
(171, 'Environmental Management - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the country information pages', '', 'https://courses.uwe.ac.uk/FJ17/environmental-management'),
(172, 'Geography - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the country information pages', '', 'https://courses.uwe.ac.uk/L700/geography'),
(173, 'Geography - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the country information pages', '', 'https://courses.uwe.ac.uk/FF89/geography'),
(174, 'Civil Engineering - MSc', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in Civil Engineering or a related discipline.', '', 'https://courses.uwe.ac.uk/H20H1/civil-engineering'),
(175, 'Environmental Consultancy - MSc', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in Civil Engineering or a related discipline.', '', 'https://courses.uwe.ac.uk/F90012/environmental-consultancy'),
(176, 'Project Management - MSc', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in Civil Engineering or a related discipline.', '', 'https://courses.uwe.ac.uk/K9N21/project-management'),
(177, 'Social Research (Health and Wellbeing) - Masters in Research (MRes)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree of 2:1', '', 'https://courses.uwe.ac.uk/L90B1/social-research-health-and-wellbeing'),
(178, 'Public Health - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a honours degree at 2:2 or above in Civil Engineering or a related discipline.', '', 'https://courses.uwe.ac.uk/BL9412/public-health'),
(179, 'Diagnostic Radiography - BSc(Hons)', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/B821/diagnostic-radiography'),
(180, 'Health Professions - Foundation Programme', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'low participation neighbourhoods ', '', 'https://courses.uwe.ac.uk/B900/health-professions'),
(181, 'Optometry - BSc(Hons)', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/B510/optometry'),
(182, 'Paramedic Science - BSc(Hons)', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/B950/paramedic-science'),
(183, 'Radiotherapy and Oncology - BSc(Hons)', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/B822/radiotherapy-and-oncology'),
(184, 'Health Technology - MSc', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements, please find your country on the Country Information pages.', '', 'https://courses.uwe.ac.uk/L5901/health-technology'),
(185, 'Media Communications (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Media Communications.', '', 'https://courses.uwe.ac.uk/P3HF/media-communications-with-foundation-year'),
(186, 'Media Production (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Media Production.', '', 'https://courses.uwe.ac.uk/P3GF/media-production-with-foundation-year'),
(187, 'Media Communications - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Media Communications (with Foundation Year).', '', 'https://courses.uwe.ac.uk/P30H/media-communications'),
(188, 'Media Production - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Media Production (with Foundation Year).', '', 'https://courses.uwe.ac.uk/P31G/media-production'),
(189, 'Journalism - MA', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We are looking for highly motivated, creative students who are passionate about telling stories', '', 'https://courses.uwe.ac.uk/P50012/journalism'),
(190, 'Journalism (Audio, Docs and Podcast) - MA', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We are looking for highly motivated, creative students who are passionate about telling stories', '', 'https://courses.uwe.ac.uk/P50J12/journalism-audio-docs-and-podcast'),
(191, 'Journalism (Specialist Features) - MA', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'International and EU applicants are required to have a minimum overall IELTS (Academic) score of 7.0 with 6.0 in each component (or approved equivalent*).', '', 'https://courses.uwe.ac.uk/P50K12/journalism-specialist-features'),
(192, 'Business and Law (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Business and Law.', '', 'https://courses.uwe.ac.uk/NM1F/business-and-law-with-foundation-year'),
(193, 'Criminology and Law (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Criminology and Law.', '', 'https://courses.uwe.ac.uk/MM9F/criminology-and-law-with-foundation-year'),
(195, 'Law (with Foundation Year) - LLB(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for LLB(Hons) Law.', '', 'https://courses.uwe.ac.uk/M10F/law-with-foundation-year'),
(196, 'Law with Business (with Foundation Year) - LLB(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'Law with Business (with Foundation Year) - LL', 'If you exceed the entry requirements you may be eligible for LLB(Hons) Law with Business.', '', 'https://courses.uwe.ac.uk/NMBF/law-with-business-with-foundation-year'),
(197, 'Business and Law - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Business and Law (with Foundation Year).', '', 'https://courses.uwe.ac.uk/NM11/business-and-law'),
(198, 'Criminology and Law - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Criminology and Law (with Foundation Year).', '', 'https://courses.uwe.ac.uk/MM19/criminology-and-law'),
(199, 'Law - LLB(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for LLB(Hons) Law (with Foundation Year).', '', 'https://courses.uwe.ac.uk/M100/law'),
(200, 'Law with Business - LLB(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for LLB(Hons) Law with Business (with Foundation Year).', '', 'https://courses.uwe.ac.uk/NM1B/law-with-business'),
(201, 'Advanced Legal Practice (LPC LLM) - LLM', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 12500.00, 0.00, ' SEP', '2 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'Qualifying law degree of 2:2 or above; or', '', 'https://courses.uwe.ac.uk/M3AC12/advanced-legal-practice-lpc-llm'),
(202, 'Bar Training Course - LLM/PGDip', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 13250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', '7.5 in the IELTS academic test (all sections)', '', 'https://courses.uwe.ac.uk/M99C12/bar-training-course'),
(203, 'Business and Events Management (with Foundation Year) - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Business and Events Management.', '', 'https://courses.uwe.ac.uk/NN2F/business-and-events-management-with-foundation-year'),
(204, 'Business Management and Marketing (with Foundation Year) - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Business Management and Marketing.', '', 'https://courses.uwe.ac.uk/NN5F/business-management-and-marketing-with-foundation-year'),
(205, 'Business Management and Marketing (with Foundation Year) - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Business Management and Marketing.', '', 'https://courses.uwe.ac.uk/NN5F/business-management-and-marketing-with-foundation-year'),
(206, 'Marketing (with Foundation Year) - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Marketing.', '', 'https://courses.uwe.ac.uk/N50F/marketing-with-foundation-year'),
(207, 'Business and Events Management - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Business and Events Management (with Foundation Year).', '', 'https://courses.uwe.ac.uk/NN21/business-and-events-management'),
(208, 'Marketing - BA(Hons)', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Marketing (with Foundation Year).', '', 'https://courses.uwe.ac.uk/N500/marketing'),
(209, 'Digital Marketing - MSc', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', ' honours degree (minimum 2:2) in Marketing or a closely related subject', '', 'https://courses.uwe.ac.uk/N5I11/digital-marketing'),
(210, 'Events Management - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '5', 20, '', 498, '0000-00-00', 3, 5.00, 14000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'honours degree (minimum 2.2) in any subject', '', 'https://courses.uwe.ac.uk/N8201/events-management'),
(211, 'Mathematics (with Foundation Year) - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Mathematics or MMath Mathematics.', '', 'https://courses.uwe.ac.uk/G10F/mathematics-with-foundation-year'),
(212, 'Mathematics - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the Country Information pages', '', 'https://courses.uwe.ac.uk/G101/mathematics'),
(213, 'Health Professions - Foundation Programme', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'low participation neighbourhoods ', '', 'https://courses.uwe.ac.uk/B900/health-professions'),
(214, 'Midwifery - BSc(Hons)', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/B711/midwifery'),
(215, 'Public Health (Specialist Community Public Health Nursing) - Postgraduate Diploma', 'Undergraduate', '3', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'Active first level registration on the Nursing and Midwifery Council (NMC) professional register.', '', 'https://courses.uwe.ac.uk/B71212/public-health-specialist-community-public-health-nursing'),
(216, 'Occupational Therapy - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements of this course, you may be eligible for the Health Professions Foundation Year.', '', 'https://courses.uwe.ac.uk/B920/occupational-therapy'),
(217, 'Physiotherapy - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements of this course, you may be eligible for the Health Professions Foundation Year.', '', 'https://courses.uwe.ac.uk/B160/physiotherapy'),
(218, 'Sport Rehabilitation - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/BC96/sport-rehabilitation'),
(219, 'Architecture and Planning (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'five years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Architecture and Planning.', '', 'https://courses.uwe.ac.uk/KK1F/architecture-and-planning-with-foundation-year'),
(220, 'Property Development and Planning (with Foundation Year) - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Property Development and Planning.', '', 'https://courses.uwe.ac.uk/K43F/property-development-and-planning-with-foundation-year'),
(221, 'Architecture and Planning - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Architecture and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/KK14/architecture-and-planning'),
(222, 'Property Development and Planning - BA(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Property Development and Planning (with Foundation Year).', '', 'https://courses.uwe.ac.uk/K430/property-development-and-planning'),
(223, 'Urban Planning - BSc(Hons)', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'For country specific entry requirements please find your country on the country information pages', '', 'https://courses.uwe.ac.uk/K401/urban-planning'),
(224, 'Planning and Urban Leadership (Distance Learning) - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 9000.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a first degree of 2:2 honours or above.', '', 'https://courses.uwe.ac.uk/K4062/planning-and-urban-leadership-distance-learning'),
(225, 'Transport Engineering and Planning - MSc', 'Undergraduate', '6', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'We normally require a first degree of 2:2 honours or above.', '', 'https://courses.uwe.ac.uk/K46D1/transport-engineering-and-planning'),
(226, 'Criminology (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Criminology.', '', 'https://courses.uwe.ac.uk/M90F/criminology-with-foundation-year'),
(227, 'Criminology and Law (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Criminology and Law.', '', 'https://courses.uwe.ac.uk/MM9F/criminology-and-law-with-foundation-year'),
(228, 'Criminology and Sociology (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Criminology and Sociology.', '', 'https://courses.uwe.ac.uk/ML3F/criminology-and-sociology-with-foundation-year'),
(229, 'Criminology with Psychology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Criminology with Psychology.', '', 'https://courses.uwe.ac.uk/M98F/criminology-with-psychology-with-foundation-year'),
(230, 'Psychology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Psychology.', '', 'https://courses.uwe.ac.uk/C80F/psychology-with-foundation-year'),
(231, 'Psychology with Criminology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Psychology with Criminology.', '', 'https://courses.uwe.ac.uk/C89F/psychology-with-criminology-with-foundation-year'),
(232, 'Sociology (with Foundation Year) - BA(Hons)', 'Undergraduate', '2', 19, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BA(Hons) Sociology.', '', 'https://courses.uwe.ac.uk/L30F/sociology-with-foundation-year'),
(233, 'Sociology with Psychology (with Foundation Year) - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'four years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you exceed the entry requirements you may be eligible for BSc(Hons) Sociology with Psychology.', '', 'https://courses.uwe.ac.uk/L38F/sociology-with-psychology-with-foundation-year'),
(234, 'Criminology and Law - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Criminology (with Foundation Year).', '', 'https://courses.uwe.ac.uk/M900/criminology'),
(235, 'Criminology - BA(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BA(Hons) Criminology and Law (with Foundation Year).', '', 'https://courses.uwe.ac.uk/MM19/criminology-and-law'),
(236, 'Psychology - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'If you don\'t meet the entry requirements, you may be eligible for BSc(Hons) Psychology (with Foundation Year).', '', 'https://courses.uwe.ac.uk/C800/psychology'),
(237, 'Business Psychology - MSc', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'You should have a good honours degree (2:2) or equivalent, in a relevant subject (e.g. Psychology, Business, Social Science or Science).', '', 'https://courses.uwe.ac.uk/C81512/business-psychology'),
(238, 'Sport and Exercise Psychology - MSc', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'an honours degree awarded by a UK institute of higher education of at least lower second status, in Psychology or a relevant subject', '', 'https://courses.uwe.ac.uk/C8901/sport-and-exercise-psychology'),
(239, 'Social Work - BSc(Hons)', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14250.00, 0.00, ' SEP', 'three years full time', 'https://www.uwe.ac.uk/courses/applying/intern', 'We require evidence of recent assessed academic study within the last three years.', '', 'https://courses.uwe.ac.uk/L500/social-work'),
(240, 'Environmental Health - MSc/Postgraduate Diploma', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14750.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'hold a first degree (minimum 2:2 honours) in a science based discipline', '', 'https://courses.uwe.ac.uk/B90032/environmental-health'),
(241, 'Public Health - MSc/Postgraduate Diploma/Postgraduate Certificate', 'Undergraduate', '2', 20, '', 498, '0000-00-00', 3, 5.00, 14500.00, 0.00, 'JAN AND SEP', '1 YEAR', 'https://www.uwe.ac.uk/courses/applying/intern', 'Applicants are normally required to have a 2:2 honours degree in a relevant subject area as well as appropriate experience. Applicants without a degree level qualification will be considered individually.', '', 'https://courses.uwe.ac.uk/BL9412/public-health'),
(242, 'Business Management (Hertford Regional College)', '', '5', 20, '', 480, '0000-00-00', 3, 5.00, 6165.00, 0.00, 'SEP', '1 YEAR', 'Grade 4/C in English Language and Mathematics', 'Made up of at least one A Level (A2), BTEC National Award or equivalent', '', 'https://www.herts.ac.uk/courses/foundation/fda-business-management2'),
(243, 'Business Management (West Herts College)', '', '5', 20, '', 480, '0000-00-00', 3, 5.00, 6165.00, 0.00, ' SEP', '1 YEAR', 'Business Management (West Herts College)', 'Made up of at least one A Level (A2), BTEC National Award or equivalent', '', 'https://www.herts.ac.uk/courses/foundation/fda-business-management4'),
(244, 'Business Management with Accounting (West Herts College)', '', '5', 20, '', 480, '0000-00-00', 3, 5.00, 6165.00, 0.00, ' SEP', '1 YEAR', 'Grade 4/C in English Language and Mathematics', 'Made up of at least one A Level (A2), BTEC National Award or equivalent', '', 'https://www.herts.ac.uk/courses/foundation/fda-business-management-with-accounting3'),
(245, 'Business Management with Accounting (Oaklands College)', '', '5', 20, '', 480, '0000-00-00', 3, 5.00, 6165.00, 0.00, ' SEP', '1 YEAR', 'Grade 4/C in English Language and Mathematics', 'Made up of at least one A Level (A2), BTEC National Award or equivalent', '', 'https://www.herts.ac.uk/courses/foundation/fda-business-management-with-accounting2'),
(246, 'Accountancy and Mathematics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/accountancy-mathematics'),
(247, 'Accountancy Bacc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/accountancy-bacc'),
(248, 'Accountancy BAcc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/accountancy-bacc-hons'),
(249, 'Finance BFin (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/finance'),
(250, 'International Finance BIFin (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/international-finance'),
(251, 'Finance -  BFin (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19500.00, 0.00, 'JAN AND SEP', '3.5 years', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/finance-accelerated'),
(252, 'International Finance - BIFin (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19500.00, 0.00, 'JAN AND SEP', '3.5 years', 'If English is not your first language, you wi', 'If you do not meet our academic grade requirements for your chosen course, we can offer you an alternative route to begin your studies. Our international incorporated degrees will develop your subject knowledge, academic English and university level study skills.', '', 'https://www.dundee.ac.uk/undergraduate/international-finance-accelerated'),
(253, 'Accounting & Finance MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN ', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in accounting, finance or related business discipline which contained elements of accounting and / or finance.', '', 'https://www.dundee.ac.uk/postgraduate/accounting-finance'),
(254, 'Finance MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN ', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in accounting, finance or related business discipline which contained elements of accounting and / or finance.', '', 'https://www.dundee.ac.uk/postgraduate/finance'),
(255, 'International Banking and Finance MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN ', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in accounting, finance or related business discipline which contained elements of accounting and / or finance.', '', 'https://www.dundee.ac.uk/postgraduate/international-banking-finance'),
(256, 'Islamic Finance MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in accounting, finance or related business discipline which contained elements of accounting and / or finance.', '', 'https://www.dundee.ac.uk/postgraduate/islamic-finance'),
(257, 'Professional Accountancy MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in accounting, finance or related business discipline which contained elements of accounting and / or finance.', '', 'https://www.dundee.ac.uk/postgraduate/professional-accountancy'),
(258, 'Anatomical Sciences BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'SEP', 'four years full time', 'If English is not your first language, you wi', 'AAB The essential subjects are Biology and Chemistry + Mathematics', '', 'https://www.dundee.ac.uk/undergraduate/anatomical-sciences'),
(259, 'Forensic Anthropology BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 0.00, 20900.00, 0.00, 'SEP', 'four years full time', 'If English is not your first language, you wi', 'AAB The essential subjects are Biology and Chemistry + Mathematics', '', 'https://www.dundee.ac.uk/undergraduate/forensic-anthropology'),
(260, 'Anatomy & Advanced Forensic Anthropology MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 19600.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A degree at 2:1 or above (or equivalent) in osteology, physical anthropology, forensic anthropology, anatomy or a related subject. Alternatively an ability to demonstrate considerable experience in a relevant field will be required.', '', 'https://www.dundee.ac.uk/postgraduate/anatomy-advanced-forensic-anthropology'),
(261, 'Forensic Anthropology MSc', '', '4', 20, '', 474, '0000-00-00', 3, 0.00, 19600.00, 0.00, 'SEP', '1 YEAR', 'If English is not your first language, you wi', 'A degree at 2:1 or above (or equivalent) in osteology, physical anthropology, forensic anthropology, anatomy or a related subject. Alternatively an ability to demonstrate considerable experience in a relevant field will be required.', '', 'https://www.dundee.ac.uk/postgraduate/forensic-anthropology'),
(262, 'Forensic Art & Facial Imaging MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A degree at 2:1 or above (or equivalent) in osteology, physical anthropology, forensic anthropology, anatomy or a related subject. Alternatively an ability to demonstrate considerable experience in a relevant field will be required.', '', 'https://www.dundee.ac.uk/postgraduate/forensic-art-facial-imaging'),
(263, 'Architecture MArch (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'The essential subject is Higher English (or other literary subject) + the recommended subjects are Nat5 in an Art & Design subject + Mathematics or Physics at C', '', 'https://www.dundee.ac.uk/undergraduate/architecture'),
(264, 'Environmental Sustainability and Geography MA (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'SEP', 'four years full time', 'If English is not your first language, you wi', 'The essential subject is Higher English (or other literary subject) + the recommended subjects are Nat5 in an Art & Design subject + Mathematics or Physics at C', '', 'https://www.dundee.ac.uk/undergraduate/environmental-sustainability-geography'),
(265, 'Geography and Planning MA (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'SEP', 'four years full time', 'If English is not your first language, you wi', 'The essential subject is Higher English (or other literary subject) + the recommended subjects are Nat5 in an Art & Design subject + Mathematics or Physics at C', '', 'https://www.dundee.ac.uk/undergraduate/geography-planning');
INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`) VALUES
(266, 'Spatial Planning with Sustainable Urban Design MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Architecture, Economics, Geography, Engineering, or a related subject at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant MSc, outlining any relevant work experience', '', 'https://www.dundee.ac.uk/postgraduate/spatial-planning-sustainable-urban-design'),
(267, 'Spatial Planning with Sustainable Urban Design MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, ' JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Architecture, Economics, Geography, Engineering, or a related subject at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant MSc, outlining any relevant work experience', '', 'https://www.dundee.ac.uk/postgraduate/spatial-planning-sustainable-urban-design'),
(268, 'Animation BDes (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'SEP', 'three years full time', 'If English is not your first language, you wi', 'Advanced Higher at BB + Higher at BB', '', 'https://www.dundee.ac.uk/undergraduate/animation'),
(269, 'Art & Design (General Foundation) BA (Hons) / BDes (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'Advanced Higher at BB + Higher at BB', '', 'https://www.dundee.ac.uk/undergraduate/art-design-general-foundation'),
(270, 'Art & Philosophy BA (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'Advanced Higher at BB + Higher at BB', '', 'https://www.dundee.ac.uk/undergraduate/art-philosophy'),
(271, 'Digital Interaction Design BSc (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'Advanced Higher at BB + Higher at BB', '', 'https://www.dundee.ac.uk/undergraduate/digital-interaction-design'),
(272, 'Graphic Design BDes (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'Advanced Higher at BB + Higher at BB', '', 'https://www.dundee.ac.uk/undergraduate/graphic-design'),
(273, 'Animation & VFX MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A first or second class honours degree in an appropriate discipline, although applicants with other qualifications or relevant professional experience will also be considered.', '', 'https://www.dundee.ac.uk/postgraduate/animation-vfx'),
(274, 'Art & Humanities MFA', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A first or second class honours degree in an appropriate discipline, although applicants with other qualifications or relevant professional experience will also be considered.', '', 'https://www.dundee.ac.uk/postgraduate/art-humanities'),
(275, 'Drawing MFA', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A first or second class honours degree in an appropriate discipline, although applicants with other qualifications or relevant professional experience will also be considered.', '', 'https://www.dundee.ac.uk/postgraduate/drawing'),
(276, 'Product Design MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A first or second class honours degree in an appropriate discipline, although applicants with other qualifications or relevant professional experience will also be considered.', '', 'https://www.dundee.ac.uk/postgraduate/product-design'),
(277, 'Biochemistry BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/biochemistry'),
(278, 'Biological Chemistry and Drug Discovery BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/biological-chemistry-drug-discovery'),
(279, 'Biological Sciences BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/biological-sciences'),
(280, 'Biomedical Sciences BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/biomedical-sciences'),
(281, 'Foundation Year in Life Sciences CertHE', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/life-sciences-foundation'),
(282, 'Microbiology BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/microbiology'),
(283, 'Pharmacology BSc (Hons)', '', '4', 20, '', 474, '0000-00-00', 3, 0.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/pharmacology'),
(284, 'Physiological Sciences BSc (Hons)', '', '4', 20, '', 0, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/physiological-sciences'),
(285, 'Biomedical and Molecular Sciences MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 24800.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/biomedical-molecular-sciences'),
(286, 'Biomedical and Molecular Sciences with Business MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/biomedical-molecular-sciences-business'),
(287, 'Biomedical and Molecular Sciences with Entrepreneurship MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/biomedical-molecular-sciences-entrepreneurship'),
(288, 'Biomedical and Molecular Sciences with Management MSc', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/biomedical-molecular-sciences-management'),
(289, 'Life Sciences Masters by Research MSc (Res)', '', '4', 20, '', 474, '0000-00-00', 3, 5.00, 27035.00, 0.00, 'JAN,MAY AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/life-sciences-msc-research'),
(290, 'Business Management BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AAB at Advanced Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-management'),
(291, 'Business Management in Practice (with Chandigarh University) BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '2 YEAR', 'If English is not your first language, you wi', 'AAB at Advanced Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-management-practice-chandigarh'),
(292, 'Business Management - 3.5 years BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '3.5 years full time', 'If English is not your first language, you wi', 'AAB at Advanced Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-management-accelerated'),
(293, 'Computing Science BSc (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'BB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/computing-science'),
(294, 'Augmentative & Alternative Communication MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'BB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/postgraduate/augmentative-alternative-communication'),
(295, 'Computer Science MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, ' JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/computing'),
(296, 'Computer Science with International Business MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/computing-international-business'),
(297, 'Information Technology & International Business MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, 'SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Lower Second-Class Honours (2:2) degree', '', 'https://www.dundee.ac.uk/postgraduate/information-technology-international-business'),
(298, 'Dentistry BDS', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 38150.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'Higher - Biology (preferably Human Biology) and Chemistry and National 5 - English, Mathematics, Biology, Chemistry, plus one more subject', '', 'https://www.dundee.ac.uk/undergraduate/dentistry'),
(299, 'Oral Health Sciences BSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 28620.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'Higher - Biology (preferably Human Biology) and Chemistry and National 5 - English, Mathematics, Biology, Chemistry, plus one more subject', '', 'https://www.dundee.ac.uk/undergraduate/oral-health-sciences'),
(300, 'Dental Public Health MDPH', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'Applicants are normally required to hold a first class or upper second class degree in a health related topic and/or in subjects including dentistry, dental therapy/hygiene, the social sciences, and medical science. Applications will also be considered from others who, although not graduates, are exceptionally well qualified in terms of seniority and experience.', '', 'https://www.dundee.ac.uk/postgraduate/dental-public-health'),
(301, 'Forensic Dentistry MSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'Applicants are normally required to hold a first class or upper second class degree in a health related topic and/or in subjects including dentistry, dental therapy/hygiene, the social sciences, and medical science. Applications will also be considered from others who, although not graduates, are exceptionally well qualified in terms of seniority and experience.', '', 'https://www.dundee.ac.uk/postgraduate/forensic-dentistry'),
(302, 'Forensic Odontology MFOdont', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '2 YEAR', 'If English is not your first language, you wi', 'Applicants are normally required to hold a first class or upper second class degree in a health related topic and/or in subjects including dentistry, dental therapy/hygiene, the social sciences, and medical science. Applications will also be considered from others who, although not graduates, are exceptionally well qualified in terms of seniority and experience.', '', 'https://www.dundee.ac.uk/postgraduate/forensic-odontology'),
(303, 'Business Economics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics'),
(304, 'Business Economics with Marketing and Geography MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-geography'),
(305, 'Business Economics with Marketing and Politics MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-politics'),
(306, 'Business Economics with Marketing and Psychology MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-psychology'),
(307, 'Business Economics with Marketing BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-bsc'),
(308, ' Business Economics with Marketing MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-ma'),
(309, 'Business Economics with Marketing with French MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-french'),
(310, 'Economics and History MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/economics-history'),
(311, 'Economics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/economics-bsc'),
(312, 'Financial Economics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/financial-economics-bsc'),
(313, 'International Business MSc', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, '', '1 YEAR', 'If English is not your first language, you wi', 'Equivalent of a UK 2:2 degree in any subject which has enabled the applicant to develop analytical and quantitative skills.', '', 'https://www.dundee.ac.uk/postgraduate/international-business'),
(314, 'Childhood Practice BA', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'You need to have at least 2 years consistent work experience, working with children and young people 0-16 years in an appropriate setting with a relevant professional practice qualification.', '', 'https://www.dundee.ac.uk/undergraduate/childhood-practice'),
(315, 'Education MA (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'You need to have at least 2 years consistent work experience, working with children and young people 0-16 years in an appropriate setting with a relevant professional practice qualification.', '', 'https://www.dundee.ac.uk/undergraduate/education'),
(316, 'Primary Education PGDE', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You need to have at least 2 years consistent work experience, working with children and young people 0-16 years in an appropriate setting with a relevant professional practice qualification.', '', 'https://www.dundee.ac.uk/undergraduate/primary-education'),
(317, 'Secondary Education (Chemistry) PGDE', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'AUG', '1 YEAR', 'If English is not your first language, you wi', 'A UK degree or a degree of an equivalent standard from outside the United Kingdom. The content of your degree must be relevant to the subject you want to teach.', '', 'https://www.dundee.ac.uk/undergraduate/secondary-education-chemistry'),
(318, 'Secondary Education (Mathematics) PGDE', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'AUG', '1 YEAR', 'If English is not your first language, you wi', 'A UK degree or a degree of an equivalent standard from outside the United Kingdom. The content of your degree must be relevant to the subject you want to teach.', '', 'https://www.dundee.ac.uk/undergraduate/secondary-education-mathematics'),
(319, 'Education (Leading Learning & Teaching) Med', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Ordinary (non-honours) degree', '', 'https://www.dundee.ac.uk/postgraduate/education-leading-learning-teaching'),
(320, 'Teaching English to Speakers of Other Languages - TESOL Med', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Ordinary (non-honours) degree', '', 'https://www.dundee.ac.uk/postgraduate/teaching-english-speakers-others-languages'),
(321, 'Graduate Career and Professional Development GradCert', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Ordinary (non-honours) degree', '', 'https://www.dundee.ac.uk/postgraduate/graduate-career-professional-development'),
(322, 'Biomedical Engineering BEng (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 23100.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/biomedical-engineering'),
(323, 'Civil and Structural Engineering BEng (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 23100.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/civil-structural-engineering-beng'),
(324, 'Mechanical Engineering BEng (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 23100.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mechanical-engineering'),
(325, 'Structural Engineering with Architecture MEng (Hons)', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 23100.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/structural-engineering-architecture'),
(326, 'Biomedical Engineering MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 23100.00, 0.00, ' JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/biomedical-engineering'),
(327, 'Civil Engineering MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/civil-engineering'),
(328, 'Industrial Engineering and Digital Marketing MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/industrial-engineering-digital-marketing'),
(329, 'Industrial Engineering and Entrepreneurship MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/industrial-engineering-entrepreneurship'),
(330, 'Industrial Engineering and International Finance MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/industrial-engineering-international-finance'),
(331, 'Industrial Engineering and Management MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 24100.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/industrial-engineering-management'),
(332, 'Structural Engineering and Concrete Materials MSc', '', '6', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'A UK Upper Second-Class Honours (2:1) degree', '', 'https://www.dundee.ac.uk/postgraduate/structural-engineering-concrete-materials'),
(333, 'Arts & Social Sciences MA', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/arts-social-sciences-ma'),
(334, 'English and Creative Writing MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-creative-writing'),
(335, 'English and European Languages MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-european-languages'),
(336, 'English and Film Studies MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-film-studies'),
(337, 'English and Mathematics MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-mathematics'),
(338, 'English MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 2, 5.00, 20900.00, 0.00, 'SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english'),
(339, 'English with French MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-french'),
(340, 'Liberal Arts MA (Hons)', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/liberal-arts'),
(341, 'English Studies Mlitt', '', '7', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'Students will normally be expected to have a 2:1 honours degree in English or a related discipline. Applicants with alternative qualifications and/or relevant experience may also be considered. Students should provide, along with their postgraduate application, copies of their degree transcript and, if applicable, a copy of their degree award certificate, two reference letters and written work in a subject relevant to literary study.', '', 'https://www.dundee.ac.uk/postgraduate/english-studies'),
(342, 'Arts & Social Sciences MA', '', '1', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/arts-social-sciences-ma'),
(343, 'Business Economics with Marketing and Geography MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-geography'),
(344, 'Environmental Science and Geography MA (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/environmental-science-geography'),
(345, 'Geography and Psychology MA (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/geography-psychology'),
(346, 'Social Research MSc', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in any discipline at lower second class or above', '', 'https://www.dundee.ac.uk/postgraduate/social-research'),
(347, 'Arts & Social Sciences MA', '', '1', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/arts-social-sciences-ma'),
(348, 'Arts & Social Sciences MA', '', '1', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/business-economics-marketing-history'),
(349, 'Economics and History MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/economics-history'),
(350, 'History MA (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/history'),
(351, 'History Mlitt', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'Students will normally be expected to have a 2:1 honours degree in History or a related discipline. Applicants with alternative qualifications and/or relevant experience may also be considered.', '', 'https://www.dundee.ac.uk/postgraduate/history-mlitt'),
(352, 'Law (Eng/NI) LLB (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/law-english-northern-irish'),
(353, 'Law (Eng/NI) with Spanish LLB (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 0.00, 20900.00, 0.00, ' SEP', '', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/law-english-northern-irish-spanish'),
(354, 'Law (Scots and English Dual Qualifying) with Energy Law LLB (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/law-scots-english-dual-qualifying-energy-law'),
(355, 'Law (Scots) with French LLB (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/law-scots-french'),
(356, 'Law (Scots) with Spanish LLB (Hons)', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + BB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/law-scots-spanish'),
(357, 'Business & Human Rights LLM', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Law at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant LLM, outlining any relevant legal work experience', '', 'https://www.dundee.ac.uk/postgraduate/business-human-rights'),
(358, 'Comparative & European Private International Law LLM', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Law at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant LLM, outlining any relevant legal work experience', '', 'https://www.dundee.ac.uk/postgraduate/comparative-european-private-international-law'),
(359, 'Environmental Law LLM', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Law at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant LLM, outlining any relevant legal work experience', '', 'https://www.dundee.ac.uk/postgraduate/environmental-law'),
(360, 'Law (General) LLM', '', '2', 20, '', 474, '0000-00-00', 3, 5.00, 19900.00, 0.00, 'JAN AND SEP', '1 YEAR', 'If English is not your first language, you wi', 'An honours degree in Law at lower second class or above Consideration will also be given to applicants with other academic backgrounds who explain clearly their motivation for undertaking the relevant LLM, outlining any relevant legal work experience', '', 'https://www.dundee.ac.uk/postgraduate/law-general'),
(361, 'Accountancy and Mathematics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/accountancy-mathematics'),
(362, 'English and Mathematics MA (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/english-mathematics'),
(363, 'Mathematical Biology BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mathematical-biology-bsc'),
(364, '\"Mathematical Biology MSci (Hons) Mathematical Biology MSci (Hons)\"', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mathematical-biology-msci'),
(365, 'Mathematics and Economics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19600.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mathematics-economics'),
(366, 'Mathematics and Economics BSc (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19600.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mathematics-economics'),
(367, 'Mathematics MMath (Hons)', '', '5', 20, '', 474, '0000-00-00', 3, 5.00, 19600.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/mathematics-mmath'),
(368, '\" Medicine - Gateway to Medicine MBChB\"', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20895.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'All Highers are to be obtained at the first attempt and at one sitting. National 5 / Intermediate 2 grades will also be taken into account.', '', 'https://www.dundee.ac.uk/undergraduate/medicine-gateway'),
(369, 'Medicine MBChB', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 50100.00, 0.00, ' SEP', 'five years full time', 'If English is not your first language, you wi', 'All Highers are to be obtained at the first attempt and at one sitting. National 5 / Intermediate 2 grades will also be taken into account.', '', 'https://www.dundee.ac.uk/undergraduate/medicine'),
(370, 'Scotgem MBChB', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 19600.00, 0.00, ' SEP', 'four years full time', 'If English is not your first language, you wi', 'All Highers are to be obtained at the first attempt and at one sitting. National 5 / Intermediate 2 grades will also be taken into account.', '', 'https://www.dundee.ac.uk/undergraduate/scotgem'),
(371, 'Healthcare Improvement (Intercalated) BMSc (Hons)', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You must have successfully completed three years of an undergraduate medical degree (MBChB/MBBS) or two years of an undergraduate dental course (BDS)', '', 'https://www.dundee.ac.uk/undergraduate/healthcare-improvement-bmsc'),
(372, 'Health Data Science for Applied Precision Medicine MSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 25300.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You will need at least an upper second class degree in a relevant scientific discipline (for example biological or medical sciences), or an appropriate health professional qualification such as medicine and allied health professions.', '', 'https://www.dundee.ac.uk/postgraduate/health-data-science-applied-precision-medicine'),
(373, 'Human Clinical Embryology & Assisted Conception MSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 33985.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You will need at least an upper second class degree in a relevant scientific discipline (for example biological or medical sciences), or an appropriate health professional qualification such as medicine and allied health professions.', '', 'https://www.dundee.ac.uk/postgraduate/human-clinical-embryology-assisted-conception'),
(374, 'Psychological Therapy in Primary Care MSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, 'JAN ', '1 YEAR', 'If English is not your first language, you wi', 'You will need at least an upper second class degree in a relevant scientific discipline (for example biological or medical sciences), or an appropriate health professional qualification such as medicine and allied health professions.', '', 'https://www.dundee.ac.uk/postgraduate/psychological-therapy-primary-care'),
(375, 'Public Health MPH', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You will need at least an upper second class degree in a relevant scientific discipline (for example biological or medical sciences), or an appropriate health professional qualification such as medicine and allied health professions.', '', 'https://www.dundee.ac.uk/postgraduate/public-health'),
(376, 'Adult Nursing (Kirkcaldy) BSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/adult-nursing-kirkcaldy'),
(377, 'Adult Nursing BSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/adult-nursing-bsc'),
(378, 'Child Nursing BSc', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', '', 'https://www.dundee.ac.uk/undergraduate/child-nursing-bsc'),
(379, 'Mental Health Nursing BSc (Hons)', '', '3', 20, '', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', 'three years full time', 'If English is not your first language, you wi', 'AB at Advanced Higher + AB at Higher', 'fdgf', 'https://www.dundee.ac.uk/undergraduate/mental-health-nursing-bsc-hons'),
(380, 'Nursing & Health MSc', '', '3', 20, '', 0, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You\'ll need to have an ordinary Bachelor\'s degree or equivalent, or a higher degree in Nursing. For information about equivalent qualifications for entry, please email the course contact.', '', 'https://www.dundee.ac.uk/postgraduate/nursing-health'),
(381, 'Infection Prevention & Control (full time) MSc', 'Postgraduate', '3', 20, 'AA', 474, '0000-00-00', 3, 5.00, 20900.00, 0.00, ' SEP', '1 YEAR', 'If English is not your first language, you wi', 'You\'ll need to have an ordinary Bachelor\'s degree or equivalent, or a higher degree in Nursing. For information about equivalent qualifications for entry, please email the course contact.', '', 'https://www.dundee.ac.uk/postgraduate/infection-prevention-control'),
(383, 'ABC', 'Undergraduate', '1', 20, 'London', 474, '2023-09-30', 3, 5.00, 1000.00, 0.00, 'SEP', '4 Years', 'abc', 'AEG', 'IELTS ', 'https://www.dundee.ac.uk/');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_program_wishlist`
--

CREATE TABLE `ggportal_tbl_program_wishlist` (
  `program_wishlist_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `user` varchar(45) NOT NULL,
  `user_id` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_province`
--

CREATE TABLE `ggportal_tbl_province` (
  `province_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_province`
--

INSERT INTO `ggportal_tbl_province` (`province_id`, `country_id`, `province_name`) VALUES
(1, 22, 'Center Province'),
(2, 22, 'Eastern Province'),
(3, 22, 'Northern Province'),
(4, 22, 'North Central Province'),
(5, 22, 'Northwestern Province'),
(6, 22, 'Sabaragamuwa Province'),
(7, 22, 'Southern Province'),
(8, 22, 'Uva Province'),
(9, 22, 'Western Province'),
(10, 1, 'New South Wales'),
(11, 1, 'Victoria'),
(12, 1, 'Queensland'),
(13, 1, 'Western Australia'),
(14, 1, 'South Australia'),
(18, 2, 'Newfoundland and Labrador'),
(19, 2, 'Prince Edward Island'),
(20, 2, 'Nova Scotia'),
(21, 2, 'New Brunswick'),
(22, 2, 'Quebec'),
(23, 2, 'Ontario'),
(24, 2, 'Manitoba'),
(25, 2, 'Saskatchewan'),
(26, 2, ' British Columbia'),
(27, 2, ' Alberta'),
(28, 3, 'Auvergne-Rhône-Alpes'),
(29, 3, 'Bourgogne-Franche-Comté'),
(30, 3, 'Brittany (Bretagne)'),
(31, 3, 'Centre-Val de Loire'),
(32, 3, 'Corsica (Corse)'),
(33, 3, 'Grand Est'),
(34, 3, 'Hauts-de-France'),
(35, 3, 'Île-de-France'),
(36, 3, 'Normandy (Normandie'),
(37, 3, 'Nouvelle-Aquitaine'),
(38, 3, 'Occitanie'),
(39, 3, 'Pays de la Loire'),
(40, 3, 'Provence-Alpes-Côte d\'Azur'),
(41, 4, 'Abkhazia '),
(42, 4, 'Adjara'),
(43, 4, 'Guria'),
(44, 4, 'Imereti'),
(45, 4, 'Kakheti'),
(46, 4, 'Kvemo Kartli'),
(47, 4, 'Mtskheta-Mtianeti'),
(48, 4, 'Racha-Lechkhumi and Kvemo Svaneti'),
(49, 4, 'Samegrelo-Zemo Svaneti'),
(50, 4, 'Samtskhe-Javakheti'),
(51, 4, 'Shida Kartli'),
(52, 4, 'Tbilisi'),
(53, 5, 'Baden-Württemberg'),
(54, 5, 'Bavaria'),
(55, 5, 'Berlin'),
(56, 5, 'Brandenburg'),
(57, 5, 'Bremen'),
(58, 5, 'Hamburg'),
(59, 5, 'Hesse'),
(60, 5, 'Lower Saxony'),
(61, 5, 'Mecklenburg-Vorpommern'),
(62, 5, 'North Rhine-Westphalia '),
(63, 5, 'Rhineland-Palatinate'),
(64, 5, 'Saarland'),
(65, 5, 'Saxony'),
(66, 5, 'Saxony-Anhalt'),
(67, 5, 'Schleswig-Holstein'),
(68, 5, 'Thuringia '),
(69, 6, 'Leinster'),
(70, 6, 'Munster'),
(71, 6, 'Connacht'),
(72, 6, 'Ulster'),
(73, 7, 'Abruzzo\r\n'),
(74, 7, 'Aosta Valley '),
(75, 7, 'Apulia '),
(76, 7, 'Basilicata'),
(77, 7, 'Calabria'),
(78, 7, 'Campania'),
(79, 7, 'Emilia-Romagna'),
(80, 7, 'Friuli Venezia Giulia'),
(81, 7, 'Lazio'),
(82, 7, 'Liguria'),
(83, 7, 'Lombardy'),
(84, 7, 'Marche'),
(85, 7, 'Molise'),
(86, 7, 'Piedmont '),
(87, 7, 'Sardinia '),
(88, 7, 'Sicily'),
(89, 7, 'Trentino-Alto Adige/Südtirol'),
(90, 7, 'Tuscany '),
(91, 7, 'Umbria'),
(92, 7, 'Veneto'),
(93, 8, 'Municipalities'),
(94, 8, 'Cities'),
(95, 8, 'Rural areas '),
(96, 9, 'Counties'),
(97, 9, 'Municipalities'),
(98, 9, 'Cities (miestai) and Towns (miesteliai)'),
(99, 11, 'Port Louis'),
(100, 11, 'Pamplemousses'),
(101, 11, 'Rivière du Rempart'),
(102, 11, 'Flacq'),
(103, 11, 'Grand Port'),
(104, 11, 'Savanne'),
(105, 11, 'Plaines Wilhems'),
(106, 11, 'Moka'),
(107, 11, 'Black River'),
(108, 12, 'Drenthe'),
(109, 12, 'Flevoland'),
(110, 12, 'Friesland '),
(111, 12, 'Gelderland'),
(112, 12, 'Groningen'),
(113, 12, 'Limburg'),
(114, 12, 'North Brabant'),
(115, 12, 'North Holland '),
(116, 12, 'Overijssel'),
(117, 12, 'South Holland'),
(118, 12, 'Utrecht'),
(119, 12, 'Zeeland'),
(120, 13, 'Auckland'),
(121, 13, 'New Plymouth'),
(122, 13, 'Hawke\'s Bay'),
(123, 13, 'Wellington'),
(124, 13, 'Nelson'),
(125, 13, 'Marlborough'),
(126, 13, 'Westland'),
(127, 13, 'Canterbury'),
(128, 13, 'Otago'),
(129, 13, 'Southland'),
(130, 14, 'Lower Silesian Voivodeship'),
(131, 14, 'Kuyavian-Pomeranian Voivodeship'),
(132, 14, 'Lubusz Voivodeship'),
(133, 14, 'Łódź Voivodeship'),
(134, 14, 'Lesser Poland Voivodeship'),
(135, 14, 'Masovian Voivodeship'),
(136, 14, 'Opole Voivodeship'),
(137, 14, 'Podlaskie Voivodeship'),
(138, 14, 'Pomeranian Voivodeship'),
(139, 14, 'Silesian Voivodeship'),
(140, 14, 'Subcarpathian Voivodeship'),
(141, 14, 'Świętokrzyskie Voivodeship'),
(142, 14, 'Warmian-Masurian Voivodeship'),
(143, 14, 'Greater Poland Voivodeship'),
(144, 14, 'West Pomeranian Voivodeship '),
(145, 14, 'Lublin Voivodeship'),
(146, 15, 'Central Federal District'),
(147, 15, 'North-West Federal District'),
(148, 15, 'Southern Federal District'),
(149, 15, 'North Caucasus Federal District'),
(150, 15, 'Privolzhsky Federal District'),
(151, 15, 'Urals Federal District'),
(152, 15, 'Siberian Federal District'),
(153, 15, 'Fareast Federal District'),
(154, 16, 'Central Region'),
(155, 16, 'West Region'),
(156, 16, 'East Region'),
(157, 16, 'North East Region'),
(158, 16, 'North Region'),
(159, 17, 'Appenzell Ausserrhoden'),
(160, 17, 'Appenzell Innerrhoden'),
(161, 17, 'Basel-Landschaft'),
(162, 17, 'Basel-Stadt'),
(163, 17, 'Bern'),
(164, 17, 'Fribourg'),
(165, 17, 'Geneva'),
(166, 17, 'Glarus'),
(167, 17, 'Graubünden'),
(168, 17, 'Jura'),
(169, 17, 'Lucerne'),
(170, 17, 'Neuchâtel'),
(171, 17, 'Nidwalden'),
(172, 17, 'Obwalden'),
(173, 17, ''),
(174, 17, 'Schaffhausen'),
(175, 17, 'Schwyz'),
(176, 17, 'Solothurn'),
(177, 17, 'St. Gallen'),
(178, 17, 'Thurgau'),
(179, 17, 'Ticino'),
(180, 17, 'Uri'),
(181, 17, 'Valais'),
(182, 17, 'Vaud '),
(183, 17, 'Zug'),
(184, 17, 'Zurich'),
(185, 17, 'Aargau'),
(186, 18, 'Oblasts'),
(187, 18, 'Autonomous Republic of Crimea'),
(188, 18, 'City of Kyiv'),
(189, 18, 'City of Sevastopol'),
(190, 19, 'Abu Dhabi'),
(191, 19, 'Dubai'),
(192, 19, 'Sharjah'),
(193, 19, 'Ajman'),
(194, 19, 'Umm Al-Quwain'),
(195, 19, 'Ras Al Khaimah'),
(196, 19, 'Fujairah'),
(197, 20, 'England'),
(198, 20, 'Scotland'),
(199, 20, 'Wales'),
(200, 20, 'Northern Ireland'),
(201, 21, 'States'),
(202, 21, 'Territories'),
(203, 21, 'Federal District');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_remainder`
--

CREATE TABLE `ggportal_tbl_remainder` (
  `remainder_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `title` varchar(120) NOT NULL,
  `remainder_date` datetime NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `note` varchar(500) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff`
--

CREATE TABLE `ggportal_tbl_staff` (
  `staff_id` int(11) NOT NULL,
  `staff_no` int(11) DEFAULT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(15) NOT NULL,
  `category` varchar(20) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` varchar(20) NOT NULL,
  `country_id` varchar(100) NOT NULL,
  `state` varchar(20) NOT NULL,
  `city` varchar(20) NOT NULL,
  `password` varchar(45) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `user_privilege_id` int(11) NOT NULL,
  `profile_picture` text NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `user_active_yn` varchar(1) NOT NULL DEFAULT 'N',
  `last_seen` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp(),
  `parent_user_type` varchar(20) NOT NULL,
  `parent_user_id` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_staff`
--

INSERT INTO `ggportal_tbl_staff` (`staff_id`, `staff_no`, `first_name`, `last_name`, `username`, `email`, `mobile`, `category`, `gender`, `date_of_birth`, `marital_status`, `country_id`, `state`, `city`, `password`, `password_salt`, `user_type`, `user_privilege_id`, `profile_picture`, `email_validate_yn`, `user_active_yn`, `last_seen`, `created_by`, `created_at`, `updated_by`, `updated_at`, `parent_user_type`, `parent_user_id`) VALUES
(57, 1023, 'Kasun', 'Kavinda', '<EMAIL>', '<EMAIL>', '0711715145', '', '', '2000-02-02', '', 'all', '0', '0', 'VUM4blBXaGo1a0UxSHMwRm12R3paZz09', '', 'SF', 1, 'dist/img/avatar3.png', 'Y', 'Y', '2023-09-28 17:29:44', 1, '2023-09-13 11:47:22', 1, '2023-09-13 11:47:22', 'AG', 35),
(56, 1022, 'Nilaksha', 'Madubani', '<EMAIL>', '<EMAIL>', '0711715145', '', '', '2000-02-02', '', 'all', '0', '0', 'a3hHU29vYUJYTFdldVY3ZDl3eVBaQT09', '', 'SF', 1, 'dist/img/avatar3.png', 'Y', 'Y', '2023-09-28 09:57:16', 1, '2023-09-13 11:42:28', 1, '2023-09-13 11:42:28', 'RA', 1);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_activity`
--

CREATE TABLE `ggportal_tbl_staff_activity` (
  `user_activity_id` int(10) UNSIGNED NOT NULL,
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `activity_description` text NOT NULL,
  `created_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_login`
--

CREATE TABLE `ggportal_tbl_staff_login` (
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `date_login` datetime NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_staff_login`
--

INSERT INTO `ggportal_tbl_staff_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(1646, 8, '2022-10-03 13:19:26', '**************', '2022-10-03 13:19:26'),
(1647, 8, '2022-10-05 12:42:18', '**************', '2022-10-05 12:42:18'),
(1648, 13, '2022-10-12 08:05:59', '***************', '2022-10-12 08:05:59'),
(1649, 13, '2022-10-13 05:20:18', '***************', '2022-10-13 05:20:18'),
(1650, 8, '2022-10-13 10:18:33', '***************', '2022-10-13 10:18:33'),
(1651, 13, '2022-10-13 10:18:43', '***************', '2022-10-13 10:18:43'),
(1652, 8, '2022-12-05 14:45:42', '**************', '2022-12-05 14:45:42'),
(1653, 8, '2022-12-05 14:45:43', '**************', '2022-12-05 14:45:43'),
(1654, 13, '2022-12-21 21:27:15', '*************', '2022-12-21 21:27:15'),
(1655, 8, '2022-12-23 06:21:48', '***************', '2022-12-23 06:21:48'),
(1656, 8, '2022-12-23 06:21:50', '***************', '2022-12-23 06:21:50'),
(1657, 19, '2023-08-03 11:08:21', '::1', '2023-08-03 11:08:21'),
(1658, 19, '2023-08-03 11:22:09', '::1', '2023-08-03 11:22:09'),
(1659, 19, '2023-08-03 14:20:55', '::1', '2023-08-03 14:20:55'),
(1660, 20, '2023-08-15 16:45:02', '127.0.0.1', '2023-08-15 16:45:02'),
(1661, 22, '2023-08-16 09:38:12', '127.0.0.1', '2023-08-16 09:38:12'),
(1662, 22, '2023-08-16 09:39:12', '127.0.0.1', '2023-08-16 09:39:12'),
(1663, 22, '2023-08-16 10:17:40', '127.0.0.1', '2023-08-16 10:17:40'),
(1664, 22, '2023-08-16 10:19:46', '127.0.0.1', '2023-08-16 10:19:46'),
(1665, 24, '2023-08-16 10:38:27', '127.0.0.1', '2023-08-16 10:38:27'),
(1666, 24, '2023-08-16 10:39:17', '127.0.0.1', '2023-08-16 10:39:17'),
(1667, 24, '2023-08-16 10:39:26', '127.0.0.1', '2023-08-16 10:39:26'),
(1668, 24, '2023-08-16 10:55:31', '127.0.0.1', '2023-08-16 10:55:31'),
(1669, 24, '2023-08-16 11:59:12', '127.0.0.1', '2023-08-16 11:59:12'),
(1670, 24, '2023-08-16 12:13:53', '127.0.0.1', '2023-08-16 12:13:53'),
(1671, 24, '2023-08-16 12:45:28', '127.0.0.1', '2023-08-16 12:45:28'),
(1672, 24, '2023-08-16 13:28:13', '127.0.0.1', '2023-08-16 13:28:13'),
(1673, 24, '2023-08-16 13:59:44', '127.0.0.1', '2023-08-16 13:59:44'),
(1674, 24, '2023-08-16 15:19:42', '127.0.0.1', '2023-08-16 15:19:42'),
(1675, 24, '2023-08-16 16:50:51', '127.0.0.1', '2023-08-16 16:50:51'),
(1676, 38, '2023-08-17 09:56:06', '::1', '2023-08-17 09:56:06'),
(1677, 1, '2023-08-17 11:11:50', '127.0.0.1', '2023-08-17 11:11:50'),
(1678, 40, '2023-08-21 11:24:01', '::1', '2023-08-21 11:24:01'),
(1679, 40, '2023-08-21 11:58:52', '::1', '2023-08-21 11:58:52'),
(1680, 40, '2023-08-21 12:21:16', '::1', '2023-08-21 12:21:16'),
(1681, 40, '2023-08-21 12:22:24', '::1', '2023-08-21 12:22:24'),
(1682, 41, '2023-08-21 12:27:13', '::1', '2023-08-21 12:27:13'),
(1683, 41, '2023-08-21 13:43:09', '::1', '2023-08-21 13:43:09'),
(1684, 49, '2023-08-22 12:02:03', '127.0.0.1', '2023-08-22 12:02:03'),
(1685, 50, '2023-08-25 08:58:59', '127.0.0.1', '2023-08-25 08:58:59'),
(1686, 50, '2023-08-25 15:03:45', '127.0.0.1', '2023-08-25 15:03:45'),
(1687, 53, '2023-08-31 12:24:39', '127.0.0.1', '2023-08-31 12:24:39'),
(1688, 56, '2023-09-20 12:27:05', '127.0.0.1', '2023-09-20 12:27:05'),
(1689, 56, '2023-09-20 13:42:38', '127.0.0.1', '2023-09-20 13:42:38'),
(1690, 56, '2023-09-20 13:44:05', '127.0.0.1', '2023-09-20 13:44:05'),
(1691, 56, '2023-09-25 10:08:47', '127.0.0.1', '2023-09-25 10:08:47'),
(1692, 56, '2023-09-25 12:34:10', '127.0.0.1', '2023-09-25 12:34:10'),
(1693, 56, '2023-09-26 09:57:02', '127.0.0.1', '2023-09-26 09:57:02'),
(1694, 56, '2023-09-26 12:24:22', '127.0.0.1', '2023-09-26 12:24:22'),
(1695, 56, '2023-09-27 13:20:03', '127.0.0.1', '2023-09-27 13:20:03');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_privilege`
--

CREATE TABLE `ggportal_tbl_staff_privilege` (
  `staff_privilege_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `access_level` varchar(20) NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_staff_privilege`
--

INSERT INTO `ggportal_tbl_staff_privilege` (`staff_privilege_id`, `name`, `access_level`, `active_yn`) VALUES
(3, 'Trainee', '80', 'Y'),
(2, 'Counselor', '85', 'Y'),
(1, 'Senior Counselor', '90', 'Y'),
(4, 'Country Relationship Managers\n', '70', 'N'),
(5, 'Accountant', '50', 'N'),
(6, 'Sales Director', '40', 'N'),
(7, 'County Head', '75', 'N'),
(8, 'Agent Staff', '90', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_state`
--

CREATE TABLE `ggportal_tbl_state` (
  `state_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `state_name` varchar(45) NOT NULL,
  `state_code` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_state`
--

INSERT INTO `ggportal_tbl_state` (`state_id`, `country_id`, `state_name`, `state_code`) VALUES
(1, 1, 'Victoria', ''),
(2, 1, 'New South Wales', ''),
(3, 1, 'Queensland', ''),
(4, 1, 'Australian Capital Territory', ''),
(5, 1, 'Western Australia', ''),
(6, 1, 'South Australia', ''),
(7, 1, 'New South Wales/Victoria', ''),
(8, 1, 'Tasmania', ''),
(9, 1, 'New South Wales/Western Australia', ''),
(10, 2, 'NovaScotia', ''),
(11, 2, 'British Columbia', ''),
(12, 2, 'Ontario', ''),
(13, 2, 'Manitoba', ''),
(14, 2, 'Quebec', ''),
(15, 2, 'Alberta', ''),
(16, 2, 'NewBrunswick', ''),
(17, 2, 'Saskatchewan', ''),
(18, 2, 'New Foundland', ''),
(19, 2, 'Prince Edward', ''),
(20, 2, 'Yukon', ''),
(21, 2, 'Quebec/British Columbia', ''),
(22, 2, 'New Brunswick', ''),
(23, 2, 'Montreal', ''),
(24, 2, 'Washington', ''),
(25, 3, 'Paris', ''),
(26, 3, 'Puy-de-Dome', ''),
(27, 3, 'North Carolina', ''),
(28, 4, 'Tbilisi', ''),
(29, 5, 'Hamburg', ''),
(30, 5, 'Berlin', ''),
(31, 5, 'Hannover', ''),
(32, 5, 'Bavaria', ''),
(33, 6, 'Ulster', ''),
(34, 6, 'Dublin', ''),
(35, 6, 'Westmeath', ''),
(36, 6, 'Maynooth', ''),
(37, 6, 'Galway', ''),
(38, 6, 'Cork', ''),
(39, 6, 'Limerick', ''),
(40, 6, 'Donegal', ''),
(41, 6, 'Carlow', ''),
(42, 6, 'Kilkenny', ''),
(43, 6, 'Waterford', ''),
(44, 7, 'Padova', ''),
(45, 8, 'Riga', ''),
(46, 9, 'Vilniaus', ''),
(47, 10, 'Valletta', ''),
(48, 10, 'Western', ''),
(49, 11, 'Port Louis', ''),
(50, 12, 'Noord-Holland', ''),
(51, 13, 'Auckland', ''),
(52, 13, 'New Plymouth', ''),
(53, 13, 'Christchurch', ''),
(54, 13, 'Auckland/Dunedin', ''),
(55, 13, 'Christchurch/ Auckland', ''),
(56, 13, 'North Island', ''),
(57, 13, 'Wellington', ''),
(58, 13, 'Nelson/ Richmond/Blenheim', ''),
(59, 13, 'Porirua/Aucklan', ''),
(60, 13, 'Invercargill/ Queenstown/ Christchurch/ Gore/', ''),
(61, 13, 'Hamilton', ''),
(62, 13, 'Oamaru', ''),
(63, 13, 'Dunedin', ''),
(64, 13, 'Whangarei', ''),
(65, 14, 'Warszawa', ''),
(66, 15, 'Adygeja', ''),
(67, 15, 'Moscow', ''),
(68, 15, 'Chechenija', ''),
(69, 15, 'Kabardino-Balkarija', ''),
(70, 15, 'Tatarstan', ''),
(71, 15, 'Pskov', ''),
(72, 15, 'Sankt-Peterburg', ''),
(73, 15, 'Tula', ''),
(74, 15, 'Jaroslavl', ''),
(75, 15, 'Tomsk', ''),
(76, 15, 'Idaho', ''),
(77, 15, 'Sverdlovsk', ''),
(78, 15, 'Krasnodar', ''),
(79, 15, 'Kyyiv', ''),
(80, 16, 'Singapore', ''),
(81, 17, 'Lucerne', ''),
(82, 18, 'Odessa', ''),
(83, 18, 'Kharkiv', ''),
(85, 18, 'Poltavska', ''),
(86, 19, 'Dubai', ''),
(87, 20, 'Wales', ''),
(88, 20, 'EAST OF ENGLAND', ''),
(89, 20, 'WEST MIDLANDS ENGLAND', ''),
(90, 20, 'SOUTH WEST ENGLAND', ''),
(91, 20, 'London', ''),
(92, 20, 'EAST MIDLANDS ENGLAND', ''),
(93, 20, 'Scotland', ''),
(94, 20, 'NORTH WEST ENGLAND', ''),
(95, 20, 'NORTH EAST ENGLAND', ''),
(96, 20, 'Northern Ireland', ''),
(97, 20, 'SOUTH EAST ENGLAND', ''),
(98, 20, 'YORKSHIRE AND HUMBER ENGLAND', ''),
(99, 20, 'WEST YORKSHIRE ENGLAND', ''),
(100, 20, 'YORKSHIRE AND THE HUMBER ENGLA', ''),
(101, 20, 'SOUTH YORKSHIRE ENGLAND', ''),
(102, 20, 'NORTH YORKSHIRE ENGLAND', ''),
(103, 20, 'WEST MIDLANDS ENGLAND AND LOND', ''),
(104, 20, 'NORTH WEST AND LONDON', ''),
(105, 20, 'Wrexham', ''),
(106, 20, 'Birmingham/London/Lampter/Swansea/Tileyard Lo', ''),
(107, 20, 'Tierra del Fuego', ''),
(108, 20, 'Pitcairn Island', ''),
(109, 20, 'England', ''),
(110, 21, 'Virginia', ''),
(111, 21, 'New York', ''),
(112, 21, 'Oregon', ''),
(113, 21, 'Florida', ''),
(114, 21, 'Missouri', ''),
(115, 21, 'West Virginia', ''),
(116, 21, 'Massachusetts', ''),
(117, 21, 'Alabama', ''),
(118, 21, 'Pennsylvania', ''),
(119, 21, 'Georgia', ''),
(120, 21, 'Illinois', ''),
(121, 21, 'New Jersey', ''),
(122, 21, 'New Hampshire', ''),
(123, 21, 'Connecticut', ''),
(124, 21, 'Colorado', ''),
(125, 21, 'Milwaukee', ''),
(126, 21, 'Ohio', ''),
(127, 21, 'Nevada', ''),
(128, 21, 'Arizona', ''),
(129, 21, 'DENVER', ''),
(130, 21, 'Maryland', ''),
(131, 21, 'Nebraska', ''),
(132, 21, 'California', ''),
(133, 21, 'Michigan', ''),
(134, 21, 'Indiana', ''),
(135, 21, 'Kansas', ''),
(136, 21, 'Tennessee', ''),
(137, 21, 'Texas', ''),
(138, 21, 'Kentucky', ''),
(139, 21, 'Medison', ''),
(140, 21, 'Oklahoma', ''),
(141, 21, 'New Jersy', ''),
(142, 21, 'Wisconsin', ''),
(143, 21, 'Mississippi', ''),
(144, 21, 'Iowa', ''),
(145, 21, 'New Mexico', ''),
(146, 21, 'Arkansas', ''),
(147, 21, 'South Carolina', ''),
(148, 21, 'Rhode Island', ''),
(149, 21, 'Louisiana', ''),
(150, 21, 'Montgomery', ''),
(151, 21, 'Utah', ''),
(152, 21, 'Vermont', ''),
(153, 21, 'Montana', ''),
(154, 21, 'Hawaii', ''),
(155, 21, 'South Dakota', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student`
--

CREATE TABLE `ggportal_tbl_student` (
  `student_id` int(11) NOT NULL,
  `student_no` int(11) NOT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(15) NOT NULL,
  `category` varchar(20) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` varchar(20) NOT NULL,
  `country` varchar(100) NOT NULL,
  `state` varchar(50) NOT NULL,
  `city` varchar(50) NOT NULL,
  `password` varchar(45) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `profile_picture` text NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `user_active_yn` varchar(1) DEFAULT 'N',
  `last_seen` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `online_status` int(1) NOT NULL DEFAULT 0,
  `emergency_contact_name` varchar(50) NOT NULL,
  `emergency_contact_relation` varchar(50) NOT NULL,
  `emergency_contact_mobile` varchar(20) NOT NULL,
  `remarks` text NOT NULL,
  `real_time_status` varchar(1) NOT NULL DEFAULT 'Y',
  `token_id` varchar(500) DEFAULT NULL,
  `token_created_time` datetime DEFAULT NULL,
  `timeline_id` int(11) NOT NULL DEFAULT 1,
  `assign_to_staff` int(11) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp(),
  `nic` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `province` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `assign_to_type` varchar(6) DEFAULT NULL,
  `assign_to_agent` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student`
--

INSERT INTO `ggportal_tbl_student` (`student_id`, `student_no`, `first_name`, `last_name`, `username`, `email`, `mobile`, `category`, `gender`, `date_of_birth`, `marital_status`, `country`, `state`, `city`, `password`, `password_salt`, `user_type`, `profile_picture`, `email_validate_yn`, `user_active_yn`, `last_seen`, `online_status`, `emergency_contact_name`, `emergency_contact_relation`, `emergency_contact_mobile`, `remarks`, `real_time_status`, `token_id`, `token_created_time`, `timeline_id`, `assign_to_staff`, `created_by`, `created_at`, `updated_by`, `updated_at`, `nic`, `address`, `province`, `postal_code`, `assign_to_type`, `assign_to_agent`) VALUES
(96, 200040, 'Adeesha', 'Ariyathilaka', '<EMAIL>', '<EMAIL>', '0719148898', 'EX', 'male', '2005-04-19', 'married', '7', '', 'Matara', 'Q2g0SHgvaUxKOXhRUGloMzEzRjFaZz09', '', 'ST', 'dist/img/Adeesha.jpg', 'N', 'Y', '2023-09-27 14:46:24', 0, 'Sandaya', 'Mother', '0711712995', '', '', NULL, NULL, 1, 56, 0, '2023-08-25 15:18:16', 1, '2023-09-25 10:19:43', '893001496v', 'Matara', '', '81000', 'SF', 39),
(93, 200040, 'Isuri', 'Prabodi', '<EMAIL>', '<EMAIL>', '0707757862', 'EX', 'female', '2020-01-20', 'married', '16', '', 'Matara', 'Q2g0SHgvaUxKOXhRUGloMzEzRjFaZz09', '', 'ST', 'dist/img/Isuri.jpg', 'N', 'Y', '2023-09-28 13:20:46', 0, 'Malaka', 'Husband', '0711715145', '', '', NULL, NULL, 2, 57, 0, '2023-08-25 15:18:16', 1, '2023-09-28 13:20:46', '', 'Matara', '156', '7668', 'SF', 35),
(94, 200043, 'Janith', 'Kashmira', '<EMAIL>', '<EMAIL>', '0702654554', 'EX', 'male', '1994-06-25', 'married', '14', '', 'Matara', 'Q2g0SHgvaUxKOXhRUGloMzEzRjFaZz09', '', 'ST', 'dist/img/Janith.jpg', 'Y', 'Y', '2023-09-27 10:34:14', 0, 'Sumithra', 'Mother', '0718164692', '', '', NULL, NULL, 2, 56, 0, '2023-09-01 10:00:57', 0, '2023-09-01 10:00:57', '', '21/37a,2nd cross road, Walpola, Matara', '14', '81000', 'SF', 39),
(95, 200046, 'Malaka', 'Witharana', '<EMAIL>', '<EMAIL>', '0711715145', 'EX', 'male', '1992-07-18', 'married', '14', '', 'Matara', 'Q2g0SHgvaUxKOXhRUGloMzEzRjFaZz09', '', 'ST', 'dist/img/Sithum.jpg', 'Y', 'Y', '2023-09-27 13:33:43', 0, 'Sumithra', 'Mother', '0718164692', '', '', NULL, NULL, 2, 57, 0, '2023-09-12 14:27:23', 95, '2023-09-12 14:31:25', '922001685v', '21/37a,2nd cross road, Walpola, Matara', '14', '81000', 'SF', 35),
(99, 200050, 'gbrd', 'rrthb', '<EMAIL>', '<EMAIL>', ' Choose Country', 'IN', 'female', '2023-09-26', 'unmarried', '15', '', '', 'TWFFOTdlMkVDQ0JtOFlDSG5nS2hLQT09', '', 'ST', 'dist/img/user2-160x160.jpg', 'N', 'Y', '2023-10-02 09:07:57', 0, 'ef', '4df', '34565', '', 'Y', NULL, NULL, 1, 35, 35, '2023-09-26 17:44:16', 35, '2023-09-26 17:44:16', 'r547978', 'ygjh', '15', '55', 'AG', 35),
(100, 200053, 'sasa', 'adw', '<EMAIL>', '<EMAIL>', ' +93545445443', 'IN', 'female', '2020-10-20', 'married', '2', '', 'adw', 'ZTkyQTN1ZW5vbGU5YWU3TXdGT1ZhZz09', '', 'ST', 'dist/uploads/student/1696218286.png', 'N', 'Y', '2023-10-02 09:36:38', 0, 'AD', 'rew', '07887676786', '', 'Y', NULL, NULL, 2, 0, 1, '2023-10-02 09:10:07', 100, '2023-10-02 09:14:46', '1999890', 'awdddd', '24', '568', '', 0);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_application`
--

CREATE TABLE `ggportal_tbl_student_application` (
  `student_application_id` int(11) NOT NULL,
  `application_no` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `my_prefer_yn` varchar(1) NOT NULL DEFAULT 'N',
  `institute_id` int(11) NOT NULL,
  `program_id` varchar(200) NOT NULL,
  `intake` varchar(20) NOT NULL,
  `year` varchar(4) NOT NULL,
  `remarks` varchar(120) NOT NULL,
  `status` varchar(50) NOT NULL,
  `application_fee` double(10,2) NOT NULL,
  `tuition_fee` double(10,2) NOT NULL,
  `tuition_fee_due_date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT current_timestamp(),
  `country_code` varchar(10) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_application`
--

INSERT INTO `ggportal_tbl_student_application` (`student_application_id`, `application_no`, `student_id`, `my_prefer_yn`, `institute_id`, `program_id`, `intake`, `year`, `remarks`, `status`, `application_fee`, `tuition_fee`, `tuition_fee_due_date`, `created_by`, `created_date`, `country_code`) VALUES
(56, 4500011, 95, 'Y', 498, '200', 'sep', '2023', '', '1', 0.00, 14250.00, '2022-12-31', 1, '2023-09-07 19:46:49', 'UK'),
(54, 4500005, 99, 'Y', 498, '12', 'jan', '2023', 'Banking Degree', '7', 0.00, 9250.00, '2022-10-17', 1, '2023-09-26 10:13:29', 'UK'),
(55, 4500007, 96, 'Y', 474, '290', 'jan', '2022', '', '0', 0.00, 20900.00, '2022-12-22', 1, '2022-12-22 02:07:41', 'AU'),
(53, 4500001, 93, 'Y', 498, '11', 'jan', '2023', 'Top up your existing qualifications to an honours degree', '1', 0.00, 14250.00, '2023-08-12', 1, '2023-09-20 08:40:09', 'FRA'),
(57, 4500014, 93, 'Y', 498, '16', 'sep', '2023', '', '4', 0.00, 14250.00, '2022-08-31', 0, '2023-09-06 20:06:11', 'AU'),
(58, 4500018, 87, 'Y', 474, '247', 'jan', '2022', 'y', '7', 0.00, 20900.00, '2023-08-02', 1, '2023-08-02 16:09:47', 'UK');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_dependence`
--

CREATE TABLE `ggportal_tbl_student_dependence` (
  `dependence_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `dependence_type` varchar(20) NOT NULL,
  `dependence_age` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_disability`
--

CREATE TABLE `ggportal_tbl_student_disability` (
  `disability_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `description` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_document`
--

CREATE TABLE `ggportal_tbl_student_document` (
  `student_document_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `file_name` varchar(200) NOT NULL,
  `doc_url` varchar(500) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_document`
--

INSERT INTO `ggportal_tbl_student_document` (`student_document_id`, `student_id`, `file_name`, `doc_url`) VALUES
(74, 63, 'education', 'dist/uploads/documents/1695011665-sample.pdf'),
(71, 72, 'receipt', 'dist/uploads/documents/1692674633-ReceiptH.jpg'),
(69, 86, 'receipt', 'dist/uploads/documents/1692604582-istockphoto-*********-612x612.jpg'),
(68, 85, 'receipt', 'dist/uploads/documents/1692597321-user2-160x160.jpg'),
(67, 84, 'cv', 'dist/uploads/documents/1692596511-CV.png'),
(66, 63, 'offer', 'dist/uploads/documents/1692592624-offer.jpg'),
(65, 82, 'refusal', 'dist/uploads/documents/1692358491-downloadwa.jpg'),
(64, 82, 'travel', 'dist/uploads/documents/1692358451-pp.jpg'),
(63, 82, 'cv', 'dist/uploads/documents/1692358252-CVS.jpg');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_education`
--

CREATE TABLE `ggportal_tbl_student_education` (
  `education_id` int(10) NOT NULL,
  `student_id` int(10) NOT NULL,
  `education_level` varchar(50) NOT NULL,
  `education_institute` varchar(100) NOT NULL,
  `education_program` varchar(100) NOT NULL,
  `study_language` varchar(50) NOT NULL,
  `degree_type` varchar(30) NOT NULL,
  `starting_date` date NOT NULL,
  `end_date` date NOT NULL,
  `education_country` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_education`
--

INSERT INTO `ggportal_tbl_student_education` (`education_id`, `student_id`, `education_level`, `education_institute`, `education_program`, `study_language`, `degree_type`, `starting_date`, `end_date`, `education_country`) VALUES
(1, 0, '', '', '', '', '', '0000-00-00', '0000-00-00', ''),
(3, 63, 'ewr', 'weff', 'ef', 'wfef', '', '1970-01-01', '2023-09-27', 'Spain'),
(20, 97, 'fdb', 'bbfbb', 'sd d ', 'fddsv ', 'c', '2023-09-06', '2023-09-21', 'Azerbaijan'),
(23, 96, '', '', '', '', 'c', '2023-08-31', '2023-09-30', 'Algeria'),
(24, 93, 'www', 'eereet', 'aaaa', 'gfgdfg', 'degree', '2023-09-09', '2023-09-30', 'Åland Islands'),
(25, 99, 'rrhb', 'rr', 'dg', 'dfd', 'c', '2023-09-07', '2023-09-14', 'Algeria'),
(26, 100, 'OL', 'wad', 'sd d ', 'eng', 'diploma', '2023-10-07', '2023-10-14', 'Andorra');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_english_test`
--

CREATE TABLE `ggportal_tbl_student_english_test` (
  `english_test_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `test_name` varchar(100) NOT NULL,
  `test_status` varchar(50) NOT NULL,
  `speaking` int(6) DEFAULT NULL,
  `listening` int(6) DEFAULT NULL,
  `reading` int(6) DEFAULT NULL,
  `writing` int(6) DEFAULT NULL,
  `test_date` date DEFAULT NULL,
  `test_description` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_english_test`
--

INSERT INTO `ggportal_tbl_student_english_test` (`english_test_id`, `student_id`, `test_name`, `test_status`, `speaking`, `listening`, `reading`, `writing`, `test_date`, `test_description`) VALUES
(1, 93, 'ielts', 'will_be_taking', 0, 0, 0, 0, '2023-09-07', ''),
(2, 99, 'ielts', 'not_taking', 0, 0, 0, 0, '0000-00-00', ''),
(3, 100, 'ielts', 'taken', 7, 3, 3, 6, '0000-00-00', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_passport`
--

CREATE TABLE `ggportal_tbl_student_passport` (
  `passport_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `passport_number` varchar(100) NOT NULL,
  `issued_country` varchar(100) NOT NULL,
  `passport_issue_date` date NOT NULL,
  `passport_expiry_date` date NOT NULL,
  `nationality` varchar(30) DEFAULT NULL,
  `birth_country` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_passport`
--

INSERT INTO `ggportal_tbl_student_passport` (`passport_id`, `student_id`, `passport_number`, `issued_country`, `passport_issue_date`, `passport_expiry_date`, `nationality`, `birth_country`) VALUES
(44, 82, '654576h', 'Åland Islands', '2023-08-02', '2026-08-19', 'rdg', 'Algeria'),
(45, 63, 'N-129543', 'Åland Islands', '2023-08-24', '2025-06-19', 'scsa', 'American Samoa'),
(46, 84, 'dg34457', 'Algeria', '2023-08-03', '2025-08-14', 'dv', 'Åland Islands'),
(47, 85, 'rr4546rth', 'Åland Islands', '2023-08-04', '2027-06-09', 'dfb', 'Algeria'),
(48, 86, 'sfwe5665', 'American Samoa', '2023-08-03', '2026-09-30', 'ert', 'Algeria'),
(49, 72, 'N342567', 'Sri Lanka', '2023-08-03', '2025-09-02', 'S', 'Sri Lanka'),
(50, 87, 'ddd6797', 'Åland Islands', '2023-08-10', '2025-08-20', 'drb', 'American Samoa'),
(51, 88, 'rtj6789', 'American Samoa', '2023-08-04', '2024-09-11', 'fhjty', 'American Samoa'),
(52, 89, 'N-124121th', 'Andorra', '2023-08-10', '2026-06-17', 'rdfb', 'American Samoa'),
(53, 90, 'drvr56', 'Algeria', '2023-08-16', '2025-09-24', 'erg', 'Algeria'),
(54, 91, 'N-129543ef', 'Andorra', '2023-08-03', '2025-10-08', 'yuyt', 'Algeria'),
(55, 92, 'erg55656', 'Åland Islands', '2023-10-08', '2026-07-22', 'drg', 'Algeria'),
(56, 93, '54grerg567', 'Åland Islands', '2023-08-02', '2025-09-18', 'egrgg', 'Åland Islands'),
(57, 94, 'N-129543adw', 'Albania', '2023-09-02', '2025-06-05', 'awd', 'Algeria'),
(58, 95, 'N-129543wa', 'Albania', '2023-09-06', '2026-10-28', 'awd', 'Albania'),
(59, 63, '', 'Åland Islands', '2023-09-18', '2023-09-18', 'scsa', ''),
(60, 97, 'xzcetert', 'Algeria', '2023-09-13', '2025-10-01', 'xcv', 'Åland Islands'),
(61, 98, 'dfert', 'Algeria', '2023-09-06', '2025-06-05', 'dfg', 'Åland Islands'),
(63, 99, 'rtutbt44', 'Algeria', '2023-09-08', '2027-09-09', 'dfg', 'Åland Islands'),
(64, 100, 'N-129543k', 'Åland Islands', '2023-09-27', '2026-06-19', 'yuyt', 'Åland Islands');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_travel_history`
--

CREATE TABLE `ggportal_tbl_student_travel_history` (
  `travel_history_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `travel_departure` date NOT NULL,
  `travel_arrival` date NOT NULL,
  `travel_reason` varchar(100) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_travel_history`
--

INSERT INTO `ggportal_tbl_student_travel_history` (`travel_history_id`, `student_id`, `country`, `travel_departure`, `travel_arrival`, `travel_reason`) VALUES
(27, 82, 'Bangladesh', '2023-08-02', '2023-08-03', 'Studies');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_visa_refusal`
--

CREATE TABLE `ggportal_tbl_student_visa_refusal` (
  `visa_refusal_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `date` date NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_visa_refusal`
--

INSERT INTO `ggportal_tbl_student_visa_refusal` (`visa_refusal_id`, `student_id`, `country`, `date`) VALUES
(37, 82, 'Sri Lanka', '2023-08-10');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_work`
--

CREATE TABLE `ggportal_tbl_student_work` (
  `work_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `company_name` varchar(200) NOT NULL,
  `position` varchar(100) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_work`
--

INSERT INTO `ggportal_tbl_student_work` (`work_id`, `student_id`, `company_name`, `position`, `start_date`, `end_date`) VALUES
(1, 93, 'afw', '556ht', '2023-09-08', '2023-10-06'),
(2, 99, 's', 'bng', '2023-09-07', '2023-09-26'),
(3, 100, 'wer', 'vvv', '2023-10-04', '2023-10-27');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_timeline`
--

CREATE TABLE `ggportal_tbl_timeline` (
  `timeline_id` int(11) NOT NULL,
  `timeline_name` varchar(50) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_timeline`
--

INSERT INTO `ggportal_tbl_timeline` (`timeline_id`, `timeline_name`) VALUES
(1, 'Signup'),
(2, 'Profile Complete'),
(3, 'Course Finalization'),
(4, 'Application in Process '),
(5, 'Course Started & Application completed'),
(6, 'Application received'),
(7, 'Application Undereviewed'),
(8, 'Program apply'),
(9, 'Conditional Offer'),
(10, 'Unconditional Offer'),
(11, 'CAS/LOA issued'),
(12, 'VISA Granted'),
(13, 'University enrolled'),
(16, 'Refund'),
(15, 'Commission'),
(18, 'Reappeal'),
(19, 'Differment'),
(20, 'Processing Completed'),
(21, 'University Payment');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_training_request`
--

CREATE TABLE `ggportal_tbl_training_request` (
  `training_request_id` int(11) NOT NULL,
  `training_type` varchar(50) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `perfer_date_1` datetime NOT NULL,
  `perfer_date_2` datetime NOT NULL,
  `perfer_date_3` datetime NOT NULL,
  `send_yn` varchar(1) NOT NULL DEFAULT 'N',
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `selected_date` varchar(20) NOT NULL,
  `meeting_link` varchar(450) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_training_request`
--

INSERT INTO `ggportal_tbl_training_request` (`training_request_id`, `training_type`, `user_id`, `user_type`, `perfer_date_1`, `perfer_date_2`, `perfer_date_3`, `send_yn`, `active_yn`, `selected_date`, `meeting_link`) VALUES
(5, 'agent', 35, 'AG', '2023-09-08 14:57:00', '2023-09-16 14:57:00', '2023-09-15 14:57:00', 'Y', 'Y', '2023-09-16 14:57:00', 'efsdf'),
(7, 'agent', 39, 'AG', '2023-09-21 11:58:00', '2023-12-12 12:00:00', '2023-09-28 14:21:00', 'Y', 'Y', '2023-09-28 14:21:00', 'https://newschannelweb.atlassian.net/jira/software/projects/GE/boards/8'),
(6, 'agent', 35, 'AG', '2023-09-27 09:47:00', '2023-09-30 09:47:00', '2023-10-03 09:47:00', 'Y', 'Y', '2023-10-03 09:47:00', 'sdfdsvddv');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user`
--

CREATE TABLE `ggportal_tbl_user` (
  `user_id` int(11) NOT NULL,
  `user_name` varchar(45) DEFAULT NULL,
  `password` varchar(200) DEFAULT NULL,
  `password_salt` varchar(45) DEFAULT NULL,
  `email` varchar(70) DEFAULT NULL,
  `email_validated_yn` varchar(1) DEFAULT NULL,
  `user_active_yn` varchar(1) DEFAULT NULL,
  `user_type` varchar(2) DEFAULT 'T' COMMENT 'AD - admin\n',
  `user_access_level` varchar(45) DEFAULT NULL COMMENT 'may not use this field\n100 = full\n',
  `last_login_date` datetime DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `online_status` int(1) NOT NULL DEFAULT 0,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `first_name` varchar(45) DEFAULT NULL,
  `last_name` varchar(45) DEFAULT NULL,
  `token_id` varchar(255) NOT NULL,
  `token_created_time` datetime DEFAULT NULL,
  `profile_picture` varchar(150) DEFAULT 'users/img/no_photo.jpg',
  `user_guid` varchar(45) DEFAULT 'rand()',
  `user_contact_no` varchar(45) DEFAULT NULL,
  `force_password_change_yn` varchar(45) DEFAULT 'N'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Dumping data for table `ggportal_tbl_user`
--

INSERT INTO `ggportal_tbl_user` (`user_id`, `user_name`, `password`, `password_salt`, `email`, `email_validated_yn`, `user_active_yn`, `user_type`, `user_access_level`, `last_login_date`, `last_seen`, `online_status`, `last_login_ip`, `first_name`, `last_name`, `token_id`, `token_created_time`, `profile_picture`, `user_guid`, `user_contact_no`, `force_password_change_yn`) VALUES
(1, '<EMAIL>', 'Sithum@1992', 'Sithum@1992', '<EMAIL>', 'Y', 'Y', 'RA', '100', NULL, '2023-10-02 09:13:31', 0, NULL, 'Nashif', 'Nashif', '', '1970-01-01 00:00:00', 'dist/img/avatar5.png', 'rand()', NULL, 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user_activity`
--

CREATE TABLE `ggportal_tbl_user_activity` (
  `user_activity_id` int(10) UNSIGNED NOT NULL,
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `activity_description` text NOT NULL,
  `created_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user_login`
--

CREATE TABLE `ggportal_tbl_user_login` (
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `date_login` datetime NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_user_login`
--

INSERT INTO `ggportal_tbl_user_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(1, 1, '2019-10-14 17:29:00', '**************', '2019-10-14 17:29:00'),
(2, 1, '2019-10-14 19:37:24', '**************', '2019-10-14 19:37:24'),
(3, 1, '2019-10-14 19:45:00', '**************', '2019-10-14 19:45:00'),
(4, 1, '2019-10-14 19:48:42', '**************', '2019-10-14 19:48:42'),
(5, 1, '2019-10-14 19:49:10', '**************', '2019-10-14 19:49:10'),
(6, 1, '2019-10-15 17:23:23', '***************', '2019-10-15 17:23:23'),
(7, 1, '2019-10-15 18:05:03', '***************', '2019-10-15 18:05:03'),
(8, 1, '2019-10-16 15:27:00', '***************', '2019-10-16 15:27:00'),
(9, 1, '2019-10-16 15:33:22', '***************', '2019-10-16 15:33:22'),
(10, 1, '2019-11-15 04:42:14', '**************', '2019-11-15 04:42:14'),
(11, 1, '2019-12-19 08:56:35', '**************', '2019-12-19 08:56:35'),
(12, 1, '2019-12-24 04:17:40', '**************', '2019-12-24 04:17:40'),
(13, 1, '2019-12-24 04:33:34', '**************', '2019-12-24 04:33:34'),
(14, 1, '2019-12-24 06:48:28', '**************', '2019-12-24 06:48:28'),
(15, 1, '2019-12-24 07:35:27', '**************', '2019-12-24 07:35:27'),
(16, 1, '2019-12-24 10:16:46', '**************', '2019-12-24 10:16:46'),
(17, 1, '2019-12-26 05:21:17', '123.231.105.167', '2019-12-26 05:21:17'),
(18, 1, '2019-12-26 05:26:37', '123.231.105.167', '2019-12-26 05:26:37'),
(19, 1, '2019-12-26 06:17:43', '123.231.105.167', '2019-12-26 06:17:43'),
(20, 1, '2019-12-26 07:15:15', '123.231.105.167', '2019-12-26 07:15:15'),
(21, 1, '2019-12-27 04:01:55', '123.231.120.4', '2019-12-27 04:01:55'),
(22, 1, '2019-12-27 04:35:43', '123.231.120.4', '2019-12-27 04:35:43'),
(23, 1, '2019-12-27 05:28:38', '123.231.120.4', '2019-12-27 05:28:38'),
(24, 1, '2019-12-27 17:23:41', '112.134.195.81', '2019-12-27 17:23:41'),
(25, 1, '2019-12-29 08:28:49', '112.134.195.164', '2019-12-29 08:28:49'),
(26, 1, '2019-12-30 10:25:53', '175.157.44.180', '2019-12-30 10:25:53'),
(27, 1, '2019-12-31 03:45:51', '123.231.105.77', '2019-12-31 03:45:51'),
(28, 1, '2019-12-31 04:26:46', '123.231.105.77', '2019-12-31 04:26:46'),
(29, 1, '2020-01-01 03:58:25', '175.157.43.187', '2020-01-01 03:58:25'),
(30, 1, '2020-01-01 04:04:47', '175.157.43.187', '2020-01-01 04:04:47'),
(31, 1, '2020-01-01 05:24:58', '175.157.43.187', '2020-01-01 05:24:58'),
(32, 1, '2020-01-01 05:25:14', '175.157.43.187', '2020-01-01 05:25:14'),
(33, 1, '2020-01-01 06:20:35', '175.157.43.187', '2020-01-01 06:20:35'),
(34, 1, '2020-01-01 07:30:14', '175.157.43.187', '2020-01-01 07:30:14'),
(35, 1, '2020-01-01 08:58:41', '175.157.43.187', '2020-01-01 08:58:41'),
(36, 1, '2020-01-01 17:27:08', '112.134.194.11', '2020-01-01 17:27:08'),
(37, 1, '2020-01-01 18:01:43', '112.134.194.11', '2020-01-01 18:01:43'),
(38, 1, '2020-01-02 03:52:34', '123.231.108.198', '2020-01-02 03:52:34'),
(39, 1, '2020-01-02 04:31:43', '123.231.108.198', '2020-01-02 04:31:43'),
(40, 1, '2020-01-02 04:56:50', '123.231.108.198', '2020-01-02 04:56:50'),
(41, 1, '2020-01-02 08:12:44', '123.231.108.198', '2020-01-02 08:12:44'),
(42, 1, '2020-01-02 09:22:45', '123.231.108.198', '2020-01-02 09:22:45'),
(43, 1, '2020-01-06 04:48:16', '123.231.108.162', '2020-01-06 04:48:16'),
(44, 1, '2020-01-06 09:08:36', '123.231.108.162', '2020-01-06 09:08:36'),
(45, 1, '2020-01-06 09:18:54', '123.231.108.162', '2020-01-06 09:18:54'),
(46, 1, '2020-01-07 04:01:28', '123.231.121.85', '2020-01-07 04:01:28'),
(47, 1, '2020-01-07 07:02:59', '123.231.121.85', '2020-01-07 07:02:59'),
(48, 1, '2020-01-07 07:55:40', '123.231.121.85', '2020-01-07 07:55:40'),
(49, 1, '2020-01-07 08:46:28', '123.231.121.85', '2020-01-07 08:46:28'),
(50, 1, '2020-01-07 10:56:46', '123.231.121.85', '2020-01-07 10:56:46'),
(51, 1, '2020-01-08 05:41:00', '123.231.120.53', '2020-01-08 05:41:00'),
(52, 1, '2020-01-09 06:56:20', '116.206.246.38', '2020-01-09 06:56:20'),
(53, 1, '2020-01-10 14:59:52', '45.121.88.93', '2020-01-10 14:59:52'),
(54, 1, '2020-01-13 04:57:02', '175.157.43.211', '2020-01-13 04:57:02'),
(55, 1, '2020-01-13 07:38:23', '175.157.43.211', '2020-01-13 07:38:23'),
(56, 1, '2020-01-14 11:47:02', '123.231.85.0', '2020-01-14 11:47:02'),
(57, 1, '2020-01-15 05:17:55', '123.231.124.234', '2020-01-15 05:17:55'),
(58, 1, '2020-01-16 04:50:00', '123.231.109.89', '2020-01-16 04:50:00'),
(59, 1, '2020-01-17 09:37:38', '175.157.46.17', '2020-01-17 09:37:38'),
(60, 1, '2020-01-23 06:39:22', '175.157.41.62', '2020-01-23 06:39:22'),
(61, 1, '2020-01-23 11:33:02', '175.157.41.62', '2020-01-23 11:33:02'),
(62, 1, '2020-01-30 09:37:32', '175.157.42.28', '2020-01-30 09:37:32'),
(63, 1, '2020-02-05 05:30:35', '175.157.42.193', '2020-02-05 05:30:35'),
(64, 1, '2020-02-05 09:00:35', '175.157.42.193', '2020-02-05 09:00:35'),
(65, 1, '2020-02-05 10:55:40', '175.157.42.193', '2020-02-05 10:55:40'),
(66, 1, '2020-02-05 11:09:10', '175.157.42.193', '2020-02-05 11:09:10'),
(67, 1, '2020-02-05 11:11:53', '175.157.42.193', '2020-02-05 11:11:53'),
(68, 1, '2020-02-05 11:12:48', '175.157.42.193', '2020-02-05 11:12:48'),
(69, 1, '2020-02-05 11:13:45', '175.157.42.193', '2020-02-05 11:13:45'),
(70, 1, '2020-02-05 11:22:09', '175.157.42.193', '2020-02-05 11:22:09'),
(71, 1, '2020-02-05 14:16:04', '112.134.195.247', '2020-02-05 14:16:04'),
(72, 1, '2020-02-05 14:19:47', '84.9.39.254', '2020-02-05 14:19:47'),
(73, 1, '2020-02-05 16:33:24', '84.9.39.254', '2020-02-05 16:33:24'),
(74, 1, '2020-02-06 03:55:07', '123.231.122.190', '2020-02-06 03:55:07'),
(75, 1, '2020-02-06 04:23:50', '123.231.122.190', '2020-02-06 04:23:50'),
(76, 1, '2020-02-06 09:04:04', '123.231.122.190', '2020-02-06 09:04:04'),
(77, 1, '2020-02-07 10:35:43', '111.223.170.199', '2020-02-07 10:35:43'),
(78, 1, '2020-02-10 04:58:52', '175.157.47.162', '2020-02-10 04:58:52'),
(79, 1, '2020-02-10 05:13:13', '175.157.47.162', '2020-02-10 05:13:13'),
(80, 1, '2020-02-14 06:31:40', '123.231.106.10', '2020-02-14 06:31:40'),
(81, 1, '2020-02-14 06:34:50', '123.231.106.10', '2020-02-14 06:34:50'),
(82, 1, '2020-02-18 06:32:17', '116.206.246.126', '2020-02-18 06:32:17'),
(83, 1, '2020-02-19 06:14:55', '175.157.41.208', '2020-02-19 06:14:55'),
(84, 1, '2020-02-20 07:44:14', '123.231.86.232', '2020-02-20 07:44:14'),
(85, 1, '2020-02-21 13:00:14', '123.231.126.216', '2020-02-21 13:00:14'),
(86, 1, '2020-02-21 13:00:38', '37.186.32.2', '2020-02-21 13:00:38'),
(87, 1, '2020-02-24 09:07:39', '123.231.111.206', '2020-02-24 09:07:39'),
(88, 1, '2020-02-24 11:05:36', '123.231.111.206', '2020-02-24 11:05:36'),
(89, 1, '2020-02-28 12:02:04', '116.206.244.228', '2020-02-28 12:02:04'),
(90, 1, '2020-03-04 05:16:00', '175.157.47.128', '2020-03-04 05:16:00'),
(91, 1, '2020-03-10 04:01:59', '123.231.125.155', '2020-03-10 04:01:59'),
(92, 1, '2020-03-10 04:44:41', '123.231.125.155', '2020-03-10 04:44:41'),
(93, 1, '2020-03-10 05:36:28', '123.231.125.155', '2020-03-10 05:36:28'),
(94, 1, '2020-03-10 05:39:08', '123.231.125.155', '2020-03-10 05:39:08'),
(95, 1, '2020-03-10 06:09:12', '123.231.125.155', '2020-03-10 06:09:12'),
(96, 1, '2020-03-10 09:55:44', '123.231.125.155', '2020-03-10 09:55:44'),
(97, 1, '2020-03-10 09:57:54', '123.231.125.155', '2020-03-10 09:57:54'),
(98, 1, '2020-03-11 11:49:32', '175.157.47.252', '2020-03-11 11:49:32'),
(99, 1, '2020-03-11 12:00:51', '175.157.47.252', '2020-03-11 12:00:51'),
(100, 1, '2020-03-12 04:04:21', '123.231.124.190', '2020-03-12 04:04:21'),
(101, 1, '2020-03-12 04:09:52', '123.231.124.190', '2020-03-12 04:09:52'),
(102, 1, '2020-03-12 05:02:24', '123.231.124.190', '2020-03-12 05:02:24'),
(103, 1, '2020-03-12 05:50:28', '123.231.124.190', '2020-03-12 05:50:28'),
(104, 1, '2020-03-12 07:21:05', '123.231.124.190', '2020-03-12 07:21:05'),
(105, 1, '2020-03-16 04:26:00', '116.206.245.195', '2020-03-16 04:26:00'),
(106, 1, '2020-03-16 07:42:39', '175.157.131.219', '2020-03-16 07:42:39'),
(107, 1, '2020-03-17 08:33:53', '123.231.125.198', '2020-03-17 08:33:53'),
(108, 1, '2020-03-26 07:56:24', '112.134.193.235', '2020-03-26 07:56:24'),
(109, 1, '2020-03-26 07:56:33', '112.134.193.235', '2020-03-26 07:56:33'),
(110, 1, '2020-03-26 07:56:53', '112.134.193.235', '2020-03-26 07:56:53'),
(111, 1, '2020-03-26 07:57:29', '112.134.193.235', '2020-03-26 07:57:29'),
(112, 1, '2020-03-26 07:58:28', '112.134.193.235', '2020-03-26 07:58:28'),
(113, 1, '2020-03-26 07:58:44', '112.134.193.235', '2020-03-26 07:58:44'),
(114, 1, '2020-03-26 07:59:24', '112.134.193.235', '2020-03-26 07:59:24'),
(115, 1, '2020-03-26 08:02:19', '112.134.193.235', '2020-03-26 08:02:19'),
(116, 1, '2020-03-26 08:04:01', '112.134.193.235', '2020-03-26 08:04:01'),
(117, 1, '2020-03-26 08:22:15', '112.134.193.235', '2020-03-26 08:22:15'),
(118, 1, '2020-03-26 08:25:14', '112.134.193.235', '2020-03-26 08:25:14'),
(119, 1, '2020-03-26 08:28:58', '112.134.193.235', '2020-03-26 08:28:58'),
(120, 1, '2020-03-26 10:38:18', '112.134.193.235', '2020-03-26 10:38:18'),
(121, 1, '2020-03-26 12:13:16', '112.134.193.235', '2020-03-26 12:13:16'),
(122, 1, '2020-03-26 12:13:48', '112.134.193.235', '2020-03-26 12:13:48'),
(123, 1, '2020-03-26 12:15:51', '112.134.193.235', '2020-03-26 12:15:51'),
(124, 1, '2020-03-26 12:16:36', '84.9.39.254', '2020-03-26 12:16:36'),
(125, 1, '2020-03-26 12:18:44', '175.157.29.161', '2020-03-26 12:18:44'),
(126, 1, '2020-03-26 12:20:42', '84.9.39.254', '2020-03-26 12:20:42'),
(127, 1, '2020-03-26 12:53:52', '112.134.193.235', '2020-03-26 12:53:52'),
(128, 1, '2020-03-27 06:44:19', '112.134.194.239', '2020-03-27 06:44:19'),
(129, 1, '2020-03-27 06:44:48', '175.157.38.77', '2020-03-27 06:44:48'),
(130, 1, '2020-03-27 06:46:09', '112.134.194.239', '2020-03-27 06:46:09'),
(131, 1, '2020-03-27 06:52:43', '112.134.194.239', '2020-03-27 06:52:43'),
(132, 1, '2020-03-27 09:02:58', '112.134.194.239', '2020-03-27 09:02:58'),
(133, 1, '2020-03-28 10:36:19', '112.134.194.77', '2020-03-28 10:36:19'),
(134, 1, '2020-03-28 10:37:57', '112.134.194.77', '2020-03-28 10:37:57'),
(135, 1, '2020-03-28 10:38:43', '112.134.194.77', '2020-03-28 10:38:43'),
(136, 1, '2020-03-28 10:39:47', '112.134.194.77', '2020-03-28 10:39:47'),
(137, 1, '2020-03-28 10:51:08', '112.134.194.77', '2020-03-28 10:51:08'),
(138, 1, '2020-03-28 10:51:44', '112.134.194.77', '2020-03-28 10:51:44'),
(139, 1, '2020-03-28 10:59:03', '112.134.194.77', '2020-03-28 10:59:03'),
(140, 1, '2020-03-28 11:28:43', '112.134.194.77', '2020-03-28 11:28:43'),
(141, 1, '2020-03-28 12:01:25', '112.134.194.77', '2020-03-28 12:01:25'),
(142, 1, '2020-04-22 18:28:11', '112.134.209.160', '2020-04-22 18:28:11'),
(143, 1, '2020-04-22 18:28:21', '112.134.209.160', '2020-04-22 18:28:21'),
(144, 1, '2020-06-02 10:03:32', '123.231.122.0', '2020-06-02 10:03:32'),
(145, 1, '2020-07-10 14:05:59', '84.9.39.254', '2020-07-10 14:05:59'),
(146, 1, '2020-07-11 19:20:54', '112.134.215.229', '2020-07-11 19:20:54'),
(147, 1, '2020-07-13 10:26:35', '123.231.87.20', '2020-07-13 10:26:35'),
(148, 1, '2020-07-13 12:52:27', '89.211.242.119', '2020-07-13 12:52:27'),
(149, 1, '2020-07-15 07:15:58', '111.223.170.99', '2020-07-15 07:15:58'),
(150, 1, '2020-07-15 07:21:08', '111.223.170.99', '2020-07-15 07:21:08'),
(151, 1, '2020-07-15 09:49:58', '111.223.170.99', '2020-07-15 09:49:58'),
(152, 1, '2020-07-15 12:03:01', '111.223.170.99', '2020-07-15 12:03:01'),
(153, 1, '2020-07-15 13:03:14', '111.223.170.99', '2020-07-15 13:03:14'),
(154, 1, '2020-07-15 13:11:24', '103.247.51.170', '2020-07-15 13:11:24'),
(155, 1, '2020-07-16 04:18:47', '175.157.44.67', '2020-07-16 04:18:47'),
(156, 1, '2020-07-16 04:47:45', '103.247.48.241', '2020-07-16 04:47:45'),
(157, 1, '2020-08-17 06:03:38', '123.231.85.63', '2020-08-17 06:03:38'),
(158, 1, '2020-08-19 13:52:54', '116.206.247.193', '2020-08-19 13:52:54'),
(159, 1, '2020-08-20 11:39:22', '123.231.105.234', '2020-08-20 11:39:22'),
(160, 1, '2020-08-20 17:35:54', '112.134.208.85', '2020-08-20 17:35:54'),
(161, 1, '2020-08-21 06:20:29', '175.157.41.110', '2020-08-21 06:20:29'),
(162, 1, '2020-08-21 10:19:02', '123.231.107.17', '2020-08-21 10:19:02'),
(163, 1, '2020-08-21 12:43:01', '123.231.107.17', '2020-08-21 12:43:01'),
(164, 1, '2020-09-03 11:59:54', '123.231.122.161', '2020-09-03 11:59:54'),
(165, 1, '2020-09-04 12:59:14', '111.223.170.82', '2020-09-04 12:59:14'),
(166, 1, '2020-09-07 07:15:13', '175.157.42.179', '2020-09-07 07:15:13'),
(167, 1, '2020-09-07 08:03:58', '175.157.203.188', '2020-09-07 08:03:58'),
(168, 1, '2020-09-07 08:08:28', '175.157.42.179', '2020-09-07 08:08:28'),
(169, 1, '2020-09-08 12:30:32', '175.157.43.243', '2020-09-08 12:30:32'),
(170, 1, '2020-09-10 08:15:26', '123.231.109.124', '2020-09-10 08:15:26'),
(171, 1, '2020-09-10 10:25:04', '123.231.87.71', '2020-09-10 10:25:04'),
(172, 1, '2020-09-13 15:05:10', '43.250.242.138', '2020-09-13 15:05:10'),
(173, 1, '2020-09-14 07:45:36', '123.231.122.57', '2020-09-14 07:45:36'),
(174, 1, '2020-09-15 05:41:05', '175.157.15.59', '2020-09-15 05:41:05'),
(175, 1, '2020-09-16 12:29:06', '43.250.243.222', '2020-09-16 12:29:06'),
(176, 1, '2020-09-17 16:38:18', '43.252.14.166', '2020-09-17 16:38:18'),
(177, 1, '2020-09-17 21:33:48', '103.247.51.174', '2020-09-17 21:33:48'),
(178, 1, '2020-09-17 22:03:45', '103.247.51.174', '2020-09-17 22:03:45'),
(179, 1, '2020-09-18 09:10:11', '61.245.161.212', '2020-09-18 09:10:11'),
(180, 1, '2020-09-18 10:01:02', '123.231.127.40', '2020-09-18 10:01:02'),
(181, 1, '2020-09-18 10:08:55', '123.231.127.40', '2020-09-18 10:08:55'),
(182, 1, '2020-09-18 11:23:57', '61.245.161.212', '2020-09-18 11:23:57'),
(183, 1, '2020-09-18 11:40:50', '112.135.3.25', '2020-09-18 11:40:50'),
(184, 1, '2020-09-18 11:41:47', '112.135.3.25', '2020-09-18 11:41:47'),
(185, 1, '2020-09-18 11:58:49', '112.135.3.25', '2020-09-18 11:58:49'),
(186, 1, '2020-09-18 12:11:46', '112.135.3.25', '2020-09-18 12:11:46'),
(187, 1, '2020-09-18 12:32:00', '112.135.3.25', '2020-09-18 12:32:00'),
(188, 1, '2020-09-18 12:35:26', '112.135.3.25', '2020-09-18 12:35:26'),
(189, 1, '2020-09-18 12:45:12', '112.135.3.25', '2020-09-18 12:45:12'),
(190, 1, '2020-09-18 13:14:59', '112.135.3.25', '2020-09-18 13:14:59'),
(191, 1, '2020-09-18 18:20:15', '112.134.215.109', '2020-09-18 18:20:15'),
(192, 1, '2020-09-21 08:41:20', '111.223.170.105', '2020-09-21 08:41:20'),
(193, 1, '2020-09-21 13:08:07', '111.223.170.105', '2020-09-21 13:08:07'),
(194, 1, '2020-09-22 12:53:03', '112.135.14.34', '2020-09-22 12:53:03'),
(195, 1, '2020-09-23 08:42:06', '112.135.5.8', '2020-09-23 08:42:06'),
(196, 1, '2020-09-23 10:34:27', '112.135.5.8', '2020-09-23 10:34:27'),
(197, 1, '2020-09-23 11:59:36', '112.135.5.8', '2020-09-23 11:59:36'),
(198, 1, '2020-09-23 13:01:36', '112.135.5.8', '2020-09-23 13:01:36'),
(199, 1, '2020-09-24 05:02:54', '175.157.47.107', '2020-09-24 05:02:54'),
(200, 1, '2020-09-24 12:16:18', '175.157.47.107', '2020-09-24 12:16:18'),
(201, 1, '2020-09-25 11:34:55', '175.157.45.214', '2020-09-25 11:34:55'),
(202, 1, '2020-09-25 13:11:18', '175.157.45.214', '2020-09-25 13:11:18'),
(203, 1, '2020-09-28 06:41:49', '175.157.114.64', '2020-09-28 06:41:49'),
(204, 1, '2020-09-28 12:13:18', '112.135.1.121', '2020-09-28 12:13:18'),
(205, 1, '2020-09-28 13:25:32', '112.135.1.121', '2020-09-28 13:25:32'),
(206, 1, '2020-09-28 17:19:53', '43.252.14.205', '2020-09-28 17:19:53'),
(207, 1, '2020-09-29 05:15:26', '112.135.7.216', '2020-09-29 05:15:26'),
(208, 1, '2020-10-01 07:44:57', '175.157.40.2', '2020-10-01 07:44:57'),
(209, 1, '2020-10-13 08:40:00', '112.135.15.150', '2020-10-13 08:40:00'),
(210, 1, '2020-10-14 11:53:27', '123.231.86.178', '2020-10-14 11:53:27'),
(211, 1, '2020-10-15 08:52:09', '112.135.8.116', '2020-10-15 08:52:09'),
(212, 1, '2020-10-16 09:59:19', '61.245.169.62', '2020-10-16 09:59:19'),
(213, 1, '2020-10-17 10:12:14', '61.245.170.2', '2020-10-17 10:12:14'),
(214, 1, '2020-10-18 07:50:49', '43.250.243.225', '2020-10-18 07:50:49'),
(215, 1, '2020-10-18 11:30:56', '43.250.243.225', '2020-10-18 11:30:56'),
(216, 1, '2020-10-18 15:52:44', '43.250.243.225', '2020-10-18 15:52:44'),
(217, 1, '2020-10-19 15:08:27', '43.250.242.108', '2020-10-19 15:08:27'),
(218, 1, '2020-10-19 18:52:00', '43.250.242.108', '2020-10-19 18:52:00'),
(219, 1, '2020-10-20 09:33:37', '112.135.14.247', '2020-10-20 09:33:37'),
(220, 1, '2020-10-20 13:18:31', '123.231.107.243', '2020-10-20 13:18:31'),
(221, 1, '2020-10-23 01:38:06', '61.245.161.57', '2020-10-23 01:38:06'),
(222, 1, '2020-10-23 02:17:43', '61.245.161.57', '2020-10-23 02:17:43'),
(223, 1, '2020-10-23 07:15:28', '112.135.9.134', '2020-10-23 07:15:28'),
(224, 1, '2020-10-25 17:58:22', '43.250.240.202', '2020-10-25 17:58:22'),
(225, 1, '2020-10-30 06:25:08', '112.134.209.150', '2020-10-30 06:25:08'),
(226, 1, '2020-11-03 17:37:23', '43.250.241.79', '2020-11-03 17:37:23'),
(227, 1, '2020-11-03 17:47:18', '43.250.241.79', '2020-11-03 17:47:18'),
(228, 1, '2020-11-08 16:10:47', '43.250.241.154', '2020-11-08 16:10:47'),
(229, 1, '2020-11-08 17:17:24', '43.250.242.199', '2020-11-08 17:17:24'),
(230, 1, '2020-11-08 18:45:09', '175.157.58.140', '2020-11-08 18:45:09'),
(231, 1, '2020-11-08 19:29:39', '175.157.58.140', '2020-11-08 19:29:39'),
(232, 1, '2020-11-17 04:54:03', '43.250.242.146', '2020-11-17 04:54:03'),
(233, 1, '2020-11-18 07:02:11', '123.231.109.160', '2020-11-18 07:02:11'),
(234, 1, '2020-11-18 07:06:08', '43.250.242.146', '2020-11-18 07:06:08'),
(235, 1, '2020-11-18 07:16:35', '43.250.242.146', '2020-11-18 07:16:35'),
(236, 1, '2020-11-20 05:53:42', '61.245.170.146', '2020-11-20 05:53:42'),
(237, 1, '2020-11-20 09:02:04', '112.135.8.241', '2020-11-20 09:02:04'),
(238, 1, '2020-11-20 09:23:07', '61.245.170.146', '2020-11-20 09:23:07'),
(239, 1, '2020-11-20 10:31:03', '61.245.170.146', '2020-11-20 10:31:03'),
(240, 1, '2021-03-31 09:33:06', '::1', '2021-03-31 09:33:06'),
(241, 1, '2021-03-31 10:00:37', '::1', '2021-03-31 10:00:37'),
(242, 1, '2021-03-31 15:22:08', '::1', '2021-03-31 15:22:08'),
(243, 1, '2021-03-31 15:56:34', '::1', '2021-03-31 15:56:34'),
(244, 1, '2021-04-01 09:50:37', '::1', '2021-04-01 09:50:37'),
(245, 1, '2021-04-01 14:49:59', '::1', '2021-04-01 14:49:59'),
(246, 1, '2021-04-02 09:49:22', '::1', '2021-04-02 09:49:22'),
(247, 1, '2021-04-02 10:22:41', '::1', '2021-04-02 10:22:41'),
(248, 1, '2021-04-02 13:53:56', '::1', '2021-04-02 13:53:56'),
(249, 1, '2021-04-05 09:45:56', '::1', '2021-04-05 09:45:56'),
(250, 1, '2021-04-05 11:44:16', '::1', '2021-04-05 11:44:16'),
(251, 1, '2021-04-05 11:58:38', '::1', '2021-04-05 11:58:38'),
(252, 1, '2021-04-05 14:16:26', '::1', '2021-04-05 14:16:26'),
(253, 1, '2021-04-05 15:23:22', '::1', '2021-04-05 15:23:22'),
(254, 1, '2021-04-05 17:49:41', '::1', '2021-04-05 17:49:41'),
(255, 1, '2021-04-06 09:40:04', '::1', '2021-04-06 09:40:04'),
(256, 1, '2021-04-06 17:33:20', '::1', '2021-04-06 17:33:20'),
(257, 1, '2021-04-07 09:52:13', '::1', '2021-04-07 09:52:13'),
(258, 1, '2021-04-07 12:14:45', '::1', '2021-04-07 12:14:45'),
(259, 1, '2021-04-07 13:38:22', '::1', '2021-04-07 13:38:22'),
(260, 1, '2021-04-07 15:20:57', '::1', '2021-04-07 15:20:57'),
(261, 1, '2021-04-08 10:58:21', '::1', '2021-04-08 10:58:21'),
(262, 1, '2021-04-08 15:44:20', '::1', '2021-04-08 15:44:20'),
(263, 1, '2021-04-19 07:51:25', '::1', '2021-04-19 07:51:25'),
(264, 1, '2021-04-19 12:00:28', '::1', '2021-04-19 12:00:28'),
(265, 1, '2021-04-19 12:57:23', '::1', '2021-04-19 12:57:23'),
(266, 1, '2021-04-19 13:30:35', '::1', '2021-04-19 13:30:35'),
(267, 1, '2021-04-20 08:28:35', '::1', '2021-04-20 08:28:35'),
(268, 1, '2021-04-20 14:38:03', '::1', '2021-04-20 14:38:03'),
(269, 1, '2021-04-20 16:18:06', '::1', '2021-04-20 16:18:06'),
(270, 1, '2021-04-20 16:25:49', '::1', '2021-04-20 16:25:49'),
(271, 1, '2021-04-20 16:34:31', '::1', '2021-04-20 16:34:31'),
(272, 1, '2021-04-21 09:40:34', '::1', '2021-04-21 09:40:34'),
(273, 1, '2021-04-22 09:04:49', '::1', '2021-04-22 09:04:49'),
(274, 1, '2021-04-22 09:52:36', '::1', '2021-04-22 09:52:36'),
(275, 1, '2021-04-22 09:44:55', '175.157.41.253', '2021-04-22 09:44:55'),
(276, 1, '2021-04-22 10:25:31', '175.157.41.253', '2021-04-22 10:25:31'),
(277, 1, '2021-04-22 11:23:26', '123.231.86.255', '2021-04-22 11:23:26'),
(278, 1, '2021-04-23 04:48:27', '175.157.41.205', '2021-04-23 04:48:27'),
(279, 1, '2021-04-23 06:21:12', '175.157.41.205', '2021-04-23 06:21:12'),
(280, 1, '2021-04-23 06:21:13', '175.157.41.205', '2021-04-23 06:21:13'),
(281, 1, '2021-04-23 09:00:04', '175.157.47.239', '2021-04-23 09:00:04'),
(282, 1, '2021-04-23 11:45:48', '175.157.47.239', '2021-04-23 11:45:48'),
(283, 1, '2021-04-26 04:07:58', '43.250.241.204', '2021-04-26 04:07:58'),
(284, 1, '2021-04-26 04:08:01', '43.250.241.204', '2021-04-26 04:08:01'),
(285, 1, '2021-04-26 05:06:31', '123.231.126.109', '2021-04-26 05:06:31'),
(286, 1, '2021-04-26 05:27:29', '123.231.126.109', '2021-04-26 05:27:29'),
(287, 1, '2021-04-26 05:32:51', '123.231.126.109', '2021-04-26 05:32:51'),
(288, 1, '2021-04-26 05:39:57', '43.250.241.204', '2021-04-26 05:39:57'),
(289, 1, '2021-04-26 06:34:10', '123.231.126.109', '2021-04-26 06:34:10'),
(290, 1, '2021-04-27 06:20:46', '123.231.85.130', '2021-04-27 06:20:46'),
(291, 1, '2021-04-27 06:21:31', '123.231.85.130', '2021-04-27 06:21:31'),
(292, 1, '2021-04-27 06:50:53', '123.231.85.130', '2021-04-27 06:50:53'),
(293, 1, '2021-04-27 08:05:59', '123.231.85.130', '2021-04-27 08:05:59'),
(294, 1, '2021-04-27 08:23:51', '123.231.85.130', '2021-04-27 08:23:51'),
(295, 1, '2021-04-27 08:23:54', '123.231.85.130', '2021-04-27 08:23:54'),
(296, 1, '2021-04-27 08:25:00', '123.231.85.130', '2021-04-27 08:25:00'),
(297, 2, '2021-04-27 08:51:44', '123.231.85.130', '2021-04-27 08:51:44'),
(298, 1, '2021-04-27 09:28:45', '123.231.85.130', '2021-04-27 09:28:45'),
(299, 1, '2021-04-27 09:32:26', '123.231.85.130', '2021-04-27 09:32:26'),
(300, 1, '2021-04-27 10:34:01', '123.231.85.130', '2021-04-27 10:34:01'),
(301, 1, '2021-04-27 10:38:44', '123.231.85.130', '2021-04-27 10:38:44'),
(302, 1, '2021-04-27 11:55:03', '123.231.85.130', '2021-04-27 11:55:03'),
(303, 1, '2021-04-28 04:37:26', '116.206.244.177', '2021-04-28 04:37:26'),
(304, 1, '2021-04-28 04:45:36', '116.206.244.177', '2021-04-28 04:45:36'),
(305, 1, '2021-04-28 05:13:13', '116.206.244.177', '2021-04-28 05:13:13'),
(306, 1, '2021-04-28 07:45:49', '116.206.244.177', '2021-04-28 07:45:49'),
(307, 1, '2021-04-28 07:46:25', '116.206.244.177', '2021-04-28 07:46:25'),
(308, 1, '2021-04-28 08:44:49', '116.206.244.177', '2021-04-28 08:44:49'),
(309, 1, '2021-04-28 09:08:46', '116.206.244.177', '2021-04-28 09:08:46'),
(310, 1, '2021-04-28 09:27:59', '116.206.244.177', '2021-04-28 09:27:59'),
(311, 1, '2021-04-28 11:00:56', '116.206.244.177', '2021-04-28 11:00:56'),
(312, 1, '2021-04-28 11:05:47', '116.206.244.177', '2021-04-28 11:05:47'),
(313, 1, '2021-04-28 11:05:52', '116.206.244.177', '2021-04-28 11:05:52'),
(314, 1, '2021-04-28 11:07:26', '116.206.244.177', '2021-04-28 11:07:26'),
(315, 1, '2021-04-28 11:28:00', '116.206.244.177', '2021-04-28 11:28:00'),
(316, 1, '2021-04-28 11:28:37', '116.206.244.177', '2021-04-28 11:28:37'),
(317, 1, '2021-04-28 11:29:47', '116.206.244.177', '2021-04-28 11:29:47'),
(318, 1, '2021-04-28 11:41:03', '116.206.244.177', '2021-04-28 11:41:03'),
(319, 1, '2021-04-28 11:51:21', '116.206.244.177', '2021-04-28 11:51:21'),
(320, 1, '2021-04-28 12:04:00', '116.206.244.177', '2021-04-28 12:04:00'),
(321, 1, '2021-04-28 12:05:55', '116.206.244.177', '2021-04-28 12:05:55'),
(322, 1, '2021-04-28 12:07:40', '116.206.244.177', '2021-04-28 12:07:40'),
(323, 1, '2021-04-29 05:09:20', '116.206.245.176', '2021-04-29 05:09:20'),
(324, 1, '2021-04-29 06:22:46', '116.206.245.176', '2021-04-29 06:22:46'),
(325, 1, '2021-04-29 07:51:25', '116.206.245.176', '2021-04-29 07:51:25'),
(326, 1, '2021-04-29 08:09:11', '116.206.245.176', '2021-04-29 08:09:11'),
(327, 3, '2021-04-29 08:19:53', '116.206.245.176', '2021-04-29 08:19:53'),
(328, 1, '2021-04-29 09:11:30', '116.206.245.176', '2021-04-29 09:11:30'),
(329, 3, '2021-04-29 09:37:45', '116.206.245.176', '2021-04-29 09:37:45'),
(330, 3, '2021-04-29 09:40:20', '116.206.245.176', '2021-04-29 09:40:20'),
(331, 1, '2021-04-29 09:45:02', '116.206.245.176', '2021-04-29 09:45:02'),
(332, 1, '2021-04-29 09:46:07', '116.206.245.176', '2021-04-29 09:46:07'),
(333, 4, '2021-04-29 09:48:33', '116.206.245.176', '2021-04-29 09:48:33'),
(334, 5, '2021-04-29 09:48:47', '116.206.245.176', '2021-04-29 09:48:47'),
(335, 1, '2021-04-29 09:48:55', '116.206.245.176', '2021-04-29 09:48:55'),
(336, 1, '2021-04-29 10:41:47', '116.206.245.176', '2021-04-29 10:41:47'),
(337, 1, '2021-04-29 10:43:14', '116.206.245.176', '2021-04-29 10:43:14'),
(338, 1, '2021-04-29 10:51:37', '116.206.245.176', '2021-04-29 10:51:37'),
(339, 1, '2021-04-29 11:44:56', '116.206.245.176', '2021-04-29 11:44:56'),
(340, 1, '2021-04-29 12:03:58', '116.206.245.176', '2021-04-29 12:03:58'),
(341, 1, '2021-04-29 12:17:51', '116.206.245.176', '2021-04-29 12:17:51'),
(342, 1, '2021-04-29 12:18:13', '116.206.245.176', '2021-04-29 12:18:13'),
(343, 1, '2021-04-29 12:19:47', '116.206.245.176', '2021-04-29 12:19:47'),
(344, 1, '2021-04-29 12:22:27', '116.206.245.176', '2021-04-29 12:22:27'),
(345, 1, '2021-04-30 03:32:08', '61.245.170.144', '2021-04-30 03:32:08'),
(346, 6, '2021-04-30 04:50:52', '61.245.170.144', '2021-04-30 04:50:52'),
(347, 6, '2021-04-30 05:05:52', '61.245.170.144', '2021-04-30 05:05:52'),
(348, 1, '2021-04-30 08:13:29', '175.157.165.29', '2021-04-30 08:13:29'),
(349, 1, '2021-05-02 14:12:39', '43.250.242.58', '2021-05-02 14:12:39'),
(350, 1, '2021-05-02 14:12:52', '43.250.242.58', '2021-05-02 14:12:52'),
(351, 1, '2021-05-03 03:00:23', '43.250.243.127', '2021-05-03 03:00:23'),
(352, 1, '2021-05-03 03:49:14', '212.104.226.127', '2021-05-03 03:49:14'),
(353, 1, '2021-05-03 04:04:49', '212.104.226.127', '2021-05-03 04:04:49'),
(354, 7, '2021-05-03 04:06:30', '212.104.226.127', '2021-05-03 04:06:30'),
(355, 1, '2021-05-03 04:08:08', '212.104.226.127', '2021-05-03 04:08:08'),
(356, 1, '2021-05-03 06:12:14', '116.206.247.222', '2021-05-03 06:12:14'),
(357, 1, '2021-05-03 07:24:49', '116.206.247.222', '2021-05-03 07:24:49'),
(358, 1, '2021-05-03 09:06:02', '116.206.247.222', '2021-05-03 09:06:02'),
(359, 1, '2021-05-03 09:17:13', '43.250.243.127', '2021-05-03 09:17:13'),
(360, 1, '2021-05-03 10:26:41', '123.231.104.76', '2021-05-03 10:26:41'),
(361, 1, '2021-05-03 11:04:25', '123.231.104.76', '2021-05-03 11:04:25'),
(362, 1, '2021-05-03 11:37:31', '175.157.47.181', '2021-05-03 11:37:31'),
(363, 1, '2021-05-03 11:44:17', '123.231.104.76', '2021-05-03 11:44:17'),
(364, 1, '2021-05-04 06:10:04', '175.157.47.181', '2021-05-04 06:10:04'),
(365, 1, '2021-05-04 06:47:56', '43.250.243.127', '2021-05-04 06:47:56'),
(366, 1, '2021-05-04 07:23:41', '175.157.47.181', '2021-05-04 07:23:41'),
(367, 1, '2021-05-04 08:48:41', '175.157.40.117', '2021-05-04 08:48:41'),
(368, 8, '2021-05-04 09:48:03', '43.250.243.127', '2021-05-04 09:48:03'),
(369, 8, '2021-05-04 09:48:56', '43.250.243.127', '2021-05-04 09:48:56'),
(370, 1, '2021-05-04 10:59:22', '43.250.243.127', '2021-05-04 10:59:22'),
(371, 1, '2021-05-04 11:56:31', '175.157.40.117', '2021-05-04 11:56:31'),
(372, 1, '2021-05-04 12:35:47', '116.206.245.46', '2021-05-04 12:35:47'),
(373, 1, '2021-05-04 12:36:36', '116.206.245.46', '2021-05-04 12:36:36'),
(374, 8, '2021-05-04 12:37:32', '116.206.245.46', '2021-05-04 12:37:32'),
(375, 1, '2021-05-05 05:12:46', '123.231.105.253', '2021-05-05 05:12:46'),
(376, 8, '2021-05-05 05:19:24', '123.231.105.253', '2021-05-05 05:19:24'),
(377, 1, '2021-05-05 08:57:14', '123.231.105.253', '2021-05-05 08:57:14'),
(378, 1, '2021-05-05 09:30:51', '175.157.43.8', '2021-05-05 09:30:51'),
(379, 1, '2021-05-05 10:09:41', '175.157.43.8', '2021-05-05 10:09:41'),
(380, 8, '2021-05-05 10:12:20', '175.157.43.8', '2021-05-05 10:12:20'),
(381, 1, '2021-05-05 10:40:42', '123.231.105.253', '2021-05-05 10:40:42'),
(382, 8, '2021-05-05 11:36:10', '123.231.105.253', '2021-05-05 11:36:10'),
(383, 1, '2021-05-05 11:38:52', '123.231.105.253', '2021-05-05 11:38:52'),
(384, 1, '2021-05-05 11:47:00', '175.157.43.8', '2021-05-05 11:47:00'),
(385, 8, '2021-05-05 11:52:01', '123.231.105.253', '2021-05-05 11:52:01'),
(386, 1, '2021-05-06 03:42:18', '212.104.226.127', '2021-05-06 03:42:18'),
(387, 1, '2021-05-06 04:59:05', '175.157.43.8', '2021-05-06 04:59:05'),
(388, 8, '2021-05-06 06:09:53', '61.245.171.127', '2021-05-06 06:09:53'),
(389, 1, '2021-05-06 06:18:54', '175.157.45.219', '2021-05-06 06:18:54'),
(390, 8, '2021-05-06 06:54:59', '61.245.171.127', '2021-05-06 06:54:59'),
(391, 1, '2021-05-06 08:14:12', '175.157.45.219', '2021-05-06 08:14:12'),
(392, 1, '2021-05-06 08:20:39', '61.245.171.127', '2021-05-06 08:20:39'),
(393, 8, '2021-05-06 08:23:05', '175.157.45.219', '2021-05-06 08:23:05'),
(394, 1, '2021-05-06 08:23:27', '175.157.45.219', '2021-05-06 08:23:27'),
(395, 8, '2021-05-06 08:23:50', '175.157.45.219', '2021-05-06 08:23:50'),
(396, 1, '2021-05-06 08:51:11', '175.157.45.219', '2021-05-06 08:51:11'),
(397, 8, '2021-05-06 10:01:02', '61.245.171.127', '2021-05-06 10:01:02'),
(398, 1, '2021-05-06 12:36:21', '175.157.223.42', '2021-05-06 12:36:21'),
(399, 1, '2021-05-06 15:20:42', '112.134.214.78', '2021-05-06 15:20:42'),
(400, 1, '2021-05-07 03:33:48', '61.245.171.127', '2021-05-07 03:33:48'),
(401, 8, '2021-05-07 03:50:54', '61.245.171.127', '2021-05-07 03:50:54'),
(402, 1, '2021-05-07 04:20:30', '61.245.171.127', '2021-05-07 04:20:30'),
(403, 1, '2021-05-07 06:12:26', '61.245.171.127', '2021-05-07 06:12:26'),
(404, 1, '2021-05-07 06:13:06', '175.157.40.232', '2021-05-07 06:13:06'),
(405, 1, '2021-05-07 06:21:35', '123.231.120.88', '2021-05-07 06:21:35'),
(406, 1, '2021-05-07 07:09:01', '123.231.120.88', '2021-05-07 07:09:01'),
(407, 1, '2021-05-07 08:17:02', '175.157.40.232', '2021-05-07 08:17:02'),
(408, 1, '2021-05-07 08:41:59', '61.245.171.127', '2021-05-07 08:41:59'),
(409, 1, '2021-05-07 09:24:37', '123.231.87.14', '2021-05-07 09:24:37'),
(410, 1, '2021-05-07 10:17:19', '61.245.171.127', '2021-05-07 10:17:19'),
(411, 1, '2021-05-07 10:29:22', '123.231.120.88', '2021-05-07 10:29:22'),
(412, 10, '2021-05-07 11:33:35', '61.245.171.127', '2021-05-07 11:33:35'),
(413, 1, '2021-05-08 19:03:21', '112.134.210.228', '2021-05-08 19:03:21'),
(414, 1, '2021-05-08 19:29:45', '113.59.214.195', '2021-05-08 19:29:45'),
(415, 1, '2021-05-08 19:31:00', '175.157.38.36', '2021-05-08 19:31:00'),
(416, 1, '2021-05-08 19:42:49', '113.59.214.195', '2021-05-08 19:42:49'),
(417, 11, '2021-05-08 20:09:18', '113.59.214.195', '2021-05-08 20:09:18'),
(418, 11, '2021-05-08 20:11:09', '113.59.214.195', '2021-05-08 20:11:09'),
(419, 1, '2021-05-08 20:14:04', '113.59.214.195', '2021-05-08 20:14:04'),
(420, 1, '2021-05-08 22:01:17', '175.157.38.36', '2021-05-08 22:01:17'),
(421, 1, '2021-05-09 12:59:31', '113.59.210.131', '2021-05-09 12:59:31'),
(422, 11, '2021-05-09 13:03:39', '113.59.210.131', '2021-05-09 13:03:39'),
(423, 1, '2021-05-09 13:35:00', '175.157.44.20', '2021-05-09 13:35:00'),
(424, 11, '2021-05-09 16:34:05', '113.59.210.131', '2021-05-09 16:34:05'),
(425, 1, '2021-05-09 16:34:41', '113.59.210.131', '2021-05-09 16:34:41'),
(426, 11, '2021-05-09 16:47:11', '113.59.210.131', '2021-05-09 16:47:11'),
(427, 11, '2021-05-09 17:14:59', '113.59.210.131', '2021-05-09 17:14:59'),
(428, 1, '2021-05-10 04:48:09', '175.157.41.213', '2021-05-10 04:48:09'),
(429, 1, '2021-05-10 05:01:59', '175.157.41.213', '2021-05-10 05:01:59'),
(430, 1, '2021-05-10 05:02:37', '175.157.41.213', '2021-05-10 05:02:37'),
(431, 9, '2021-05-10 05:03:05', '175.157.41.213', '2021-05-10 05:03:05'),
(432, 1, '2021-05-10 05:55:09', '43.250.243.138', '2021-05-10 05:55:09'),
(433, 1, '2021-05-10 06:31:07', '43.250.243.138', '2021-05-10 06:31:07'),
(434, 1, '2021-05-10 06:31:46', '43.250.243.138', '2021-05-10 06:31:46'),
(435, 1, '2021-05-10 06:31:52', '175.157.41.213', '2021-05-10 06:31:52'),
(436, 9, '2021-05-10 06:32:00', '43.250.243.138', '2021-05-10 06:32:00'),
(437, 1, '2021-05-10 06:32:40', '43.250.243.138', '2021-05-10 06:32:40'),
(438, 1, '2021-05-10 06:45:58', '175.157.41.213', '2021-05-10 06:45:58'),
(439, 9, '2021-05-10 06:48:16', '43.250.243.138', '2021-05-10 06:48:16'),
(440, 1, '2021-05-10 07:34:22', '175.157.47.191', '2021-05-10 07:34:22'),
(441, 1, '2021-05-11 10:17:15', '175.157.47.191', '2021-05-11 10:17:15'),
(442, 1, '2021-05-11 16:34:22', '113.59.194.113', '2021-05-11 16:34:22'),
(443, 11, '2021-05-11 16:53:36', '113.59.194.113', '2021-05-11 16:53:36'),
(444, 1, '2021-05-11 19:44:42', '113.59.194.113', '2021-05-11 19:44:42'),
(445, 1, '2021-05-12 04:21:28', '43.252.15.142', '2021-05-12 04:21:28'),
(446, 1, '2021-05-12 10:37:00', '203.189.185.18', '2021-05-12 10:37:00'),
(447, 1, '2021-05-12 10:37:33', '113.59.214.86', '2021-05-12 10:37:33'),
(448, 1, '2021-05-12 10:51:02', '175.157.41.45', '2021-05-12 10:51:02'),
(449, 1, '2021-05-12 11:33:13', '113.59.214.86', '2021-05-12 11:33:13'),
(450, 1, '2021-05-12 11:37:36', '175.157.41.45', '2021-05-12 11:37:36'),
(451, 1, '2021-05-12 12:54:50', '203.189.185.33', '2021-05-12 12:54:50'),
(452, 1, '2021-05-12 16:48:30', '113.59.217.199', '2021-05-12 16:48:30'),
(453, 1, '2021-05-13 09:44:55', '175.157.165.29', '2021-05-13 09:44:55'),
(454, 1, '2021-05-13 10:19:40', '175.157.165.29', '2021-05-13 10:19:40'),
(455, 1, '2021-05-13 10:25:30', '175.157.165.29', '2021-05-13 10:25:30'),
(456, 1, '2021-05-13 16:57:15', '113.59.214.122', '2021-05-13 16:57:15'),
(457, 1, '2021-05-14 09:30:03', '175.157.47.225', '2021-05-14 09:30:03'),
(458, 1, '2021-05-14 12:04:09', '112.134.208.136', '2021-05-14 12:04:09'),
(459, 12, '2021-05-15 07:04:49', '112.134.209.235', '2021-05-15 07:04:49'),
(460, 1, '2021-05-15 07:23:14', '112.134.209.235', '2021-05-15 07:23:14'),
(461, 1, '2021-05-15 07:27:18', '112.134.209.235', '2021-05-15 07:27:18'),
(462, 12, '2021-05-15 07:29:25', '112.134.209.235', '2021-05-15 07:29:25'),
(463, 12, '2021-05-15 07:36:13', '123.231.86.123', '2021-05-15 07:36:13'),
(464, 12, '2021-05-15 07:36:14', '123.231.86.123', '2021-05-15 07:36:14'),
(465, 12, '2021-05-15 09:29:18', '123.231.86.123', '2021-05-15 09:29:18'),
(466, 12, '2021-05-15 12:42:36', '**************', '2021-05-15 12:42:36'),
(467, 1, '2021-05-15 12:42:41', '112.134.215.158', '2021-05-15 12:42:41'),
(468, 12, '2021-05-15 13:06:36', '113.59.210.74', '2021-05-15 13:06:36'),
(469, 12, '2021-05-15 13:10:37', '113.59.210.74', '2021-05-15 13:10:37'),
(470, 12, '2021-05-15 13:14:32', '113.59.210.74', '2021-05-15 13:14:32'),
(471, 12, '2021-05-15 13:17:38', '113.59.210.74', '2021-05-15 13:17:38'),
(472, 13, '2021-05-15 13:18:13', '113.59.210.74', '2021-05-15 13:18:13'),
(473, 12, '2021-05-15 14:15:58', '**************', '2021-05-15 14:15:58'),
(474, 12, '2021-05-15 14:17:27', '**************', '2021-05-15 14:17:27'),
(475, 12, '2021-05-15 14:54:12', '113.59.210.74', '2021-05-15 14:54:12'),
(476, 12, '2021-05-15 15:06:13', '**************', '2021-05-15 15:06:13'),
(477, 12, '2021-05-15 15:06:14', '113.59.210.74', '2021-05-15 15:06:14'),
(478, 1, '2021-05-15 15:18:49', '112.134.215.158', '2021-05-15 15:18:49'),
(479, 1, '2021-05-15 15:46:53', '112.134.215.158', '2021-05-15 15:46:53'),
(480, 12, '2021-05-15 15:56:10', '123.231.86.123', '2021-05-15 15:56:10'),
(481, 12, '2021-05-15 16:17:55', '113.59.210.74', '2021-05-15 16:17:55'),
(482, 1, '2021-05-15 16:44:05', '112.134.215.158', '2021-05-15 16:44:05'),
(483, 12, '2021-05-15 16:57:55', '113.59.210.74', '2021-05-15 16:57:55'),
(484, 12, '2021-05-15 17:04:05', '123.231.86.123', '2021-05-15 17:04:05'),
(485, 12, '2021-05-15 17:15:44', '113.59.210.74', '2021-05-15 17:15:44'),
(486, 13, '2021-05-15 17:15:56', '113.59.210.74', '2021-05-15 17:15:56'),
(487, 1, '2021-05-15 18:03:49', '112.134.215.158', '2021-05-15 18:03:49'),
(488, 12, '2021-05-15 18:17:55', '113.59.210.74', '2021-05-15 18:17:55'),
(489, 12, '2021-05-15 18:23:29', '113.59.210.74', '2021-05-15 18:23:29'),
(490, 12, '2021-05-15 18:23:57', '**************', '2021-05-15 18:23:57'),
(491, 1, '2021-05-15 19:22:21', '112.134.215.158', '2021-05-15 19:22:21'),
(492, 12, '2021-05-15 21:59:15', '175.157.32.48', '2021-05-15 21:59:15'),
(493, 12, '2021-05-16 02:25:10', '175.157.32.48', '2021-05-16 02:25:10'),
(494, 12, '2021-05-16 02:46:57', '**************', '2021-05-16 02:46:57'),
(495, 12, '2021-05-16 03:11:06', '**************', '2021-05-16 03:11:06'),
(496, 12, '2021-05-16 03:38:26', '**************', '2021-05-16 03:38:26'),
(497, 12, '2021-05-16 04:55:33', '**************', '2021-05-16 04:55:33'),
(498, 12, '2021-05-16 04:55:40', '**************', '2021-05-16 04:55:40'),
(499, 12, '2021-05-16 05:25:48', '**************', '2021-05-16 05:25:48'),
(500, 12, '2021-05-16 05:25:54', '**************', '2021-05-16 05:25:54'),
(501, 12, '2021-05-16 05:51:00', '**************', '2021-05-16 05:51:00'),
(502, 12, '2021-05-16 06:22:11', '**************', '2021-05-16 06:22:11'),
(503, 12, '2021-05-16 07:19:26', '**************', '2021-05-16 07:19:26'),
(504, 12, '2021-05-16 08:23:14', '**************', '2021-05-16 08:23:14'),
(505, 12, '2021-05-16 08:23:21', '**************', '2021-05-16 08:23:21'),
(506, 1, '2021-05-16 09:09:11', '112.134.212.24', '2021-05-16 09:09:11'),
(507, 12, '2021-05-16 09:57:14', '**************', '2021-05-16 09:57:14'),
(508, 12, '2021-05-16 10:16:33', '**************', '2021-05-16 10:16:33'),
(509, 12, '2021-05-16 11:00:11', '111.223.160.106', '2021-05-16 11:00:11'),
(510, 12, '2021-05-16 11:25:42', '**************', '2021-05-16 11:25:42'),
(511, 12, '2021-05-16 11:26:01', '**************', '2021-05-16 11:26:01'),
(512, 12, '2021-05-16 11:47:29', '123.231.86.123', '2021-05-16 11:47:29'),
(513, 1, '2021-05-16 12:18:42', '112.134.212.24', '2021-05-16 12:18:42'),
(514, 1, '2021-05-16 12:32:39', '84.9.39.254', '2021-05-16 12:32:39'),
(515, 1, '2021-05-16 12:37:17', '112.134.212.24', '2021-05-16 12:37:17'),
(516, 12, '2021-05-16 13:39:37', '**************', '2021-05-16 13:39:37'),
(517, 12, '2021-05-16 13:40:00', '**************', '2021-05-16 13:40:00'),
(518, 12, '2021-05-16 14:12:09', '**************', '2021-05-16 14:12:09'),
(519, 12, '2021-05-16 14:12:19', '**************', '2021-05-16 14:12:19'),
(520, 12, '2021-05-16 14:48:38', '**************', '2021-05-16 14:48:38'),
(521, 12, '2021-05-16 14:48:46', '**************', '2021-05-16 14:48:46'),
(522, 12, '2021-05-16 14:59:45', '**************', '2021-05-16 14:59:45'),
(523, 12, '2021-05-16 15:06:40', '**************', '2021-05-16 15:06:40'),
(524, 12, '2021-05-16 15:15:55', '123.231.86.123', '2021-05-16 15:15:55'),
(525, 1, '2021-05-16 15:40:03', '112.134.212.24', '2021-05-16 15:40:03'),
(526, 12, '2021-05-16 17:03:37', '113.59.209.4', '2021-05-16 17:03:37'),
(527, 12, '2021-05-16 17:04:51', '113.59.209.4', '2021-05-16 17:04:51'),
(528, 13, '2021-05-16 17:05:46', '113.59.209.4', '2021-05-16 17:05:46'),
(529, 12, '2021-05-16 17:06:15', '**************', '2021-05-16 17:06:15'),
(530, 15, '2021-05-16 17:06:28', '113.59.209.4', '2021-05-16 17:06:28'),
(531, 12, '2021-05-16 17:14:37', '113.59.209.4', '2021-05-16 17:14:37'),
(532, 12, '2021-05-16 17:49:06', '**************', '2021-05-16 17:49:06'),
(533, 12, '2021-05-16 18:01:11', '**************', '2021-05-16 18:01:11'),
(534, 12, '2021-05-16 18:14:08', '123.231.86.123', '2021-05-16 18:14:08'),
(535, 1, '2021-05-16 19:18:55', '112.134.208.142', '2021-05-16 19:18:55'),
(536, 12, '2021-05-16 19:25:14', '**************', '2021-05-16 19:25:14'),
(537, 12, '2021-05-16 19:25:30', '**************', '2021-05-16 19:25:30'),
(538, 12, '2021-05-16 20:00:23', '**************', '2021-05-16 20:00:23'),
(539, 12, '2021-05-16 20:00:29', '**************', '2021-05-16 20:00:29'),
(540, 12, '2021-05-16 22:09:05', '**************', '2021-05-16 22:09:05'),
(541, 12, '2021-05-16 22:09:10', '**************', '2021-05-16 22:09:10'),
(542, 12, '2021-05-16 22:11:02', '**************', '2021-05-16 22:11:02'),
(543, 12, '2021-05-16 22:19:19', '**************', '2021-05-16 22:19:19'),
(544, 12, '2021-05-16 22:20:32', '**************', '2021-05-16 22:20:32'),
(545, 12, '2021-05-17 04:59:37', '123.231.86.88', '2021-05-17 04:59:37'),
(546, 12, '2021-05-17 05:22:36', '175.157.25.197', '2021-05-17 05:22:36'),
(547, 12, '2021-05-17 06:35:32', '**************', '2021-05-17 06:35:32'),
(548, 12, '2021-05-17 06:49:15', '**************', '2021-05-17 06:49:15'),
(549, 12, '2021-05-17 06:59:16', '123.231.86.88', '2021-05-17 06:59:16'),
(550, 12, '2021-05-17 08:09:32', '**************', '2021-05-17 08:09:32'),
(551, 12, '2021-05-17 08:09:37', '**************', '2021-05-17 08:09:37'),
(552, 12, '2021-05-17 08:14:53', '**************', '2021-05-17 08:14:53'),
(553, 1, '2021-05-17 08:16:59', '175.157.42.131', '2021-05-17 08:16:59'),
(554, 12, '2021-05-17 09:05:07', '**************', '2021-05-17 09:05:07'),
(555, 12, '2021-05-17 09:05:12', '**************', '2021-05-17 09:05:12'),
(556, 12, '2021-05-17 09:07:31', '**************', '2021-05-17 09:07:31'),
(557, 1, '2021-05-17 09:15:09', '175.157.42.131', '2021-05-17 09:15:09'),
(558, 12, '2021-05-17 10:27:22', '**************', '2021-05-17 10:27:22'),
(559, 12, '2021-05-17 10:27:27', '**************', '2021-05-17 10:27:27'),
(560, 1, '2021-05-17 10:33:53', '175.157.42.131', '2021-05-17 10:33:53'),
(561, 12, '2021-05-17 10:35:55', '**************', '2021-05-17 10:35:55'),
(562, 12, '2021-05-17 11:06:41', '**************', '2021-05-17 11:06:41'),
(563, 12, '2021-05-17 11:20:00', '**************', '2021-05-17 11:20:00'),
(564, 12, '2021-05-17 11:50:41', '113.59.209.4', '2021-05-17 11:50:41'),
(565, 13, '2021-05-17 11:58:05', '113.59.209.4', '2021-05-17 11:58:05'),
(566, 12, '2021-05-17 12:26:09', '**************', '2021-05-17 12:26:09'),
(567, 12, '2021-05-17 12:26:15', '**************', '2021-05-17 12:26:15'),
(568, 1, '2021-05-17 12:55:42', '175.157.42.131', '2021-05-17 12:55:42'),
(569, 12, '2021-05-17 13:00:11', '**************', '2021-05-17 13:00:11'),
(570, 12, '2021-05-17 14:01:40', '**************', '2021-05-17 14:01:40'),
(571, 12, '2021-05-17 14:44:45', '175.157.166.178', '2021-05-17 14:44:45'),
(572, 12, '2021-05-17 15:30:00', '**************', '2021-05-17 15:30:00'),
(573, 12, '2021-05-17 15:47:33', '**************', '2021-05-17 15:47:33'),
(574, 12, '2021-05-17 16:10:09', '113.59.213.216', '2021-05-17 16:10:09'),
(575, 12, '2021-05-17 16:19:50', '113.59.213.216', '2021-05-17 16:19:50'),
(576, 13, '2021-05-17 16:25:40', '113.59.213.216', '2021-05-17 16:25:40'),
(577, 12, '2021-05-17 16:30:42', '**************', '2021-05-17 16:30:42'),
(578, 12, '2021-05-17 16:33:53', '113.59.213.216', '2021-05-17 16:33:53'),
(579, 12, '2021-05-17 16:45:26', '**************', '2021-05-17 16:45:26'),
(580, 12, '2021-05-17 16:59:43', '**************', '2021-05-17 16:59:43'),
(581, 15, '2021-05-17 17:04:48', '113.59.213.216', '2021-05-17 17:04:48'),
(582, 12, '2021-05-17 17:04:57', '113.59.213.216', '2021-05-17 17:04:57'),
(583, 12, '2021-05-17 17:24:11', '**************', '2021-05-17 17:24:11'),
(584, 12, '2021-05-17 17:37:24', '123.231.86.88', '2021-05-17 17:37:24'),
(585, 12, '2021-05-17 18:05:39', '**************', '2021-05-17 18:05:39'),
(586, 12, '2021-05-17 18:05:45', '**************', '2021-05-17 18:05:45'),
(587, 12, '2021-05-17 18:09:25', '**************', '2021-05-17 18:09:25'),
(588, 12, '2021-05-17 18:32:09', '123.231.86.88', '2021-05-17 18:32:09'),
(589, 1, '2021-05-17 18:41:37', '112.134.214.52', '2021-05-17 18:41:37'),
(590, 12, '2021-05-17 18:50:14', '123.231.86.88', '2021-05-17 18:50:14'),
(591, 12, '2021-05-17 20:02:22', '**************', '2021-05-17 20:02:22'),
(592, 12, '2021-05-17 22:05:31', '113.59.214.209', '2021-05-17 22:05:31'),
(593, 12, '2021-05-17 22:05:40', '113.59.214.209', '2021-05-17 22:05:40'),
(594, 12, '2021-05-18 05:19:57', '123.231.86.88', '2021-05-18 05:19:57'),
(595, 12, '2021-05-18 05:45:29', '175.157.177.167', '2021-05-18 05:45:29'),
(596, 1, '2021-05-18 06:28:51', '175.157.47.141', '2021-05-18 06:28:51'),
(597, 12, '2021-05-18 07:52:09', '**************', '2021-05-18 07:52:09'),
(598, 12, '2021-05-18 08:13:33', '175.157.140.114', '2021-05-18 08:13:33'),
(599, 1, '2021-05-18 08:26:54', '175.157.47.141', '2021-05-18 08:26:54'),
(600, 12, '2021-05-18 09:00:17', '175.157.140.114', '2021-05-18 09:00:17'),
(601, 12, '2021-05-18 09:31:17', '203.189.185.144', '2021-05-18 09:31:17'),
(602, 12, '2021-05-18 09:48:44', '175.157.13.138', '2021-05-18 09:48:44'),
(603, 12, '2021-05-18 10:33:58', '175.157.170.41', '2021-05-18 10:33:58'),
(604, 12, '2021-05-18 11:35:42', '123.231.86.88', '2021-05-18 11:35:42'),
(605, 1, '2021-05-18 12:14:01', '175.157.47.141', '2021-05-18 12:14:01'),
(606, 12, '2021-05-18 17:05:35', '**************', '2021-05-18 17:05:35'),
(607, 12, '2021-05-18 18:47:41', '123.231.86.88', '2021-05-18 18:47:41'),
(608, 12, '2021-05-18 19:03:32', '123.231.86.88', '2021-05-18 19:03:32'),
(609, 12, '2021-05-18 19:11:31', '113.59.217.158', '2021-05-18 19:11:31'),
(610, 13, '2021-05-18 19:11:51', '113.59.217.158', '2021-05-18 19:11:51'),
(611, 12, '2021-05-18 22:09:42', '**************', '2021-05-18 22:09:42'),
(612, 12, '2021-05-19 00:55:31', '123.231.125.181', '2021-05-19 00:55:31'),
(613, 12, '2021-05-19 01:02:26', '123.231.125.181', '2021-05-19 01:02:26'),
(614, 12, '2021-05-19 02:07:44', '123.231.125.181', '2021-05-19 02:07:44'),
(615, 12, '2021-05-19 02:31:55', '123.231.125.181', '2021-05-19 02:31:55'),
(616, 12, '2021-05-19 04:11:18', '113.59.217.158', '2021-05-19 04:11:18'),
(617, 12, '2021-05-19 04:12:19', '123.231.125.181', '2021-05-19 04:12:19'),
(618, 15, '2021-05-19 04:16:13', '113.59.217.158', '2021-05-19 04:16:13'),
(619, 13, '2021-05-19 04:16:57', '113.59.217.158', '2021-05-19 04:16:57'),
(620, 12, '2021-05-19 04:22:02', '113.59.217.158', '2021-05-19 04:22:02'),
(621, 12, '2021-05-19 04:50:03', '123.231.125.181', '2021-05-19 04:50:03'),
(622, 12, '2021-05-19 05:03:47', '175.157.8.14', '2021-05-19 05:03:47'),
(623, 12, '2021-05-19 06:42:44', '43.252.14.185', '2021-05-19 06:42:44'),
(624, 1, '2021-05-19 06:52:03', '123.231.120.30', '2021-05-19 06:52:03'),
(625, 12, '2021-05-19 07:23:05', '175.157.38.63', '2021-05-19 07:23:05'),
(626, 12, '2021-05-19 07:51:27', '123.231.125.181', '2021-05-19 07:51:27'),
(627, 12, '2021-05-19 08:54:58', '113.59.217.157', '2021-05-19 08:54:58'),
(628, 1, '2021-05-19 09:15:35', '123.231.120.30', '2021-05-19 09:15:35'),
(629, 12, '2021-05-19 10:03:04', '**************', '2021-05-19 10:03:04'),
(630, 12, '2021-05-19 10:03:11', '**************', '2021-05-19 10:03:11'),
(631, 12, '2021-05-19 10:09:06', '**************', '2021-05-19 10:09:06'),
(632, 12, '2021-05-19 10:12:37', '**************', '2021-05-19 10:12:37'),
(633, 1, '2021-05-19 11:11:01', '123.231.120.30', '2021-05-19 11:11:01'),
(634, 12, '2021-05-19 11:17:48', '**************', '2021-05-19 11:17:48'),
(635, 12, '2021-05-19 11:52:37', '175.157.14.211', '2021-05-19 11:52:37'),
(636, 1, '2021-05-19 12:27:38', '123.231.120.30', '2021-05-19 12:27:38'),
(637, 1, '2021-05-19 12:42:27', '123.231.120.30', '2021-05-19 12:42:27'),
(638, 12, '2021-05-19 13:09:03', '**************', '2021-05-19 13:09:03'),
(639, 12, '2021-05-19 14:17:42', '113.59.217.134', '2021-05-19 14:17:42'),
(640, 1, '2021-05-19 17:59:43', '112.134.210.241', '2021-05-19 17:59:43'),
(641, 12, '2021-05-19 18:12:10', '123.231.125.181', '2021-05-19 18:12:10'),
(642, 12, '2021-05-19 18:58:26', '113.59.217.134', '2021-05-19 18:58:26'),
(643, 12, '2021-05-20 04:32:23', '**************', '2021-05-20 04:32:23'),
(644, 12, '2021-05-20 04:45:58', '**************', '2021-05-20 04:45:58'),
(645, 12, '2021-05-20 04:51:24', '**************', '2021-05-20 04:51:24'),
(646, 12, '2021-05-20 04:52:55', '**************', '2021-05-20 04:52:55'),
(647, 12, '2021-05-20 05:15:09', '**************', '2021-05-20 05:15:09'),
(648, 12, '2021-05-20 05:22:38', '175.157.46.179', '2021-05-20 05:22:38'),
(649, 1, '2021-05-20 06:08:27', '123.231.85.163', '2021-05-20 06:08:27'),
(650, 1, '2021-05-20 06:51:28', '123.231.85.163', '2021-05-20 06:51:28'),
(651, 12, '2021-05-20 06:59:34', '175.157.46.179', '2021-05-20 06:59:34'),
(652, 1, '2021-05-20 08:47:39', '123.231.85.163', '2021-05-20 08:47:39'),
(653, 12, '2021-05-20 09:03:46', '103.247.48.228', '2021-05-20 09:03:46'),
(654, 12, '2021-05-20 10:20:37', '113.59.217.90', '2021-05-20 10:20:37'),
(655, 12, '2021-05-20 10:20:52', '175.157.43.236', '2021-05-20 10:20:52'),
(656, 12, '2021-05-20 10:37:39', '113.59.217.90', '2021-05-20 10:37:39'),
(657, 12, '2021-05-20 10:44:52', '175.157.43.236', '2021-05-20 10:44:52'),
(658, 1, '2021-05-20 11:22:27', '123.231.85.163', '2021-05-20 11:22:27'),
(659, 12, '2021-05-20 11:28:33', '175.157.43.236', '2021-05-20 11:28:33'),
(660, 12, '2021-05-20 12:55:55', '175.157.43.236', '2021-05-20 12:55:55'),
(661, 12, '2021-05-20 12:56:01', '175.157.43.236', '2021-05-20 12:56:01'),
(662, 12, '2021-05-20 13:19:51', '175.157.43.236', '2021-05-20 13:19:51'),
(663, 12, '2021-05-20 17:22:15', '175.157.40.248', '2021-05-20 17:22:15'),
(664, 12, '2021-05-20 18:20:11', '175.157.40.248', '2021-05-20 18:20:11'),
(665, 12, '2021-05-20 18:42:03', '175.157.40.248', '2021-05-20 18:42:03'),
(666, 1, '2021-05-20 18:46:32', '123.231.122.1', '2021-05-20 18:46:32'),
(667, 12, '2021-05-20 20:03:42', '175.157.40.248', '2021-05-20 20:03:42'),
(668, 12, '2021-05-20 21:18:38', '**************', '2021-05-20 21:18:38'),
(669, 12, '2021-05-20 21:18:44', '**************', '2021-05-20 21:18:44'),
(670, 12, '2021-05-21 04:21:37', '**************', '2021-05-21 04:21:37'),
(671, 12, '2021-05-21 06:02:40', '**************', '2021-05-21 06:02:40'),
(672, 12, '2021-05-21 06:02:48', '**************', '2021-05-21 06:02:48'),
(673, 12, '2021-05-21 06:07:27', '**************', '2021-05-21 06:07:27'),
(674, 12, '2021-05-21 06:21:09', '175.157.40.248', '2021-05-21 06:21:09'),
(675, 12, '2021-05-21 06:26:22', '113.59.209.22', '2021-05-21 06:26:22'),
(676, 15, '2021-05-21 06:40:12', '113.59.209.22', '2021-05-21 06:40:12'),
(677, 1, '2021-05-21 07:59:09', '175.157.42.240', '2021-05-21 07:59:09'),
(678, 1, '2021-05-21 08:37:51', '175.157.42.240', '2021-05-21 08:37:51'),
(679, 12, '2021-05-21 09:37:25', '**************', '2021-05-21 09:37:25'),
(680, 12, '2021-05-21 09:37:32', '**************', '2021-05-21 09:37:32'),
(681, 12, '2021-05-21 10:02:15', '**************', '2021-05-21 10:02:15'),
(682, 12, '2021-05-21 10:21:44', '175.157.40.248', '2021-05-21 10:21:44'),
(683, 12, '2021-05-21 11:15:44', '175.157.40.248', '2021-05-21 11:15:44'),
(684, 12, '2021-05-21 11:15:51', '175.157.40.248', '2021-05-21 11:15:51'),
(685, 15, '2021-05-21 13:35:50', '**************', '2021-05-21 13:35:50'),
(686, 12, '2021-05-21 13:36:03', '**************', '2021-05-21 13:36:03'),
(687, 15, '2021-05-21 13:36:27', '**************', '2021-05-21 13:36:27'),
(688, 12, '2021-05-21 14:06:01', '**************', '2021-05-21 14:06:01'),
(689, 12, '2021-05-21 14:06:07', '**************', '2021-05-21 14:06:07'),
(690, 12, '2021-05-21 14:36:16', '**************', '2021-05-21 14:36:16'),
(691, 15, '2021-05-21 14:48:20', '**************', '2021-05-21 14:48:20'),
(692, 12, '2021-05-21 14:49:34', '**************', '2021-05-21 14:49:34'),
(693, 12, '2021-05-21 14:53:29', '**************', '2021-05-21 14:53:29'),
(694, 15, '2021-05-21 14:59:58', '**************', '2021-05-21 14:59:58'),
(695, 15, '2021-05-21 15:37:12', '**************', '2021-05-21 15:37:12'),
(696, 12, '2021-05-21 15:39:30', '**************', '2021-05-21 15:39:30');
INSERT INTO `ggportal_tbl_user_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(697, 12, '2021-05-21 15:40:18', '**************', '2021-05-21 15:40:18'),
(698, 12, '2021-05-21 15:46:23', '**************', '2021-05-21 15:46:23'),
(699, 12, '2021-05-21 16:13:41', '**************', '2021-05-21 16:13:41'),
(700, 12, '2021-05-21 16:13:50', '**************', '2021-05-21 16:13:50'),
(701, 15, '2021-05-21 17:36:26', '**************', '2021-05-21 17:36:26'),
(702, 12, '2021-05-21 18:36:58', '**************', '2021-05-21 18:36:58'),
(703, 12, '2021-05-21 19:03:38', '**************', '2021-05-21 19:03:38'),
(704, 1, '2021-05-21 20:09:49', '***************', '2021-05-21 20:09:49'),
(705, 12, '2021-05-21 23:47:05', '**************', '2021-05-21 23:47:05'),
(706, 12, '2021-05-22 01:28:23', '**************', '2021-05-22 01:28:23'),
(707, 12, '2021-05-22 01:52:44', '**************', '2021-05-22 01:52:44'),
(708, 15, '2021-05-22 01:53:44', '**************', '2021-05-22 01:53:44'),
(709, 15, '2021-05-22 02:04:43', '**************', '2021-05-22 02:04:43'),
(710, 12, '2021-05-22 02:59:12', '**************', '2021-05-22 02:59:12'),
(711, 13, '2021-05-22 03:05:32', '**************', '2021-05-22 03:05:32'),
(712, 12, '2021-05-22 03:07:25', '**************', '2021-05-22 03:07:25'),
(713, 12, '2021-05-22 05:37:26', '**************', '2021-05-22 05:37:26'),
(714, 12, '2021-05-22 05:37:32', '**************', '2021-05-22 05:37:32'),
(715, 12, '2021-05-22 05:47:04', '123.231.105.44', '2021-05-22 05:47:04'),
(716, 12, '2021-05-22 07:46:34', '**************', '2021-05-22 07:46:34'),
(717, 12, '2021-05-22 08:49:46', '103.247.48.39', '2021-05-22 08:49:46'),
(718, 12, '2021-05-22 10:54:39', '103.247.48.106', '2021-05-22 10:54:39'),
(719, 12, '2021-05-22 10:57:31', '**************', '2021-05-22 10:57:31'),
(720, 12, '2021-05-22 11:21:53', '103.247.48.106', '2021-05-22 11:21:53'),
(721, 12, '2021-05-22 13:14:55', '**************', '2021-05-22 13:14:55'),
(722, 12, '2021-05-22 15:08:53', '123.231.105.44', '2021-05-22 15:08:53'),
(723, 1, '2021-05-22 15:19:25', '116.206.245.101', '2021-05-22 15:19:25'),
(724, 15, '2021-05-22 16:45:32', '113.59.213.103', '2021-05-22 16:45:32'),
(725, 12, '2021-05-22 16:46:20', '**************', '2021-05-22 16:46:20'),
(726, 13, '2021-05-22 16:47:02', '113.59.213.103', '2021-05-22 16:47:02'),
(727, 12, '2021-05-22 17:39:21', '103.247.51.222', '2021-05-22 17:39:21'),
(728, 12, '2021-05-22 19:13:49', '123.231.123.168', '2021-05-22 19:13:49'),
(729, 12, '2021-05-22 20:18:47', '123.231.123.168', '2021-05-22 20:18:47'),
(730, 12, '2021-05-22 20:53:23', '123.231.123.168', '2021-05-22 20:53:23'),
(731, 12, '2021-05-23 00:01:30', '175.157.128.92', '2021-05-23 00:01:30'),
(732, 12, '2021-05-23 05:48:21', '111.223.161.42', '2021-05-23 05:48:21'),
(733, 1, '2021-05-23 05:57:28', '112.134.212.7', '2021-05-23 05:57:28'),
(734, 12, '2021-05-23 06:09:24', '123.231.123.168', '2021-05-23 06:09:24'),
(735, 12, '2021-05-23 07:43:15', '123.231.123.168', '2021-05-23 07:43:15'),
(736, 12, '2021-05-23 08:37:34', '123.231.123.168', '2021-05-23 08:37:34'),
(737, 12, '2021-05-23 08:53:13', '123.231.123.168', '2021-05-23 08:53:13'),
(738, 1, '2021-05-23 10:09:00', '175.157.191.14', '2021-05-23 10:09:00'),
(739, 1, '2021-05-23 11:49:55', '123.231.86.92', '2021-05-23 11:49:55'),
(740, 12, '2021-05-23 11:50:19', '175.157.129.13', '2021-05-23 11:50:19'),
(741, 12, '2021-05-23 14:39:08', '111.223.160.42', '2021-05-23 14:39:08'),
(742, 13, '2021-05-23 15:13:44', '113.59.214.56', '2021-05-23 15:13:44'),
(743, 12, '2021-05-23 16:12:43', '175.157.12.60', '2021-05-23 16:12:43'),
(744, 12, '2021-05-23 16:48:17', '175.157.12.60', '2021-05-23 16:48:17'),
(745, 12, '2021-05-23 16:48:25', '175.157.12.60', '2021-05-23 16:48:25'),
(746, 12, '2021-05-23 17:00:51', '123.231.123.168', '2021-05-23 17:00:51'),
(747, 12, '2021-05-23 18:44:13', '123.231.123.168', '2021-05-23 18:44:13'),
(748, 12, '2021-05-23 19:15:23', '123.231.123.168', '2021-05-23 19:15:23'),
(749, 12, '2021-05-24 04:20:02', '123.231.123.168', '2021-05-24 04:20:02'),
(750, 12, '2021-05-24 05:22:52', '123.231.123.168', '2021-05-24 05:22:52'),
(751, 12, '2021-05-24 06:44:53', '123.231.123.168', '2021-05-24 06:44:53'),
(752, 12, '2021-05-24 07:17:53', '123.231.123.168', '2021-05-24 07:17:53'),
(753, 12, '2021-05-24 07:39:15', '123.231.123.168', '2021-05-24 07:39:15'),
(754, 12, '2021-05-24 08:38:10', '123.231.123.168', '2021-05-24 08:38:10'),
(755, 12, '2021-05-24 09:42:14', '123.231.123.168', '2021-05-24 09:42:14'),
(756, 12, '2021-05-24 09:59:31', '123.231.123.168', '2021-05-24 09:59:31'),
(757, 12, '2021-05-24 12:07:12', '103.247.48.120', '2021-05-24 12:07:12'),
(758, 12, '2021-05-24 12:17:36', '113.59.210.71', '2021-05-24 12:17:36'),
(759, 1, '2021-05-24 12:25:54', '112.134.210.146', '2021-05-24 12:25:54'),
(760, 12, '2021-05-24 12:36:24', '113.59.210.71', '2021-05-24 12:36:24'),
(761, 12, '2021-05-24 14:17:29', '175.157.187.21', '2021-05-24 14:17:29'),
(762, 12, '2021-05-24 16:38:01', '123.231.123.168', '2021-05-24 16:38:01'),
(763, 12, '2021-05-24 16:54:10', '123.231.123.168', '2021-05-24 16:54:10'),
(764, 1, '2021-05-24 17:27:42', '123.231.85.44', '2021-05-24 17:27:42'),
(765, 12, '2021-05-24 17:49:52', '113.59.213.1', '2021-05-24 17:49:52'),
(766, 12, '2021-05-24 18:53:02', '123.231.123.168', '2021-05-24 18:53:02'),
(767, 1, '2021-05-24 19:03:40', '116.206.244.240', '2021-05-24 19:03:40'),
(768, 12, '2021-05-24 20:03:20', '103.247.51.50', '2021-05-24 20:03:20'),
(769, 12, '2021-05-25 04:41:53', '123.231.123.168', '2021-05-25 04:41:53'),
(770, 12, '2021-05-25 04:41:55', '123.231.123.168', '2021-05-25 04:41:55'),
(771, 12, '2021-05-25 06:41:09', '123.231.123.168', '2021-05-25 06:41:09'),
(772, 12, '2021-05-25 06:53:07', '103.247.51.10', '2021-05-25 06:53:07'),
(773, 12, '2021-05-25 07:55:50', '175.157.47.154', '2021-05-25 07:55:50'),
(774, 1, '2021-05-25 08:55:23', '116.206.247.165', '2021-05-25 08:55:23'),
(775, 1, '2021-05-25 09:36:43', '116.206.247.165', '2021-05-25 09:36:43'),
(776, 1, '2021-05-25 10:14:00', '116.206.247.165', '2021-05-25 10:14:00'),
(777, 12, '2021-05-25 11:26:16', '175.157.47.154', '2021-05-25 11:26:16'),
(778, 12, '2021-05-25 14:28:24', '203.189.184.248', '2021-05-25 14:28:24'),
(779, 12, '2021-05-25 14:32:22', '203.189.184.248', '2021-05-25 14:32:22'),
(780, 12, '2021-05-25 15:39:47', '175.157.0.227', '2021-05-25 15:39:47'),
(781, 12, '2021-05-25 16:45:16', '175.157.40.149', '2021-05-25 16:45:16'),
(782, 12, '2021-05-25 17:20:25', '175.157.40.149', '2021-05-25 17:20:25'),
(783, 12, '2021-05-25 17:50:07', '43.252.14.140', '2021-05-25 17:50:07'),
(784, 12, '2021-05-25 18:12:13', '175.157.40.149', '2021-05-25 18:12:13'),
(785, 1, '2021-05-25 18:27:34', '112.134.211.146', '2021-05-25 18:27:34'),
(786, 12, '2021-05-25 20:36:49', '175.157.40.149', '2021-05-25 20:36:49'),
(787, 12, '2021-05-25 21:30:51', '175.157.40.149', '2021-05-25 21:30:51'),
(788, 12, '2021-05-25 23:45:05', '175.157.40.149', '2021-05-25 23:45:05'),
(789, 12, '2021-05-26 03:45:03', '113.59.210.160', '2021-05-26 03:45:03'),
(790, 12, '2021-05-26 04:12:26', '175.157.40.149', '2021-05-26 04:12:26'),
(791, 12, '2021-05-26 04:44:03', '175.157.40.149', '2021-05-26 04:44:03'),
(792, 12, '2021-05-26 14:58:58', '113.59.217.191', '2021-05-26 14:58:58'),
(793, 1, '2021-05-26 15:50:31', '175.157.44.203', '2021-05-26 15:50:31'),
(794, 12, '2021-05-26 16:43:32', '175.157.35.6', '2021-05-26 16:43:32'),
(795, 12, '2021-05-26 17:06:30', '103.247.51.90', '2021-05-26 17:06:30'),
(796, 1, '2021-05-26 18:25:24', '123.231.127.200', '2021-05-26 18:25:24'),
(797, 12, '2021-05-26 18:40:38', '175.157.40.149', '2021-05-26 18:40:38'),
(798, 12, '2021-05-26 21:45:12', '175.157.40.149', '2021-05-26 21:45:12'),
(799, 12, '2021-05-27 04:21:13', '175.157.40.149', '2021-05-27 04:21:13'),
(800, 1, '2021-05-27 04:38:59', '175.157.32.249', '2021-05-27 04:38:59'),
(801, 12, '2021-05-27 04:52:57', '175.157.40.149', '2021-05-27 04:52:57'),
(802, 12, '2021-05-27 05:11:42', '175.157.40.149', '2021-05-27 05:11:42'),
(803, 12, '2021-05-27 05:49:58', '175.157.40.149', '2021-05-27 05:49:58'),
(804, 12, '2021-05-27 06:59:48', '175.157.40.149', '2021-05-27 06:59:48'),
(805, 1, '2021-05-27 07:07:02', '175.157.42.141', '2021-05-27 07:07:02'),
(806, 12, '2021-05-27 07:07:40', '175.157.40.149', '2021-05-27 07:07:40'),
(807, 1, '2021-05-27 09:31:46', '175.157.42.141', '2021-05-27 09:31:46'),
(808, 12, '2021-05-27 10:26:51', '175.157.1.115', '2021-05-27 10:26:51'),
(809, 1, '2021-05-27 12:46:37', '175.157.42.141', '2021-05-27 12:46:37'),
(810, 1, '2021-05-27 13:30:41', '175.157.42.141', '2021-05-27 13:30:41'),
(811, 12, '2021-05-27 14:03:08', '113.59.214.206', '2021-05-27 14:03:08'),
(812, 12, '2021-05-27 15:33:21', '113.59.214.206', '2021-05-27 15:33:21'),
(813, 12, '2021-05-27 17:20:10', '116.206.247.199', '2021-05-27 17:20:10'),
(814, 12, '2021-05-27 18:24:42', '116.206.247.199', '2021-05-27 18:24:42'),
(815, 12, '2021-05-27 19:55:36', '175.157.168.117', '2021-05-27 19:55:36'),
(816, 1, '2021-05-27 20:38:53', '112.134.215.105', '2021-05-27 20:38:53'),
(817, 12, '2021-05-28 04:36:29', '175.157.168.117', '2021-05-28 04:36:29'),
(818, 12, '2021-05-28 05:22:12', '203.189.185.108', '2021-05-28 05:22:12'),
(819, 12, '2021-05-28 06:41:26', '116.206.247.199', '2021-05-28 06:41:26'),
(820, 12, '2021-05-28 07:13:18', '116.206.247.199', '2021-05-28 07:13:18'),
(821, 12, '2021-05-28 08:29:17', '113.59.210.234', '2021-05-28 08:29:17'),
(822, 13, '2021-05-28 08:33:21', '113.59.210.234', '2021-05-28 08:33:21'),
(823, 15, '2021-05-28 08:33:39', '113.59.210.234', '2021-05-28 08:33:39'),
(824, 12, '2021-05-28 08:35:20', '113.59.210.234', '2021-05-28 08:35:20'),
(825, 15, '2021-05-28 08:35:33', '113.59.210.234', '2021-05-28 08:35:33'),
(826, 12, '2021-05-28 08:35:49', '113.59.210.234', '2021-05-28 08:35:49'),
(827, 15, '2021-05-28 08:43:55', '113.59.210.234', '2021-05-28 08:43:55'),
(828, 13, '2021-05-28 08:44:48', '113.59.210.234', '2021-05-28 08:44:48'),
(829, 1, '2021-05-28 10:01:01', '116.206.246.82', '2021-05-28 10:01:01'),
(830, 13, '2021-05-28 11:35:58', '113.59.194.90', '2021-05-28 11:35:58'),
(831, 15, '2021-05-28 11:36:08', '113.59.194.90', '2021-05-28 11:36:08'),
(832, 13, '2021-05-28 11:36:43', '113.59.194.90', '2021-05-28 11:36:43'),
(833, 13, '2021-05-28 11:38:21', '113.59.194.90', '2021-05-28 11:38:21'),
(834, 1, '2021-05-28 12:05:18', '116.206.246.82', '2021-05-28 12:05:18'),
(835, 12, '2021-05-28 13:03:59', '175.157.191.141', '2021-05-28 13:03:59'),
(836, 12, '2021-05-28 17:47:27', '123.231.126.246', '2021-05-28 17:47:27'),
(837, 12, '2021-05-29 04:31:01', '123.231.125.43', '2021-05-29 04:31:01'),
(838, 12, '2021-05-29 05:02:45', '123.231.125.43', '2021-05-29 05:02:45'),
(839, 1, '2021-05-29 06:06:24', '116.206.244.175', '2021-05-29 06:06:24'),
(840, 1, '2021-05-29 07:46:22', '116.206.244.175', '2021-05-29 07:46:22'),
(841, 12, '2021-05-29 08:57:09', '113.59.194.28', '2021-05-29 08:57:09'),
(842, 12, '2021-05-29 09:55:10', '175.157.169.141', '2021-05-29 09:55:10'),
(843, 1, '2021-05-29 10:47:30', '116.206.244.175', '2021-05-29 10:47:30'),
(844, 12, '2021-05-29 13:52:31', '113.59.194.28', '2021-05-29 13:52:31'),
(845, 12, '2021-05-29 14:01:58', '175.157.21.138', '2021-05-29 14:01:58'),
(846, 12, '2021-05-29 16:04:29', '175.157.37.250', '2021-05-29 16:04:29'),
(847, 12, '2021-05-29 17:32:29', '123.231.125.43', '2021-05-29 17:32:29'),
(848, 12, '2021-05-29 20:00:52', '113.59.194.28', '2021-05-29 20:00:52'),
(849, 12, '2021-05-29 22:23:27', '175.157.37.250', '2021-05-29 22:23:27'),
(850, 12, '2021-05-30 03:23:35', '175.157.157.21', '2021-05-30 03:23:35'),
(851, 12, '2021-05-30 03:42:51', '123.231.125.43', '2021-05-30 03:42:51'),
(852, 12, '2021-05-30 06:18:38', '175.157.157.21', '2021-05-30 06:18:38'),
(853, 1, '2021-05-30 07:29:14', '123.231.84.38', '2021-05-30 07:29:14'),
(854, 12, '2021-05-30 07:53:34', '123.231.125.43', '2021-05-30 07:53:34'),
(855, 1, '2021-05-30 10:09:24', '123.231.84.38', '2021-05-30 10:09:24'),
(856, 12, '2021-05-30 10:42:50', '175.157.157.21', '2021-05-30 10:42:50'),
(857, 12, '2021-05-30 11:32:29', '175.157.157.21', '2021-05-30 11:32:29'),
(858, 1, '2021-05-30 13:09:48', '123.231.84.38', '2021-05-30 13:09:48'),
(859, 1, '2021-05-30 14:13:26', '123.231.84.38', '2021-05-30 14:13:26'),
(860, 12, '2021-05-30 15:01:23', '175.157.157.21', '2021-05-30 15:01:23'),
(861, 12, '2021-05-30 15:37:33', '175.157.30.64', '2021-05-30 15:37:33'),
(862, 12, '2021-05-30 16:17:25', '175.157.157.21', '2021-05-30 16:17:25'),
(863, 12, '2021-05-30 16:38:19', '175.157.157.21', '2021-05-30 16:38:19'),
(864, 12, '2021-05-30 18:01:36', '175.157.2.90', '2021-05-30 18:01:36'),
(865, 1, '2021-05-30 18:30:12', '112.134.210.180', '2021-05-30 18:30:12'),
(866, 1, '2021-05-30 18:30:33', '112.134.210.180', '2021-05-30 18:30:33'),
(867, 12, '2021-05-30 19:45:55', '116.206.247.21', '2021-05-30 19:45:55'),
(868, 1, '2021-05-30 20:11:41', '112.134.210.180', '2021-05-30 20:11:41'),
(869, 12, '2021-05-30 23:46:19', '116.206.247.21', '2021-05-30 23:46:19'),
(870, 12, '2021-05-31 04:57:29', '175.157.157.21', '2021-05-31 04:57:29'),
(871, 12, '2021-05-31 05:20:13', '116.206.247.21', '2021-05-31 05:20:13'),
(872, 12, '2021-05-31 07:41:57', '116.206.247.21', '2021-05-31 07:41:57'),
(873, 12, '2021-05-31 08:24:19', '116.206.247.21', '2021-05-31 08:24:19'),
(874, 1, '2021-05-31 09:42:19', '123.231.87.153', '2021-05-31 09:42:19'),
(875, 12, '2021-05-31 10:11:21', '116.206.247.21', '2021-05-31 10:11:21'),
(876, 12, '2021-05-31 14:01:11', '116.206.247.21', '2021-05-31 14:01:11'),
(877, 1, '2021-05-31 14:01:52', '123.231.87.153', '2021-05-31 14:01:52'),
(878, 12, '2021-05-31 15:11:20', '116.206.247.21', '2021-05-31 15:11:20'),
(879, 12, '2021-05-31 15:12:10', '116.206.247.21', '2021-05-31 15:12:10'),
(880, 12, '2021-05-31 17:55:49', '123.231.109.46', '2021-05-31 17:55:49'),
(881, 12, '2021-05-31 18:40:28', '123.231.84.8', '2021-05-31 18:40:28'),
(882, 1, '2021-05-31 18:40:28', '175.157.165.29', '2021-05-31 18:40:28'),
(883, 12, '2021-05-31 19:28:07', '111.223.160.64', '2021-05-31 19:28:07'),
(884, 12, '2021-05-31 21:39:38', '111.223.160.64', '2021-05-31 21:39:38'),
(885, 13, '2021-06-01 03:08:56', '113.59.209.172', '2021-06-01 03:08:56'),
(886, 15, '2021-06-01 03:10:27', '113.59.209.172', '2021-06-01 03:10:27'),
(887, 13, '2021-06-01 03:11:21', '113.59.209.172', '2021-06-01 03:11:21'),
(888, 12, '2021-06-01 04:17:55', '43.252.14.149', '2021-06-01 04:17:55'),
(889, 12, '2021-06-01 05:25:00', '175.157.46.136', '2021-06-01 05:25:00'),
(890, 1, '2021-06-01 07:08:28', '112.134.212.58', '2021-06-01 07:08:28'),
(891, 12, '2021-06-01 08:07:03', '175.157.44.128', '2021-06-01 08:07:03'),
(892, 12, '2021-06-01 09:16:44', '175.157.34.189', '2021-06-01 09:16:44'),
(893, 12, '2021-06-01 10:09:20', '175.157.34.189', '2021-06-01 10:09:20'),
(894, 1, '2021-06-01 10:22:11', '112.134.212.58', '2021-06-01 10:22:11'),
(895, 12, '2021-06-01 10:22:15', '43.252.15.53', '2021-06-01 10:22:15'),
(896, 1, '2021-06-01 11:09:40', '112.134.212.58', '2021-06-01 11:09:40'),
(897, 1, '2021-06-01 12:33:07', '112.134.215.196', '2021-06-01 12:33:07'),
(898, 1, '2021-06-01 13:05:49', '112.134.215.196', '2021-06-01 13:05:49'),
(899, 1, '2021-06-01 13:16:44', '112.134.215.196', '2021-06-01 13:16:44'),
(900, 1, '2021-06-01 14:02:11', '112.134.215.196', '2021-06-01 14:02:11'),
(901, 13, '2021-06-01 14:29:26', '113.59.214.245', '2021-06-01 14:29:26'),
(902, 15, '2021-06-01 14:32:30', '113.59.214.245', '2021-06-01 14:32:30'),
(903, 12, '2021-06-01 15:21:03', '175.157.34.189', '2021-06-01 15:21:03'),
(904, 1, '2021-06-01 15:47:58', '112.134.215.196', '2021-06-01 15:47:58'),
(905, 12, '2021-06-01 16:15:43', '175.157.34.189', '2021-06-01 16:15:43'),
(906, 12, '2021-06-01 17:39:15', '175.157.43.166', '2021-06-01 17:39:15'),
(907, 1, '2021-06-01 19:08:19', '112.134.215.196', '2021-06-01 19:08:19'),
(908, 12, '2021-06-01 19:39:40', '175.157.43.166', '2021-06-01 19:39:40'),
(909, 12, '2021-06-01 22:32:17', '175.157.43.166', '2021-06-01 22:32:17'),
(910, 12, '2021-06-01 22:41:02', '175.157.43.166', '2021-06-01 22:41:02'),
(911, 12, '2021-06-01 23:46:40', '175.157.43.166', '2021-06-01 23:46:40'),
(912, 12, '2021-06-02 00:08:01', '175.157.43.166', '2021-06-02 00:08:01'),
(913, 12, '2021-06-02 01:36:12', '175.157.34.189', '2021-06-02 01:36:12'),
(914, 12, '2021-06-02 04:36:18', '175.157.43.166', '2021-06-02 04:36:18'),
(915, 1, '2021-06-02 07:25:17', '123.231.85.148', '2021-06-02 07:25:17'),
(916, 12, '2021-06-02 08:00:11', '175.157.43.166', '2021-06-02 08:00:11'),
(917, 12, '2021-06-02 08:33:55', '123.231.110.191', '2021-06-02 08:33:55'),
(918, 1, '2021-06-02 10:01:48', '112.134.213.69', '2021-06-02 10:01:48'),
(919, 12, '2021-06-02 10:24:58', '203.189.185.122', '2021-06-02 10:24:58'),
(920, 12, '2021-06-02 10:54:51', '203.189.185.122', '2021-06-02 10:54:51'),
(921, 1, '2021-06-02 11:51:02', '112.134.213.69', '2021-06-02 11:51:02'),
(922, 12, '2021-06-02 11:52:59', '203.189.185.122', '2021-06-02 11:52:59'),
(923, 12, '2021-06-02 12:02:03', '175.157.34.189', '2021-06-02 12:02:03'),
(924, 13, '2021-06-02 12:13:50', '112.134.213.69', '2021-06-02 12:13:50'),
(925, 1, '2021-06-02 12:17:18', '112.134.213.69', '2021-06-02 12:17:18'),
(926, 1, '2021-06-02 12:34:27', '112.134.213.69', '2021-06-02 12:34:27'),
(927, 12, '2021-06-02 15:17:54', '175.157.3.95', '2021-06-02 15:17:54'),
(928, 12, '2021-06-02 15:39:05', '175.157.3.95', '2021-06-02 15:39:05'),
(929, 12, '2021-06-02 16:18:45', '123.231.111.83', '2021-06-02 16:18:45'),
(930, 12, '2021-06-02 16:27:40', '123.231.111.83', '2021-06-02 16:27:40'),
(931, 13, '2021-06-02 16:36:47', '203.189.184.33', '2021-06-02 16:36:47'),
(932, 13, '2021-06-02 16:51:05', '203.189.184.33', '2021-06-02 16:51:05'),
(933, 13, '2021-06-02 17:00:31', '203.189.184.33', '2021-06-02 17:00:31'),
(934, 12, '2021-06-02 17:02:26', '203.189.184.33', '2021-06-02 17:02:26'),
(935, 13, '2021-06-02 17:09:08', '113.59.209.152', '2021-06-02 17:09:08'),
(936, 12, '2021-06-02 17:11:33', '123.231.111.83', '2021-06-02 17:11:33'),
(937, 12, '2021-06-02 18:17:44', '123.231.111.83', '2021-06-02 18:17:44'),
(938, 12, '2021-06-03 02:47:40', '175.157.3.95', '2021-06-03 02:47:40'),
(939, 12, '2021-06-03 02:54:47', '175.157.3.95', '2021-06-03 02:54:47'),
(940, 12, '2021-06-03 03:23:29', '123.231.111.83', '2021-06-03 03:23:29'),
(941, 13, '2021-06-03 04:38:21', '203.189.185.87', '2021-06-03 04:38:21'),
(942, 12, '2021-06-03 05:39:29', '116.206.246.228', '2021-06-03 05:39:29'),
(943, 12, '2021-06-03 05:39:30', '116.206.246.228', '2021-06-03 05:39:30'),
(944, 12, '2021-06-03 06:30:51', '116.206.246.228', '2021-06-03 06:30:51'),
(945, 1, '2021-06-03 09:28:31', '175.157.46.189', '2021-06-03 09:28:31'),
(946, 12, '2021-06-03 11:13:31', '175.157.36.45', '2021-06-03 11:13:31'),
(947, 12, '2021-06-03 11:13:37', '175.157.36.45', '2021-06-03 11:13:37'),
(948, 12, '2021-06-03 12:33:16', '175.157.36.45', '2021-06-03 12:33:16'),
(949, 12, '2021-06-03 13:31:34', '203.189.185.229', '2021-06-03 13:31:34'),
(950, 15, '2021-06-03 13:32:29', '203.189.185.229', '2021-06-03 13:32:29'),
(951, 1, '2021-06-03 14:14:22', '112.134.211.47', '2021-06-03 14:14:22'),
(952, 12, '2021-06-03 14:55:05', '175.157.36.45', '2021-06-03 14:55:05'),
(953, 12, '2021-06-03 16:28:52', '123.231.85.88', '2021-06-03 16:28:52'),
(954, 12, '2021-06-03 18:11:41', '123.231.85.88', '2021-06-03 18:11:41'),
(955, 12, '2021-06-04 03:00:06', '123.231.85.88', '2021-06-04 03:00:06'),
(956, 12, '2021-06-04 03:31:23', '123.231.85.88', '2021-06-04 03:31:23'),
(957, 12, '2021-06-04 05:31:29', '123.231.85.88', '2021-06-04 05:31:29'),
(958, 12, '2021-06-04 07:12:14', '123.231.110.81', '2021-06-04 07:12:14'),
(959, 1, '2021-06-04 07:57:06', '112.134.215.23', '2021-06-04 07:57:06'),
(960, 13, '2021-06-04 08:47:47', '103.247.48.49', '2021-06-04 08:47:47'),
(961, 12, '2021-06-04 08:48:53', '103.247.48.49', '2021-06-04 08:48:53'),
(962, 1, '2021-06-04 09:17:23', '112.134.208.226', '2021-06-04 09:17:23'),
(963, 1, '2021-06-04 12:45:14', '112.134.208.226', '2021-06-04 12:45:14'),
(964, 12, '2021-06-04 13:26:28', '175.157.43.2', '2021-06-04 13:26:28'),
(965, 13, '2021-06-04 14:04:09', '113.59.209.97', '2021-06-04 14:04:09'),
(966, 12, '2021-06-04 14:04:30', '113.59.209.97', '2021-06-04 14:04:30'),
(967, 13, '2021-06-04 14:05:45', '113.59.209.97', '2021-06-04 14:05:45'),
(968, 1, '2021-06-04 14:09:20', '112.134.208.226', '2021-06-04 14:09:20'),
(969, 13, '2021-06-04 14:38:47', '113.59.209.97', '2021-06-04 14:38:47'),
(970, 12, '2021-06-04 15:14:49', '175.157.43.2', '2021-06-04 15:14:49'),
(971, 13, '2021-06-04 15:21:31', '45.121.91.109', '2021-06-04 15:21:31'),
(972, 12, '2021-06-04 16:46:44', '175.157.8.221', '2021-06-04 16:46:44'),
(973, 12, '2021-06-04 18:02:29', '175.157.43.2', '2021-06-04 18:02:29'),
(974, 1, '2021-06-04 20:06:52', '112.134.208.72', '2021-06-04 20:06:52'),
(975, 12, '2021-06-05 03:01:59', '175.157.8.221', '2021-06-05 03:01:59'),
(976, 12, '2021-06-05 03:12:18', '175.157.43.2', '2021-06-05 03:12:18'),
(977, 13, '2021-06-05 03:48:59', '113.59.210.232', '2021-06-05 03:48:59'),
(978, 15, '2021-06-05 03:59:36', '113.59.210.232', '2021-06-05 03:59:36'),
(979, 12, '2021-06-05 04:13:58', '113.59.210.232', '2021-06-05 04:13:58'),
(980, 15, '2021-06-05 04:15:41', '113.59.210.232', '2021-06-05 04:15:41'),
(981, 12, '2021-06-05 04:21:05', '113.59.210.232', '2021-06-05 04:21:05'),
(982, 15, '2021-06-05 04:22:07', '113.59.210.232', '2021-06-05 04:22:07'),
(983, 12, '2021-06-05 04:22:24', '175.157.43.2', '2021-06-05 04:22:24'),
(984, 12, '2021-06-05 04:23:33', '113.59.210.232', '2021-06-05 04:23:33'),
(985, 15, '2021-06-05 04:27:21', '113.59.210.232', '2021-06-05 04:27:21'),
(986, 12, '2021-06-05 05:01:27', '175.157.43.2', '2021-06-05 05:01:27'),
(987, 13, '2021-06-05 05:23:00', '113.59.194.35', '2021-06-05 05:23:00'),
(988, 12, '2021-06-05 06:51:28', '175.157.43.2', '2021-06-05 06:51:28'),
(989, 12, '2021-06-05 07:22:07', '175.157.43.2', '2021-06-05 07:22:07'),
(990, 1, '2021-06-05 08:05:24', '112.134.214.68', '2021-06-05 08:05:24'),
(991, 12, '2021-06-05 08:16:33', '175.157.8.221', '2021-06-05 08:16:33'),
(992, 12, '2021-06-05 12:41:38', '175.157.8.221', '2021-06-05 12:41:38'),
(993, 12, '2021-06-05 12:53:11', '175.157.8.221', '2021-06-05 12:53:11'),
(994, 12, '2021-06-05 14:21:04', '175.157.8.221', '2021-06-05 14:21:04'),
(995, 12, '2021-06-05 15:09:10', '175.157.8.221', '2021-06-05 15:09:10'),
(996, 1, '2021-06-05 16:13:04', '112.134.212.104', '2021-06-05 16:13:04'),
(997, 12, '2021-06-05 16:23:10', '175.157.34.178', '2021-06-05 16:23:10'),
(998, 12, '2021-06-05 16:48:27', '175.157.43.2', '2021-06-05 16:48:27'),
(999, 1, '2021-06-05 18:17:01', '112.134.212.104', '2021-06-05 18:17:01'),
(1000, 12, '2021-06-05 18:28:50', '175.157.43.2', '2021-06-05 18:28:50'),
(1001, 1, '2021-06-05 19:01:22', '112.134.212.104', '2021-06-05 19:01:22'),
(1002, 12, '2021-06-05 19:19:16', '123.231.110.52', '2021-06-05 19:19:16'),
(1003, 12, '2021-06-06 04:04:48', '123.231.110.52', '2021-06-06 04:04:48'),
(1004, 12, '2021-06-06 04:32:21', '123.231.110.52', '2021-06-06 04:32:21'),
(1005, 12, '2021-06-06 06:16:20', '123.231.110.52', '2021-06-06 06:16:20'),
(1006, 12, '2021-06-06 08:32:54', '175.157.34.178', '2021-06-06 08:32:54'),
(1007, 1, '2021-06-06 08:38:26', '112.134.214.108', '2021-06-06 08:38:26'),
(1008, 1, '2021-06-06 10:20:05', '112.134.214.108', '2021-06-06 10:20:05'),
(1009, 12, '2021-06-06 11:04:14', '175.157.34.178', '2021-06-06 11:04:14'),
(1010, 12, '2021-06-06 12:57:23', '175.157.34.178', '2021-06-06 12:57:23'),
(1011, 1, '2021-06-06 13:46:28', '112.134.215.131', '2021-06-06 13:46:28'),
(1012, 12, '2021-06-06 14:54:01', '175.157.47.17', '2021-06-06 14:54:01'),
(1013, 13, '2021-06-06 15:32:41', '103.21.165.40', '2021-06-06 15:32:41'),
(1014, 1, '2021-06-06 16:21:28', '112.134.215.131', '2021-06-06 16:21:28'),
(1015, 12, '2021-06-06 20:07:33', '175.157.34.178', '2021-06-06 20:07:33'),
(1016, 12, '2021-06-06 20:07:45', '175.157.34.178', '2021-06-06 20:07:45'),
(1017, 12, '2021-06-06 21:40:23', '175.157.47.17', '2021-06-06 21:40:23'),
(1018, 12, '2021-06-07 02:17:40', '175.157.47.17', '2021-06-07 02:17:40'),
(1019, 12, '2021-06-07 03:04:36', '175.157.47.17', '2021-06-07 03:04:36'),
(1020, 13, '2021-06-07 03:57:22', '103.247.48.152', '2021-06-07 03:57:22'),
(1021, 12, '2021-06-07 04:11:56', '175.157.12.2', '2021-06-07 04:11:56'),
(1022, 12, '2021-06-07 04:56:03', '175.157.47.17', '2021-06-07 04:56:03'),
(1023, 13, '2021-06-07 05:08:32', '113.59.194.221', '2021-06-07 05:08:32'),
(1024, 15, '2021-06-07 05:15:14', '113.59.194.221', '2021-06-07 05:15:14'),
(1025, 15, '2021-06-07 05:43:13', '113.59.194.221', '2021-06-07 05:43:13'),
(1026, 12, '2021-06-07 07:11:09', '175.157.47.17', '2021-06-07 07:11:09'),
(1027, 1, '2021-06-07 07:45:44', '112.134.209.232', '2021-06-07 07:45:44'),
(1028, 1, '2021-06-07 08:34:43', '112.134.209.232', '2021-06-07 08:34:43'),
(1029, 12, '2021-06-07 09:20:31', '175.157.47.17', '2021-06-07 09:20:31'),
(1030, 1, '2021-06-07 10:17:30', '112.134.209.232', '2021-06-07 10:17:30'),
(1031, 12, '2021-06-07 10:22:03', '175.157.47.17', '2021-06-07 10:22:03'),
(1032, 12, '2021-06-07 10:51:28', '175.157.12.2', '2021-06-07 10:51:28'),
(1033, 1, '2021-06-07 10:53:45', '112.134.209.232', '2021-06-07 10:53:45'),
(1034, 1, '2021-06-07 12:06:15', '112.134.209.232', '2021-06-07 12:06:15'),
(1035, 12, '2021-06-07 12:15:41', '175.157.47.17', '2021-06-07 12:15:41'),
(1036, 12, '2021-06-07 13:44:19', '175.157.47.17', '2021-06-07 13:44:19'),
(1037, 1, '2021-06-07 15:08:37', '112.134.209.232', '2021-06-07 15:08:37'),
(1038, 12, '2021-06-07 15:12:09', '175.157.47.17', '2021-06-07 15:12:09'),
(1039, 12, '2021-06-07 15:51:40', '175.157.46.114', '2021-06-07 15:51:40'),
(1040, 12, '2021-06-07 17:28:45', '175.157.46.114', '2021-06-07 17:28:45'),
(1041, 15, '2021-06-07 17:48:42', '113.59.214.13', '2021-06-07 17:48:42'),
(1042, 12, '2021-06-07 17:58:16', '175.157.46.114', '2021-06-07 17:58:16'),
(1043, 12, '2021-06-07 17:59:39', '113.59.214.13', '2021-06-07 17:59:39'),
(1044, 15, '2021-06-07 18:03:32', '113.59.214.13', '2021-06-07 18:03:32'),
(1045, 12, '2021-06-07 18:07:21', '175.157.46.114', '2021-06-07 18:07:21'),
(1046, 1, '2021-06-07 18:11:53', '112.134.214.170', '2021-06-07 18:11:53'),
(1047, 12, '2021-06-07 19:23:09', '175.157.46.114', '2021-06-07 19:23:09'),
(1048, 12, '2021-06-07 23:36:21', '175.157.163.80', '2021-06-07 23:36:21'),
(1049, 12, '2021-06-08 05:11:13', '175.157.46.114', '2021-06-08 05:11:13'),
(1050, 1, '2021-06-08 06:46:43', '112.134.211.253', '2021-06-08 06:46:43'),
(1051, 12, '2021-06-08 06:49:11', '175.157.171.105', '2021-06-08 06:49:11'),
(1052, 13, '2021-06-08 07:11:15', '103.247.48.111', '2021-06-08 07:11:15'),
(1053, 12, '2021-06-08 08:46:17', '175.157.171.105', '2021-06-08 08:46:17'),
(1054, 1, '2021-06-08 09:27:20', '112.134.211.253', '2021-06-08 09:27:20'),
(1055, 1, '2021-06-08 10:04:07', '112.134.211.253', '2021-06-08 10:04:07'),
(1056, 12, '2021-06-08 11:39:38', '175.157.14.68', '2021-06-08 11:39:38'),
(1057, 13, '2021-06-08 11:57:24', '113.59.209.71', '2021-06-08 11:57:24'),
(1058, 12, '2021-06-08 12:30:44', '175.157.14.68', '2021-06-08 12:30:44'),
(1059, 12, '2021-06-08 15:58:32', '123.231.120.250', '2021-06-08 15:58:32'),
(1060, 13, '2021-06-08 16:47:44', '203.189.185.168', '2021-06-08 16:47:44'),
(1061, 12, '2021-06-08 16:58:34', '123.231.120.250', '2021-06-08 16:58:34'),
(1062, 13, '2021-06-08 17:27:25', '113.59.194.219', '2021-06-08 17:27:25'),
(1063, 1, '2021-06-08 17:47:08', '112.134.213.235', '2021-06-08 17:47:08'),
(1064, 12, '2021-06-08 18:00:01', '123.231.120.250', '2021-06-08 18:00:01'),
(1065, 1, '2021-06-08 20:50:29', '112.134.213.235', '2021-06-08 20:50:29'),
(1066, 12, '2021-06-09 03:45:37', '123.231.120.250', '2021-06-09 03:45:37'),
(1067, 12, '2021-06-09 05:30:43', '123.231.120.250', '2021-06-09 05:30:43'),
(1068, 12, '2021-06-09 06:43:35', '123.231.120.250', '2021-06-09 06:43:35'),
(1069, 1, '2021-06-09 07:17:25', '112.134.215.40', '2021-06-09 07:17:25'),
(1070, 12, '2021-06-09 07:29:06', '123.231.120.250', '2021-06-09 07:29:06'),
(1071, 12, '2021-06-09 07:42:33', '123.231.120.250', '2021-06-09 07:42:33'),
(1072, 1, '2021-06-09 08:02:42', '112.134.215.40', '2021-06-09 08:02:42'),
(1073, 12, '2021-06-09 08:26:32', '175.157.14.68', '2021-06-09 08:26:32'),
(1074, 13, '2021-06-09 09:08:32', '113.59.217.105', '2021-06-09 09:08:32'),
(1075, 13, '2021-06-09 09:18:27', '113.59.217.105', '2021-06-09 09:18:27'),
(1076, 1, '2021-06-09 09:39:04', '175.157.165.29', '2021-06-09 09:39:04'),
(1077, 12, '2021-06-09 11:16:21', '175.157.24.54', '2021-06-09 11:16:21'),
(1078, 12, '2021-06-09 13:36:18', '175.157.24.54', '2021-06-09 13:36:18'),
(1079, 1, '2021-06-09 15:07:32', '112.134.210.177', '2021-06-09 15:07:32'),
(1080, 15, '2021-06-09 15:39:16', '113.59.217.105', '2021-06-09 15:39:16'),
(1081, 12, '2021-06-09 15:46:08', '113.59.217.105', '2021-06-09 15:46:08'),
(1082, 12, '2021-06-09 16:25:58', '116.206.245.195', '2021-06-09 16:25:58'),
(1083, 13, '2021-06-09 16:52:00', '113.59.217.105', '2021-06-09 16:52:00'),
(1084, 15, '2021-06-09 16:58:57', '113.59.217.105', '2021-06-09 16:58:57'),
(1085, 13, '2021-06-09 17:09:34', '113.59.217.105', '2021-06-09 17:09:34'),
(1086, 15, '2021-06-09 17:11:26', '113.59.217.105', '2021-06-09 17:11:26'),
(1087, 15, '2021-06-09 17:16:04', '113.59.217.105', '2021-06-09 17:16:04'),
(1088, 12, '2021-06-09 17:26:42', '116.206.245.195', '2021-06-09 17:26:42'),
(1089, 12, '2021-06-09 20:00:55', '175.157.11.250', '2021-06-09 20:00:55'),
(1090, 12, '2021-06-10 03:39:34', '123.231.127.126', '2021-06-10 03:39:34'),
(1091, 12, '2021-06-10 06:22:36', '175.157.11.250', '2021-06-10 06:22:36'),
(1092, 12, '2021-06-10 06:44:34', '123.231.127.126', '2021-06-10 06:44:34'),
(1093, 1, '2021-06-10 07:48:10', '112.134.212.76', '2021-06-10 07:48:10'),
(1094, 12, '2021-06-10 09:27:24', '123.231.121.95', '2021-06-10 09:27:24'),
(1095, 1, '2021-06-10 09:52:25', '112.134.212.76', '2021-06-10 09:52:25'),
(1096, 13, '2021-06-10 11:14:45', '113.59.217.105', '2021-06-10 11:14:45'),
(1097, 1, '2021-06-10 11:30:52', '112.134.212.76', '2021-06-10 11:30:52'),
(1098, 12, '2021-06-10 12:24:50', '175.157.34.57', '2021-06-10 12:24:50'),
(1099, 13, '2021-06-10 12:28:34', '113.59.217.105', '2021-06-10 12:28:34'),
(1100, 12, '2021-06-10 13:59:38', '123.231.121.95', '2021-06-10 13:59:38'),
(1101, 12, '2021-06-10 14:57:11', '123.231.84.209', '2021-06-10 14:57:11'),
(1102, 14, '2021-06-10 15:09:57', '212.104.237.26', '2021-06-10 15:09:57'),
(1103, 12, '2021-06-10 15:30:35', '123.231.84.209', '2021-06-10 15:30:35'),
(1104, 1, '2021-06-10 15:48:54', '112.134.210.139', '2021-06-10 15:48:54'),
(1105, 1, '2021-06-10 16:43:05', '112.134.210.139', '2021-06-10 16:43:05'),
(1106, 12, '2021-06-10 18:22:37', '123.231.84.209', '2021-06-10 18:22:37'),
(1107, 12, '2021-06-10 19:50:43', '113.59.209.103', '2021-06-10 19:50:43'),
(1108, 12, '2021-06-10 20:08:07', '175.157.34.57', '2021-06-10 20:08:07'),
(1109, 12, '2021-06-10 20:59:24', '175.157.34.57', '2021-06-10 20:59:24'),
(1110, 12, '2021-06-11 03:49:12', '123.231.84.209', '2021-06-11 03:49:12'),
(1111, 12, '2021-06-11 06:06:45', '123.231.84.209', '2021-06-11 06:06:45'),
(1112, 12, '2021-06-11 06:59:12', '175.157.22.203', '2021-06-11 06:59:12'),
(1113, 12, '2021-06-11 06:59:32', '175.157.22.203', '2021-06-11 06:59:32'),
(1114, 12, '2021-06-11 07:32:03', '123.231.84.209', '2021-06-11 07:32:03'),
(1115, 12, '2021-06-11 09:03:42', '175.157.22.203', '2021-06-11 09:03:42'),
(1116, 1, '2021-06-11 10:05:12', '112.134.208.140', '2021-06-11 10:05:12'),
(1117, 12, '2021-06-11 10:43:02', '175.157.22.203', '2021-06-11 10:43:02'),
(1118, 12, '2021-06-11 11:29:55', '175.157.22.203', '2021-06-11 11:29:55'),
(1119, 12, '2021-06-11 11:49:09', '175.157.22.203', '2021-06-11 11:49:09'),
(1120, 12, '2021-06-11 13:29:37', '175.157.22.203', '2021-06-11 13:29:37'),
(1121, 1, '2021-06-11 14:00:57', '112.134.215.244', '2021-06-11 14:00:57'),
(1122, 12, '2021-06-11 14:17:32', '113.59.210.19', '2021-06-11 14:17:32'),
(1123, 12, '2021-06-11 14:34:01', '175.157.22.203', '2021-06-11 14:34:01'),
(1124, 12, '2021-06-11 15:05:07', '175.157.22.203', '2021-06-11 15:05:07'),
(1125, 1, '2021-06-11 15:26:11', '112.134.215.244', '2021-06-11 15:26:11'),
(1126, 12, '2021-06-11 15:30:48', '175.157.22.203', '2021-06-11 15:30:48'),
(1127, 1, '2021-06-11 15:47:34', '112.134.215.244', '2021-06-11 15:47:34'),
(1128, 12, '2021-06-11 15:59:53', '123.231.87.213', '2021-06-11 15:59:53'),
(1129, 1, '2021-06-11 16:03:37', '112.134.215.244', '2021-06-11 16:03:37'),
(1130, 15, '2021-06-11 16:38:36', '113.59.213.66', '2021-06-11 16:38:36'),
(1131, 12, '2021-06-11 17:00:15', '123.231.87.213', '2021-06-11 17:00:15'),
(1132, 1, '2021-06-11 17:57:24', '112.134.215.244', '2021-06-11 17:57:24'),
(1133, 12, '2021-06-11 20:21:56', '123.231.87.213', '2021-06-11 20:21:56'),
(1134, 12, '2021-06-11 21:17:04', '175.157.22.203', '2021-06-11 21:17:04'),
(1135, 12, '2021-06-11 23:56:37', '175.157.22.203', '2021-06-11 23:56:37'),
(1136, 12, '2021-06-12 00:45:58', '175.157.22.203', '2021-06-12 00:45:58'),
(1137, 12, '2021-06-12 02:53:18', '123.231.87.213', '2021-06-12 02:53:18'),
(1138, 12, '2021-06-12 04:19:21', '175.157.22.203', '2021-06-12 04:19:21'),
(1139, 12, '2021-06-12 05:03:43', '123.231.87.213', '2021-06-12 05:03:43'),
(1140, 12, '2021-06-12 08:02:55', '123.231.87.213', '2021-06-12 08:02:55'),
(1141, 12, '2021-06-12 10:55:30', '123.231.87.213', '2021-06-12 10:55:30'),
(1142, 12, '2021-06-12 12:19:21', '123.231.87.213', '2021-06-12 12:19:21'),
(1143, 1, '2021-06-12 12:56:16', '112.134.246.247', '2021-06-12 12:56:16'),
(1144, 1, '2021-06-12 13:07:00', '112.134.246.247', '2021-06-12 13:07:00'),
(1145, 12, '2021-06-12 14:02:43', '123.231.87.213', '2021-06-12 14:02:43'),
(1146, 12, '2021-06-12 14:57:40', '175.157.38.15', '2021-06-12 14:57:40'),
(1147, 1, '2021-06-12 15:02:12', '112.134.246.247', '2021-06-12 15:02:12'),
(1148, 13, '2021-06-12 15:17:55', '45.121.88.109', '2021-06-12 15:17:55'),
(1149, 12, '2021-06-12 16:05:08', '175.157.38.15', '2021-06-12 16:05:08'),
(1150, 13, '2021-06-12 16:24:26', '113.59.213.81', '2021-06-12 16:24:26'),
(1151, 13, '2021-06-12 16:24:55', '113.59.213.81', '2021-06-12 16:24:55'),
(1152, 12, '2021-06-12 17:20:59', '113.59.213.81', '2021-06-12 17:20:59'),
(1153, 13, '2021-06-12 17:23:33', '113.59.213.81', '2021-06-12 17:23:33'),
(1154, 12, '2021-06-12 17:43:03', '113.59.213.81', '2021-06-12 17:43:03'),
(1155, 16, '2021-06-12 17:45:45', '113.59.213.81', '2021-06-12 17:45:45'),
(1156, 1, '2021-06-12 17:47:10', '112.134.246.247', '2021-06-12 17:47:10'),
(1157, 12, '2021-06-12 18:17:35', '175.157.47.126', '2021-06-12 18:17:35'),
(1158, 1, '2021-06-12 18:46:51', '112.134.246.247', '2021-06-12 18:46:51'),
(1159, 12, '2021-06-12 19:41:00', '175.157.38.15', '2021-06-12 19:41:00'),
(1160, 12, '2021-06-12 19:42:29', '175.157.38.15', '2021-06-12 19:42:29'),
(1161, 12, '2021-06-12 21:31:44', '175.157.38.15', '2021-06-12 21:31:44'),
(1162, 12, '2021-06-13 04:10:08', '175.157.38.15', '2021-06-13 04:10:08'),
(1163, 12, '2021-06-13 04:16:10', '175.157.38.15', '2021-06-13 04:16:10'),
(1164, 12, '2021-06-13 04:40:13', '116.206.247.73', '2021-06-13 04:40:13'),
(1165, 12, '2021-06-13 06:31:36', '113.59.213.81', '2021-06-13 06:31:36'),
(1166, 1, '2021-06-13 08:06:08', '112.134.242.62', '2021-06-13 08:06:08'),
(1167, 12, '2021-06-13 11:34:22', '113.59.213.81', '2021-06-13 11:34:22'),
(1168, 12, '2021-06-13 11:41:01', '113.59.213.81', '2021-06-13 11:41:01'),
(1169, 12, '2021-06-13 12:05:31', '116.206.247.73', '2021-06-13 12:05:31'),
(1170, 1, '2021-06-13 12:54:49', '112.134.245.19', '2021-06-13 12:54:49'),
(1171, 1, '2021-06-23 13:01:10', '212.104.236.31', '2021-06-23 13:01:10'),
(1172, 1, '2021-06-24 11:57:59', '123.231.109.30', '2021-06-24 11:57:59'),
(1173, 1, '2021-06-24 20:33:52', '112.134.211.173', '2021-06-24 20:33:52'),
(1174, 1, '2021-06-25 08:34:48', '175.157.74.57', '2021-06-25 08:34:48'),
(1175, 1, '2021-06-25 08:57:57', '175.157.246.249', '2021-06-25 08:57:57'),
(1176, 1, '2021-06-25 09:09:03', '103.21.165.119', '2021-06-25 09:09:03'),
(1177, 1, '2021-06-25 09:19:28', '123.231.123.213', '2021-06-25 09:19:28'),
(1178, 1, '2021-06-25 09:57:20', '103.21.165.119', '2021-06-25 09:57:20'),
(1179, 1, '2021-06-25 10:14:37', '123.231.123.213', '2021-06-25 10:14:37'),
(1180, 1, '2021-06-25 10:21:13', '123.231.123.213', '2021-06-25 10:21:13'),
(1181, 1, '2021-06-25 10:41:15', '116.206.247.242', '2021-06-25 10:41:15'),
(1182, 1, '2021-06-25 14:01:30', '45.121.88.46', '2021-06-25 14:01:30'),
(1183, 1, '2021-06-25 14:01:32', '45.121.88.46', '2021-06-25 14:01:32'),
(1184, 1, '2021-06-25 14:06:59', '123.231.123.213', '2021-06-25 14:06:59'),
(1185, 1, '2021-06-25 14:07:15', '123.231.123.213', '2021-06-25 14:07:15'),
(1186, 1, '2021-06-26 18:34:37', '203.189.184.80', '2021-06-26 18:34:37'),
(1187, 1, '2021-06-28 05:30:54', '103.21.166.103', '2021-06-28 05:30:54'),
(1188, 1, '2021-06-28 07:13:19', '116.206.247.166', '2021-06-28 07:13:19'),
(1189, 1, '2021-06-28 07:36:16', '103.21.166.103', '2021-06-28 07:36:16'),
(1190, 1, '2021-06-28 13:09:28', '45.121.91.252', '2021-06-28 13:09:28'),
(1191, 1, '2021-06-28 13:26:55', '116.206.245.43', '2021-06-28 13:26:55'),
(1192, 1, '2021-06-28 13:45:04', '103.21.164.175', '2021-06-28 13:45:04'),
(1193, 1, '2021-06-29 18:57:26', '123.231.120.3', '2021-06-29 18:57:26'),
(1194, 1, '2021-06-29 19:41:14', '112.134.209.207', '2021-06-29 19:41:14'),
(1195, 1, '2021-06-29 20:25:07', '112.134.209.207', '2021-06-29 20:25:07'),
(1196, 1, '2021-06-30 06:01:39', '103.21.164.241', '2021-06-30 06:01:39'),
(1197, 1, '2021-06-30 07:23:43', '123.231.86.205', '2021-06-30 07:23:43'),
(1198, 1, '2021-06-30 08:06:07', '103.247.48.247', '2021-06-30 08:06:07'),
(1199, 1, '2021-06-30 08:33:09', '103.21.164.241', '2021-06-30 08:33:09'),
(1200, 1, '2021-06-30 09:08:21', '212.104.236.31', '2021-06-30 09:08:21'),
(1201, 1, '2021-06-30 11:29:23', '123.231.105.19', '2021-06-30 11:29:23'),
(1202, 1, '2021-06-30 13:17:43', '123.231.123.123', '2021-06-30 13:17:43'),
(1203, 1, '2021-06-30 13:18:28', '123.231.105.19', '2021-06-30 13:18:28'),
(1204, 1, '2021-06-30 20:40:59', '123.231.84.29', '2021-06-30 20:40:59'),
(1205, 1, '2021-07-01 10:45:02', '175.157.43.13', '2021-07-01 10:45:02'),
(1206, 1, '2021-07-01 12:23:45', '175.157.43.13', '2021-07-01 12:23:45'),
(1207, 1, '2021-07-01 12:23:45', '116.206.244.77', '2021-07-01 12:23:45'),
(1208, 1, '2021-07-01 12:44:38', '175.157.43.13', '2021-07-01 12:44:38'),
(1209, 1, '2021-07-02 08:09:15', '45.121.88.77', '2021-07-02 08:09:15'),
(1210, 1, '2021-07-02 08:49:26', '175.157.43.104', '2021-07-02 08:49:26'),
(1211, 1, '2021-07-02 08:55:46', '175.157.43.104', '2021-07-02 08:55:46'),
(1212, 1, '2021-07-02 09:03:46', '175.157.43.104', '2021-07-02 09:03:46'),
(1213, 1, '2021-07-05 04:27:28', '43.252.15.132', '2021-07-05 04:27:28'),
(1214, 1, '2021-07-05 04:47:19', '43.252.15.132', '2021-07-05 04:47:19'),
(1215, 1, '2021-07-05 07:19:21', '43.252.15.132', '2021-07-05 07:19:21'),
(1216, 1, '2021-07-05 11:02:25', '175.157.111.39', '2021-07-05 11:02:25'),
(1217, 1, '2021-07-05 11:02:37', '123.231.124.139', '2021-07-05 11:02:37'),
(1218, 1, '2021-07-05 11:25:39', '175.157.111.39', '2021-07-05 11:25:39'),
(1219, 1, '2021-07-06 12:41:45', '116.206.245.32', '2021-07-06 12:41:45'),
(1220, 20, '2021-07-06 12:44:26', '116.206.245.32', '2021-07-06 12:44:26'),
(1221, 20, '2021-07-06 12:49:14', '175.157.42.125', '2021-07-06 12:49:14'),
(1222, 1, '2021-07-06 20:34:18', '223.224.3.106', '2021-07-06 20:34:18'),
(1223, 1, '2021-07-07 05:48:02', '116.206.244.227', '2021-07-07 05:48:02'),
(1224, 1, '2021-07-07 06:39:05', '116.206.244.227', '2021-07-07 06:39:05'),
(1225, 1, '2021-07-07 07:20:03', '116.206.247.59', '2021-07-07 07:20:03'),
(1226, 1, '2021-07-07 07:38:44', '116.206.244.227', '2021-07-07 07:38:44'),
(1227, 1, '2021-07-07 07:49:56', '116.206.244.227', '2021-07-07 07:49:56'),
(1228, 1, '2021-07-07 07:51:39', '116.206.244.227', '2021-07-07 07:51:39'),
(1229, 1, '2021-07-07 08:20:34', '116.206.244.227', '2021-07-07 08:20:34'),
(1230, 1, '2021-07-07 08:22:24', '116.206.244.227', '2021-07-07 08:22:24'),
(1231, 1, '2021-07-07 09:02:07', '116.206.244.227', '2021-07-07 09:02:07'),
(1232, 1, '2021-07-08 11:30:52', '116.206.244.43', '2021-07-08 11:30:52'),
(1233, 1, '2021-07-08 11:49:33', '116.206.244.227', '2021-07-08 11:49:33'),
(1234, 1, '2021-07-08 12:22:59', '116.206.244.227', '2021-07-08 12:22:59'),
(1235, 1, '2021-07-08 12:45:44', '103.247.48.29', '2021-07-08 12:45:44'),
(1236, 1, '2021-07-08 12:57:18', '103.247.48.29', '2021-07-08 12:57:18'),
(1237, 1, '2021-07-08 13:22:22', '116.206.244.227', '2021-07-08 13:22:22'),
(1238, 1, '2021-07-09 05:48:52', '116.206.244.227', '2021-07-09 05:48:52'),
(1239, 1, '2021-07-09 05:51:21', '116.206.244.227', '2021-07-09 05:51:21'),
(1240, 1, '2021-07-09 05:55:30', '116.206.244.227', '2021-07-09 05:55:30'),
(1241, 1, '2021-07-09 06:00:38', '116.206.244.227', '2021-07-09 06:00:38'),
(1242, 1, '2021-07-09 12:05:27', '116.206.245.175', '2021-07-09 12:05:27'),
(1243, 1, '2021-07-09 12:13:31', '112.135.7.88', '2021-07-09 12:13:31'),
(1244, 1, '2021-07-09 12:16:29', '112.135.7.88', '2021-07-09 12:16:29'),
(1245, 1, '2021-07-09 12:18:26', '112.135.7.88', '2021-07-09 12:18:26'),
(1246, 1, '2021-07-09 12:22:26', '112.135.7.88', '2021-07-09 12:22:26'),
(1247, 1, '2021-07-09 12:27:16', '112.135.7.88', '2021-07-09 12:27:16'),
(1248, 1, '2021-07-09 12:27:26', '112.135.7.88', '2021-07-09 12:27:26'),
(1249, 1, '2021-07-09 12:27:27', '112.135.7.88', '2021-07-09 12:27:27'),
(1250, 1, '2021-07-09 12:29:01', '112.135.7.88', '2021-07-09 12:29:01'),
(1251, 1, '2021-07-09 12:30:52', '116.206.244.227', '2021-07-09 12:30:52'),
(1252, 1, '2021-07-09 13:59:06', '116.206.244.227', '2021-07-09 13:59:06'),
(1253, 1, '2021-07-09 14:08:23', '112.135.7.88', '2021-07-09 14:08:23'),
(1254, 1, '2021-07-09 14:08:39', '112.135.7.88', '2021-07-09 14:08:39'),
(1255, 1, '2021-07-09 19:23:02', '112.134.215.57', '2021-07-09 19:23:02'),
(1256, 1, '2021-07-12 11:43:14', '116.206.244.238', '2021-07-12 11:43:14'),
(1257, 1, '2021-07-13 11:56:06', '103.21.166.204', '2021-07-13 11:56:06'),
(1258, 1, '2021-07-13 19:02:58', '175.157.165.29', '2021-07-13 19:02:58'),
(1259, 1, '2021-07-14 08:39:52', '103.21.166.185', '2021-07-14 08:39:52'),
(1260, 1, '2021-07-14 09:06:08', '212.104.236.158', '2021-07-14 09:06:08'),
(1261, 1, '2021-07-14 09:42:19', '103.21.166.185', '2021-07-14 09:42:19'),
(1262, 1, '2021-07-14 10:24:58', '103.21.166.185', '2021-07-14 10:24:58'),
(1263, 1, '2021-07-14 11:14:34', '212.104.236.158', '2021-07-14 11:14:34'),
(1264, 1, '2021-07-14 11:43:46', '103.21.166.154', '2021-07-14 11:43:46'),
(1265, 1, '2021-07-14 12:34:32', '212.104.236.158', '2021-07-14 12:34:32'),
(1266, 1, '2021-07-15 04:43:50', '212.104.237.158', '2021-07-15 04:43:50'),
(1267, 1, '2021-07-15 08:20:40', '212.104.237.158', '2021-07-15 08:20:40'),
(1268, 1, '2021-07-20 10:12:42', '116.206.247.193', '2021-07-20 10:12:42'),
(1269, 1, '2021-07-20 12:45:20', '45.121.90.132', '2021-07-20 12:45:20'),
(1270, 1, '2021-07-22 08:02:49', '103.21.166.141', '2021-07-22 08:02:49'),
(1271, 1, '2021-07-22 09:32:50', '116.206.245.129', '2021-07-22 09:32:50'),
(1272, 1, '2021-07-22 09:40:18', '103.21.166.141', '2021-07-22 09:40:18'),
(1273, 1, '2021-07-22 10:42:49', '103.21.166.141', '2021-07-22 10:42:49'),
(1274, 1, '2021-07-22 12:14:18', '103.21.166.235', '2021-07-22 12:14:18'),
(1275, 1, '2021-07-22 13:14:52', '103.21.166.235', '2021-07-22 13:14:52'),
(1276, 1, '2021-07-29 09:05:47', '123.231.84.203', '2021-07-29 09:05:47'),
(1277, 1, '2021-07-29 09:07:19', '103.21.166.143', '2021-07-29 09:07:19'),
(1278, 1, '2021-07-29 09:15:21', '103.21.166.143', '2021-07-29 09:15:21'),
(1279, 1, '2021-07-29 10:52:13', '103.21.166.143', '2021-07-29 10:52:13'),
(1280, 1, '2021-07-29 10:52:14', '103.21.166.143', '2021-07-29 10:52:14'),
(1281, 1, '2021-07-29 10:55:00', '103.21.166.143', '2021-07-29 10:55:00'),
(1282, 13, '2021-07-29 10:55:52', '103.21.166.143', '2021-07-29 10:55:52'),
(1283, 1, '2021-07-30 10:21:44', '45.121.90.109', '2021-07-30 10:21:44'),
(1284, 1, '2021-07-30 10:23:17', '175.157.43.213', '2021-07-30 10:23:17'),
(1285, 1, '2021-07-30 11:28:17', '116.206.246.155', '2021-07-30 11:28:17'),
(1286, 1, '2021-08-02 06:00:45', '45.121.90.165', '2021-08-02 06:00:45'),
(1287, 1, '2021-08-02 06:08:30', '45.121.90.165', '2021-08-02 06:08:30'),
(1288, 20, '2021-08-02 06:08:54', '45.121.90.165', '2021-08-02 06:08:54'),
(1289, 1, '2021-08-02 06:11:31', '45.121.90.165', '2021-08-02 06:11:31'),
(1290, 1, '2021-08-03 12:28:40', '112.134.212.97', '2021-08-03 12:28:40'),
(1291, 1, '2021-08-04 10:46:23', '45.121.90.105', '2021-08-04 10:46:23'),
(1292, 1, '2021-08-04 10:46:45', '45.121.90.105', '2021-08-04 10:46:45'),
(1293, 13, '2021-08-04 10:47:10', '45.121.90.105', '2021-08-04 10:47:10'),
(1294, 13, '2021-08-04 10:49:16', '45.121.90.105', '2021-08-04 10:49:16'),
(1295, 1, '2021-08-15 10:49:22', '112.134.209.77', '2021-08-15 10:49:22'),
(1296, 1, '2021-08-15 15:36:59', '112.134.209.77', '2021-08-15 15:36:59'),
(1297, 1, '2021-08-15 18:14:22', '112.134.215.133', '2021-08-15 18:14:22'),
(1298, 1, '2021-08-15 18:14:22', '112.134.215.133', '2021-08-15 18:14:22'),
(1299, 1, '2021-08-15 19:22:25', '112.134.215.133', '2021-08-15 19:22:25'),
(1300, 1, '2021-08-16 10:56:09', '175.157.46.127', '2021-08-16 10:56:09'),
(1301, 1, '2021-08-26 13:16:49', '::1', '2021-08-26 13:16:49'),
(1302, 1, '2021-08-31 10:13:11', '::1', '2021-08-31 10:13:11'),
(1303, 1, '2021-09-01 10:40:18', '::1', '2021-09-01 10:40:18'),
(1304, 1, '2021-09-06 09:39:47', '::1', '2021-09-06 09:39:47'),
(1305, 1, '2021-09-06 10:20:34', '::1', '2021-09-06 10:20:34'),
(1306, 1, '2021-09-07 09:42:26', '::1', '2021-09-07 09:42:26'),
(1307, 1, '2021-09-13 13:50:57', '::1', '2021-09-13 13:50:57'),
(1308, 1, '2021-09-14 11:24:32', '::1', '2021-09-14 11:24:32'),
(1309, 1, '2021-09-18 12:00:23', '::1', '2021-09-18 12:00:23'),
(1310, 1, '2021-09-22 10:45:00', '::1', '2021-09-22 10:45:00'),
(1311, 1, '2021-09-22 16:17:06', '::1', '2021-09-22 16:17:06'),
(1312, 1, '2021-09-22 17:20:55', '::1', '2021-09-22 17:20:55'),
(1313, 1, '2021-09-23 10:08:20', '::1', '2021-09-23 10:08:20'),
(1314, 1, '2021-09-23 11:10:02', '::1', '2021-09-23 11:10:02'),
(1315, 1, '2021-09-23 13:08:04', '::1', '2021-09-23 13:08:04'),
(1316, 1, '2021-09-23 15:41:51', '::1', '2021-09-23 15:41:51'),
(1317, 1, '2021-09-24 10:31:50', '127.0.0.1', '2021-09-24 10:31:50'),
(1318, 1, '2021-09-24 12:00:23', '::1', '2021-09-24 12:00:23'),
(1319, 1, '2021-09-24 13:33:25', '::1', '2021-09-24 13:33:25'),
(1320, 1, '2021-09-24 15:20:26', '::1', '2021-09-24 15:20:26'),
(1321, 1, '2021-09-27 10:29:20', '::1', '2021-09-27 10:29:20'),
(1322, 1, '2021-09-27 12:03:27', '::1', '2021-09-27 12:03:27'),
(1323, 1, '2021-09-28 16:54:16', '::1', '2021-09-28 16:54:16'),
(1324, 1, '2021-09-28 17:41:01', '::1', '2021-09-28 17:41:01'),
(1325, 1, '2021-09-29 11:59:34', '::1', '2021-09-29 11:59:34'),
(1326, 1, '2021-09-29 12:47:45', '::1', '2021-09-29 12:47:45'),
(1327, 1, '2021-09-29 14:51:01', '::1', '2021-09-29 14:51:01'),
(1328, 1, '2021-09-29 15:39:09', '::1', '2021-09-29 15:39:09'),
(1329, 1, '2021-09-29 16:24:20', '::1', '2021-09-29 16:24:20'),
(1330, 1, '2021-09-30 11:36:41', '::1', '2021-09-30 11:36:41'),
(1331, 1, '2021-09-30 13:50:32', '::1', '2021-09-30 13:50:32'),
(1332, 1, '2021-09-30 15:36:26', '::1', '2021-09-30 15:36:26'),
(1333, 1, '2021-10-01 10:26:37', '::1', '2021-10-01 10:26:37'),
(1334, 1, '2021-10-01 11:22:13', '::1', '2021-10-01 11:22:13'),
(1335, 1, '2021-10-01 18:02:33', '::1', '2021-10-01 18:02:33'),
(1336, 1, '2021-10-02 00:14:32', '::1', '2021-10-02 00:14:32'),
(1337, 1, '2021-10-04 09:35:14', '::1', '2021-10-04 09:35:14'),
(1338, 1, '2021-10-04 11:45:34', '::1', '2021-10-04 11:45:34'),
(1339, 1, '2021-10-04 13:08:25', '127.0.0.1', '2021-10-04 13:08:25'),
(1340, 1, '2021-10-04 14:16:55', '::1', '2021-10-04 14:16:55'),
(1341, 1, '2021-10-04 18:32:43', '::1', '2021-10-04 18:32:43'),
(1342, 1, '2021-10-05 13:14:07', '::1', '2021-10-05 13:14:07'),
(1343, 1, '2021-10-05 15:07:16', '::1', '2021-10-05 15:07:16'),
(1344, 1, '2021-10-06 12:17:07', '::1', '2021-10-06 12:17:07'),
(1345, 1, '2021-10-06 12:41:08', '::1', '2021-10-06 12:41:08'),
(1346, 1, '2021-10-06 13:03:39', '::1', '2021-10-06 13:03:39'),
(1347, 1, '2021-10-07 15:40:09', '::1', '2021-10-07 15:40:09'),
(1348, 1, '2021-10-08 09:39:16', '::1', '2021-10-08 09:39:16'),
(1349, 1, '2021-10-08 12:32:03', '::1', '2021-10-08 12:32:03'),
(1350, 1, '2021-10-08 13:29:28', '::1', '2021-10-08 13:29:28'),
(1351, 1, '2021-10-08 17:16:36', '::1', '2021-10-08 17:16:36'),
(1352, 1, '2021-10-11 09:23:29', '::1', '2021-10-11 09:23:29'),
(1353, 1, '2021-10-11 12:46:56', '::1', '2021-10-11 12:46:56'),
(1354, 1, '2021-10-12 09:52:19', '::1', '2021-10-12 09:52:19'),
(1355, 1, '2021-10-12 16:03:47', '::1', '2021-10-12 16:03:47'),
(1356, 1, '2021-10-12 17:34:44', '127.0.0.1', '2021-10-12 17:34:44'),
(1357, 1, '2021-10-15 14:44:18', '::1', '2021-10-15 14:44:18'),
(1358, 1, '2021-10-15 15:25:59', '::1', '2021-10-15 15:25:59'),
(1359, 1, '2021-10-18 09:35:23', '::1', '2021-10-18 09:35:23'),
(1360, 1, '2021-10-19 21:09:43', '::1', '2021-10-19 21:09:43'),
(1361, 1, '2021-10-21 09:21:40', '::1', '2021-10-21 09:21:40'),
(1362, 1, '2021-10-21 10:19:15', '::1', '2021-10-21 10:19:15'),
(1363, 1, '2021-10-21 10:49:46', '::1', '2021-10-21 10:49:46'),
(1364, 13, '2021-10-21 11:59:07', '::1', '2021-10-21 11:59:07'),
(1365, 1, '2021-10-21 13:54:09', '::1', '2021-10-21 13:54:09'),
(1366, 1, '2021-10-21 14:03:41', '::1', '2021-10-21 14:03:41'),
(1367, 1, '2021-10-21 15:34:06', '::1', '2021-10-21 15:34:06'),
(1368, 1, '2021-10-21 16:14:05', '::1', '2021-10-21 16:14:05'),
(1369, 1, '2021-10-21 18:15:26', '::1', '2021-10-21 18:15:26'),
(1370, 1, '2021-10-22 09:39:11', '::1', '2021-10-22 09:39:11'),
(1371, 1, '2021-10-22 10:24:02', '::1', '2021-10-22 10:24:02'),
(1372, 1, '2021-10-22 13:11:09', '::1', '2021-10-22 13:11:09'),
(1373, 1, '2021-10-22 14:46:04', '::1', '2021-10-22 14:46:04'),
(1374, 1, '2021-10-22 15:57:44', '::1', '2021-10-22 15:57:44'),
(1375, 1, '2021-10-22 17:06:33', '::1', '2021-10-22 17:06:33'),
(1376, 1, '2021-10-25 09:49:27', '::1', '2021-10-25 09:49:27'),
(1377, 1, '2021-10-25 15:35:28', '::1', '2021-10-25 15:35:28'),
(1378, 1, '2021-10-25 16:17:59', '::1', '2021-10-25 16:17:59'),
(1379, 1, '2021-10-26 09:42:46', '::1', '2021-10-26 09:42:46'),
(1380, 1, '2021-10-26 11:32:26', '::1', '2021-10-26 11:32:26'),
(1381, 1, '2021-10-27 09:31:38', '::1', '2021-10-27 09:31:38'),
(1382, 1, '2021-10-28 10:29:43', '::1', '2021-10-28 10:29:43'),
(1383, 1, '2021-10-28 10:51:45', '::1', '2021-10-28 10:51:45'),
(1384, 1, '2021-10-28 12:43:47', '::1', '2021-10-28 12:43:47'),
(1385, 1, '2021-10-28 14:32:14', '::1', '2021-10-28 14:32:14'),
(1386, 1, '2021-10-28 15:39:00', '::1', '2021-10-28 15:39:00'),
(1387, 1, '2021-10-28 16:33:28', '127.0.0.1', '2021-10-28 16:33:28'),
(1388, 13, '2021-10-28 16:39:43', '::1', '2021-10-28 16:39:43'),
(1389, 1, '2021-10-28 17:12:09', '::1', '2021-10-28 17:12:09'),
(1390, 1, '2021-10-29 09:44:33', '::1', '2021-10-29 09:44:33'),
(1391, 1, '2021-10-29 10:58:31', '::1', '2021-10-29 10:58:31'),
(1392, 1, '2021-10-29 13:13:05', '::1', '2021-10-29 13:13:05');
INSERT INTO `ggportal_tbl_user_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(1393, 1, '2021-10-29 16:13:08', '::1', '2021-10-29 16:13:08'),
(1394, 1, '2021-11-01 09:21:15', '127.0.0.1', '2021-11-01 09:21:15'),
(1395, 1, '2021-11-02 15:20:52', '::1', '2021-11-02 15:20:52'),
(1396, 1, '2021-11-02 17:38:15', '::1', '2021-11-02 17:38:15'),
(1397, 1, '2021-11-03 10:33:20', '::1', '2021-11-03 10:33:20'),
(1398, 1, '2021-11-04 23:05:46', '::1', '2021-11-04 23:05:46'),
(1399, 1, '2021-11-04 23:12:04', '::1', '2021-11-04 23:12:04'),
(1400, 1, '2021-11-05 08:52:53', '::1', '2021-11-05 08:52:53'),
(1401, 1, '2021-11-05 08:55:56', '::1', '2021-11-05 08:55:56'),
(1402, 1, '2021-11-06 20:43:15', '::1', '2021-11-06 20:43:15'),
(1403, 1, '2021-11-08 09:46:43', '::1', '2021-11-08 09:46:43'),
(1404, 1, '2021-11-08 18:30:18', '::1', '2021-11-08 18:30:18'),
(1405, 1, '2021-11-09 09:19:19', '::1', '2021-11-09 09:19:19'),
(1406, 1, '2021-11-10 11:01:09', '::1', '2021-11-10 11:01:09'),
(1407, 1, '2021-11-12 10:42:50', '::1', '2021-11-12 10:42:50'),
(1408, 1, '2021-11-15 17:52:16', '::1', '2021-11-15 17:52:16'),
(1409, 1, '2021-11-16 06:51:12', '::1', '2021-11-16 06:51:12'),
(1410, 1, '2021-11-17 09:16:49', '::1', '2021-11-17 09:16:49'),
(1411, 1, '2021-11-18 09:55:57', '::1', '2021-11-18 09:55:57'),
(1412, 1, '2021-11-22 10:31:31', '::1', '2021-11-22 10:31:31'),
(1413, 1, '2021-11-22 18:28:44', '::1', '2021-11-22 18:28:44'),
(1414, 1, '2021-11-23 10:29:35', '::1', '2021-11-23 10:29:35'),
(1415, 21, '2021-11-23 17:37:04', '::1', '2021-11-23 17:37:04'),
(1416, 1, '2021-11-24 11:35:50', '::1', '2021-11-24 11:35:50'),
(1417, 1, '2021-11-24 15:29:03', '::1', '2021-11-24 15:29:03'),
(1418, 1, '2021-11-24 23:29:23', '::1', '2021-11-24 23:29:23'),
(1419, 1, '2021-11-25 14:07:21', '::1', '2021-11-25 14:07:21'),
(1420, 1, '2021-11-30 09:30:51', '::1', '2021-11-30 09:30:51'),
(1421, 1, '2021-12-03 10:34:15', '::1', '2021-12-03 10:34:15'),
(1422, 1, '2021-12-13 16:02:23', '::1', '2021-12-13 16:02:23'),
(1423, 21, '2021-12-13 16:57:28', '::1', '2021-12-13 16:57:28'),
(1424, 1, '2021-12-20 13:23:17', '::1', '2021-12-20 13:23:17'),
(1425, 1, '2021-12-21 11:33:54', '::1', '2021-12-21 11:33:54'),
(1426, 1, '2021-12-28 10:19:43', '::1', '2021-12-28 10:19:43'),
(1427, 1, '2021-12-29 09:43:35', '::1', '2021-12-29 09:43:35'),
(1428, 1, '2021-12-29 17:44:16', '::1', '2021-12-29 17:44:16'),
(1429, 1, '2022-01-06 10:48:15', '::1', '2022-01-06 10:48:15'),
(1430, 1, '2022-01-06 14:38:20', '::1', '2022-01-06 14:38:20'),
(1431, 1, '2022-01-07 11:10:25', '::1', '2022-01-07 11:10:25'),
(1432, 1, '2022-01-07 14:25:24', '::1', '2022-01-07 14:25:24'),
(1433, 1, '2022-01-07 16:02:20', '::1', '2022-01-07 16:02:20'),
(1434, 1, '2022-01-10 11:05:27', '::1', '2022-01-10 11:05:27'),
(1435, 1, '2022-01-11 15:41:15', '::1', '2022-01-11 15:41:15'),
(1436, 1, '2022-01-13 13:11:13', '::1', '2022-01-13 13:11:13'),
(1437, 1, '2022-01-18 09:37:19', '::1', '2022-01-18 09:37:19'),
(1438, 1, '2022-01-18 12:44:56', '::1', '2022-01-18 12:44:56'),
(1439, 1, '2022-01-18 12:58:45', '::1', '2022-01-18 12:58:45'),
(1440, 1, '2022-01-18 12:58:49', '::1', '2022-01-18 12:58:49'),
(1441, 1, '2022-01-19 11:12:36', '::1', '2022-01-19 11:12:36'),
(1442, 1, '2022-01-20 09:23:14', '::1', '2022-01-20 09:23:14'),
(1443, 1, '2022-01-24 11:03:58', '::1', '2022-01-24 11:03:58'),
(1444, 1, '2022-01-25 09:29:11', '::1', '2022-01-25 09:29:11'),
(1445, 1, '2022-02-03 17:03:33', '::1', '2022-02-03 17:03:33'),
(1446, 1, '2022-02-14 12:09:29', '::1', '2022-02-14 12:09:29'),
(1447, 1, '2022-02-14 12:14:20', '::1', '2022-02-14 12:14:20'),
(1448, 1, '2022-02-14 12:14:23', '::1', '2022-02-14 12:14:23'),
(1449, 1, '2022-02-14 12:14:26', '::1', '2022-02-14 12:14:26'),
(1450, 1, '2022-02-15 13:59:54', '::1', '2022-02-15 13:59:54'),
(1451, 1, '2022-02-15 16:47:26', '::1', '2022-02-15 16:47:26'),
(1452, 1, '2022-02-15 16:48:39', '::1', '2022-02-15 16:48:39'),
(1453, 1, '2022-02-15 16:49:20', '::1', '2022-02-15 16:49:20'),
(1454, 1, '2022-02-17 10:13:12', '::1', '2022-02-17 10:13:12'),
(1455, 1, '2022-02-17 10:48:03', '::1', '2022-02-17 10:48:03'),
(1456, 1, '2022-02-18 13:45:31', '::1', '2022-02-18 13:45:31'),
(1457, 1, '2022-03-10 09:19:18', '::1', '2022-03-10 09:19:18'),
(1458, 1, '2022-03-10 09:21:17', '::1', '2022-03-10 09:21:17'),
(1459, 1, '2022-03-10 17:26:34', '::1', '2022-03-10 17:26:34'),
(1460, 1, '2022-03-10 20:01:15', '::1', '2022-03-10 20:01:15'),
(1461, 1, '2022-03-15 12:30:25', '::1', '2022-03-15 12:30:25'),
(1462, 1, '2022-03-15 13:02:43', '::1', '2022-03-15 13:02:43'),
(1463, 1, '2022-03-21 12:00:37', '::1', '2022-03-21 12:00:37'),
(1464, 1, '2022-03-23 11:20:30', '::1', '2022-03-23 11:20:30'),
(1465, 1, '2022-03-23 12:54:12', '::1', '2022-03-23 12:54:12'),
(1466, 1, '2022-03-23 22:28:05', '::1', '2022-03-23 22:28:05'),
(1467, 21, '2022-03-23 22:34:25', '::1', '2022-03-23 22:34:25'),
(1468, 1, '2022-03-23 22:48:21', '::1', '2022-03-23 22:48:21'),
(1469, 1, '2022-04-06 10:31:00', '::1', '2022-04-06 10:31:00'),
(1470, 1, '2022-04-06 20:18:40', '::1', '2022-04-06 20:18:40'),
(1471, 1, '2022-04-06 20:48:32', '::1', '2022-04-06 20:48:32'),
(1472, 1, '2022-04-06 20:50:01', '::1', '2022-04-06 20:50:01'),
(1473, 1, '2022-04-20 12:11:33', '::1', '2022-04-20 12:11:33'),
(1474, 1, '2022-04-21 09:42:44', '::1', '2022-04-21 09:42:44'),
(1475, 1, '2022-05-13 09:05:07', '::1', '2022-05-13 09:05:07'),
(1476, 1, '2022-05-22 22:52:17', '::1', '2022-05-22 22:52:17'),
(1477, 1, '2022-06-03 11:39:14', '::1', '2022-06-03 11:39:14'),
(1478, 1, '2022-06-03 21:58:10', '::1', '2022-06-03 21:58:10'),
(1479, 1, '2022-06-05 14:10:11', '::1', '2022-06-05 14:10:11'),
(1480, 1, '2022-06-06 09:38:23', '::1', '2022-06-06 09:38:23'),
(1481, 1, '2022-06-07 09:00:17', '::1', '2022-06-07 09:00:17'),
(1482, 1, '2022-06-07 16:33:41', '::1', '2022-06-07 16:33:41'),
(1483, 1, '2022-06-07 16:33:47', '::1', '2022-06-07 16:33:47'),
(1484, 1, '2022-06-07 16:36:24', '::1', '2022-06-07 16:36:24'),
(1485, 1, '2022-06-07 16:36:51', '::1', '2022-06-07 16:36:51'),
(1486, 1, '2022-06-07 16:38:00', '::1', '2022-06-07 16:38:00'),
(1487, 1, '2022-06-07 17:08:19', '::1', '2022-06-07 17:08:19'),
(1488, 1, '2022-06-08 12:23:10', '::1', '2022-06-08 12:23:10'),
(1489, 1, '2022-06-08 13:16:36', '::1', '2022-06-08 13:16:36'),
(1490, 1, '2022-06-08 15:12:28', '::1', '2022-06-08 15:12:28'),
(1491, 1, '2022-06-08 15:14:54', '::1', '2022-06-08 15:14:54'),
(1492, 1, '2022-06-08 22:58:23', '::1', '2022-06-08 22:58:23'),
(1493, 1, '2022-06-12 23:51:06', '::1', '2022-06-12 23:51:06'),
(1494, 1, '2022-06-13 12:20:35', '::1', '2022-06-13 12:20:35'),
(1495, 1, '2022-06-13 14:55:51', '::1', '2022-06-13 14:55:51'),
(1496, 1, '2022-06-16 23:33:46', '::1', '2022-06-16 23:33:46'),
(1497, 1, '2022-06-17 00:55:02', '::1', '2022-06-17 00:55:02'),
(1498, 1, '2022-06-18 23:44:23', '::1', '2022-06-18 23:44:23'),
(1499, 1, '2022-06-19 16:40:37', '::1', '2022-06-19 16:40:37'),
(1500, 1, '2022-06-19 19:09:57', '::1', '2022-06-19 19:09:57'),
(1501, 1, '2022-06-19 19:42:27', '::1', '2022-06-19 19:42:27'),
(1502, 1, '2022-06-19 21:35:15', '::1', '2022-06-19 21:35:15'),
(1503, 1, '2022-06-19 22:50:33', '::1', '2022-06-19 22:50:33'),
(1504, 1, '2022-06-20 10:55:13', '::1', '2022-06-20 10:55:13'),
(1505, 1, '2022-06-20 12:07:47', '::1', '2022-06-20 12:07:47'),
(1506, 1, '2022-06-20 14:31:49', '::1', '2022-06-20 14:31:49'),
(1507, 1, '2022-06-20 14:33:45', '::1', '2022-06-20 14:33:45'),
(1508, 1, '2022-06-20 15:57:58', '::1', '2022-06-20 15:57:58'),
(1509, 1, '2022-06-21 08:39:58', '::1', '2022-06-21 08:39:58'),
(1510, 1, '2022-06-21 16:09:53', '::1', '2022-06-21 16:09:53'),
(1511, 1, '2022-06-21 16:55:58', '::1', '2022-06-21 16:55:58'),
(1512, 1, '2022-06-21 19:45:31', '::1', '2022-06-21 19:45:31'),
(1513, 1, '2022-06-21 21:51:29', '::1', '2022-06-21 21:51:29'),
(1514, 1, '2022-06-22 09:51:20', '::1', '2022-06-22 09:51:20'),
(1515, 1, '2022-06-22 12:24:11', '::1', '2022-06-22 12:24:11'),
(1516, 1, '2022-06-22 13:34:45', '::1', '2022-06-22 13:34:45'),
(1517, 1, '2022-06-22 17:16:34', '::1', '2022-06-22 17:16:34'),
(1518, 1, '2022-06-22 22:42:24', '::1', '2022-06-22 22:42:24'),
(1519, 1, '2022-06-23 12:27:18', '::1', '2022-06-23 12:27:18'),
(1520, 1, '2022-06-23 13:31:25', '::1', '2022-06-23 13:31:25'),
(1521, 1, '2022-06-23 16:31:24', '::1', '2022-06-23 16:31:24'),
(1522, 1, '2022-06-23 17:35:55', '::1', '2022-06-23 17:35:55'),
(1523, 1, '2022-06-23 18:44:10', '::1', '2022-06-23 18:44:10'),
(1524, 1, '2022-06-24 11:17:47', '::1', '2022-06-24 11:17:47'),
(1525, 1, '2022-06-24 14:26:00', '::1', '2022-06-24 14:26:00'),
(1526, 1, '2022-06-24 16:26:31', '::1', '2022-06-24 16:26:31'),
(1527, 1, '2022-06-24 17:55:46', '::1', '2022-06-24 17:55:46'),
(1528, 1, '2022-06-26 22:29:56', '::1', '2022-06-26 22:29:56'),
(1529, 1, '2022-06-26 23:23:37', '::1', '2022-06-26 23:23:37'),
(1530, 1, '2022-06-27 09:36:14', '::1', '2022-06-27 09:36:14'),
(1531, 1, '2022-06-27 12:18:46', '::1', '2022-06-27 12:18:46'),
(1532, 1, '2022-06-27 13:04:50', '::1', '2022-06-27 13:04:50'),
(1533, 1, '2022-06-27 14:25:56', '::1', '2022-06-27 14:25:56'),
(1534, 1, '2022-06-27 15:18:08', '::1', '2022-06-27 15:18:08'),
(1535, 1, '2022-06-27 16:15:33', '::1', '2022-06-27 16:15:33'),
(1536, 1, '2022-06-27 23:12:39', '::1', '2022-06-27 23:12:39'),
(1537, 1, '2022-06-28 11:18:17', '::1', '2022-06-28 11:18:17'),
(1538, 1, '2022-06-28 12:58:03', '::1', '2022-06-28 12:58:03'),
(1539, 1, '2022-06-28 13:47:28', '::1', '2022-06-28 13:47:28'),
(1540, 1, '2022-06-28 16:16:37', '::1', '2022-06-28 16:16:37'),
(1541, 1, '2022-06-29 01:11:05', '::1', '2022-06-29 01:11:05'),
(1542, 1, '2022-06-29 10:16:47', '::1', '2022-06-29 10:16:47'),
(1543, 1, '2022-06-29 12:03:14', '::1', '2022-06-29 12:03:14'),
(1544, 1, '2022-07-04 10:04:10', '::1', '2022-07-04 10:04:10'),
(1545, 1, '2022-07-04 11:08:03', '::1', '2022-07-04 11:08:03'),
(1546, 1, '2022-07-04 12:53:50', '::1', '2022-07-04 12:53:50'),
(1547, 1, '2022-07-04 16:32:44', '123.231.86.166', '2022-07-04 16:32:44'),
(1548, 1, '2022-07-05 07:08:53', '103.21.165.251', '2022-07-05 07:08:53'),
(1549, 1, '2022-07-05 08:53:24', '112.134.211.221', '2022-07-05 08:53:24'),
(1550, 1, '2022-07-05 19:45:30', '45.121.88.193', '2022-07-05 19:45:30'),
(1551, 1, '2022-07-05 19:50:54', '112.134.209.68', '2022-07-05 19:50:54'),
(1552, 1, '2022-07-06 08:29:13', '112.134.210.11', '2022-07-06 08:29:13'),
(1553, 1, '2022-07-06 11:56:40', '45.121.91.56', '2022-07-06 11:56:40'),
(1554, 1, '2022-07-06 12:09:38', '123.231.84.238', '2022-07-06 12:09:38'),
(1555, 1, '2022-07-06 12:54:11', '103.21.164.31', '2022-07-06 12:54:11'),
(1556, 1, '2022-07-06 16:04:32', '112.134.209.167', '2022-07-06 16:04:32'),
(1557, 1, '2022-07-06 18:05:56', '112.134.210.76', '2022-07-06 18:05:56'),
(1558, 1, '2022-07-07 07:15:05', '45.121.91.151', '2022-07-07 07:15:05'),
(1559, 1, '2022-07-07 07:59:38', '112.134.210.226', '2022-07-07 07:59:38'),
(1560, 1, '2022-07-07 08:17:00', '45.121.91.151', '2022-07-07 08:17:00'),
(1561, 1, '2022-07-07 10:22:26', '112.134.210.226', '2022-07-07 10:22:26'),
(1562, 1, '2022-07-07 14:06:41', '123.231.85.187', '2022-07-07 14:06:41'),
(1563, 1, '2022-07-09 18:12:12', '112.134.209.117', '2022-07-09 18:12:12'),
(1564, 1, '2022-07-11 06:38:40', '112.134.214.20', '2022-07-11 06:38:40'),
(1565, 1, '2022-07-11 13:13:52', '45.121.88.46', '2022-07-11 13:13:52'),
(1566, 1, '2022-07-11 13:22:31', '112.134.208.204', '2022-07-11 13:22:31'),
(1567, 1, '2022-07-12 13:04:33', '112.134.211.7', '2022-07-12 13:04:33'),
(1568, 1, '2022-07-12 13:09:28', '112.134.211.7', '2022-07-12 13:09:28'),
(1569, 1, '2022-07-12 13:19:19', '111.223.160.24', '2022-07-12 13:19:19'),
(1570, 1, '2022-07-12 14:02:13', '175.157.5.246', '2022-07-12 14:02:13'),
(1571, 1, '2022-07-14 07:40:24', '103.21.165.139', '2022-07-14 07:40:24'),
(1572, 1, '2022-07-14 08:52:54', '112.134.211.121', '2022-07-14 08:52:54'),
(1573, 1, '2022-07-14 09:00:44', '103.21.165.139', '2022-07-14 09:00:44'),
(1574, 1, '2022-07-14 09:48:57', '103.21.165.139', '2022-07-14 09:48:57'),
(1575, 1, '2022-07-14 11:49:40', '123.231.84.27', '2022-07-14 11:49:40'),
(1576, 1, '2022-07-14 14:11:13', '112.134.214.20', '2022-07-14 14:11:13'),
(1577, 1, '2022-07-15 07:11:51', '112.134.209.246', '2022-07-15 07:11:51'),
(1578, 1, '2022-07-15 09:35:50', '112.134.204.254', '2022-07-15 09:35:50'),
(1579, 1, '2022-07-18 07:38:45', '103.21.165.104', '2022-07-18 07:38:45'),
(1580, 1, '2022-07-18 08:13:26', '112.134.208.247', '2022-07-18 08:13:26'),
(1581, 1, '2022-07-18 09:10:25', '175.157.41.123', '2022-07-18 09:10:25'),
(1582, 1, '2022-07-18 10:11:03', '103.21.165.104', '2022-07-18 10:11:03'),
(1583, 1, '2022-07-19 09:22:59', '123.231.126.189', '2022-07-19 09:22:59'),
(1584, 1, '2022-07-19 09:53:57', '45.121.91.55', '2022-07-19 09:53:57'),
(1585, 1, '2022-07-19 18:36:33', '45.121.91.16', '2022-07-19 18:36:33'),
(1586, 1, '2022-07-20 07:48:01', '175.157.47.255', '2022-07-20 07:48:01'),
(1587, 1, '2022-07-20 08:22:51', '175.157.47.255', '2022-07-20 08:22:51'),
(1588, 1, '2022-07-20 11:11:22', '103.21.165.62', '2022-07-20 11:11:22'),
(1589, 1, '2022-07-20 11:28:08', '175.157.41.79', '2022-07-20 11:28:08'),
(1590, 1, '2022-07-20 12:31:20', '175.157.41.79', '2022-07-20 12:31:20'),
(1591, 1, '2022-07-20 17:21:38', '175.157.42.142', '2022-07-20 17:21:38'),
(1592, 1, '2022-07-20 20:04:52', '112.134.213.147', '2022-07-20 20:04:52'),
(1593, 1, '2022-07-20 20:07:13', '84.9.39.254', '2022-07-20 20:07:13'),
(1594, 1, '2022-07-23 12:51:42', '123.231.85.147', '2022-07-23 12:51:42'),
(1595, 1, '2022-07-23 18:07:19', '112.134.203.254', '2022-07-23 18:07:19'),
(1596, 1, '2022-07-23 18:25:10', '123.231.108.85', '2022-07-23 18:25:10'),
(1597, 1, '2022-07-24 18:16:22', '123.231.105.38', '2022-07-24 18:16:22'),
(1598, 1, '2022-07-24 22:11:08', '175.157.8.12', '2022-07-24 22:11:08'),
(1599, 1, '2022-07-26 06:26:45', '103.21.166.115', '2022-07-26 06:26:45'),
(1600, 1, '2022-07-26 06:27:08', '123.231.126.123', '2022-07-26 06:27:08'),
(1601, 1, '2022-07-26 07:38:38', '103.21.166.115', '2022-07-26 07:38:38'),
(1602, 1, '2022-07-26 07:43:14', '123.231.126.123', '2022-07-26 07:43:14'),
(1603, 1, '2022-07-26 09:49:49', '123.231.126.123', '2022-07-26 09:49:49'),
(1604, 1, '2022-07-26 12:06:18', '103.21.165.176', '2022-07-26 12:06:18'),
(1605, 1, '2022-07-26 12:16:41', '123.231.84.23', '2022-07-26 12:16:41'),
(1606, 1, '2022-07-26 20:11:53', '112.134.215.220', '2022-07-26 20:11:53'),
(1607, 1, '2022-07-27 08:15:26', '103.21.164.18', '2022-07-27 08:15:26'),
(1608, 1, '2022-07-27 08:53:57', '116.206.244.86', '2022-07-27 08:53:57'),
(1609, 1, '2022-07-27 10:48:41', '116.206.244.86', '2022-07-27 10:48:41'),
(1610, 1, '2022-07-27 14:57:58', '112.134.209.159', '2022-07-27 14:57:58'),
(1611, 1, '2022-07-27 15:45:43', '103.21.165.83', '2022-07-27 15:45:43'),
(1612, 1, '2022-07-28 06:47:23', '175.157.46.248', '2022-07-28 06:47:23'),
(1613, 1, '2022-07-28 07:06:28', '103.21.166.29', '2022-07-28 07:06:28'),
(1614, 1, '2022-07-28 11:25:40', '175.157.32.222', '2022-07-28 11:25:40'),
(1615, 1, '2022-07-28 12:05:21', '112.134.211.7', '2022-07-28 12:05:21'),
(1616, 1, '2022-07-28 17:52:37', '175.157.41.35', '2022-07-28 17:52:37'),
(1617, 1, '2022-07-28 20:27:45', '175.157.41.35', '2022-07-28 20:27:45'),
(1618, 1, '2022-07-28 20:58:57', '112.134.200.141', '2022-07-28 20:58:57'),
(1619, 1, '2022-07-29 12:20:22', '116.206.244.105', '2022-07-29 12:20:22'),
(1620, 1, '2022-07-30 08:34:42', '175.157.45.237', '2022-07-30 08:34:42'),
(1621, 1, '2022-07-30 10:49:22', '175.157.45.237', '2022-07-30 10:49:22'),
(1622, 1, '2022-07-31 11:10:21', '45.121.88.172', '2022-07-31 11:10:21'),
(1623, 1, '2022-07-31 11:36:25', '175.157.43.112', '2022-07-31 11:36:25'),
(1624, 1, '2022-08-01 09:42:14', '103.21.165.88', '2022-08-01 09:42:14'),
(1625, 1, '2022-08-01 10:03:05', '103.21.165.88', '2022-08-01 10:03:05'),
(1626, 1, '2022-08-01 14:35:48', '112.134.214.203', '2022-08-01 14:35:48'),
(1627, 1, '2022-08-05 17:22:10', '112.134.210.36', '2022-08-05 17:22:10'),
(1628, 1, '2022-08-05 20:20:27', '112.134.210.36', '2022-08-05 20:20:27'),
(1629, 1, '2022-08-07 20:49:57', '112.134.206.125', '2022-08-07 20:49:57'),
(1630, 1, '2022-08-08 08:34:24', '112.134.208.70', '2022-08-08 08:34:24'),
(1631, 1, '2022-08-08 13:52:25', '112.134.208.70', '2022-08-08 13:52:25'),
(1632, 1, '2022-08-08 19:23:03', '112.134.210.208', '2022-08-08 19:23:03'),
(1633, 1, '2022-08-09 05:13:00', '45.121.88.122', '2022-08-09 05:13:00'),
(1634, 1, '2022-08-09 05:38:56', '45.121.88.122', '2022-08-09 05:38:56'),
(1635, 1, '2022-08-09 08:02:45', '112.134.213.138', '2022-08-09 08:02:45'),
(1636, 1, '2022-08-09 08:24:02', '103.21.166.114', '2022-08-09 08:24:02'),
(1637, 1, '2022-08-09 09:40:37', '112.134.213.138', '2022-08-09 09:40:37'),
(1638, 1, '2022-08-09 10:07:58', '103.21.166.114', '2022-08-09 10:07:58'),
(1639, 1, '2022-08-09 17:33:36', '112.134.211.57', '2022-08-09 17:33:36'),
(1640, 1, '2022-08-09 17:41:41', '45.121.91.145', '2022-08-09 17:41:41'),
(1641, 1, '2022-08-10 07:05:27', '112.134.211.151', '2022-08-10 07:05:27'),
(1642, 1, '2022-08-10 13:10:43', '112.134.211.77', '2022-08-10 13:10:43'),
(1643, 1, '2022-08-10 13:46:47', '175.157.24.119', '2022-08-10 13:46:47'),
(1644, 1, '2022-08-10 13:49:00', '175.157.24.119', '2022-08-10 13:49:00'),
(1645, 1, '2022-08-10 13:51:22', '103.21.165.109', '2022-08-10 13:51:22'),
(1646, 1, '2022-08-10 15:44:16', '112.134.210.174', '2022-08-10 15:44:16'),
(1647, 1, '2022-08-10 16:44:43', '112.134.210.174', '2022-08-10 16:44:43'),
(1648, 1, '2022-08-10 17:03:25', '112.134.210.174', '2022-08-10 17:03:25'),
(1649, 1, '2022-08-10 19:38:49', '112.134.207.209', '2022-08-10 19:38:49'),
(1650, 1, '2022-08-11 07:44:03', '123.231.50.245', '2022-08-11 07:44:03'),
(1651, 1, '2022-08-11 08:19:00', '123.231.50.245', '2022-08-11 08:19:00'),
(1652, 1, '2022-08-11 11:15:18', '123.231.50.245', '2022-08-11 11:15:18'),
(1653, 1, '2022-08-11 11:26:50', '123.231.50.245', '2022-08-11 11:26:50'),
(1654, 1, '2022-08-11 16:28:50', '112.134.208.40', '2022-08-11 16:28:50'),
(1655, 1, '2022-08-12 06:30:48', '45.121.91.64', '2022-08-12 06:30:48'),
(1656, 1, '2022-08-12 11:01:13', '112.134.215.187', '2022-08-12 11:01:13'),
(1657, 1, '2022-08-12 11:13:09', '112.134.208.220', '2022-08-12 11:13:09'),
(1658, 1, '2022-08-12 12:49:47', '103.21.165.165', '2022-08-12 12:49:47'),
(1659, 1, '2022-08-12 13:46:04', '112.134.215.187', '2022-08-12 13:46:04'),
(1660, 1, '2022-08-12 14:29:19', '112.134.215.187', '2022-08-12 14:29:19'),
(1661, 1, '2022-08-12 16:11:12', '112.134.209.76', '2022-08-12 16:11:12'),
(1662, 1, '2022-08-12 18:28:22', '112.134.209.76', '2022-08-12 18:28:22'),
(1663, 1, '2022-08-13 09:31:12', '112.134.214.69', '2022-08-13 09:31:12'),
(1664, 1, '2022-08-14 16:07:09', '175.157.37.155', '2022-08-14 16:07:09'),
(1665, 1, '2022-08-15 07:55:01', '45.121.91.181', '2022-08-15 07:55:01'),
(1666, 1, '2022-08-15 10:32:45', '175.157.13.162', '2022-08-15 10:32:45'),
(1667, 1, '2022-08-15 19:23:16', '175.157.37.155', '2022-08-15 19:23:16'),
(1668, 1, '2022-08-17 06:06:08', '103.21.165.55', '2022-08-17 06:06:08'),
(1669, 1, '2022-08-17 09:26:46', '123.231.108.179', '2022-08-17 09:26:46'),
(1670, 1, '2022-08-17 10:07:01', '45.121.88.77', '2022-08-17 10:07:01'),
(1671, 1, '2022-08-17 10:44:44', '112.134.214.85', '2022-08-17 10:44:44'),
(1672, 1, '2022-08-17 10:50:34', '45.121.88.77', '2022-08-17 10:50:34'),
(1673, 1, '2022-08-17 10:54:43', '123.231.105.192', '2022-08-17 10:54:43'),
(1674, 1, '2022-08-17 11:55:15', '45.121.88.77', '2022-08-17 11:55:15'),
(1675, 1, '2022-08-17 11:57:17', '45.121.88.77', '2022-08-17 11:57:17'),
(1676, 1, '2022-08-17 11:58:08', '112.134.212.32', '2022-08-17 11:58:08'),
(1677, 1, '2022-08-17 12:00:30', '45.121.88.77', '2022-08-17 12:00:30'),
(1678, 1, '2022-08-17 12:01:46', '112.134.214.85', '2022-08-17 12:01:46'),
(1679, 1, '2022-08-17 13:22:21', '112.134.212.168', '2022-08-17 13:22:21'),
(1680, 1, '2022-08-17 14:15:51', '112.134.212.168', '2022-08-17 14:15:51'),
(1681, 1, '2022-08-18 07:53:41', '123.231.50.245', '2022-08-18 07:53:41'),
(1682, 1, '2022-08-18 09:22:53', '45.121.88.10', '2022-08-18 09:22:53'),
(1683, 1, '2022-08-18 10:44:00', '123.231.50.245', '2022-08-18 10:44:00'),
(1684, 1, '2022-08-18 11:09:19', '112.134.210.62', '2022-08-18 11:09:19'),
(1685, 1, '2022-08-18 11:24:16', '103.21.164.225', '2022-08-18 11:24:16'),
(1686, 1, '2022-08-18 11:33:16', '123.231.120.12', '2022-08-18 11:33:16'),
(1687, 1, '2022-08-18 11:55:21', '103.21.164.225', '2022-08-18 11:55:21'),
(1688, 1, '2022-08-18 11:55:41', '123.231.120.12', '2022-08-18 11:55:41'),
(1689, 1, '2022-08-19 07:14:25', '112.134.213.200', '2022-08-19 07:14:25'),
(1690, 1, '2022-08-19 08:54:29', '45.121.91.158', '2022-08-19 08:54:29'),
(1691, 1, '2022-08-19 09:11:38', '123.231.123.45', '2022-08-19 09:11:38'),
(1692, 1, '2022-08-19 09:48:50', '123.231.124.185', '2022-08-19 09:48:50'),
(1693, 1, '2022-08-19 13:14:39', '112.134.209.188', '2022-08-19 13:14:39'),
(1694, 1, '2022-08-20 09:47:29', '112.134.201.66', '2022-08-20 09:47:29'),
(1695, 1, '2022-08-20 12:21:15', '175.157.41.149', '2022-08-20 12:21:15'),
(1696, 1, '2022-08-20 12:35:47', '175.157.41.149', '2022-08-20 12:35:47'),
(1697, 1, '2022-08-21 11:01:41', '112.134.211.4', '2022-08-21 11:01:41'),
(1698, 1, '2022-08-22 06:51:14', '123.231.121.46', '2022-08-22 06:51:14'),
(1699, 1, '2022-08-22 11:39:00', '112.134.209.78', '2022-08-22 11:39:00'),
(1700, 1, '2022-08-22 13:34:19', '112.134.210.67', '2022-08-22 13:34:19'),
(1701, 1, '2022-08-22 14:31:53', '112.134.212.89', '2022-08-22 14:31:53'),
(1702, 1, '2022-08-22 16:24:18', '123.231.124.65', '2022-08-22 16:24:18'),
(1703, 1, '2022-08-22 19:05:52', '112.134.214.88', '2022-08-22 19:05:52'),
(1704, 1, '2022-08-23 06:05:12', '123.231.87.112', '2022-08-23 06:05:12'),
(1705, 1, '2022-08-23 06:05:30', '112.134.210.216', '2022-08-23 06:05:30'),
(1706, 1, '2022-08-23 10:39:53', '103.21.164.44', '2022-08-23 10:39:53'),
(1707, 1, '2022-08-23 14:05:49', '112.134.247.138', '2022-08-23 14:05:49'),
(1708, 1, '2022-08-23 15:51:45', '123.231.109.63', '2022-08-23 15:51:45'),
(1709, 1, '2022-08-23 15:56:35', '112.134.247.138', '2022-08-23 15:56:35'),
(1710, 1, '2022-08-23 17:30:33', '175.157.142.159', '2022-08-23 17:30:33'),
(1711, 1, '2022-08-24 12:59:18', '112.134.212.159', '2022-08-24 12:59:18'),
(1712, 1, '2022-08-24 16:30:37', '112.134.214.74', '2022-08-24 16:30:37'),
(1713, 1, '2022-08-25 07:07:49', '123.231.85.2', '2022-08-25 07:07:49'),
(1714, 1, '2022-08-25 07:35:03', '112.134.213.51', '2022-08-25 07:35:03'),
(1715, 1, '2022-08-25 10:14:13', '112.134.213.51', '2022-08-25 10:14:13'),
(1716, 1, '2022-08-25 10:20:41', '112.134.210.105', '2022-08-25 10:20:41'),
(1717, 1, '2022-08-25 11:17:34', '123.231.105.234', '2022-08-25 11:17:34'),
(1718, 1, '2022-08-25 11:18:32', '123.231.105.234', '2022-08-25 11:18:32'),
(1719, 1, '2022-08-26 07:17:54', '116.206.244.137', '2022-08-26 07:17:54'),
(1720, 1, '2022-08-26 09:48:02', '116.206.244.137', '2022-08-26 09:48:02'),
(1721, 1, '2022-08-26 10:46:32', '123.231.85.242', '2022-08-26 10:46:32'),
(1722, 1, '2022-08-26 10:46:35', '123.231.85.242', '2022-08-26 10:46:35'),
(1723, 1, '2022-08-26 10:46:36', '123.231.85.242', '2022-08-26 10:46:36'),
(1724, 1, '2022-08-26 10:46:37', '123.231.85.242', '2022-08-26 10:46:37'),
(1725, 1, '2022-08-26 10:46:37', '123.231.85.242', '2022-08-26 10:46:37'),
(1726, 1, '2022-08-26 12:41:27', '112.134.215.81', '2022-08-26 12:41:27'),
(1727, 1, '2022-08-26 12:58:18', '112.134.215.81', '2022-08-26 12:58:18'),
(1728, 1, '2022-08-26 12:59:56', '112.134.215.81', '2022-08-26 12:59:56'),
(1729, 1, '2022-08-26 13:01:06', '112.134.215.81', '2022-08-26 13:01:06'),
(1730, 1, '2022-08-26 13:43:53', '112.134.215.81', '2022-08-26 13:43:53'),
(1731, 1, '2022-08-27 17:59:45', '123.231.109.116', '2022-08-27 17:59:45'),
(1732, 1, '2022-08-28 20:21:18', '123.231.124.222', '2022-08-28 20:21:18'),
(1733, 1, '2022-08-29 06:14:31', '103.21.166.11', '2022-08-29 06:14:31'),
(1734, 1, '2022-08-29 06:51:43', '123.231.109.128', '2022-08-29 06:51:43'),
(1735, 1, '2022-08-29 12:07:12', '123.231.109.128', '2022-08-29 12:07:12'),
(1736, 1, '2022-08-29 13:15:07', '123.231.109.128', '2022-08-29 13:15:07'),
(1737, 1, '2022-08-29 13:15:13', '103.21.165.94', '2022-08-29 13:15:13'),
(1738, 1, '2022-08-30 06:03:49', '123.231.85.0', '2022-08-30 06:03:49'),
(1739, 1, '2022-08-30 08:02:39', '123.231.124.206', '2022-08-30 08:02:39'),
(1740, 1, '2022-08-30 08:38:59', '123.231.85.0', '2022-08-30 08:38:59'),
(1741, 1, '2022-08-30 13:22:59', '123.231.108.45', '2022-08-30 13:22:59'),
(1742, 1, '2022-08-30 16:31:41', '123.231.124.127', '2022-08-30 16:31:41'),
(1743, 1, '2022-08-30 18:18:36', '223.224.13.227', '2022-08-30 18:18:36'),
(1744, 1, '2022-08-31 05:41:29', '123.231.85.10', '2022-08-31 05:41:29'),
(1745, 1, '2022-08-31 06:13:09', '123.231.125.49', '2022-08-31 06:13:09'),
(1746, 1, '2022-08-31 08:20:53', '123.231.125.49', '2022-08-31 08:20:53'),
(1747, 1, '2022-08-31 09:11:03', '123.231.125.49', '2022-08-31 09:11:03'),
(1748, 1, '2022-08-31 09:37:31', '123.231.125.49', '2022-08-31 09:37:31'),
(1749, 1, '2022-08-31 15:03:02', '123.231.125.49', '2022-08-31 15:03:02'),
(1750, 1, '2022-08-31 15:03:11', '175.157.159.176', '2022-08-31 15:03:11'),
(1751, 1, '2022-08-31 19:04:44', '175.157.159.176', '2022-08-31 19:04:44'),
(1752, 1, '2022-08-31 19:07:07', '175.157.159.176', '2022-08-31 19:07:07'),
(1753, 1, '2022-09-01 11:33:12', '123.231.107.128', '2022-09-01 11:33:12'),
(1754, 1, '2022-09-01 12:02:48', '123.231.84.244', '2022-09-01 12:02:48'),
(1755, 1, '2022-09-02 08:54:39', '112.134.209.82', '2022-09-02 08:54:39'),
(1756, 1, '2022-09-02 09:31:44', '112.134.209.82', '2022-09-02 09:31:44'),
(1757, 1, '2022-09-02 09:42:26', '175.157.41.156', '2022-09-02 09:42:26'),
(1758, 1, '2022-09-04 12:56:34', '112.134.52.104', '2022-09-04 12:56:34'),
(1759, 1, '2022-09-04 13:05:36', '112.134.52.104', '2022-09-04 13:05:36'),
(1760, 1, '2022-09-04 13:08:53', '112.134.52.104', '2022-09-04 13:08:53'),
(1761, 1, '2022-09-04 14:59:31', '112.134.52.204', '2022-09-04 14:59:31'),
(1762, 1, '2022-09-06 07:44:51', '175.157.41.79', '2022-09-06 07:44:51'),
(1763, 1, '2022-09-06 07:54:00', '123.231.121.111', '2022-09-06 07:54:00'),
(1764, 1, '2022-09-07 19:50:19', '112.134.215.67', '2022-09-07 19:50:19'),
(1765, 1, '2022-09-08 08:13:40', '112.134.209.129', '2022-09-08 08:13:40'),
(1766, 1, '2022-09-08 19:30:15', '112.134.211.92', '2022-09-08 19:30:15'),
(1767, 1, '2022-09-09 06:30:39', '112.134.208.86', '2022-09-09 06:30:39'),
(1768, 1, '2022-09-09 07:28:27', '175.157.41.76', '2022-09-09 07:28:27'),
(1769, 1, '2022-09-09 10:51:14', '112.134.214.11', '2022-09-09 10:51:14'),
(1770, 1, '2022-09-09 12:43:43', '112.134.214.11', '2022-09-09 12:43:43'),
(1771, 1, '2022-09-09 15:43:53', '112.134.214.11', '2022-09-09 15:43:53'),
(1772, 1, '2022-09-10 08:52:53', '45.121.88.53', '2022-09-10 08:52:53'),
(1773, 1, '2022-09-10 09:14:54', '112.134.213.7', '2022-09-10 09:14:54'),
(1774, 1, '2022-09-11 16:06:59', '112.134.214.192', '2022-09-11 16:06:59'),
(1775, 1, '2022-09-12 06:15:04', '112.134.213.37', '2022-09-12 06:15:04'),
(1776, 1, '2022-09-12 06:23:43', '103.21.165.123', '2022-09-12 06:23:43'),
(1777, 1, '2022-09-12 08:11:58', '112.134.213.127', '2022-09-12 08:11:58'),
(1778, 1, '2022-09-12 09:52:35', '116.206.246.58', '2022-09-12 09:52:35'),
(1779, 1, '2022-09-12 11:41:13', '103.21.164.193', '2022-09-12 11:41:13'),
(1780, 1, '2022-09-12 11:41:46', '116.206.246.58', '2022-09-12 11:41:46'),
(1781, 1, '2022-09-12 11:50:54', '116.206.246.58', '2022-09-12 11:50:54'),
(1782, 1, '2022-09-12 11:51:46', '116.206.246.58', '2022-09-12 11:51:46'),
(1783, 1, '2022-09-13 10:34:36', '123.231.84.171', '2022-09-13 10:34:36'),
(1784, 1, '2022-09-13 11:06:31', '112.134.209.34', '2022-09-13 11:06:31'),
(1785, 1, '2022-09-13 11:11:22', '123.231.84.171', '2022-09-13 11:11:22'),
(1786, 1, '2022-09-14 04:40:17', '112.134.215.100', '2022-09-14 04:40:17'),
(1787, 1, '2022-09-14 07:39:22', '175.157.37.243', '2022-09-14 07:39:22'),
(1788, 1, '2022-09-14 07:39:51', '175.157.37.243', '2022-09-14 07:39:51'),
(1789, 1, '2022-09-14 19:30:04', '112.134.213.50', '2022-09-14 19:30:04'),
(1790, 1, '2022-09-15 11:11:09', '112.134.212.2', '2022-09-15 11:11:09'),
(1791, 1, '2022-09-16 07:07:02', '112.134.213.31', '2022-09-16 07:07:02'),
(1792, 1, '2022-09-19 06:35:55', '112.134.211.33', '2022-09-19 06:35:55'),
(1793, 1, '2022-09-20 07:06:02', '112.134.209.106', '2022-09-20 07:06:02'),
(1794, 1, '2022-09-22 06:14:30', '112.134.209.38', '2022-09-22 06:14:30'),
(1795, 1, '2022-09-22 15:17:44', '175.157.41.17', '2022-09-22 15:17:44'),
(1796, 1, '2022-09-26 09:54:38', '112.134.214.138', '2022-09-26 09:54:38'),
(1797, 1, '2022-09-26 17:10:16', '123.231.125.43', '2022-09-26 17:10:16'),
(1798, 1, '2022-09-27 13:40:54', '112.134.210.242', '2022-09-27 13:40:54'),
(1799, 1, '2022-09-29 07:25:09', '111.223.161.27', '2022-09-29 07:25:09'),
(1800, 1, '2022-10-03 11:13:06', '112.134.213.61', '2022-10-03 11:13:06'),
(1801, 1, '2022-10-03 11:54:33', '**************', '2022-10-03 11:54:33'),
(1802, 1, '2022-10-03 12:07:30', '**************', '2022-10-03 12:07:30'),
(1803, 1, '2022-10-03 12:14:24', '**************', '2022-10-03 12:14:24'),
(1804, 1, '2022-10-03 13:10:33', '**************', '2022-10-03 13:10:33'),
(1805, 1, '2022-10-03 13:12:31', '**************', '2022-10-03 13:12:31'),
(1806, 1, '2022-10-03 13:14:53', '**************', '2022-10-03 13:14:53'),
(1807, 1, '2022-10-03 14:13:52', '175.157.34.17', '2022-10-03 14:13:52'),
(1808, 1, '2022-10-05 08:30:33', '112.134.215.105', '2022-10-05 08:30:33'),
(1809, 1, '2022-10-05 12:41:03', '**************', '2022-10-05 12:41:03'),
(1810, 1, '2022-10-05 12:42:48', '**************', '2022-10-05 12:42:48'),
(1811, 1, '2022-10-07 13:12:56', '112.134.213.49', '2022-10-07 13:12:56'),
(1812, 1, '2022-10-08 15:27:46', '112.134.213.148', '2022-10-08 15:27:46'),
(1813, 1, '2022-10-10 13:23:25', '112.134.208.27', '2022-10-10 13:23:25'),
(1814, 1, '2022-10-12 07:57:41', '***************', '2022-10-12 07:57:41'),
(1815, 1, '2022-10-12 08:06:36', '***************', '2022-10-12 08:06:36'),
(1816, 1, '2022-10-12 08:10:33', '***************', '2022-10-12 08:10:33'),
(1817, 1, '2022-10-12 10:48:08', '***************', '2022-10-12 10:48:08'),
(1818, 1, '2022-10-12 11:24:58', '***************', '2022-10-12 11:24:58'),
(1819, 1, '2022-10-13 05:04:56', '***************', '2022-10-13 05:04:56'),
(1820, 1, '2022-10-13 05:17:00', '***************', '2022-10-13 05:17:00'),
(1821, 1, '2022-10-13 05:23:11', '***************', '2022-10-13 05:23:11'),
(1822, 1, '2022-10-13 05:43:28', '***************', '2022-10-13 05:43:28'),
(1823, 1, '2022-10-13 06:54:57', '***************', '2022-10-13 06:54:57'),
(1824, 1, '2022-10-13 10:16:53', '***************', '2022-10-13 10:16:53'),
(1825, 1, '2022-10-13 17:13:35', '112.134.211.28', '2022-10-13 17:13:35'),
(1826, 1, '2022-10-14 10:02:38', '112.134.211.238', '2022-10-14 10:02:38'),
(1827, 1, '2022-10-16 11:52:53', '101.2.177.13', '2022-10-16 11:52:53'),
(1828, 1, '2022-10-17 08:29:55', '112.134.211.194', '2022-10-17 08:29:55'),
(1829, 1, '2022-10-17 11:26:31', '112.134.211.194', '2022-10-17 11:26:31'),
(1830, 1, '2022-10-17 16:28:21', '175.157.45.15', '2022-10-17 16:28:21'),
(1831, 1, '2022-10-18 07:58:24', '123.231.124.208', '2022-10-18 07:58:24'),
(1832, 1, '2022-10-19 11:06:22', '112.134.215.124', '2022-10-19 11:06:22'),
(1833, 1, '2022-10-19 12:05:28', '175.157.33.36', '2022-10-19 12:05:28'),
(1834, 1, '2022-10-20 08:39:41', '112.134.215.42', '2022-10-20 08:39:41'),
(1835, 1, '2022-10-21 18:43:29', '112.134.214.83', '2022-10-21 18:43:29'),
(1836, 1, '2022-10-23 16:05:26', '123.231.86.83', '2022-10-23 16:05:26'),
(1837, 1, '2022-10-23 16:29:25', '123.231.86.83', '2022-10-23 16:29:25'),
(1838, 1, '2022-10-23 17:00:13', '123.231.86.83', '2022-10-23 17:00:13'),
(1839, 1, '2022-10-24 05:34:04', '112.134.212.186', '2022-10-24 05:34:04'),
(1840, 1, '2022-10-24 06:20:09', '112.134.212.186', '2022-10-24 06:20:09'),
(1841, 1, '2022-10-24 08:22:49', '112.134.212.186', '2022-10-24 08:22:49'),
(1842, 1, '2022-10-24 08:49:32', '112.134.212.186', '2022-10-24 08:49:32'),
(1843, 1, '2022-10-25 06:25:15', '112.134.215.226', '2022-10-25 06:25:15'),
(1844, 1, '2022-10-25 10:38:17', '112.134.215.226', '2022-10-25 10:38:17'),
(1845, 1, '2022-10-26 06:31:23', '112.134.214.202', '2022-10-26 06:31:23'),
(1846, 1, '2022-10-26 10:27:22', '112.134.214.202', '2022-10-26 10:27:22'),
(1847, 1, '2022-10-27 05:00:53', '112.134.214.141', '2022-10-27 05:00:53'),
(1848, 1, '2022-10-28 13:39:33', '175.157.47.160', '2022-10-28 13:39:33'),
(1849, 1, '2022-10-29 16:34:13', '101.2.178.27', '2022-10-29 16:34:13'),
(1850, 1, '2022-10-29 16:41:03', '112.134.204.121', '2022-10-29 16:41:03'),
(1851, 1, '2022-10-29 17:11:37', '101.2.178.27', '2022-10-29 17:11:37'),
(1852, 1, '2022-10-29 18:24:43', '112.134.204.19', '2022-10-29 18:24:43'),
(1853, 1, '2022-10-29 18:24:59', '112.134.204.19', '2022-10-29 18:24:59'),
(1854, 1, '2022-10-30 10:29:18', '101.2.178.27', '2022-10-30 10:29:18'),
(1855, 1, '2022-10-30 10:29:18', '101.2.178.27', '2022-10-30 10:29:18'),
(1856, 1, '2022-10-31 06:20:31', '112.134.212.229', '2022-10-31 06:20:31'),
(1857, 1, '2022-10-31 07:32:33', '112.134.212.229', '2022-10-31 07:32:33'),
(1858, 1, '2022-11-01 05:58:08', '112.134.209.137', '2022-11-01 05:58:08'),
(1859, 1, '2022-11-01 06:18:54', '112.134.209.137', '2022-11-01 06:18:54'),
(1860, 1, '2022-11-02 04:26:49', '112.134.214.75', '2022-11-02 04:26:49'),
(1861, 1, '2022-11-02 05:31:54', '112.134.214.75', '2022-11-02 05:31:54'),
(1862, 1, '2022-11-02 08:17:44', '112.134.214.75', '2022-11-02 08:17:44'),
(1863, 1, '2022-11-03 05:14:05', '112.134.208.97', '2022-11-03 05:14:05'),
(1864, 1, '2022-11-03 08:37:23', '112.134.211.208', '2022-11-03 08:37:23'),
(1865, 1, '2022-11-04 04:44:22', '112.134.208.61', '2022-11-04 04:44:22'),
(1866, 1, '2022-11-08 04:25:19', '112.134.212.219', '2022-11-08 04:25:19'),
(1867, 1, '2022-11-08 04:53:09', '112.134.212.219', '2022-11-08 04:53:09'),
(1868, 1, '2022-11-09 07:49:46', '112.134.209.151', '2022-11-09 07:49:46'),
(1869, 1, '2022-11-10 11:59:23', '175.157.148.20', '2022-11-10 11:59:23'),
(1870, 1, '2022-11-11 04:31:22', '112.134.215.130', '2022-11-11 04:31:22'),
(1871, 1, '2022-11-16 18:29:18', '112.134.201.111', '2022-11-16 18:29:18'),
(1872, 1, '2022-11-18 04:23:15', '112.134.212.88', '2022-11-18 04:23:15'),
(1873, 1, '2022-12-21 15:21:05', '*************', '2022-12-21 15:21:05'),
(1874, 1, '2022-12-21 17:03:09', '101.2.182.121', '2022-12-21 17:03:09'),
(1875, 1, '2022-12-21 17:15:27', '101.2.182.121', '2022-12-21 17:15:27'),
(1876, 1, '2022-12-21 21:15:35', '*************', '2022-12-21 21:15:35'),
(1877, 1, '2022-12-21 21:16:19', '*************', '2022-12-21 21:16:19'),
(1878, 1, '2022-12-21 21:34:12', '*************', '2022-12-21 21:34:12'),
(1879, 1, '2022-12-22 02:06:20', '61.245.161.203', '2022-12-22 02:06:20'),
(1880, 1, '2022-12-22 05:55:59', '112.134.211.238', '2022-12-22 05:55:59'),
(1881, 1, '2022-12-22 06:06:11', '112.134.211.238', '2022-12-22 06:06:11'),
(1882, 1, '2022-12-22 08:27:49', '175.157.43.150', '2022-12-22 08:27:49'),
(1883, 1, '2022-12-22 08:32:45', '112.134.211.238', '2022-12-22 08:32:45'),
(1884, 1, '2022-12-22 11:59:01', '112.134.211.238', '2022-12-22 11:59:01'),
(1885, 1, '2022-12-22 12:27:24', '112.134.210.204', '2022-12-22 12:27:24'),
(1886, 1, '2022-12-23 06:16:29', '***************', '2022-12-23 06:16:29'),
(1887, 1, '2022-12-25 18:03:00', '116.206.245.195', '2022-12-25 18:03:00'),
(1888, 1, '2022-12-31 19:33:21', '112.134.207.84', '2022-12-31 19:33:21'),
(1889, 1, '2022-12-31 19:52:29', '112.134.207.84', '2022-12-31 19:52:29'),
(1890, 1, '2022-12-31 20:02:02', '112.134.207.84', '2022-12-31 20:02:02'),
(1891, 1, '2023-01-02 14:30:22', '123.231.127.207', '2023-01-02 14:30:22'),
(1892, 1, '2023-01-25 12:04:42', '111.119.49.128', '2023-01-25 12:04:42'),
(1893, 1, '2023-01-29 19:53:30', '112.134.211.92', '2023-01-29 19:53:30'),
(1894, 6, '2023-08-02 11:19:21', '::1', '2023-08-02 11:19:21'),
(1895, 6, '2023-08-02 11:22:17', '::1', '2023-08-02 11:22:17'),
(1896, 1, '2023-08-02 15:23:45', '::1', '2023-08-02 15:23:45'),
(1897, 8, '2023-08-03 10:57:15', '::1', '2023-08-03 10:57:15'),
(1898, 1, '2023-08-03 11:26:19', '::1', '2023-08-03 11:26:19'),
(1899, 1, '2023-08-03 13:56:36', '::1', '2023-08-03 13:56:36'),
(1900, 3, '2023-08-03 14:25:29', '::1', '2023-08-03 14:25:29'),
(1901, 3, '2023-08-03 14:28:39', '::1', '2023-08-03 14:28:39'),
(1902, 1, '2023-08-03 14:30:00', '::1', '2023-08-03 14:30:00'),
(1903, 1, '2023-08-04 08:39:25', '127.0.0.1', '2023-08-04 08:39:25'),
(1904, 1, '2023-08-04 11:10:48', '::1', '2023-08-04 11:10:48'),
(1905, 1, '2023-08-04 13:00:54', '127.0.0.1', '2023-08-04 13:00:54'),
(1906, 1, '2023-08-07 09:34:12', '::1', '2023-08-07 09:34:12'),
(1907, 4, '2023-08-07 10:28:50', '::1', '2023-08-07 10:28:50'),
(1908, 1, '2023-08-08 11:30:48', '::1', '2023-08-08 11:30:48'),
(1909, 1, '2023-08-08 11:57:47', '127.0.0.1', '2023-08-08 11:57:47'),
(1910, 9, '2023-08-08 12:05:34', '::1', '2023-08-08 12:05:34'),
(1911, 9, '2023-08-08 13:16:04', '::1', '2023-08-08 13:16:04'),
(1912, 1, '2023-08-08 13:35:23', '::1', '2023-08-08 13:35:23'),
(1913, 13, '2023-08-08 13:46:46', '::1', '2023-08-08 13:46:46'),
(1914, 1, '2023-08-08 15:17:59', '127.0.0.1', '2023-08-08 15:17:59'),
(1915, 1, '2023-08-08 15:52:01', '127.0.0.1', '2023-08-08 15:52:01'),
(1916, 1, '2023-08-09 08:50:43', '::1', '2023-08-09 08:50:43'),
(1917, 23, '2023-08-09 08:53:24', '127.0.0.1', '2023-08-09 08:53:24'),
(1918, 24, '2023-08-09 09:33:02', '127.0.0.1', '2023-08-09 09:33:02'),
(1919, 25, '2023-08-09 09:55:08', '127.0.0.1', '2023-08-09 09:55:08'),
(1920, 25, '2023-08-09 10:00:05', '127.0.0.1', '2023-08-09 10:00:05'),
(1921, 25, '2023-08-09 10:29:29', '127.0.0.1', '2023-08-09 10:29:29'),
(1922, 1, '2023-08-09 10:48:33', '::1', '2023-08-09 10:48:33'),
(1923, 25, '2023-08-09 11:04:18', '127.0.0.1', '2023-08-09 11:04:18'),
(1924, 1, '2023-08-09 11:24:56', '::1', '2023-08-09 11:24:56'),
(1925, 25, '2023-08-09 13:30:54', '::1', '2023-08-09 13:30:54'),
(1926, 1, '2023-08-09 13:31:24', '::1', '2023-08-09 13:31:24'),
(1927, 1, '2023-08-09 14:13:05', '::1', '2023-08-09 14:13:05'),
(1928, 1, '2023-08-09 16:21:07', '127.0.0.1', '2023-08-09 16:21:07'),
(1929, 1, '2023-08-09 16:26:07', '::1', '2023-08-09 16:26:07'),
(1930, 1, '2023-08-09 16:38:32', '::1', '2023-08-09 16:38:32'),
(1931, 1, '2023-08-10 08:56:38', '::1', '2023-08-10 08:56:38'),
(1932, 1, '2023-08-10 09:26:45', '::1', '2023-08-10 09:26:45'),
(1933, 1, '2023-08-10 10:43:41', '::1', '2023-08-10 10:43:41'),
(1934, 1, '2023-08-10 11:02:07', '::1', '2023-08-10 11:02:07'),
(1935, 1, '2023-08-10 11:03:56', '::1', '2023-08-10 11:03:56'),
(1936, 1, '2023-08-10 11:35:16', '::1', '2023-08-10 11:35:16'),
(1937, 1, '2023-08-10 11:35:50', '::1', '2023-08-10 11:35:50'),
(1938, 1, '2023-08-10 14:35:17', '::1', '2023-08-10 14:35:17'),
(1939, 1, '2023-08-10 15:00:58', '::1', '2023-08-10 15:00:58'),
(1940, 1, '2023-08-10 16:47:27', '::1', '2023-08-10 16:47:27'),
(1941, 1, '2023-08-10 16:59:38', '::1', '2023-08-10 16:59:38'),
(1942, 1, '2023-08-11 11:28:22', '::1', '2023-08-11 11:28:22'),
(1943, 25, '2023-08-11 13:34:59', '::1', '2023-08-11 13:34:59'),
(1944, 25, '2023-08-11 13:47:25', '::1', '2023-08-11 13:47:25'),
(1945, 25, '2023-08-11 13:48:34', '::1', '2023-08-11 13:48:34'),
(1946, 29, '2023-08-11 13:50:29', '::1', '2023-08-11 13:50:29'),
(1947, 25, '2023-08-11 13:51:32', '::1', '2023-08-11 13:51:32'),
(1948, 1, '2023-08-11 13:53:36', '::1', '2023-08-11 13:53:36'),
(1949, 25, '2023-08-11 14:41:43', '127.0.0.1', '2023-08-11 14:41:43'),
(1950, 1, '2023-08-11 15:03:05', '::1', '2023-08-11 15:03:05'),
(1951, 1, '2023-08-11 15:06:43', '::1', '2023-08-11 15:06:43'),
(1952, 25, '2023-08-11 16:31:16', '127.0.0.1', '2023-08-11 16:31:16'),
(1953, 1, '2023-08-14 09:24:00', '::1', '2023-08-14 09:24:00'),
(1954, 25, '2023-08-14 10:00:12', '127.0.0.1', '2023-08-14 10:00:12'),
(1955, 25, '2023-08-14 10:40:12', '127.0.0.1', '2023-08-14 10:40:12'),
(1956, 25, '2023-08-14 14:57:29', '127.0.0.1', '2023-08-14 14:57:29'),
(1957, 25, '2023-08-14 16:12:16', '127.0.0.1', '2023-08-14 16:12:16'),
(1958, 1, '2023-08-15 08:55:56', '::1', '2023-08-15 08:55:56'),
(1959, 35, '2023-08-15 09:13:38', '127.0.0.1', '2023-08-15 09:13:38'),
(1960, 35, '2023-08-15 09:53:13', '127.0.0.1', '2023-08-15 09:53:13'),
(1961, 37, '2023-08-15 09:55:26', '127.0.0.1', '2023-08-15 09:55:26'),
(1962, 35, '2023-08-15 10:20:06', '127.0.0.1', '2023-08-15 10:20:06'),
(1963, 38, '2023-08-15 10:31:09', '127.0.0.1', '2023-08-15 10:31:09'),
(1964, 38, '2023-08-15 10:41:45', '127.0.0.1', '2023-08-15 10:41:45'),
(1965, 38, '2023-08-15 10:41:57', '127.0.0.1', '2023-08-15 10:41:57'),
(1966, 35, '2023-08-15 15:16:56', '127.0.0.1', '2023-08-15 15:16:56'),
(1967, 35, '2023-08-15 16:50:23', '127.0.0.1', '2023-08-15 16:50:23'),
(1968, 35, '2023-08-16 09:03:03', '127.0.0.1', '2023-08-16 09:03:03'),
(1969, 35, '2023-08-16 10:24:54', '::1', '2023-08-16 10:24:54'),
(1970, 35, '2023-08-16 12:00:30', '::1', '2023-08-16 12:00:30'),
(1971, 25, '2023-08-16 12:08:23', '::1', '2023-08-16 12:08:23'),
(1972, 35, '2023-08-16 12:10:22', '::1', '2023-08-16 12:10:22'),
(1973, 35, '2023-08-16 12:46:00', '::1', '2023-08-16 12:46:00'),
(1974, 35, '2023-08-16 13:27:17', '::1', '2023-08-16 13:27:17'),
(1975, 35, '2023-08-16 14:51:47', '::1', '2023-08-16 14:51:47'),
(1976, 35, '2023-08-16 15:05:27', '::1', '2023-08-16 15:05:27'),
(1977, 35, '2023-08-16 16:03:15', '::1', '2023-08-16 16:03:15'),
(1978, 35, '2023-08-16 16:34:25', '::1', '2023-08-16 16:34:25'),
(1979, 35, '2023-08-17 08:55:31', '::1', '2023-08-17 08:55:31'),
(1980, 35, '2023-08-17 09:19:18', '::1', '2023-08-17 09:19:18'),
(1981, 35, '2023-08-17 09:19:43', '::1', '2023-08-17 09:19:43'),
(1982, 35, '2023-08-17 09:58:01', '::1', '2023-08-17 09:58:01'),
(1983, 1, '2023-08-17 10:40:35', '::1', '2023-08-17 10:40:35'),
(1984, 35, '2023-08-17 13:24:19', '127.0.0.1', '2023-08-17 13:24:19'),
(1985, 1, '2023-08-17 14:34:11', '::1', '2023-08-17 14:34:11'),
(1986, 1, '2023-08-17 15:26:14', '127.0.0.1', '2023-08-17 15:26:14'),
(1987, 1, '2023-08-18 08:59:58', '127.0.0.1', '2023-08-18 08:59:58'),
(1988, 1, '2023-08-18 09:34:18', '127.0.0.1', '2023-08-18 09:34:18'),
(1989, 1, '2023-08-18 13:51:29', '127.0.0.1', '2023-08-18 13:51:29'),
(1990, 1, '2023-08-18 15:19:36', '127.0.0.1', '2023-08-18 15:19:36'),
(1991, 1, '2023-08-18 15:24:58', '127.0.0.1', '2023-08-18 15:24:58'),
(1992, 1, '2023-08-18 15:57:01', '127.0.0.1', '2023-08-18 15:57:01'),
(1993, 1, '2023-08-18 16:13:10', '127.0.0.1', '2023-08-18 16:13:10'),
(1994, 1, '2023-08-18 16:27:27', '127.0.0.1', '2023-08-18 16:27:27'),
(1995, 1, '2023-08-18 16:45:38', '127.0.0.1', '2023-08-18 16:45:38'),
(1996, 1, '2023-08-18 16:59:36', '127.0.0.1', '2023-08-18 16:59:36'),
(1997, 1, '2023-08-21 09:02:51', '::1', '2023-08-21 09:02:51'),
(1998, 1, '2023-08-21 09:45:27', '::1', '2023-08-21 09:45:27'),
(1999, 1, '2023-08-21 09:45:49', '127.0.0.1', '2023-08-21 09:45:49'),
(2000, 1, '2023-08-21 09:46:18', '::1', '2023-08-21 09:46:18'),
(2001, 1, '2023-08-21 09:48:58', '127.0.0.1', '2023-08-21 09:48:58'),
(2002, 1, '2023-08-21 10:05:05', '::1', '2023-08-21 10:05:05'),
(2003, 35, '2023-08-21 11:09:25', '127.0.0.1', '2023-08-21 11:09:25'),
(2004, 35, '2023-08-21 11:20:18', '127.0.0.1', '2023-08-21 11:20:18'),
(2005, 35, '2023-08-21 11:25:47', '127.0.0.1', '2023-08-21 11:25:47'),
(2006, 1, '2023-08-21 12:33:40', '::1', '2023-08-21 12:33:40'),
(2007, 35, '2023-08-21 13:23:42', '127.0.0.1', '2023-08-21 13:23:42'),
(2008, 35, '2023-08-21 13:44:26', '::1', '2023-08-21 13:44:26'),
(2009, 35, '2023-08-21 13:49:09', '127.0.0.1', '2023-08-21 13:49:09'),
(2010, 1, '2023-08-21 15:03:17', '127.0.0.1', '2023-08-21 15:03:17'),
(2011, 35, '2023-08-21 15:18:18', '127.0.0.1', '2023-08-21 15:18:18'),
(2012, 1, '2023-08-21 15:22:34', '::1', '2023-08-21 15:22:34'),
(2013, 1, '2023-08-21 16:45:44', '127.0.0.1', '2023-08-21 16:45:44'),
(2014, 35, '2023-08-21 16:50:53', '127.0.0.1', '2023-08-21 16:50:53'),
(2015, 1, '2023-08-22 08:48:37', '::1', '2023-08-22 08:48:37'),
(2016, 35, '2023-08-22 08:48:51', '127.0.0.1', '2023-08-22 08:48:51'),
(2017, 35, '2023-08-22 11:51:06', '127.0.0.1', '2023-08-22 11:51:06'),
(2018, 35, '2023-08-22 12:32:49', '127.0.0.1', '2023-08-22 12:32:49'),
(2019, 35, '2023-08-22 12:37:18', '127.0.0.1', '2023-08-22 12:37:18'),
(2020, 1, '2023-08-22 13:40:14', '::1', '2023-08-22 13:40:14'),
(2021, 35, '2023-08-22 13:43:23', '127.0.0.1', '2023-08-22 13:43:23'),
(2022, 39, '2023-08-22 13:45:55', '127.0.0.1', '2023-08-22 13:45:55'),
(2023, 35, '2023-08-22 14:05:27', '127.0.0.1', '2023-08-22 14:05:27'),
(2024, 1, '2023-08-23 09:19:00', '::1', '2023-08-23 09:19:00'),
(2025, 1, '2023-08-23 09:39:16', '::1', '2023-08-23 09:39:16'),
(2026, 1, '2023-08-23 13:13:21', '::1', '2023-08-23 13:13:21'),
(2027, 1, '2023-08-25 08:48:06', '::1', '2023-08-25 08:48:06'),
(2028, 35, '2023-08-25 08:49:50', '127.0.0.1', '2023-08-25 08:49:50'),
(2029, 35, '2023-08-25 10:52:40', '127.0.0.1', '2023-08-25 10:52:40'),
(2030, 1, '2023-08-25 11:37:28', '127.0.0.1', '2023-08-25 11:37:28'),
(2031, 1, '2023-08-25 13:55:02', '127.0.0.1', '2023-08-25 13:55:02'),
(2032, 1, '2023-08-25 15:10:48', '127.0.0.1', '2023-08-25 15:10:48'),
(2033, 1, '2023-08-28 09:53:39', '::1', '2023-08-28 09:53:39'),
(2034, 1, '2023-08-28 11:58:09', '::1', '2023-08-28 11:58:09'),
(2035, 1, '2023-08-28 17:04:41', '::1', '2023-08-28 17:04:41'),
(2036, 1, '2023-08-29 08:54:39', '::1', '2023-08-29 08:54:39'),
(2037, 35, '2023-08-29 12:11:13', '127.0.0.1', '2023-08-29 12:11:13'),
(2038, 35, '2023-08-29 12:37:06', '127.0.0.1', '2023-08-29 12:37:06'),
(2039, 1, '2023-08-29 14:14:14', '::1', '2023-08-29 14:14:14'),
(2040, 1, '2023-08-31 09:16:51', '::1', '2023-08-31 09:16:51'),
(2041, 1, '2023-08-31 11:37:20', '::1', '2023-08-31 11:37:20'),
(2042, 1, '2023-08-31 14:53:02', '::1', '2023-08-31 14:53:02'),
(2043, 35, '2023-08-31 15:07:54', '127.0.0.1', '2023-08-31 15:07:54'),
(2044, 35, '2023-08-31 15:51:22', '127.0.0.1', '2023-08-31 15:51:22'),
(2045, 1, '2023-08-31 16:50:19', '127.0.0.1', '2023-08-31 16:50:19'),
(2046, 1, '2023-09-01 08:51:01', '::1', '2023-09-01 08:51:01'),
(2047, 1, '2023-09-01 09:49:44', '::1', '2023-09-01 09:49:44'),
(2048, 1, '2023-09-01 13:44:51', '::1', '2023-09-01 13:44:51'),
(2049, 1, '2023-09-04 08:50:11', '::1', '2023-09-04 08:50:11'),
(2050, 1, '2023-09-04 09:42:56', '::1', '2023-09-04 09:42:56'),
(2051, 1, '2023-09-05 08:57:37', '::1', '2023-09-05 08:57:37'),
(2052, 1, '2023-09-05 12:23:36', '::1', '2023-09-05 12:23:36'),
(2053, 1, '2023-09-06 10:06:01', '::1', '2023-09-06 10:06:01'),
(2054, 1, '2023-09-06 12:09:00', '::1', '2023-09-06 12:09:00'),
(2055, 1, '2023-09-11 09:28:02', '::1', '2023-09-11 09:28:02'),
(2056, 1, '2023-09-11 15:00:36', '::1', '2023-09-11 15:00:36'),
(2057, 35, '2023-09-11 15:22:03', '127.0.0.1', '2023-09-11 15:22:03'),
(2058, 1, '2023-09-12 08:44:29', '::1', '2023-09-12 08:44:29'),
(2059, 35, '2023-09-12 15:31:14', '127.0.0.1', '2023-09-12 15:31:14'),
(2060, 1, '2023-09-13 09:19:32', '::1', '2023-09-13 09:19:32'),
(2061, 35, '2023-09-13 09:21:45', '127.0.0.1', '2023-09-13 09:21:45'),
(2062, 35, '2023-09-13 09:39:30', '127.0.0.1', '2023-09-13 09:39:30'),
(2063, 35, '2023-09-13 13:05:36', '127.0.0.1', '2023-09-13 13:05:36'),
(2064, 35, '2023-09-13 14:10:33', '127.0.0.1', '2023-09-13 14:10:33'),
(2065, 35, '2023-09-13 14:41:37', '127.0.0.1', '2023-09-13 14:41:37'),
(2066, 35, '2023-09-13 16:03:26', '127.0.0.1', '2023-09-13 16:03:26'),
(2067, 1, '2023-09-14 09:27:14', '::1', '2023-09-14 09:27:14'),
(2068, 1, '2023-09-14 11:43:16', '127.0.0.1', '2023-09-14 11:43:16'),
(2069, 35, '2023-09-14 13:20:26', '127.0.0.1', '2023-09-14 13:20:26'),
(2070, 35, '2023-09-14 14:02:34', '127.0.0.1', '2023-09-14 14:02:34'),
(2071, 35, '2023-09-14 15:32:28', '127.0.0.1', '2023-09-14 15:32:28'),
(2072, 35, '2023-09-14 16:20:10', '127.0.0.1', '2023-09-14 16:20:10'),
(2073, 1, '2023-09-15 09:40:01', '::1', '2023-09-15 09:40:01'),
(2074, 1, '2023-09-15 10:34:06', '::1', '2023-09-15 10:34:06'),
(2075, 1, '2023-09-15 10:37:40', '127.0.0.1', '2023-09-15 10:37:40'),
(2076, 1, '2023-09-15 11:51:10', '::1', '2023-09-15 11:51:10'),
(2077, 1, '2023-09-18 09:30:21', '::1', '2023-09-18 09:30:21'),
(2078, 1, '2023-09-18 17:47:40', '127.0.0.1', '2023-09-18 17:47:40'),
(2079, 1, '2023-09-19 08:52:48', '127.0.0.1', '2023-09-19 08:52:48'),
(2080, 1, '2023-09-19 08:55:16', '::1', '2023-09-19 08:55:16'),
(2081, 1, '2023-09-19 14:38:36', '127.0.0.1', '2023-09-19 14:38:36'),
(2082, 35, '2023-09-20 10:31:24', '127.0.0.1', '2023-09-20 10:31:24'),
(2083, 35, '2023-09-20 14:50:05', '127.0.0.1', '2023-09-20 14:50:05'),
(2084, 1, '2023-09-25 09:19:29', '::1', '2023-09-25 09:19:29'),
(2085, 35, '2023-09-25 10:05:28', '127.0.0.1', '2023-09-25 10:05:28'),
(2086, 1, '2023-09-25 13:44:42', '::1', '2023-09-25 13:44:42'),
(2087, 1, '2023-09-25 13:46:22', '127.0.0.1', '2023-09-25 13:46:22'),
(2088, 1, '2023-09-25 15:10:35', '127.0.0.1', '2023-09-25 15:10:35'),
(2089, 1, '2023-09-25 16:31:38', '127.0.0.1', '2023-09-25 16:31:38'),
(2090, 1, '2023-09-26 09:56:29', '::1', '2023-09-26 09:56:29'),
(2091, 35, '2023-09-26 16:17:42', '127.0.0.1', '2023-09-26 16:17:42'),
(2092, 35, '2023-09-26 17:19:29', '127.0.0.1', '2023-09-26 17:19:29'),
(2093, 1, '2023-09-27 10:09:04', '::1', '2023-09-27 10:09:04'),
(2094, 35, '2023-09-27 10:16:33', '127.0.0.1', '2023-09-27 10:16:33'),
(2095, 35, '2023-09-27 11:22:48', '127.0.0.1', '2023-09-27 11:22:48'),
(2096, 35, '2023-09-27 11:48:55', '127.0.0.1', '2023-09-27 11:48:55'),
(2097, 35, '2023-09-27 12:16:33', '127.0.0.1', '2023-09-27 12:16:33'),
(2098, 35, '2023-09-27 12:36:23', '127.0.0.1', '2023-09-27 12:36:23'),
(2099, 35, '2023-09-27 12:39:26', '127.0.0.1', '2023-09-27 12:39:26'),
(2100, 35, '2023-09-27 13:30:18', '127.0.0.1', '2023-09-27 13:30:18'),
(2101, 35, '2023-09-27 14:41:18', '127.0.0.1', '2023-09-27 14:41:18'),
(2102, 35, '2023-09-27 16:31:22', '127.0.0.1', '2023-09-27 16:31:22'),
(2103, 1, '2023-09-28 09:31:06', '::1', '2023-09-28 09:31:06'),
(2104, 35, '2023-09-28 09:33:25', '127.0.0.1', '2023-09-28 09:33:25'),
(2105, 35, '2023-09-28 12:11:26', '127.0.0.1', '2023-09-28 12:11:26'),
(2106, 35, '2023-09-28 13:26:16', '127.0.0.1', '2023-09-28 13:26:16'),
(2107, 1, '2023-09-28 16:40:36', '::1', '2023-09-28 16:40:36'),
(2108, 1, '2023-10-02 09:06:05', '::1', '2023-10-02 09:06:05');

-- --------------------------------------------------------

--
-- Table structure for table `nbcourier_tbl_license`
--

CREATE TABLE `nbcourier_tbl_license` (
  `license_id` int(11) NOT NULL,
  `message` varchar(191) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `due_date` date NOT NULL,
  `status_yn` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `nbcourier_tbl_license`
--

INSERT INTO `nbcourier_tbl_license` (`license_id`, `message`, `start_date`, `end_date`, `due_date`, `status_yn`) VALUES
(1, 'Please contact the administrator', '2022-09-01 23:40:18', '2022-10-31 20:58:54', '2022-10-28', 'N');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ggportal_tbl_agent`
--
ALTER TABLE `ggportal_tbl_agent`
  ADD PRIMARY KEY (`agent_id`);

--
-- Indexes for table `ggportal_tbl_application_log`
--
ALTER TABLE `ggportal_tbl_application_log`
  ADD PRIMARY KEY (`application_log_id`);

--
-- Indexes for table `ggportal_tbl_application_status`
--
ALTER TABLE `ggportal_tbl_application_status`
  ADD PRIMARY KEY (`application_status_id`);

--
-- Indexes for table `ggportal_tbl_bank`
--
ALTER TABLE `ggportal_tbl_bank`
  ADD PRIMARY KEY (`bank_id`);

--
-- Indexes for table `ggportal_tbl_city`
--
ALTER TABLE `ggportal_tbl_city`
  ADD PRIMARY KEY (`city_id`);

--
-- Indexes for table `ggportal_tbl_commission`
--
ALTER TABLE `ggportal_tbl_commission`
  ADD PRIMARY KEY (`commission_id`);

--
-- Indexes for table `ggportal_tbl_content`
--
ALTER TABLE `ggportal_tbl_content`
  ADD PRIMARY KEY (`content_id`);

--
-- Indexes for table `ggportal_tbl_country`
--
ALTER TABLE `ggportal_tbl_country`
  ADD PRIMARY KEY (`country_id`);

--
-- Indexes for table `ggportal_tbl_course`
--
ALTER TABLE `ggportal_tbl_course`
  ADD PRIMARY KEY (`course_id`);

--
-- Indexes for table `ggportal_tbl_currency`
--
ALTER TABLE `ggportal_tbl_currency`
  ADD PRIMARY KEY (`currency_id`);

--
-- Indexes for table `ggportal_tbl_institute`
--
ALTER TABLE `ggportal_tbl_institute`
  ADD PRIMARY KEY (`institute_id`);

--
-- Indexes for table `ggportal_tbl_message`
--
ALTER TABLE `ggportal_tbl_message`
  ADD PRIMARY KEY (`message_id`);

--
-- Indexes for table `ggportal_tbl_message_staff`
--
ALTER TABLE `ggportal_tbl_message_staff`
  ADD PRIMARY KEY (`message_id`);

--
-- Indexes for table `ggportal_tbl_program`
--
ALTER TABLE `ggportal_tbl_program`
  ADD PRIMARY KEY (`program_id`);

--
-- Indexes for table `ggportal_tbl_program_wishlist`
--
ALTER TABLE `ggportal_tbl_program_wishlist`
  ADD PRIMARY KEY (`program_wishlist_id`);

--
-- Indexes for table `ggportal_tbl_province`
--
ALTER TABLE `ggportal_tbl_province`
  ADD PRIMARY KEY (`province_id`);

--
-- Indexes for table `ggportal_tbl_remainder`
--
ALTER TABLE `ggportal_tbl_remainder`
  ADD PRIMARY KEY (`remainder_id`);

--
-- Indexes for table `ggportal_tbl_staff`
--
ALTER TABLE `ggportal_tbl_staff`
  ADD PRIMARY KEY (`staff_id`);

--
-- Indexes for table `ggportal_tbl_staff_activity`
--
ALTER TABLE `ggportal_tbl_staff_activity`
  ADD PRIMARY KEY (`user_activity_id`);

--
-- Indexes for table `ggportal_tbl_staff_login`
--
ALTER TABLE `ggportal_tbl_staff_login`
  ADD PRIMARY KEY (`user_login_id`);

--
-- Indexes for table `ggportal_tbl_staff_privilege`
--
ALTER TABLE `ggportal_tbl_staff_privilege`
  ADD PRIMARY KEY (`staff_privilege_id`);

--
-- Indexes for table `ggportal_tbl_state`
--
ALTER TABLE `ggportal_tbl_state`
  ADD PRIMARY KEY (`state_id`);

--
-- Indexes for table `ggportal_tbl_student`
--
ALTER TABLE `ggportal_tbl_student`
  ADD PRIMARY KEY (`student_id`),
  ADD UNIQUE KEY `nic` (`student_id`);

--
-- Indexes for table `ggportal_tbl_student_application`
--
ALTER TABLE `ggportal_tbl_student_application`
  ADD PRIMARY KEY (`student_application_id`);

--
-- Indexes for table `ggportal_tbl_student_dependence`
--
ALTER TABLE `ggportal_tbl_student_dependence`
  ADD PRIMARY KEY (`dependence_id`);

--
-- Indexes for table `ggportal_tbl_student_disability`
--
ALTER TABLE `ggportal_tbl_student_disability`
  ADD PRIMARY KEY (`disability_id`);

--
-- Indexes for table `ggportal_tbl_student_document`
--
ALTER TABLE `ggportal_tbl_student_document`
  ADD PRIMARY KEY (`student_document_id`);

--
-- Indexes for table `ggportal_tbl_student_education`
--
ALTER TABLE `ggportal_tbl_student_education`
  ADD PRIMARY KEY (`education_id`);

--
-- Indexes for table `ggportal_tbl_student_english_test`
--
ALTER TABLE `ggportal_tbl_student_english_test`
  ADD PRIMARY KEY (`english_test_id`);

--
-- Indexes for table `ggportal_tbl_student_passport`
--
ALTER TABLE `ggportal_tbl_student_passport`
  ADD PRIMARY KEY (`passport_id`),
  ADD UNIQUE KEY `passport_number` (`passport_number`);

--
-- Indexes for table `ggportal_tbl_student_travel_history`
--
ALTER TABLE `ggportal_tbl_student_travel_history`
  ADD PRIMARY KEY (`travel_history_id`);

--
-- Indexes for table `ggportal_tbl_student_visa_refusal`
--
ALTER TABLE `ggportal_tbl_student_visa_refusal`
  ADD PRIMARY KEY (`visa_refusal_id`);

--
-- Indexes for table `ggportal_tbl_student_work`
--
ALTER TABLE `ggportal_tbl_student_work`
  ADD PRIMARY KEY (`work_id`);

--
-- Indexes for table `ggportal_tbl_timeline`
--
ALTER TABLE `ggportal_tbl_timeline`
  ADD PRIMARY KEY (`timeline_id`),
  ADD KEY `timeline_name` (`timeline_name`);

--
-- Indexes for table `ggportal_tbl_training_request`
--
ALTER TABLE `ggportal_tbl_training_request`
  ADD PRIMARY KEY (`training_request_id`);

--
-- Indexes for table `ggportal_tbl_user`
--
ALTER TABLE `ggportal_tbl_user`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `ggportal_tbl_user_activity`
--
ALTER TABLE `ggportal_tbl_user_activity`
  ADD PRIMARY KEY (`user_activity_id`);

--
-- Indexes for table `ggportal_tbl_user_login`
--
ALTER TABLE `ggportal_tbl_user_login`
  ADD PRIMARY KEY (`user_login_id`);

--
-- Indexes for table `nbcourier_tbl_license`
--
ALTER TABLE `nbcourier_tbl_license`
  ADD PRIMARY KEY (`license_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ggportal_tbl_agent`
--
ALTER TABLE `ggportal_tbl_agent`
  MODIFY `agent_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `ggportal_tbl_application_log`
--
ALTER TABLE `ggportal_tbl_application_log`
  MODIFY `application_log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=221;

--
-- AUTO_INCREMENT for table `ggportal_tbl_application_status`
--
ALTER TABLE `ggportal_tbl_application_status`
  MODIFY `application_status_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=122;

--
-- AUTO_INCREMENT for table `ggportal_tbl_bank`
--
ALTER TABLE `ggportal_tbl_bank`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=93;

--
-- AUTO_INCREMENT for table `ggportal_tbl_city`
--
ALTER TABLE `ggportal_tbl_city`
  MODIFY `city_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_commission`
--
ALTER TABLE `ggportal_tbl_commission`
  MODIFY `commission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=773;

--
-- AUTO_INCREMENT for table `ggportal_tbl_content`
--
ALTER TABLE `ggportal_tbl_content`
  MODIFY `content_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ggportal_tbl_country`
--
ALTER TABLE `ggportal_tbl_country`
  MODIFY `country_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `ggportal_tbl_course`
--
ALTER TABLE `ggportal_tbl_course`
  MODIFY `course_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `ggportal_tbl_currency`
--
ALTER TABLE `ggportal_tbl_currency`
  MODIFY `currency_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `ggportal_tbl_institute`
--
ALTER TABLE `ggportal_tbl_institute`
  MODIFY `institute_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=759;

--
-- AUTO_INCREMENT for table `ggportal_tbl_message`
--
ALTER TABLE `ggportal_tbl_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `ggportal_tbl_message_staff`
--
ALTER TABLE `ggportal_tbl_message_staff`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `ggportal_tbl_program`
--
ALTER TABLE `ggportal_tbl_program`
  MODIFY `program_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=566;

--
-- AUTO_INCREMENT for table `ggportal_tbl_program_wishlist`
--
ALTER TABLE `ggportal_tbl_program_wishlist`
  MODIFY `program_wishlist_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `ggportal_tbl_province`
--
ALTER TABLE `ggportal_tbl_province`
  MODIFY `province_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=204;

--
-- AUTO_INCREMENT for table `ggportal_tbl_remainder`
--
ALTER TABLE `ggportal_tbl_remainder`
  MODIFY `remainder_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff`
--
ALTER TABLE `ggportal_tbl_staff`
  MODIFY `staff_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_activity`
--
ALTER TABLE `ggportal_tbl_staff_activity`
  MODIFY `user_activity_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_login`
--
ALTER TABLE `ggportal_tbl_staff_login`
  MODIFY `user_login_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1696;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_privilege`
--
ALTER TABLE `ggportal_tbl_staff_privilege`
  MODIFY `staff_privilege_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `ggportal_tbl_state`
--
ALTER TABLE `ggportal_tbl_state`
  MODIFY `state_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=156;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student`
--
ALTER TABLE `ggportal_tbl_student`
  MODIFY `student_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=101;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_application`
--
ALTER TABLE `ggportal_tbl_student_application`
  MODIFY `student_application_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_dependence`
--
ALTER TABLE `ggportal_tbl_student_dependence`
  MODIFY `dependence_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_disability`
--
ALTER TABLE `ggportal_tbl_student_disability`
  MODIFY `disability_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_document`
--
ALTER TABLE `ggportal_tbl_student_document`
  MODIFY `student_document_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_education`
--
ALTER TABLE `ggportal_tbl_student_education`
  MODIFY `education_id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_english_test`
--
ALTER TABLE `ggportal_tbl_student_english_test`
  MODIFY `english_test_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_passport`
--
ALTER TABLE `ggportal_tbl_student_passport`
  MODIFY `passport_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_travel_history`
--
ALTER TABLE `ggportal_tbl_student_travel_history`
  MODIFY `travel_history_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_visa_refusal`
--
ALTER TABLE `ggportal_tbl_student_visa_refusal`
  MODIFY `visa_refusal_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_work`
--
ALTER TABLE `ggportal_tbl_student_work`
  MODIFY `work_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `ggportal_tbl_timeline`
--
ALTER TABLE `ggportal_tbl_timeline`
  MODIFY `timeline_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `ggportal_tbl_training_request`
--
ALTER TABLE `ggportal_tbl_training_request`
  MODIFY `training_request_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user`
--
ALTER TABLE `ggportal_tbl_user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user_activity`
--
ALTER TABLE `ggportal_tbl_user_activity`
  MODIFY `user_activity_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user_login`
--
ALTER TABLE `ggportal_tbl_user_login`
  MODIFY `user_login_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2109;

--
-- AUTO_INCREMENT for table `nbcourier_tbl_license`
--
ALTER TABLE `nbcourier_tbl_license`
  MODIFY `license_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
