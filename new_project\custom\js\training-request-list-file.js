$(document).ready(function () {
  $("#example2").DataTable({
    paging: true,
    lengthChange: true,
    searching: true,
    ordering: true,
    info: true,
    autoWidth: false,
    responsive: true,
  });

  $('input[name="checkbox-new"]').on("change", function () {
    let active_yn = "N";
    if (this.checked) {
      active_yn = "Y";
    } else {
      active_yn = "N";
    }

    let row_elem = $(this).attr("id");
    let training_request_id = $(
      'input[name="training_request_id_' + row_elem + '"]'
    ).val();

    $.ajax({
      type: "POST",
      url: "controller/training-controller.php",
      data: {
        training_request_id: training_request_id,
        updateTraining: 1,
        active_yn: active_yn,
      },
      dataType: "json",
      success: function (data) {
        //console.log(data);
        if (data.data > 0) {
          toastr.success("Updated");
          window.location.reload();
        } else {
          toastr.error("There is an error while saving.");
        }
      },
    });
  });

  $(".delete_btn").on("click", function () {
    var training_request_id = $(this).data("training_request_id");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/training-controller.php",
          data: {
            training_request_id: training_request_id,
            deleteTraining: 1,
          },
          dataType: "json",
          success: function (data) {
            //console.log(data);
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your file has been deleted.", "success");
              window.location.reload();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  $("#quickform").validate({
    submitHandler: function (form) {
      var formData = new FormData(form);
      formData.append("trainingUpdate", "1");
      console.log(formData);
      $.ajax({
        type: "POST",
        url: "controller/training-controller.php",
        data: formData,
        dataType: "json",
        mimeType: "multipart/form-data",
        contentType: false,
        processData: false,
        cache: false,
        success: function (data) {
          if (data.data > 0) {
            toastr.success("Saved Successfully");
            window.location.href = "training-request-list.php";
          } else {
            toastr.error(data.status);
          }
        },
      });
    },
  });
});
