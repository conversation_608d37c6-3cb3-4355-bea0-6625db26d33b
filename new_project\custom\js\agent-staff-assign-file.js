$(document).ready(function () {
  bsCustomFileInput.init();
  $('.select2bs4').select2({
    theme: 'bootstrap4'
  });

  $('.select2bs4').each(function(index) {
    if (!$(this).attr('id')) {
        $(this).attr('id', 'select2bs4-' + index);
    }
});

// Capture initial values from the select elements
var initialValues = {};
$('.select2bs4').each(function() {
    var id = $(this).attr('id');
    var initialValue = $(this).val();
    initialValues[id] = initialValue;
});

// Handle reset button click
$('#reset-button').click(function() {       
    $('.select2bs4').each(function() {
        var id = $(this).attr('id');
        var initialValue = initialValues[id];
        $(this).val(initialValue).trigger('change');
    });
    $('#quickform').validate().resetForm();       
});


  $('#quickform').validate({
    rules: {
      agent_id: {
          required: true,
      },
      staff_id: {
          required: true,
      }
  },
  messages: {
      agent_id: {
          required: "Please Select One",
      },
      staff_id: {
          required: "Please Select One",
      },     
  },
  errorElement: 'span',
  errorPlacement: function (error, element) {
      error.addClass('invalid-feedback');
      element.closest('.form-group').append(error);
  },
  highlight: function (element, errorClass, validClass) {
      $(element).addClass('is-invalid');
  },
  unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass('is-invalid');
  },
    submitHandler: function (form) {
      console.log('test');
      var formData = new FormData(form);
      formData.append('agentAssignSave', '1');
      $.ajax({
        type: "POST",
        url: "controller/agent-controller.php",
        data: formData,
        dataType: 'json',
        mimeType: "multipart/form-data",
        contentType: false,
        processData: false,
        cache: false,
        success: function (data) {
          console.log(data);
          if (data.data > 0) {
            toastr.success('Saved Successfully');
            $('.assign-button').text('Assigned');
            window.location.href = 'agent-list.php';
          } else {
            toastr.error(data.status);
          }

        }

      });
    }
  });

  $('.staff_id, .agent_id').on('change', function () {
    $(this).valid();
  });


});