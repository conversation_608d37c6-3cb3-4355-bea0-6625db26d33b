-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 29, 2024 at 12:46 AM
-- Server version: 10.11.8-MariaDB
-- PHP Version: 8.1.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ggportal_tbl_agent`
--

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_agent`
--

CREATE TABLE `ggportal_tbl_agent` (
  `agent_id` int(11) NOT NULL,
  `parent_agent_id` int(11) NOT NULL,
  `ag_company_name` varchar(100) DEFAULT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(20) NOT NULL,
  `country` varchar(200) NOT NULL,
  `city` varchar(100) NOT NULL,
  `br_document` varchar(255) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `profile_picture` text NOT NULL,
  `user_access_level` int(11) NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `active_yn` varchar(1) NOT NULL,
  `agreement_signed_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `commission_rate` int(11) NOT NULL,
  `last_seen` datetime DEFAULT NULL,
  `online_status` int(1) NOT NULL DEFAULT 0,
  `ag_document` varchar(255) NOT NULL,
  `assigned_staff` int(11) NOT NULL DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_agent`
--

INSERT INTO `ggportal_tbl_agent` (`agent_id`, `parent_agent_id`, `ag_company_name`, `first_name`, `last_name`, `username`, `email`, `mobile`, `country`, `city`, `br_document`, `password`, `password_salt`, `user_type`, `profile_picture`, `user_access_level`, `email_validate_yn`, `active_yn`, `agreement_signed_yn`, `commission_rate`, `last_seen`, `online_status`, `ag_document`, `assigned_staff`) VALUES
(45, 0, 'ABCD', 'Amasha1', 'Ranaweera', '<EMAIL>', '<EMAIL>', ' +94778941234', 'Sri Lanka', 'Matara', 'dist/uploads/agent/1710323276.pdf', 'Zk5vQTE3MG90ZHFrT0h4RnY4UkRoUT09', '', 'AG', 'dist/uploads/agent/1710323276.jpg', 100, 'Y', 'Y', 'Y', 0, '2024-05-21 17:23:28', 0, 'dist/uploads/agent/**********.pdf', 89),
(44, 0, 'Global Guidance', 'Global', 'Guidance', '<EMAIL>', '<EMAIL>', ' +94743500150', 'Sri Lanka', 'Colombo', '', 'N1Y3bnNCeFVLOVVUWnZxNDhQY0IzUT09', '', 'AG', 'dist/uploads/agent/1715316684.jpg', 100, 'Y', 'Y', 'Y', 0, '2024-05-10 10:43:55', 0, 'dist/uploads/agent/1715316765.pdf', 89),
(48, 0, 'ABC Company ', 'Kaushalya ', 'wanigarathna', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'China', 'Melbourne ', 'dist/uploads/agent/1713347336.pdf', 'QSt4T3ZmcXNZMjN5NTNvdEJLV0tVdz09', '', 'AG', 'dist/uploads/agent/1713347336.png', 100, 'Y', 'Y', 'Y', 0, '2024-04-17 16:08:29', 0, 'dist/uploads/agent/1713348119.pdf', 0),
(51, 0, 'aaaaa', 'tashen', 'maduwantha', '<EMAIL>', '<EMAIL>', ' +94717177177171', 'American Samoa', 'mathara', 'dist/uploads/agent/1716801971.pdf', 'SEkzYVJFZlVneXoyMUtBbzRsbUs2QT09', '', 'AG', 'dist/uploads/agent/1716801971.jfif', 100, '', 'Y', 'N', 0, NULL, 0, 'dist/uploads/agent/1716801971.pdf', 0);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_application_log`
--

CREATE TABLE `ggportal_tbl_application_log` (
  `application_log_id` int(11) NOT NULL,
  `student_application_id` int(11) NOT NULL,
  `status_id` int(11) NOT NULL,
  `updated_date` datetime DEFAULT NULL,
  `remarks` varchar(120) NOT NULL,
  `due_date` datetime NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'N',
  `special_note` varchar(100) DEFAULT NULL,
  `date` date NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_application_log`
--

INSERT INTO `ggportal_tbl_application_log` (`application_log_id`, `student_application_id`, `status_id`, `updated_date`, `remarks`, `due_date`, `active_yn`, `special_note`, `date`) VALUES
(367, 69, 8, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(366, 69, 7, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(365, 69, 5, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(364, 69, 4, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(363, 69, 3, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(362, 69, 2, '2024-04-18 03:34:33', '', '2024-04-18 03:32:37', 'Y', NULL, '0000-00-00'),
(361, 69, 1, '2024-04-18 03:32:50', '', '2024-04-18 03:32:37', 'Y', NULL, '0000-00-00'),
(360, 68, 6, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(359, 68, 14, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(358, 68, 13, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(357, 68, 12, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(356, 68, 11, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(355, 68, 10, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(354, 68, 9, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(353, 68, 8, '2024-04-18 02:41:55', '', '2024-04-18 02:37:00', 'Y', NULL, '0000-00-00'),
(352, 68, 7, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(351, 68, 5, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(350, 68, 4, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(349, 68, 3, NULL, '', '2024-04-18 02:37:00', 'N', NULL, '0000-00-00'),
(348, 68, 2, '2024-04-18 02:40:00', '', '2024-04-18 02:37:00', 'Y', NULL, '0000-00-00'),
(347, 68, 1, '2024-04-18 03:09:11', '', '2024-04-18 02:37:00', 'Y', NULL, '0000-00-00'),
(346, 67, 6, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(345, 67, 14, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(344, 67, 13, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(343, 67, 12, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(342, 67, 11, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(341, 67, 10, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(340, 67, 9, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(339, 67, 8, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(338, 67, 7, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(337, 67, 5, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(336, 67, 4, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(335, 67, 3, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(334, 67, 2, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(333, 67, 1, NULL, '', '2024-04-09 06:08:55', 'N', NULL, '0000-00-00'),
(332, 66, 6, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(331, 66, 14, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(330, 66, 13, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(329, 66, 12, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(328, 66, 11, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(327, 66, 10, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(326, 66, 9, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(325, 66, 8, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(324, 66, 7, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(323, 66, 5, '2024-04-04 07:34:45', 'rgrgrgrg', '2024-04-04 05:29:59', 'Y', NULL, '2024-04-26'),
(322, 66, 4, '2024-04-04 07:26:50', 'gfhghhhh', '2024-04-04 05:29:59', 'Y', NULL, '2024-04-18'),
(321, 66, 3, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(320, 66, 2, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(319, 66, 1, NULL, '', '2024-04-04 05:29:59', 'N', NULL, '0000-00-00'),
(318, 65, 6, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(316, 65, 13, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(317, 65, 14, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(315, 65, 12, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(314, 65, 11, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(313, 65, 10, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(312, 65, 9, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(311, 65, 8, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(310, 65, 7, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(309, 65, 5, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(308, 65, 4, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(307, 65, 3, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(306, 65, 2, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(305, 65, 1, NULL, '', '2024-04-04 05:28:40', 'N', NULL, '0000-00-00'),
(304, 64, 6, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(303, 64, 14, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(302, 64, 13, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(301, 64, 12, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(300, 64, 11, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(299, 64, 10, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(298, 64, 9, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(297, 64, 8, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(296, 64, 7, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(295, 64, 5, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(294, 64, 4, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(293, 64, 3, NULL, '', '2024-03-20 01:28:23', 'N', NULL, '0000-00-00'),
(292, 64, 2, '2024-04-01 06:14:36', 'dsdssssf', '2024-03-20 01:28:23', 'Y', NULL, '2024-04-16'),
(291, 64, 1, '2024-03-28 05:48:29', '', '2024-03-20 01:28:23', 'Y', NULL, '2024-03-27'),
(290, 63, 6, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(289, 63, 14, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(288, 63, 13, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(287, 63, 12, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(286, 63, 11, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(285, 63, 10, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(284, 63, 9, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(283, 63, 8, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(282, 63, 7, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(281, 63, 5, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(280, 63, 4, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(279, 63, 3, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(278, 63, 2, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(277, 63, 1, NULL, '', '2024-03-20 00:22:28', 'N', NULL, '0000-00-00'),
(276, 62, 6, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(275, 62, 14, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(274, 62, 13, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(273, 62, 12, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(272, 62, 11, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(271, 62, 10, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(270, 62, 9, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(269, 62, 8, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(268, 62, 7, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(267, 62, 5, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(266, 62, 4, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(265, 62, 3, NULL, '', '2024-03-20 00:21:36', 'N', NULL, '0000-00-00'),
(264, 62, 2, '2024-04-17 00:03:51', '', '2024-03-20 00:21:36', 'Y', NULL, '0000-00-00'),
(263, 62, 1, '2024-04-09 05:54:18', '', '2024-03-20 00:21:36', 'Y', NULL, '0000-00-00'),
(368, 69, 9, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(369, 69, 10, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(370, 69, 11, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(371, 69, 12, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(372, 69, 13, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(373, 69, 14, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(374, 69, 6, NULL, '', '2024-04-18 03:32:37', 'N', NULL, '0000-00-00'),
(375, 70, 1, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(376, 70, 2, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(377, 70, 3, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(378, 70, 4, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(379, 70, 5, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(380, 70, 7, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(381, 70, 8, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(382, 70, 9, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(383, 70, 10, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(384, 70, 11, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(385, 70, 12, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(386, 70, 13, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(387, 70, 14, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(388, 70, 6, NULL, '', '2024-04-19 05:53:57', 'N', NULL, '0000-00-00'),
(389, 71, 1, '2024-04-24 02:47:59', '', '2024-04-04 00:00:00', 'Y', NULL, '0000-00-00'),
(390, 71, 2, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(391, 71, 3, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(392, 71, 4, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(393, 71, 5, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(394, 71, 7, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(395, 71, 8, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(396, 71, 9, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(397, 71, 10, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(398, 71, 11, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(399, 71, 12, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(400, 71, 13, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(401, 71, 14, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(402, 71, 6, NULL, '', '2024-04-19 06:14:56', 'N', NULL, '0000-00-00'),
(403, 72, 1, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(404, 72, 2, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(405, 72, 3, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(406, 72, 4, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(407, 72, 5, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(408, 72, 7, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(409, 72, 8, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(410, 72, 9, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(411, 72, 10, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(412, 72, 11, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(413, 72, 12, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(414, 72, 13, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(415, 72, 14, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(416, 72, 6, NULL, '', '2024-04-22 00:34:12', 'N', NULL, '0000-00-00'),
(417, 73, 1, '2024-04-24 07:07:46', '', '2024-04-24 07:07:20', 'Y', NULL, '0000-00-00'),
(418, 73, 2, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(419, 73, 3, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(420, 73, 4, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(421, 73, 5, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(422, 73, 7, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(423, 73, 8, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(424, 73, 9, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(425, 73, 10, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(426, 73, 11, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(427, 73, 12, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(428, 73, 13, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(429, 73, 14, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(430, 73, 6, NULL, '', '2024-04-24 07:07:20', 'N', NULL, '0000-00-00'),
(431, 74, 1, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(432, 74, 2, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(433, 74, 3, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(434, 74, 4, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(435, 74, 5, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(436, 74, 7, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(437, 74, 8, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(438, 74, 9, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(439, 74, 10, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(440, 74, 11, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(441, 74, 12, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(442, 74, 13, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(443, 74, 14, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(444, 74, 6, NULL, '', '2024-05-02 01:41:27', 'N', NULL, '0000-00-00'),
(445, 75, 1, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(446, 75, 2, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(447, 75, 3, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(448, 75, 4, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(449, 75, 5, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(450, 75, 7, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(451, 75, 8, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(452, 75, 9, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(453, 75, 10, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(454, 75, 11, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(455, 75, 12, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(456, 75, 13, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(457, 75, 14, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(458, 75, 6, NULL, '', '2024-05-03 02:40:31', 'N', NULL, '0000-00-00'),
(459, 76, 1, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(460, 76, 2, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(461, 76, 3, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(462, 76, 4, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(463, 76, 5, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(464, 76, 7, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(465, 76, 8, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(466, 76, 9, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(467, 76, 10, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(468, 76, 11, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(469, 76, 12, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(470, 76, 13, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(471, 76, 14, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(472, 76, 6, NULL, '', '2024-05-03 02:42:49', 'N', NULL, '0000-00-00'),
(473, 77, 1, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(474, 77, 2, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(475, 77, 3, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(476, 77, 4, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(477, 77, 5, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(478, 77, 7, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(479, 77, 8, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(480, 77, 9, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(481, 77, 10, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(482, 77, 11, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(483, 77, 12, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(484, 77, 13, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(485, 77, 14, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(486, 77, 6, NULL, '', '2024-05-03 03:06:34', 'N', NULL, '0000-00-00'),
(487, 78, 1, '2024-05-21 05:19:06', '', '2024-05-09 02:41:11', 'Y', NULL, '0000-00-00'),
(488, 78, 2, '2024-05-15 01:51:26', '', '2024-05-09 02:41:11', 'Y', NULL, '0000-00-00'),
(489, 78, 3, '2024-05-16 05:37:51', '', '2024-05-09 02:41:11', 'Y', NULL, '0000-00-00'),
(490, 78, 4, '2024-05-21 05:17:38', '', '2024-05-09 02:41:11', 'Y', NULL, '0000-00-00'),
(491, 78, 5, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(492, 78, 7, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(493, 78, 8, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(494, 78, 9, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(495, 78, 10, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(496, 78, 11, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(497, 78, 12, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(498, 78, 13, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(499, 78, 14, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(500, 78, 6, NULL, '', '2024-05-09 02:41:11', 'N', NULL, '0000-00-00'),
(501, 79, 1, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(502, 79, 2, '2024-05-21 05:14:58', '', '2024-05-21 05:11:49', 'Y', NULL, '0000-00-00'),
(503, 79, 3, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(504, 79, 4, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(505, 79, 5, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(506, 79, 7, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(507, 79, 8, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(508, 79, 9, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(509, 79, 10, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(510, 79, 11, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(511, 79, 12, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(512, 79, 13, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(513, 79, 14, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(514, 79, 6, NULL, '', '2024-05-21 05:11:49', 'N', NULL, '0000-00-00'),
(515, 80, 1, '2024-05-27 07:17:56', '', '2024-05-21 05:49:15', 'Y', NULL, '0000-00-00'),
(516, 80, 2, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(517, 80, 3, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(518, 80, 4, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(519, 80, 5, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(520, 80, 7, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(521, 80, 8, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(522, 80, 9, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(523, 80, 10, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(524, 80, 11, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(525, 80, 12, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(526, 80, 13, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(527, 80, 14, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00'),
(528, 80, 6, NULL, '', '2024-05-21 05:49:15', 'N', NULL, '0000-00-00');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_application_status`
--

CREATE TABLE `ggportal_tbl_application_status` (
  `application_status_id` int(11) NOT NULL,
  `status_name` varchar(45) NOT NULL,
  `status_code` varchar(45) NOT NULL,
  `date_count` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_application_status`
--

INSERT INTO `ggportal_tbl_application_status` (`application_status_id`, `status_name`, `status_code`, `date_count`) VALUES
(1, 'Application Received', ' AR', 30),
(2, 'Application Undereviewed ', ' AU', 23),
(3, 'Program Apply', ' PA', 14),
(4, 'Conditional Offer', ' CO', 7),
(5, 'Unconditional Offer', ' UO', 25),
(7, 'CAS/LOA Issued', ' CL', 30),
(8, 'VISA Granted', ' VG', 32),
(9, 'University Enrolled', ' UE', 32),
(10, 'Commission', ' C', 42),
(11, 'Refund', ' Rd', 42),
(12, 'Reappeal', ' Rl', 42),
(13, 'Differment', ' D', 10),
(14, ' Processing Completed', 'PC', 30),
(6, 'University Payment', 'UP', 25);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_bank`
--

CREATE TABLE `ggportal_tbl_bank` (
  `bank_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(255) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `account_no` varchar(100) NOT NULL,
  `bank_name` varchar(150) NOT NULL,
  `branch_name` varchar(150) NOT NULL,
  `branch_code` varchar(10) NOT NULL,
  `swift_code` varchar(15) NOT NULL,
  `bank_code` varchar(50) NOT NULL,
  `iban` varchar(50) NOT NULL,
  `is_active` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_bank`
--

INSERT INTO `ggportal_tbl_bank` (`bank_id`, `user_id`, `user_type`, `account_name`, `account_no`, `bank_name`, `branch_name`, `branch_code`, `swift_code`, `bank_code`, `iban`, `is_active`) VALUES
(97, 44, 'AG', 'test bank account', '123345', 'test bank', 'col 4', '', '', '', '', 'Y'),
(98, 45, 'AG', '343840937359375091', '***************', 'bvbhfhfhh', 'hfhdhdhd', 'hgdhdgh', 'dhfdhd54', 'hfdhfdh', '5345435', 'Y'),
(99, 46, 'AG', '343840937359375091', '***********', 'NSB', 'MATARA', '45', '', '456', '', 'Y'),
(100, 47, 'AG', '343840937359375091', '***********', 'bvbhfhfhh', 'hfhdhdhd', 'hgdhdgh', 'dhfdhd54', 'hfdhfdh', '', 'Y'),
(101, 48, 'AG', '343840937359375091', '***********', 'bvbhfhfhh', 'hfhdhdhd', 'hgdhdgh', 'dhfdhd54', 'hfdhfdh', '', 'Y'),
(102, 49, 'AG', '343840937359375091', '***********', 'bvbhfhfhh', 'hfhdhdhd', '', '', 'hfdhfdh', '', 'Y'),
(103, 50, 'AG', 'fixed', '********', 'BOC', 'mathara', '92020', '111', '111111', '', 'Y'),
(104, 51, 'AG', 'fixed', '********', 'BOC', 'mathara', '92020', '111', '111111', '111', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_city`
--

CREATE TABLE `ggportal_tbl_city` (
  `city_id` int(11) NOT NULL,
  `state_id` int(11) NOT NULL,
  `city_name` varchar(45) NOT NULL,
  `city_code` varchar(45) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_commission`
--

CREATE TABLE `ggportal_tbl_commission` (
  `commission_id` int(11) NOT NULL,
  `institute_id` int(11) NOT NULL,
  `rate` varchar(45) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_commission`
--

INSERT INTO `ggportal_tbl_commission` (`commission_id`, `institute_id`, `rate`) VALUES
(783, 1, '9'),
(782, 498, '8%'),
(781, 474, '5%'),
(780, 498, '8%'),
(779, 474, '5%'),
(533, 533, '0.078'),
(534, 534, 'USD 1790'),
(535, 535, 'USD 1750'),
(536, 536, '0.078'),
(537, 537, '0.078'),
(538, 538, '0.063'),
(539, 539, '0.07'),
(540, 540, 'USD 630'),
(541, 541, '0.063'),
(542, 542, 'USD 1890'),
(543, 543, 'USD 2500'),
(544, 544, '0.094'),
(545, 545, '0.126'),
(546, 546, 'USD 155'),
(547, 547, '0.094'),
(548, 548, 'USD 2000'),
(549, 549, 'USD 945'),
(550, 550, '0.094'),
(551, 551, '0.094'),
(552, 552, '0.063'),
(553, 553, 'USD 470'),
(554, 554, 'USD 1765'),
(555, 555, '0.063'),
(556, 556, '0.063'),
(557, 557, '0.063'),
(558, 558, '0.063'),
(559, 559, '0.063'),
(560, 560, 'USD 2205'),
(561, 561, '0.063'),
(562, 562, '0.063'),
(563, 563, 'USD 945'),
(564, 564, '0.063'),
(565, 565, 'USD 945'),
(566, 566, 'USD 945'),
(567, 567, '0.125'),
(568, 568, '0.063'),
(569, 569, '0.063'),
(570, 570, '0.0945'),
(571, 571, 'USD 440'),
(572, 572, 'USD 3780'),
(573, 573, '0.063'),
(574, 574, '0.125'),
(575, 575, 'USD 2835'),
(576, 576, 'USD 315'),
(577, 577, 'USD 1385'),
(578, 578, 'USD 2500'),
(579, 579, 'USD 630'),
(580, 580, '0.0945'),
(581, 581, '0.0945'),
(582, 582, '0.125'),
(583, 583, 'USD 315'),
(584, 584, 'USD 315'),
(585, 585, '0.0945'),
(586, 586, 'USD 1575'),
(587, 587, 'USD 1450'),
(588, 588, '0.063'),
(589, 589, '0.0945'),
(590, 590, 'USD 945'),
(591, 591, '0.063'),
(592, 592, 'USD 945'),
(593, 593, 'USD 630'),
(594, 594, '0.0945'),
(595, 595, 'USD 630'),
(596, 596, 'USD 945'),
(597, 597, 'USD 630'),
(598, 598, 'USD 945'),
(599, 599, 'USD 945'),
(600, 600, 'USD 12.5'),
(601, 601, 'USD 13.5'),
(602, 602, 'USD 945'),
(603, 603, '0.063'),
(604, 604, 'USD 1575'),
(605, 605, 'USD 945'),
(606, 606, 'USD 945'),
(607, 607, 'USD 1575'),
(608, 608, 'USD 945'),
(610, 610, 'USD 1260'),
(611, 611, 'USD 945'),
(612, 612, '0.094'),
(613, 613, 'USD 1070'),
(614, 614, 'USD 315'),
(615, 615, '0.063'),
(616, 616, '0.063'),
(617, 617, '0.063'),
(618, 618, '0.094'),
(619, 619, '0.094'),
(620, 620, 'USD 1385'),
(621, 621, 'USD 1575'),
(622, 622, '0.094'),
(623, 623, '0.063'),
(624, 624, 'USD 1575'),
(625, 625, 'USD 630'),
(626, 626, 'USD 1730'),
(627, 627, 'USD 440'),
(628, 628, 'USD 945'),
(629, 629, 'USD 9.4'),
(630, 630, '0.063'),
(631, 631, 'USD 630'),
(632, 632, '0.094'),
(633, 633, '0.094'),
(634, 634, '0.105'),
(635, 635, '0.063'),
(636, 636, 'USD 630'),
(637, 637, '0.094'),
(638, 638, 'USD 785'),
(639, 639, 'USD 1260'),
(640, 640, 'USD 1260'),
(641, 641, '0.05'),
(642, 642, '0.063'),
(643, 643, 'USD 630'),
(644, 644, 'USD 755'),
(645, 645, 'USD 1260'),
(646, 646, 'USD 945'),
(647, 647, '0.094'),
(648, 648, '0.075'),
(649, 649, 'USD 945'),
(650, 650, '0.125'),
(651, 651, '0.063'),
(652, 652, '0.063'),
(653, 653, '0.094'),
(654, 654, 'USD 1575'),
(655, 655, '0.03'),
(656, 656, '0.063'),
(657, 657, '0.063'),
(658, 658, 'USD 2500'),
(659, 659, 'USD 630'),
(660, 660, '0.094'),
(661, 661, '0.094'),
(662, 662, 'USD 2500'),
(663, 663, 'USD 785'),
(664, 664, 'USD 2700'),
(665, 665, 'USD 630'),
(666, 666, '0.063'),
(667, 667, '0.063'),
(668, 668, 'USD 630'),
(669, 669, 'USD 1890'),
(670, 670, '0.094'),
(671, 671, 'USD 630'),
(672, 672, 'USD 470'),
(673, 673, '0.063'),
(674, 674, '0.063'),
(675, 675, 'USD 945'),
(676, 676, 'USD 630'),
(677, 677, '0.063'),
(678, 678, '0.125'),
(679, 679, 'USD 375'),
(680, 680, 'USD 785'),
(681, 681, 'USD 945'),
(682, 682, 'USD 1575'),
(683, 683, 'USD 2500'),
(684, 684, 'USD 315'),
(685, 685, '0.094'),
(686, 686, 'USD 2500'),
(687, 687, 'USD 315'),
(688, 688, '0.063'),
(689, 689, 'USD 315'),
(690, 690, 'USD 880'),
(691, 691, 'USD 2500'),
(692, 692, '0.063'),
(693, 693, '0.063'),
(694, 694, '0.063'),
(695, 695, '0.063'),
(696, 696, '0.063'),
(697, 697, '0.063'),
(698, 698, '0.078'),
(699, 699, '0.063'),
(700, 700, '0.063'),
(701, 701, '0.094'),
(702, 702, '0.094'),
(703, 703, 'USD 785'),
(704, 704, '0.094'),
(705, 705, '0.094'),
(706, 706, '0.094'),
(707, 707, 'USD 2500'),
(708, 708, 'USD 2500'),
(709, 709, 'USD 2500'),
(710, 710, 'USD 2500'),
(711, 711, 'USD 2500'),
(712, 712, '0.094'),
(713, 713, '0.094'),
(714, 714, '0.094'),
(715, 715, 'USD 0'),
(716, 716, '0.094'),
(717, 717, '0.047'),
(718, 718, 'USD 2500'),
(719, 719, 'USD 2500'),
(720, 720, 'USD 2500'),
(721, 721, '0.094'),
(722, 722, 'USD 2500'),
(723, 723, 'USD 2500'),
(724, 724, 'USD 2500'),
(725, 725, 'USD 2500'),
(726, 726, 'USD 2500'),
(727, 727, 'USD 2500'),
(728, 728, '0.094'),
(729, 729, '0.094'),
(730, 730, 'USD 2500'),
(731, 731, 'USD 2500'),
(732, 732, 'USD 2500'),
(733, 733, 'USD 2500'),
(734, 734, '0.1'),
(735, 735, '0.07'),
(736, 736, 'USD 1700'),
(737, 737, 'USD 800'),
(738, 738, '0.1'),
(739, 739, 'USD 2500'),
(740, 740, 'USD 2500'),
(741, 741, 'USD 2500'),
(742, 742, 'USD 2500'),
(743, 743, 'USD 2500'),
(744, 744, 'USD 2500'),
(745, 745, 'USD 2500'),
(746, 746, 'USD 2500'),
(747, 747, 'USD 2500'),
(748, 748, 'USD 2500'),
(749, 749, 'USD 2500'),
(750, 750, 'USD 2500'),
(751, 751, 'USD 2500'),
(752, 752, 'USD 2500'),
(753, 753, 'USD 2500'),
(754, 754, 'USD 2500'),
(755, 755, 'USD 2500'),
(756, 756, 'USD 2500'),
(771, 474, '5%'),
(772, 498, '8%'),
(773, 474, '5%'),
(774, 498, '8%'),
(777, 474, '5%'),
(778, 498, '8%');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_content`
--

CREATE TABLE `ggportal_tbl_content` (
  `content_id` int(11) NOT NULL,
  `content_name` varchar(50) NOT NULL,
  `content_body` text NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_country`
--

CREATE TABLE `ggportal_tbl_country` (
  `country_id` int(11) NOT NULL,
  `country_name` varchar(45) NOT NULL,
  `country_code` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_country`
--

INSERT INTO `ggportal_tbl_country` (`country_id`, `country_name`, `country_code`) VALUES
(1, ' Australia', 'AU'),
(2, ' Canada', 'CA'),
(3, ' France', 'FRA'),
(4, ' Georgia', 'GA'),
(5, ' Germany', 'DE'),
(6, ' Ireland', 'IRL'),
(7, ' Italy', 'ITA'),
(8, ' Latvia', 'LV'),
(9, ' Lithuania', 'LT'),
(10, ' Malta', 'MT'),
(11, ' Mauritius', 'MU'),
(12, ' Netherlands', 'NL'),
(13, ' New Zealand', 'NZ'),
(14, ' Poland', 'PL'),
(15, ' Russia', 'RU'),
(16, ' Singapore', 'SG'),
(17, ' Switzerland', 'CH'),
(18, ' Ukraine', 'UA'),
(19, ' United Arab Emirates', 'UAE'),
(20, ' United Kingdom', 'UK'),
(21, ' United States', 'USA'),
(35, 'sri lanka', 'SL');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_course`
--

CREATE TABLE `ggportal_tbl_course` (
  `course_id` int(11) NOT NULL,
  `course_name` varchar(450) NOT NULL,
  `course_code` varchar(20) NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_course`
--

INSERT INTO `ggportal_tbl_course` (`course_id`, `course_name`, `course_code`, `active_yn`) VALUES
(1, 'Arts', 'tc', 'Y'),
(2, 'Law, Politics, Social, Community Service and Teaching', '', 'Y'),
(3, 'Health Sciences, Medicine, Nursing, Paramedic and Kinesiology', '', 'Y'),
(4, 'Sciences', '', 'Y'),
(5, 'Business, Management and Economics', '', 'Y'),
(6, 'Engineering and Technology', '', 'Y'),
(7, 'English for Academic Studies', '', 'Y'),
(8, 'Logistic', '', 'Y'),
(9, 'history', '', 'Y'),
(10, 'geogrophy', '', 'Y'),
(11, 'Supply Chain', '', 'Y'),
(12, 'English', '', 'Y'),
(13, 'AI', '', 'Y'),
(14, 'Applied science', '', 'Y'),
(15, 'Applied', '', 'Y'),
(16, 'Applied maths', '', 'Y'),
(17, 'Pure maths', '', 'Y'),
(18, 'Art', '', 'Y'),
(19, ' business', '', 'Y'),
(20, 'Sciense', '', 'Y'),
(21, 'Education & Law', '', 'Y'),
(22, 'Hospitality, Leisure & Sports', '', 'Y'),
(23, 'Journalism, Social Sciences & Professions', '', 'Y'),
(24, 'Medicine, Health & Bio Science', '', 'Y'),
(25, 'Arts, Design, Media & Architecture', '', 'Y'),
(26, 'Business & Management', '', 'Y'),
(27, 'Computer Science, Engineering & Technology', '', 'Y'),
(28, 'Environmental, Agriculture & Forestry', '', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_currency`
--

CREATE TABLE `ggportal_tbl_currency` (
  `currency_id` int(11) NOT NULL,
  `currency_name` varchar(45) NOT NULL,
  `currency_code` varchar(20) NOT NULL,
  `currency_symbol` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_currency`
--

INSERT INTO `ggportal_tbl_currency` (`currency_id`, `currency_name`, `currency_code`, `currency_symbol`) VALUES
(1, 'USD', 'USD $', '$'),
(2, 'CAD', 'CAD $', '$'),
(3, 'GBP', 'GBP £', '£'),
(4, 'AUD ', 'AUD $', '$'),
(5, 'NZD', 'NZD $', '$'),
(6, 'EUR', 'EUR €', '€');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_institute`
--

CREATE TABLE `ggportal_tbl_institute` (
  `institute_id` int(11) NOT NULL,
  `institute_name` varchar(180) NOT NULL,
  `institute_country_id` int(11) NOT NULL,
  `institute_type` varchar(180) NOT NULL,
  `institute_state` varchar(180) NOT NULL,
  `institute_city` varchar(45) NOT NULL,
  `web_url` varchar(450) NOT NULL,
  `logo_url` varchar(450) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_institute`
--

INSERT INTO `ggportal_tbl_institute` (`institute_id`, `institute_name`, `institute_country_id`, `institute_type`, `institute_state`, `institute_city`, `web_url`, `logo_url`, `email`, `mobile`) VALUES
(1, ' Academies Australasia Polytechnic', 1, 'college', 'Victoria', '', '', '', '', ''),
(2, ' Academy of Information Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(3, ' Airways Aviation', 1, 'college', 'Queensland', '', '', '', '', ''),
(4, ' Amber Aviation Academy', 1, 'college', 'Victoria', '', '', '', '', ''),
(5, ' APM College of Business and Communication', 1, 'college', 'New South Wales', '', '', '', '', ''),
(6, ' Australian College of Applied Psychology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(7, ' Australian Institute of Higher Education', 1, 'college', 'New South Wales', '', '', '', '', ''),
(8, ' Australian National University', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(9, ' Australian National University College', 1, 'college', 'Australian Capital Territory', '', '', '', '', ''),
(10, ' Bedford College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(11, ' Billy Blue College of Design', 1, 'college', 'New South Wales', '', '', '', '', ''),
(12, ' Blue Mountains International Hotel Management School', 1, 'college', 'New South Wales', '', '', '', '', ''),
(13, ' Bond University', 1, 'university', 'Queensland', '', '', '', '', ''),
(14, ' Box Hill Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(15, ' Cambridge International College', 1, 'college', 'Victoria', '', '', '', '', ''),
(16, ' Curtin College', 1, 'college', 'Western Australia', '', '', '', '', ''),
(17, ' Curtin University', 1, 'university', 'Western Australia', '', '', '', '', ''),
(18, ' Deakin College', 1, 'college', 'Victoria', '', '', '', '', ''),
(19, ' Deakin University', 1, 'university', 'Victoria', '', '', '', '', ''),
(20, ' Edith Cowan College', 1, 'college', 'Western Australia', '', '', '', '', ''),
(21, ' Edith Cowan University', 1, 'university', 'Western Australia', '', '', '', '', ''),
(22, ' Excelsia College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(23, ' Eynesbury College', 1, 'college', 'South Australia', '', '', '', '', ''),
(24, ' Federation University (ATMC College)', 1, 'college', 'Victoria', '', '', '', '', ''),
(25, ' Federation University', 1, 'university', 'Victoria', '', '', '', '', ''),
(26, ' Flinders International Study Centre', 1, 'college', 'South Australia', '', '', '', '', ''),
(27, ' Flinders University', 1, 'university', 'South Australia', '', '', '', '', ''),
(28, ' Griffith College', 1, 'college', 'Queensland', '', '', '', '', ''),
(29, ' Griffith University', 1, 'university', 'Queensland', '', '', '', '', ''),
(30, ' Global Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(31, ' Holmes Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(32, ' Holmesglen Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(33, ' IES College', 1, 'college', 'Queensland', '', '', '', '', ''),
(34, ' International College of Management Sydney.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(35, ' International Institute of Business & Information Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(36, ' James Cook University', 1, ' university', 'Queensland', '', '', '', '', ''),
(37, ' James Cook University', 1, ' university', 'Queensland', '', '', '', '', ''),
(38, ' King\'s Own Institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(39, ' Laneway International College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(40, ' La Trobe College', 1, 'college', 'Victoria', '', '', '', '', ''),
(41, ' La Trobe University', 1, 'university', 'New South Wales/Victoria', '', '', '', '', ''),
(42, ' Macquarie University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(43, ' Melbourne Polytechnic', 1, 'college', 'Victoria', '', '', '', '', ''),
(44, ' Melbourne Institute of Technology', 1, 'college', 'New South Wales', '', '', '', '', ''),
(45, ' Monash University', 1, 'university', 'Victoria', '', '', '', '', ''),
(46, ' Monash College', 1, 'college', 'Victoria', '', '', '', '', ''),
(47, ' Murdoch Institute of Technology', 1, 'college', 'Western Australia', '', '', '', '', ''),
(48, ' Queensland International Institute', 1, 'college', 'Queensland', '', '', '', '', ''),
(49, ' Queensland University of Technology', 1, 'university', 'Queensland', '', '', '', '', ''),
(50, ' RMIT University', 1, 'university', 'Victoria', '', '', '', '', ''),
(51, ' Russo Business School', 1, 'college', 'Queensland', '', '', '', '', ''),
(52, ' SAE creative media institute', 1, 'college', 'New South Wales', '', '', '', '', ''),
(53, ' Sarina Russo Institute', 1, 'college', 'Queensland', '', '', '', '', ''),
(54, ' Stott\'s College', 1, 'college', 'Victoria', '', '', '', '', ''),
(55, ' SIBT', 1, 'college', 'New South Wales', '', '', '', '', ''),
(56, ' South Australia Institute of Business & Technology', 1, 'college', 'South Australia', '', '', '', '', ''),
(57, ' Southern Cross University', 1, 'university', 'Queensland', '', '', '', '', ''),
(58, ' Stratfield College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(59, ' Swinburne University of Technology', 1, 'university', 'Victoria', '', '', '', '', ''),
(60, ' TAFE', 1, 'college', 'Queensland', '', '', '', '', ''),
(61, ' TAFE TASMANIA', 1, 'college', 'Tasmania', '', '', '', '', ''),
(62, ' TAFE SOUTH AUSTRALIA', 1, 'college', 'South Australia', '', '', '', '', ''),
(63, ' Taylors College', 1, ' perth college', 'New South Wales/Western Australia', '', '', '', '', ''),
(64, ' Trinity College', 1, 'college', 'Victoria', '', '', '', '', ''),
(65, ' The Hotel School', 1, 'college', 'New South Wales', '', '', '', '', ''),
(66, ' Torrens University Australia', 1, 'university', 'South Australia', '', '', '', '', ''),
(67, ' University of Adelaide', 1, 'university', 'South Australia', '', '', '', '', ''),
(68, ' University of Adelaide College', 1, 'college', 'South Australia', '', '', '', '', ''),
(69, ' University of Canberra', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(70, ' University of Canberra College', 1, 'college', 'Australian Capital Territory', '', '', '', '', ''),
(71, ' University of Melbourne', 1, 'university', 'Victoria', '', '', '', '', ''),
(72, ' University of New England', 1, 'university', 'New South Wales', '', '', '', '', ''),
(73, ' University of New South Wales', 1, 'university', 'New South Wales', '', '', '', '', ''),
(74, ' University of Newcastle', 1, 'university', 'New South Wales', '', '', '', '', ''),
(75, ' University of Queensland', 1, 'university', 'Queensland', '', '', '', '', ''),
(76, ' University of South Australia', 1, 'university', 'South Australia', '', '', '', '', ''),
(77, ' University of Southern Queensland', 1, 'university', 'New South Wales', '', '', '', '', ''),
(78, ' University of Sunshine Coast (ATMC College)', 1, 'university', 'Victoria', '', '', '', '', ''),
(79, ' University of Sydney', 1, 'university', 'New South Wales', '', '', '', '', ''),
(80, ' University of Tasmania', 1, 'university', 'Tasmania', '', '', '', '', ''),
(81, ' University of Technology', 1, 'university', 'New South Wales', '', '', '', '', ''),
(82, ' University of the Sunshine Coast', 1, 'university', 'New South Wales', '', '', '', '', ''),
(83, ' University of Western Australia', 1, 'university', 'Western Australia', '', '', '', '', ''),
(84, ' University of Wollongong', 1, 'university', 'New South Wales', '', '', '', '', ''),
(85, ' UTS Insearch', 1, 'college', 'New South Wales', '', '', '', '', ''),
(86, ' Victoria University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(87, ' Victoria University', 1, 'university', 'Victoria', '', '', '', '', ''),
(88, ' Western Sydney University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(89, ' Western Sydney University International College', 1, 'college', 'New South Wales', '', '', '', '', ''),
(90, ' William Angliss Institute', 1, 'college', 'Victoria', '', '', '', '', ''),
(91, ' Le Cordon Bleu', 1, 'college', 'South Australia', '', '', '', '', ''),
(92, ' Newcastle International College.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(93, ' Sydney Institute of Business & Technology.', 1, 'college', 'New South Wales', '', '', '', '', ''),
(94, ' TAFE International Western Australia.', 1, 'college', 'Western Australia', '', '', '', '', ''),
(95, ' Taylors College', 1, ' college', 'New South Wales', '', '', '', '', ''),
(96, ' Asia Pacific International College', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(97, ' Australian Catholic University', 1, 'university', 'Australian Capital Territory', '', '', '', '', ''),
(98, ' Charles Sturt University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(99, ' Central Queensland University', 1, 'university', 'New South Wales', '', '', '', '', ''),
(100, ' Engineering Institute of Technology.', 1, 'college', 'Western Australia', '', '', '', '', ''),
(101, ' Acadia University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(102, ' Acsenda School of Management', 2, 'college', 'British Columbia', '', '', '', '', ''),
(103, ' Adler University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(104, ' Alexander College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(105, ' Algonquin College', 2, 'college', 'Ontario', '', '', '', '', ''),
(106, ' Assiniboine Community College', 2, 'college', 'Manitoba', '', '', '', '', ''),
(107, ' Avalon College', 2, 'college', 'Quebec', '', '', '', '', ''),
(108, ' Aviron Technical Institute', 2, 'college', 'Quebec', '', '', '', '', ''),
(109, ' Brighton College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(110, ' British Columbia Institute of Technology', 2, 'college', 'British Columbia', '', '', '', '', ''),
(111, ' Brock University', 2, 'university', 'Ontario', '', '', '', '', ''),
(112, ' Bow Valley College', 2, 'college', 'Alberta', '', '', '', '', ''),
(113, ' Cambrian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(114, ' Cambrian College at Hanson', 2, 'college', 'Ontario', '', '', '', '', ''),
(115, ' Canada College', 2, 'college', 'Quebec', '', '', '', '', ''),
(116, ' Canadian College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(117, ' Canadore College', 2, 'college', 'Ontario', '', '', '', '', ''),
(118, ' Cape Breton University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(119, ' Capilano University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(120, ' CCSQ', 2, 'college', 'Quebec', '', '', '', '', ''),
(121, ' CDI College', 2, 'college', 'Quebec', '', '', '', '', ''),
(122, ' Cegep de la Gaspesie et des Iles', 2, 'college', 'Quebec', '', '', '', '', ''),
(123, ' Cegep Marie Victorin', 2, 'college', 'Quebec', '', '', '', '', ''),
(124, ' Centennial College', 2, 'college', 'Ontario', 'Toronto', 'www.centennialcollege.ca', 'dist/uploads/institute/1715852331.jpeg', '-', '-'),
(125, ' College of New Caledonia', 2, 'college', 'British Columbia', '', '', '', '', ''),
(126, ' College of the Rockies', 2, 'college', 'British Columbia', '', '', '', '', ''),
(127, ' College Multihexa', 2, 'college', 'Quebec', '', '', '', '', ''),
(128, ' College St. Michel', 2, 'college', 'Quebec', '', '', '', '', ''),
(129, ' College Universel', 2, 'college', 'Quebec', '', '', '', '', ''),
(130, ' Columbia College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(131, ' Conestoga College', 2, 'college', 'Ontario', '', '', '', '', ''),
(132, ' Confederation College', 2, 'college', 'Ontario', '', '', '', '', ''),
(133, ' Crandall University', 2, 'university', 'NewBrunswick', '', '', '', '', ''),
(134, ' Dalhousie University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(135, ' Douglas College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(136, ' Durham College', 2, 'college', 'Ontario', '', '', '', '', ''),
(137, ' Evergreen College', 2, 'college', 'Ontario', '', '', '', '', ''),
(138, ' Fairleigh Dickinson University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(139, ' Fanshawe College', 2, 'college', 'Ontario', '', '', '', '', ''),
(140, ' Fleming College', 2, 'college', 'Ontario', '', '', '', '', ''),
(141, ' Focus College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(142, ' George Brown College', 2, 'college', 'Ontario', '', '', '', '', ''),
(143, ' Georgian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(144, ' Great Plains College', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(145, ' Humber College', 2, 'college', 'Ontario', '', '', '', '', ''),
(146, ' Kwantlen Polytechnic University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(147, ' Langara College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(148, ' Lakehead University', 2, 'university', 'Ontario', '', '', '', '', ''),
(149, ' Lakeland College', 2, 'college', 'Alberta', '', '', '', '', ''),
(150, ' Lambton College', 2, 'college', 'Ontario', '', '', '', '', ''),
(151, ' M College', 2, 'college', 'Quebec', '', '', '', '', ''),
(152, ' MacEwan University', 2, 'university', 'Alberta', '', '', '', '', ''),
(153, ' Manitoba Institute of Trades and Technology', 2, 'college', 'Manitoba', '', '', '', '', ''),
(154, ' Medicine Hat College', 2, 'university', 'Alberta', '', '', '', '', ''),
(155, ' Memorial University of Newfoundland', 2, 'university', 'New Foundland', '', '', '', '', ''),
(156, ' Mount Allison University', 2, 'university', 'NewBrunswick', '', '', '', '', ''),
(157, ' Mount Saint Vincent University', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(158, ' New York Institute of Technology', 2, 'college', 'British Columbia', '', '', '', '', ''),
(159, ' Niagara College', 2, 'college', 'Ontario', '', '', '', '', ''),
(160, ' Nipissing University', 2, 'university', 'Ontario', '', '', '', '', ''),
(161, ' NorQuest College', 2, 'college', 'Alberta', '', '', '', '', ''),
(162, ' North Island College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(163, ' Northern College', 2, 'college', 'Ontario', '', '', '', '', ''),
(164, ' Northern College at Pures-Toronto', 2, 'college', 'Ontario', '', '', '', '', ''),
(165, ' Northern Lights College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(166, ' Parkland College', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(167, ' Royal Roads University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(168, ' Saskatchewan Polytechnic', 2, 'college', 'Saskatchewan', '', '', '', '', ''),
(169, ' Sault College', 2, 'college', 'Ontario', '', '', '', '', ''),
(170, ' Selkirk College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(171, ' Seneca College', 2, 'college', 'Ontario', '', '', '', '', ''),
(172, ' Seneca International Academy', 2, 'college', 'Ontario', '', '', '', '', ''),
(173, ' Sheridan College', 2, 'college', 'Ontario', '', '', '', '', ''),
(174, ' Simon Fraser University through Fraser International College', 2, 'university', 'British Columbia', '', '', '', '', ''),
(175, ' Sprott Shaw College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(176, ' St. Lawrence College', 2, 'college', 'Ontario', '', '', '', '', ''),
(177, ' Stenberg College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(178, ' Thompson Rivers University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(179, ' Toronto Film School', 2, 'university', 'Ontario', '', '', '', '', ''),
(180, ' Trent University', 2, 'university', 'Ontario', '', '', '', '', ''),
(181, ' Trinity Western University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(182, ' University Canada West', 2, 'university', 'British Columbia', '', '', '', '', ''),
(183, ' University of Guelph', 2, 'university', 'Ontario', '', '', '', '', ''),
(184, ' University of Lethbridge', 2, 'university', 'Alberta', '', '', '', '', ''),
(185, ' University of Manitoba', 2, 'university', 'Manitoba', '', '', '', '', ''),
(186, ' University of Manitoba through International College of Manitoba', 2, 'college', 'Manitoba', '', '', '', '', ''),
(187, ' University of Prince Edward Island', 2, 'university', 'Prince Edward', '', '', '', '', ''),
(188, ' University of Regina', 2, 'university', 'Saskatchewan', '', '', '', '', ''),
(189, ' University of Saskatchewan', 2, 'university', 'Saskatchewan', '', '', '', '', ''),
(190, ' University of the Fraser Valley', 2, 'university', 'British Columbia', '', '', '', '', ''),
(191, ' University of Victoria', 2, 'university', 'British Columbia', '', '', '', '', ''),
(192, ' University of Waterloo', 2, 'university', 'Ontario', '', '', '', '', ''),
(193, ' University of Windsor', 2, 'university', 'Ontario', '', '', '', '', ''),
(194, ' University of Winnipeg', 2, 'university', 'Manitoba', '', '', '', '', ''),
(195, ' Vancouver Community College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(196, ' Vancouver Film School', 2, 'college', 'British Columbia', '', '', '', '', ''),
(197, ' Vancouver Island University', 2, 'university', 'British Columbia', '', '', '', '', ''),
(198, ' Wilfrid Laurier University', 2, 'university', 'Ontario', '', '', '', '', ''),
(199, ' York University', 2, 'university', 'Ontario', '', '', '', '', ''),
(200, ' Yukon University', 2, 'university', 'Yukon', '', '', '', '', ''),
(201, ' Brandon University', 2, 'university', 'Manitoba', '', '', '', '', ''),
(202, ' Coquitlam College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(203, ' Academy of Art University at Langara College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(204, ' CDE College', 2, 'college', 'Quebec', '', '', '', '', ''),
(205, ' Eton College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(206, ' ISI College', 2, 'college', 'Quebec', '', '', '', '', ''),
(207, ' Kings College (University of Western Ontario)', 2, 'university', 'Ontario', '', '', '', '', ''),
(208, ' Lakehead University through Georgian College', 2, 'college', 'Ontario', '', '', '', '', ''),
(209, ' LaSalle College', 2, 'college', 'Quebec/British Columbia', '', '', '', '', ''),
(210, ' Le Cordon Bleu', 2, 'college', 'Ontario', '', '', '', '', ''),
(211, ' Loyalist College', 2, 'college', 'Ontario', '', '', '', '', ''),
(212, ' Matrix College', 2, 'college', 'Quebec', '', '', '', '', ''),
(213, ' Montreal College of Information Technology', 2, 'college', 'Quebec', '', '', '', '', ''),
(214, ' Oulton College', 2, 'college', 'New Brunswick', '', '', '', '', ''),
(215, ' Pacific Link College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(216, ' Queens College of Business', 2, '  college', 'Ontario', '', '', '', '', ''),
(217, ' St. Thomas University', 2, 'university', 'New Brunswick', '', '', '', '', ''),
(218, ' The Kings University', 2, 'university', 'Alberta', '', '', '', '', ''),
(219, ' University of New Brunswick', 2, 'university', 'New Brunswick', '', '', '', '', ''),
(220, ' University of Northern British Columbia', 2, 'university', 'British Columbia', '', '', '', '', ''),
(221, ' Vancouver Film School', 2, 'college', 'British Columbia', '', '', '', '', ''),
(222, ' Yukon College', 2, 'college', 'Yukon', '', '', '', '', ''),
(223, ' Coast Mountain College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(224, ' CodeCore Collge', 2, 'college', 'British Columbia', '', '', '', '', ''),
(225, ' SAIT College', 2, 'college', 'Alberta', '', '', '', '', ''),
(226, ' St. Clair College', 2, 'college', 'Ontario', '', '', '', '', ''),
(227, ' St.Lawrence Alpha', 2, 'college', 'Ontario', '', '', '', '', ''),
(228, ' Okanagan College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(229, ' St.Francis Xavier university', 2, 'university', 'NovaScotia', '', '', '', '', ''),
(230, ' Red River College', 2, 'college', 'Manitoba', '', '', '', '', ''),
(231, ' ST.MARY UNIVERSITY', 2, 'university', 'Alberta', '', '', '', '', ''),
(232, ' SUNVIEW COLLEGE', 2, 'college', 'Ontario', '', '', '', '', ''),
(233, ' College National of Science & Technology', 2, 'college', 'Quebec', '', '', '', '', ''),
(234, ' Toronto School of Management', 2, 'college', 'Ontario', '', '', '', '', ''),
(235, ' Trebas Institute', 2, 'college', 'Quebec', '', '', '', '', ''),
(236, ' Kensley college', 2, 'college', 'Montreal', '', '', '', '', ''),
(237, ' Insignia College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(238, ' Kensley College', 2, 'college', 'Quebec', '', '', '', '', ''),
(239, ' Keyano College', 2, 'college', 'Alberta', '', '', '', '', ''),
(240, ' Cambria College', 2, 'college', 'British Columbia', '', '', '', '', ''),
(241, ' Northeastern University', 2, ' toronto university', 'Ontario', '', '', '', '', ''),
(242, ' Red Deer College', 2, 'college', 'Alberta', '', '', '', '', ''),
(243, ' CDC Pont-Viau', 2, 'college', 'Quebec', '', '', '', '', ''),
(244, ' Q College', 2, 'college', 'Washington', '', '', '', '', ''),
(245, ' University of Leithbridge', 2, 'university', 'Alberta', '', '', '', '', ''),
(246, ' Niagara College Toronto', 2, 'college', 'Ontario', '', '', '', '', ''),
(247, ' ISC-Business School', 3, 'college', 'Paris', '', '', '', '', ''),
(248, ' Neoma Business School', 3, ' rouen college', 'Paris', '', '', '', '', ''),
(249, ' ESC Clermont Business School', 3, 'college', 'Puy-de-Dome', '', '', '', '', ''),
(250, ' Leonard de Vinci', 3, 'university', 'Puy-de-Dome', '', '', '', '', ''),
(251, ' Skema Business School', 3, 'college', 'North Carolina', '', '', '', '', ''),
(252, ' IPAG Business School', 3, 'college', 'Paris', '', '', '', '', ''),
(253, ' Ilia state University', 4, 'university', 'Tbilisi', '', '', '', '', ''),
(254, ' ISET - International School of Economics at TSU', 4, 'college', 'Tbilisi', '', '', '', '', ''),
(255, ' University of applied science', 5, 'university', 'Hamburg', '', '', '', '', ''),
(256, ' BSBI', 5, 'college', 'Berlin', '', '', '', '', ''),
(257, ' GISMA Business School', 5, 'college', 'Hannover', '', '', '', '', ''),
(258, ' IUBH University of Applied Sciences', 5, 'university', 'Bavaria', '', '', '', '', ''),
(259, ' Steinbeis University Steinbeis School Of Management And Innovation', 5, 'college', 'Berlin', '', '', '', '', ''),
(260, ' SRH Hochschule University', 5, 'university', 'Berlin', '', '', '', '', ''),
(261, ' University of Europe for Applied Sciences', 5, 'university', 'Hamburg', '', '', '', '', ''),
(262, ' Arden University', 5, 'university', 'Berlin', '', '', '', '', ''),
(263, ' LYIT', 6, 'university', 'Ulster', '', '', '', '', ''),
(264, ' Dublin Business School', 6, 'college', 'Dublin', '', '', '', '', ''),
(265, ' Athlone Institute of Technology (AIT)', 6, 'college', 'Westmeath', '', '', '', '', ''),
(266, ' National College of Ireland', 6, 'college', 'Dublin', '', '', '', '', ''),
(267, ' Griffith College', 6, 'college', 'Dublin', '', '', '', '', ''),
(268, ' Maynooth University', 6, 'university', 'Maynooth', '', '', '', '', ''),
(269, ' Trinity College Dublin', 6, 'college', 'Dublin', '', '', '', '', ''),
(270, ' University College Dublin', 6, 'college', 'Dublin', '', '', '', '', ''),
(271, ' National University of Ireland Galway', 6, 'university', 'Galway', '', '', '', '', ''),
(272, ' University College Cork', 6, 'university', 'Cork', '', '', '', '', ''),
(273, ' Dublin City University', 6, 'university', 'Dublin', '', '', '', '', ''),
(274, ' University of Limerick', 6, 'university', 'Limerick', '', '', '', '', ''),
(275, ' Technological University Dublin', 6, 'university', 'Dublin', '', '', '', '', ''),
(276, ' Dundalk Institute of Technology', 6, 'college', 'Donegal', '', '', '', '', ''),
(277, ' Institute of Technology Carlow', 6, 'college', 'Carlow', '', '', '', '', ''),
(278, ' Limerick Institute of Technology', 6, 'college', 'Limerick', '', '', '', '', ''),
(279, ' Letterkenny Institute of Technology', 6, 'college', 'Kilkenny', '', '', '', '', ''),
(280, ' Waterford Institute of Technology', 6, 'college', 'Waterford', '', '', '', '', ''),
(281, ' Cork Institute of Technology', 6, 'college', 'Cork', '', '', '', '', ''),
(282, ' University of Padua', 7, 'university', 'Padova', '', '', '', '', ''),
(283, ' Transport and Telecommunication Institute', 8, 'college', 'Riga', '', '', '', '', ''),
(284, ' Kazimieras Simonavicius University(KSU)', 9, 'university', 'Vilniaus', '', '', '', '', ''),
(285, ' EIE', 10, 'college', 'Valletta', '', '', '', '', ''),
(286, ' College of Logistics and Management Studies', 10, 'college', 'Western', '', '', '', '', ''),
(287, ' St Edward\'s College', 10, 'college', 'Valletta', '', '', '', '', ''),
(288, ' Stephen Business School', 11, 'college', 'Port Louis', '', '', '', '', ''),
(289, ' Holland International Study Centre', 12, 'college', 'Noord-Holland', '', '', '', '', ''),
(290, ' Cornell Institute of Business & Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(291, ' New Zealand School of Education (NZSE)', 13, 'college', 'Auckland', '', '', '', '', ''),
(292, ' Pacific International Hotel Management School', 13, 'college', 'New Plymouth', '', '', '', '', ''),
(293, ' UUNZ Institute of Business', 13, 'college', 'Auckland', '', '', '', '', ''),
(294, ' Ara Institute of Canterbury', 13, 'college', 'Christchurch', '', '', '', '', ''),
(295, ' Auckland University of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(296, ' Lincoln University', 13, 'university', 'Christchurch', '', '', '', '', ''),
(297, ' Western Institute of Technology at Taranaki (WITT)', 13, 'college', 'New Plymouth', '', '', '', '', ''),
(298, ' EDENZ Colleges', 13, 'college', 'Auckland', '', '', '', '', ''),
(299, ' Otago Polytechnic', 13, 'college', 'Auckland/Dunedin', '', '', '', '', ''),
(300, ' University of Canterbury', 13, 'university', 'Christchurch', '', '', '', '', ''),
(301, ' Avonmore Tertiary Institute', 13, 'college', 'Christchurch/ Auckland', '', '', '', '', ''),
(302, ' Manukau Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(303, ' Aspire2International Group', 13, 'college', 'Auckland', '', '', '', '', ''),
(304, ' Toi Ohomai Institute of Technology', 13, 'college', 'North Island', '', '', '', '', ''),
(305, ' Wellington Institute of Technology (WelTec)', 13, 'college', 'Wellington', '', '', '', '', ''),
(306, ' North Tec', 13, 'college', 'Auckland', '', '', '', '', ''),
(307, ' Nelson & Marlborough Institute of Technology', 13, 'college', 'Nelson/ Richmond/Blenheim', '', '', '', '', ''),
(308, ' Whitireia New Zealand', 13, 'college', 'Porirua/Aucklan', '', '', '', '', ''),
(309, ' Southern Institute of Technology', 13, 'college', 'Invercargill/ Queenstown/ Christchurch/ Gore/ Auckland/ Otanomono', '', '', '', '', ''),
(310, ' Auckland Institute of Studies', 13, 'college', 'Auckland', '', '', '', '', ''),
(311, ' Eastern Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(312, ' University of Waikato', 13, 'university', 'Hamilton', '', '', '', '', ''),
(313, ' Victoria University of Wellington', 13, 'university', 'Wellington', '', '', '', '', ''),
(314, ' Whitecliff College of Arts and Design/Fashion and Sustainability /Technology and Innovation', 13, 'college', 'Auckland', '', '', '', '', ''),
(315, ' ATMC NZ- Nelson Marlborough Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(316, ' New Zealand Airline Academy', 13, 'college', 'Oamaru', '', '', '', '', ''),
(317, ' Kauri Academy', 13, 'college', 'Auckland', '', '', '', '', ''),
(318, ' The University of AUCKLAND', 13, 'university', 'Auckland', '', '', '', '', ''),
(319, ' AUT International House', 13, 'college', 'Auckland', '', '', '', '', ''),
(320, ' AUT University', 13, 'university', 'Auckland', '', '', '', '', ''),
(321, ' Massey University', 13, 'university', 'Auckland', '', '', '', '', ''),
(322, ' Waikato University', 13, 'university', 'Hamilton', '', '', '', '', ''),
(323, ' Waikato Institute of Technology', 13, 'college', 'Hamilton', '', '', '', '', ''),
(324, ' University of Canterbury International College', 13, 'college', 'Christchurch', '', '', '', '', ''),
(325, ' Kaplan International English', 13, 'college', 'Auckland', '', '', '', '', ''),
(326, ' Yoobee Colleges Limited', 13, 'college', 'Auckland', '', '', '', '', ''),
(327, ' New Zealand Management Academies (NZMA)', 13, 'college', 'Auckland', '', '', '', '', ''),
(328, ' New Zealand School of Tourism', 13, 'college', 'Auckland', '', '', '', '', ''),
(329, ' North Shore International Academy', 13, 'college', 'Auckland', '', '', '', '', ''),
(330, ' New Zealand Institute of Sport', 13, 'college', 'Auckland', '', '', '', '', ''),
(331, ' The New Zealand College of Massage', 13, 'college', 'Auckland', '', '', '', '', ''),
(332, ' Otago University', 13, 'university', 'Dunedin', '', '', '', '', ''),
(333, ' Unitec Institute of Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(334, ' Northland Polytechnic', 13, 'college', 'Whangarei', '', '', '', '', ''),
(335, ' New Zealand Tertiary College', 13, 'college', 'Christchurch/ Auckland', '', '', '', '', ''),
(336, ' Newton College of Business and Technology', 13, 'college', 'Auckland', '', '', '', '', ''),
(337, ' Lazarski University', 14, 'university', 'Warszawa', '', '', '', '', ''),
(338, ' Dagestan State Medical University', 15, 'university', 'Adygeja', '', '', '', '', ''),
(339, ' Pirogov Russian National Research Medical University', 15, 'university', 'Moscow', '', '', '', '', ''),
(340, ' Chechen state Medical university', 15, 'university', 'Chechenija', '', '', '', '', ''),
(341, ' Kabardino-Balkarian State University', 15, 'university', 'Kabardino-Balkarija', '', '', '', '', ''),
(342, ' Peoples\' Friendship University of Russia', 15, 'university', 'Moscow', '', '', '', '', ''),
(343, ' First Moscow State Medical University', 15, 'university', 'Moscow', '', '', '', '', ''),
(344, ' First Moscow State Medical University', 15, 'university', 'Tatarstan', '', '', '', '', ''),
(345, ' Pskov State University', 15, 'university', 'Pskov', '', '', '', '', ''),
(346, ' Saint Petersburg State University', 15, 'university', 'Sankt-Peterburg', '', '', '', '', ''),
(347, ' Tula State Pedagogical University', 15, 'university', 'Tula', '', '', '', '', ''),
(348, ' Rybinsk State Aviation Technical University', 15, 'university', 'Jaroslavl', '', '', '', '', ''),
(349, ' Kazan National Research Technical University named after A.N.Tupolev', 15, 'university', 'Tatarstan', '', '', '', '', ''),
(350, ' Grozny State Oil Technical University named after M.D. Millionshikova', 15, 'university', 'Chechenija', '', '', '', '', ''),
(351, ' Tomsk Polytechnic University', 15, 'university', 'Tomsk', '', '', '', '', ''),
(352, ' Moscow Aviation Institute', 15, 'university', 'Idaho', '', '', '', '', ''),
(353, ' Moscow Institute of Physics and Technology', 15, 'university', 'Idaho', '', '', '', '', ''),
(354, ' Saint Petersburg State University of Industrial Technologies and Design', 15, 'university', 'Sankt-Peterburg', '', '', '', '', ''),
(355, ' Ural State University of Economics', 15, 'university', 'Sverdlovsk', '', '', '', '', ''),
(356, ' Admiral Ushakov State Maritime University', 15, 'university', 'Krasnodar', '', '', '', '', ''),
(357, ' International European University', 15, 'university', 'Kyyiv', '', '', '', '', ''),
(358, ' London School of Business & Finance', 16, 'singapore college', 'Singapore', '', '', '', '', ''),
(359, ' HTMI', 17, 'college', 'Lucerne', '', '', '', '', ''),
(360, ' South Ukrainian National Pedagogical University named after K.D. Ushynsky', 18, 'university', 'Odessa', '', '', '', '', ''),
(361, ' Kharkiv National Medical University', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(362, ' V.N Karazin Kharkiv National University', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(363, ' Kiev Medical University', 18, 'university', 'Kyyiv', '', '', '', '', ''),
(364, ' Poltava state Medical University', 18, 'university', 'Poltavs\'ka', '', '', '', '', ''),
(365, ' Kharkiv university of airforce', 18, 'university', 'Kharkiv', '', '', '', '', ''),
(366, ' Heriot Watt University', 19, 'university', 'Dubai', '', '', '', '', ''),
(367, ' Aberystwyth University', 20, 'university', 'Wales', '', '', '', '', ''),
(368, ' Anglia Ruskin University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(369, ' Anglia Ruskin University College', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(370, ' Aston University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(371, ' Bangor University', 20, 'university', 'Wales', '', '', '', '', ''),
(372, ' Birmingham City University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(373, ' Bournemouth University', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(374, ' BPP University', 20, 'university', 'London', '', '', '', '', ''),
(375, ' Brunel University London', 20, 'university', 'London', '', '', '', '', ''),
(376, ' Cardiff Metropolitan University', 20, 'university', 'Wales', '', '', '', '', ''),
(377, ' Cardiff University International Study Centre', 20, 'college', 'Wales', '', '', '', '', ''),
(378, ' Coventry University London International Study Centre', 20, 'college', 'London', '', '', '', '', ''),
(379, ' Coventry University Coventry Campus', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(380, ' Coventry University London Campus', 20, 'university', 'London', '', '', '', '', ''),
(381, ' Cranfield University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(382, ' De MontFort University', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(383, ' Edinburgh Napier University', 20, 'university', 'Scotland', '', '', '', '', ''),
(384, ' Glasgow Caledonian University', 20, 'university', 'Scotland', '', '', '', '', ''),
(385, ' Heriot Watt University', 20, 'university', 'Scotland', '', '', '', '', ''),
(386, ' Hult International Business School', 20, 'college', 'London', '', '', '', '', ''),
(387, ' Instituto Marangoni', 20, 'college', 'London', '', '', '', '', ''),
(388, ' INTO Glasgow Caledonian University', 20, 'university', 'Scotland', '', '', '', '', ''),
(389, ' INTO City University', 20, 'university', 'London', '', '', '', '', ''),
(390, ' INTO London', 20, 'university', 'London', '', '', '', '', ''),
(391, ' INTO Manchester Metropolitan University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(392, ' INTO Newcastle University', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(393, ' INTO Queen\'s University Belfast', 20, 'university', 'Northern Ireland', '', '', '', '', ''),
(394, ' INTO University of East of Anglia', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(395, ' INTO University of Exeter', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(396, ' INTO University of Gloucestershire', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(397, ' INTO University of Manchester', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(398, ' Kaplan- Bournemouth University', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(399, ' Kaplan International College London', 20, 'college', 'London', '', '', '', '', ''),
(400, ' Kaplan- Nottingham Trent International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(401, ' Kaplan- Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(402, ' Kaplan- University of Essex International College', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(403, ' Kaplan- University of Glasgow International College', 20, 'college', 'Scotland', '', '', '', '', ''),
(404, ' Kaplan- University of Liverpool International College', 20, 'college', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(405, ' Kaplan- University of Nottingham International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(406, ' Kaplan- UWE Bristol', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(407, ' Kaplan-University of Brighton', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(408, ' Kaplan-University of York', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(409, ' Kingston University', 20, 'university', 'London', '', '', '', '', ''),
(410, ' Le - Cordon Bleu', 20, 'college', 'London', '', '', '', '', ''),
(411, ' Leeds Beckett University', 20, 'university', 'WEST YORKSHIRE ENGLAND', '', '', '', '', ''),
(412, ' Liverpool Hope University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(413, ' London Metropolitan University', 20, 'university', 'London', '', '', '', '', ''),
(414, ' London South Bank University', 20, 'university', 'London', '', '', '', '', ''),
(415, ' Manchester Metropolitan University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(416, ' Middlesex University', 20, 'university', 'London', '', '', '', '', ''),
(417, ' Navitas University of Leicester Global Study Centre', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(418, ' Navitas Birmingham City University- International College', 20, 'college', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(419, ' Navitas Hertfordshire International Colleage', 20, 'college', 'EAST OF ENGLAND', '', '', '', '', ''),
(420, ' Navitas Lancaster University', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(421, ' Navitas London Brunel International College', 20, 'college', 'London', '', '', '', '', ''),
(422, ' Navitas- Robert Gordon University', 20, 'university', 'Scotland', '', '', '', '', ''),
(423, ' Navitas University of International College Portsmouth', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(424, ' Navitas University of Northampton International College', 20, 'college', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(425, ' Navitas University of Plymouth International College', 20, 'college', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(426, ' Newcastle University', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(427, ' Northumbria University London Campus', 20, 'university', 'London', '', '', '', '', ''),
(428, ' Northumbria University Newcastle Campus', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(429, ' Nottingham Trent University', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(430, ' OnCampus Coventry University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(431, ' OnCampus Goldsmiths University of London', 20, 'university', 'London', '', '', '', '', ''),
(432, ' OnCampus University of Central Lancashire', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(433, ' OnCampus University of Hull', 20, 'university', 'YORKSHIRE AND THE HUMBER ENGLA', '', '', '', '', ''),
(434, ' OnCampus University of Reading', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(435, ' OnCampus Birkbeck University of London', 20, 'university', 'London', '', '', '', '', ''),
(436, ' Oncampus Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(437, ' Oxford Brookes University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(438, ' QAHE - Northumbria University', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(439, ' QAHE - Solent University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(440, ' Queen Mary University of London', 20, 'university', 'London', '', '', '', '', ''),
(441, ' Queen\'s University Belfast', 20, 'university', 'Northern Ireland', '', '', '', '', ''),
(442, ' Regent\'s University London', 20, 'university', 'London', '', '', '', '', ''),
(443, ' Royal Holloway University of London', 20, 'university', 'London', '', '', '', '', ''),
(444, ' Sheffield Hallam University', 20, 'university', 'SOUTH YORKSHIRE ENGLAND', '', '', '', '', ''),
(445, ' Solent University', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(446, ' Staffordshire University', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(447, ' Study Group - University of Surrey International Study Centre', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(448, ' Study Group- Bellerbys College Brighton', 20, 'college', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(449, ' Study Group- Durham University Durham', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(450, ' Study Group- Lancaster University Lancaster', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(451, ' Study Group- Leeds Beckett University Leeds', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(452, ' Study Group- Liverpool John Moores University Liverpool', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(453, ' Study Group- The University of Sheffield International College', 20, 'college', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(454, ' Study Group University of Huddersfield', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(455, ' Study Group- University of Lincoln Lincoln', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(456, ' Study Group- University of Sussex Falmer', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(457, ' Swansea University', 20, 'university', 'Wales', '', '', '', '', ''),
(458, ' Teesside University', 20, 'university', 'NORTH YORKSHIRE ENGLAND', '', '', '', '', ''),
(459, ' The College Swansea University', 20, 'university', 'Wales', '', '', '', '', ''),
(460, ' The University of Huddersfield', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(461, ' The University of Huddersfield London Campus', 20, 'university', 'London', '', '', '', '', ''),
(462, ' The University of Northampton', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(463, ' Ulster University Birmingham Campus', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(464, ' Ulster University London Campus', 20, 'university', 'WEST MIDLANDS ENGLAND AND LOND', '', '', '', '', ''),
(465, ' University for the Creative Arts', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(466, ' University of Aberdeen - ISC', 20, 'university', 'Scotland', '', '', '', '', ''),
(467, ' University of Bedfordshire', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(468, ' University of Birmingham', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(469, ' University of Bolton', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(470, ' University of Bradford', 20, 'university', 'WEST YORKSHIRE ENGLAND', '', '', '', '', ''),
(471, ' University of Brighton', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(472, ' University ', 20, 'university', 'SOUTH WEST ENGLAND', 'ENGLAND', '', '', '', ''),
(473, ' University of Chester', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(474, 'University of Dundee', 20, 'university', 'Scotland', 'Dundee', '', 'dist/uploads/institute/1665571461.jpg', '', ''),
(475, ' University of East London', 20, 'university', 'London', '', '', '', '', ''),
(476, ' University of East London - ISC', 20, 'university', 'London', '', '', '', '', ''),
(477, ' University of Essex', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(478, ' University of Glasgow', 20, 'university', 'Scotland', '', '', '', '', ''),
(479, ' University of Greenwich', 20, 'university', 'Scotland', '', '', '', '', ''),
(480, ' University of Hertfordshire', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(481, ' University of Kingston - ISC', 20, 'university', 'London', '', '', '', '', ''),
(482, ' University of Law', 20, 'university', 'London', '', '', '', '', ''),
(483, ' University of Leicester', 20, 'university', 'EAST MIDLANDS ENGLAND', '', '', '', '', ''),
(484, ' University of Liverpool', 20, 'university', 'NORTH WEST AND LONDON', '', '', '', '', ''),
(485, ' University of Plymouth', 20, 'university', 'SOUTH WEST ENGLAND', '', '', '', '', ''),
(486, ' University of Portsmouth', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(487, ' University of Roehampton', 20, 'university', 'London', '', '', '', '', ''),
(488, ' University of Salford', 20, 'university', 'NORTH WEST ENGLAND', '', '', '', '', ''),
(489, ' University of South Wales', 20, 'university', 'Wales', '', '', '', '', ''),
(490, ' University of Stirling', 20, 'university', 'Scotland', '', '', '', '', ''),
(491, ' University of Strathclyde', 20, 'university', 'Scotland', '', '', '', '', ''),
(492, ' University of Strathclyde International Study Centre', 20, 'college', 'Scotland', '', '', '', '', ''),
(493, ' University of Suffolk', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(494, ' University of Sunderland London Campus', 20, 'university', 'London', '', '', '', '', ''),
(495, ' University of Sunderland Sunderland Campus', 20, 'university', 'NORTH EAST ENGLAND', '', '', '', '', ''),
(496, ' University of Surrey', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(497, ' University of the Creative Arts', 20, 'university', 'SOUTH EAST ENGLAND', '', '', '', '', ''),
(498, 'UWE Bristol', 20, 'university', 'SOUTH WEST ENGLAND', 'Bristol', '', 'dist/uploads/institute/1665570639.jpeg', '', ''),
(499, ' University of West London', 20, 'university', 'London', '', '', '', '', ''),
(500, ' University of West of Scotland', 20, 'university', 'Scotland', '', '', '', '', ''),
(501, ' University of Westminster', 20, 'university', 'London', '', '', '', '', ''),
(502, ' University of Wolverhampton', 20, 'university', 'WEST MIDLANDS ENGLAND', '', '', '', '', ''),
(503, ' University of York', 20, 'university', 'YORKSHIRE AND HUMBER ENGLAND', '', '', '', '', ''),
(504, ' University of East Anglia', 20, 'university', 'EAST OF ENGLAND', '', '', '', '', ''),
(505, ' Wrexham Glyndwr University', 20, 'university', 'Wrexham', '', '', '', '', ''),
(506, ' University of Wales trinity saint david', 20, 'university', 'Birmingham/London/Lampter/Swansea/Tileyard London/Carmarthen', '', '', '', '', ''),
(507, ' wrexham glyndwr university', 20, 'university', 'Wales', '', '', '', '', ''),
(508, ' St Andrew College Cambridge', 20, 'college', 'Tierra del Fuego', '', '', '', '', ''),
(509, ' Canterbury Christ Church University', 20, 'university', 'Pitcairn Island', '', '', '', '', ''),
(510, ' university of kingston international', 20, 'university', 'London', '', '', '', '', ''),
(511, ' City University of London', 20, 'university', 'England', '', '', '', '', ''),
(512, ' Buckinghamshire New University', 20, 'university', 'England', '', '', '', '', ''),
(513, ' St Clares Oxford', 20, 'college', 'England', '', '', '', '', ''),
(514, ' St Patricks College', 20, 'college', 'England', '', '', '', '', ''),
(515, ' University of Leeds', 20, 'university', 'England', '', '', '', '', ''),
(516, ' University College Birmingham', 20, 'university', 'England', '', '', '', '', ''),
(517, ' Durham University', 20, 'university', 'England', '', '', '', '', ''),
(518, ' University of Nottingham', 20, 'university', 'England', '', '', '', '', ''),
(519, ' DMU International College (DMUIC)', 20, 'college', 'England', '', '', '', '', ''),
(520, ' University of Gloucestershire', 20, 'university', 'England', '', '', '', '', ''),
(521, ' George Mason University', 21, 'university', 'Virginia', '', '', '', '', ''),
(522, ' Hofstra Universiy', 21, 'university', 'New York', '', '', '', '', ''),
(523, ' Washington State University', 21, 'university', 'Washington', '', '', '', '', ''),
(524, ' Oregon State University - (Tier I Research University) : Public', 21, 'university', 'Oregon', '', '', '', '', ''),
(525, ' University of South Florida - (-Tier I Research University) : Public', 21, 'university', 'Florida', '', '', '', '', ''),
(526, ' Saint Louis University ( Private research University) : Private', 21, 'university', 'Missouri', '', '', '', '', ''),
(527, ' Marshall University : Public', 21, 'university', 'West Virginia', '', '', '', '', ''),
(528, ' Suffolk University', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(529, ' The University of Alabama at Birmingham', 21, 'university', 'Alabama', '', '', '', '', ''),
(530, ' Jefferson University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(531, ' Long Island University Brooklyn', 21, 'university', 'New York', '', '', '', '', ''),
(532, ' Long Island University Post', 21, 'university', 'New York', '', '', '', '', ''),
(533, ' Mercer University', 21, 'university', 'Georgia', '', '', '', '', ''),
(534, ' Illinois State University', 21, 'university', 'Illinois', '', '', '', '', ''),
(535, ' Drew University', 21, 'university', 'New Jersey', '', '', '', '', ''),
(536, ' New England College', 21, 'college', 'New Hampshire', '', '', '', '', ''),
(537, ' Quinnipiac University', 21, 'university', 'Connecticut', '', '', '', '', ''),
(538, ' Navitas- Virginia Commonwealth University', 21, ' public university (ug  pg pathway and direct entry) universit', 'Virginia', '', '', '', '', ''),
(539, ' Colorado State University (Tier I Research University): Public', 21, 'university', 'Colorado', '', '', '', '', ''),
(540, ' University of Idaho', 21, 'university', 'Idaho', '', '', '', '', ''),
(541, ' Seattle Pacific University', 21, 'university', 'Washington', '', '', '', '', ''),
(542, ' University of Wisconsin--Milwaukee', 21, 'university', 'Milwaukee', '', '', '', '', ''),
(543, ' Cleveland State University', 21, 'university', 'Ohio', '', '', '', '', ''),
(544, ' University of Nevada', 21, ' reno university', 'Nevada', '', '', '', '', ''),
(545, ' Ohio University (Only UG) : Public', 21, 'university', 'Ohio', '', '', '', '', ''),
(546, ' University of Arizona- (Well Spring)', 21, 'university', 'Arizona', '', '', '', '', ''),
(547, ' Virginia Tech Language and Culture Institute (Only UG Pathways)', 21, 'college', 'Virginia', '', '', '', '', ''),
(548, ' Arizona State University', 21, 'university', 'Arizona', '', '', '', '', ''),
(549, ' University of Tampa', 21, 'university', 'Florida', '', '', '', '', ''),
(550, ' University at Albany (SUNY)', 21, 'university', 'New York', '', '', '', '', ''),
(551, ' The State University of New York at Geneseo (SUNY Geneseo)', 21, 'university', 'New York', '', '', '', '', ''),
(552, ' The State University of New York Polytechnic Institute (SUNY Polytechnic Institute)', 21, 'college', 'New York', '', '', '', '', ''),
(553, ' University of Colorado Denver', 21, 'university', 'DENVER', '', '', '', '', ''),
(554, ' University of Cincinnati', 21, ' ohio. : public university', 'Ohio', '', '', '', '', ''),
(555, ' University of Maryland', 21, ' baltimore county  the graduate school at umbc, university', 'Maryland', '', '', '', '', ''),
(556, ' University of Nebraska Lincoln', 21, 'university', 'Nebraska', '', '', '', '', ''),
(557, ' University of Nebraska at Kearney (Only UG)', 21, 'university', 'Nebraska', '', '', '', '', ''),
(558, ' Nebraska Wesleyan University', 21, 'university', 'Nebraska', '', '', '', '', '');
INSERT INTO `ggportal_tbl_institute` (`institute_id`, `institute_name`, `institute_country_id`, `institute_type`, `institute_state`, `institute_city`, `web_url`, `logo_url`, `email`, `mobile`) VALUES
(559, ' MCPHS University (Massachusetts College of Pharmacy and Health Sciences)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(560, ' University of Massachusetts Lowell', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(561, ' New Jersey Institute of Technology (NJIT)', 21, 'college', 'New Jersey', '', '', '', '', ''),
(562, ' Duquesne University (Private University)', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(563, ' San Jose State University', 21, 'university', 'California', '', '', '', '', ''),
(564, ' Montana State University : Public', 21, 'university', 'Michigan', '', '', '', '', ''),
(565, ' Ashland University', 21, 'university', 'Ohio', '', '', '', '', ''),
(566, ' Indiana State University', 21, 'university', 'Indiana', '', '', '', '', ''),
(567, ' Kent State University : Public', 21, 'university', 'Ohio', '', '', '', '', ''),
(568, ' The State University of New York at New Paltz (SUNY New Paltz)', 21, 'university', 'New York', '', '', '', '', ''),
(569, ' State University of New York at Fredonia (UG and PG Programs)', 21, 'university', 'New York', '', '', '', '', ''),
(570, ' Indiana University of Pennsylvania', 21, 'university', 'Indiana', '', '', '', '', ''),
(571, ' Wichita State University : Public', 21, 'university', 'Kansas', '', '', '', '', ''),
(572, ' Gannon University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(573, ' The University of Memphis', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(574, ' Tennessee Tech University : Public', 21, 'university', 'Tennessee', '', '', '', '', ''),
(575, ' Florida Institute Of Technology', 21, 'university', 'Florida', '', '', '', '', ''),
(576, ' Midwestern State University : Public', 21, 'university', 'Texas', '', '', '', '', ''),
(577, ' Southeast Missouri State University : Public', 21, 'university', 'Missouri', '', '', '', '', ''),
(578, ' University of Missouri', 21, 'university', 'Missouri', '', '', '', '', ''),
(579, ' Youngstown State University of Youngstown', 21, 'university', 'Ohio', '', '', '', '', ''),
(580, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(581, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(582, ' California State University', 21, ' dominguez hills - public university', 'California', '', '', '', '', ''),
(583, ' California State University', 21, ' stanislaus university', 'California', '', '', '', '', ''),
(584, ' California Baptist University', 21, 'university', 'California', '', '', '', '', ''),
(585, ' Humboldt State University : Public', 21, 'university', 'California', '', '', '', '', ''),
(586, ' Western Kentucky University : Public', 21, 'university', 'Kentucky', '', '', '', '', ''),
(587, ' Texas State University', 21, ' san marcos university', 'Texas', '', '', '', '', ''),
(588, ' Dakota State University', 21, 'university', 'Medison', '', '', '', '', ''),
(589, ' Pittsburg State University', 21, 'university', 'Kansas', '', '', '', '', ''),
(590, ' University Of Central Oklahoma', 21, 'university', 'Oklahoma', '', '', '', '', ''),
(591, ' Northwest Missourie State University : Public', 21, 'university', 'Missouri', '', '', '', '', ''),
(592, ' Missouri State University', 21, 'university', 'Missouri', '', '', '', '', ''),
(593, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(594, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(595, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(596, ' University of New Hampshire', 21, 'university', 'DENVER', '', '', '', '', ''),
(597, ' California State University', 21, 'university', 'California', '', '', '', '', ''),
(598, ' University of New Haven', 21, 'university', 'California', '', '', '', '', ''),
(599, ' University of New Haven', 21, 'private university', 'Connecticut', '', '', '', '', ''),
(600, ' University of Bridgeport : Private', 21, 'university', 'Connecticut', '', '', '', '', ''),
(601, ' Pacific Lutheran University (International Pathways and UG/PG Programs)', 21, 'university', 'Washington', '', '', '', '', ''),
(602, ' California Lutheran University', 21, 'university', 'California', '', '', '', '', ''),
(603, ' Murray State University. : Public', 21, 'university', 'Kentucky', '', '', '', '', ''),
(604, ' Altantis University', 21, 'university', 'Florida', '', '', '', '', ''),
(605, ' Rider University', 21, 'university', 'New Jersy', '', '', '', '', ''),
(606, ' NY Tech (NYIT) New York : Private', 21, 'college', 'New York', '', '', '', '', ''),
(607, ' Marist College', 21, 'college', 'New York', '', '', '', '', ''),
(608, ' Golden Gate University', 21, 'university', 'California', '', '', '', '', ''),
(609, ' University of Wisconsin-Eau Claire.', 21, 'university', 'Wisconsin', '', '', '', '', ''),
(610, ' University of WisconsinLa Crosse', 21, 'university', 'Wisconsin', '', '', '', '', ''),
(611, ' State University of New York at Plattsburgh (SUNY Plattsburgh)', 21, 'university', 'New York', '', '', '', '', ''),
(612, ' State University of New York College at Old Westbury', 21, 'university', 'New York', '', '', '', '', ''),
(613, ' University of Mary Hardin-Baylor', 21, 'university', 'Texas', '', '', '', '', ''),
(614, ' Mississippi College : Private', 21, 'college', 'Mississippi', '', '', '', '', ''),
(615, ' Marshall University', 21, 'university', 'West Virginia', '', '', '', '', ''),
(616, ' Troy University : Public', 21, 'university', 'Alabama', '', '', '', '', ''),
(617, ' Texas Wesleyan University', 21, 'university', 'Texas', '', '', '', '', ''),
(618, ' Canisius College', 21, 'college', 'New York', '', '', '', '', ''),
(619, ' Oklahoma city University ', 21, 'private university', 'Oklahoma', '', '', '', '', ''),
(620, ' Dallas Baptist University', 21, 'university', 'Texas', '', '', '', '', ''),
(621, ' The University of Findlay', 21, 'university', 'Ohio', '', '', '', '', ''),
(622, ' Western New England University', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(623, ' Southern New Hampshire', 21, 'college', 'New Hampshire', '', '', '', '', ''),
(624, ' Rivier University', 21, 'university', 'New Hampshire', '', '', '', '', ''),
(625, ' Northwood University', 21, 'university', 'Michigan', '', '', '', '', ''),
(626, ' New England Institute of Technology: Private', 21, 'college', '', '', '', '', '', ''),
(627, ' Monroe College', 21, ' new york : private university', 'New York', '', '', '', '', ''),
(628, ' University of California', 21, 'university', 'California', '', '', '', '', ''),
(629, ' University of California Riverside Extension', 21, ' california (for international education program) university', 'California', '', '', '', '', ''),
(630, ' University of California', 21, ' irvine division of continuing education university', 'California', '', '', '', '', ''),
(631, ' Drexel University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(632, ' California State University', 21, ' bakersfield university', 'California', '', '', '', '', ''),
(633, ' California State University', 21, ' los angeles (elp programs at ug/pg level) university', 'California', '', '', '', '', ''),
(634, ' Academy of Art University', 21, 'university', 'California', '', '', '', '', ''),
(635, ' Full Sail University', 21, 'university', 'Florida', '', '', '', '', ''),
(636, ' Cleary University', 21, 'university', 'Michigan', '', '', '', '', ''),
(637, ' New York Film Academy', 21, ' new york city : private', 'New York', '', '', '', '', ''),
(638, ' Rocky Mountain College of Art and Design (RMCAD)', 21, 'college', 'Colorado', '', '', '', '', ''),
(639, ' Upper Iowa University : Private', 21, 'university', 'Iowa', '', '', '', '', ''),
(640, ' Briar Cliff University', 21, 'university', 'Iowa', '', '', '', '', ''),
(641, ' California Flight Center', 21, 'college', 'California', '', '', '', '', ''),
(642, ' San Francisco State University', 21, 'university', 'California', '', '', '', '', ''),
(643, ' Fisher College', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(644, ' Northeastern University in Boston - The College of Professional Studies (CPS)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(645, ' Pace University', 21, 'university', 'New York', '', '', '', '', ''),
(646, ' University of North Texas', 21, ' texas public university', 'Texas', '', '', '', '', ''),
(647, ' University of New Mexico', 21, 'university', 'New Mexico', '', '', '', '', ''),
(648, ' Arkansas State University', 21, 'university', 'Arkansas', '', '', '', '', ''),
(649, ' Southern Illinois University', 21, 'university', 'Illinois', '', '', '', '', ''),
(650, ' Tiffin University', 21, 'university', 'Ohio', '', '', '', '', ''),
(651, ' Alvernia University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(652, ' Point Park University', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(653, ' Bellevue College', 21, 'united states washington', 'Washington', '', '', '', '', ''),
(654, ' St. Thomas Aquinas College', 21, 'college', 'New York', '', '', '', '', ''),
(655, ' Slippery Rock University of Pennsylvania', 21, 'university', 'Pennsylvania', '', '', '', '', ''),
(656, ' Hult International Business School Boston & San Francisco', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(657, ' Charleston Southern University', 21, 'university', 'South Carolina', '', '', '', '', ''),
(658, ' University of Charleston', 21, 'university', 'West Virginia', '', '', '', '', ''),
(659, ' DeVry University', 21, 'university', 'Illinois', '', '', '', '', ''),
(660, ' Schiller International University', 21, 'united states florida', 'Florida', '', '', '', '', ''),
(661, ' National University', 21, 'united states california', 'California', '', '', '', '', ''),
(662, ' Concordia University Chicago', 21, 'university', 'Illinois', '', '', '', '', ''),
(663, ' Agnes Scott College', 21, 'college', 'Georgia', '', '', '', '', ''),
(664, ' Webster University', 21, 'university', 'Missouri', '', '', '', '', ''),
(665, ' Milwaukee School of Engineering', 21, 'college', 'Wisconsin', '', '', '', '', ''),
(666, ' West Virginia State University', 21, 'university', 'West Virginia', '', '', '', '', ''),
(667, ' Virginia Wesleyan University', 21, 'university', 'Virginia', '', '', '', '', ''),
(668, ' Saginaw Valley State University', 21, ' public university', 'Michigan', '', '', '', '', ''),
(669, ' University of Tulsa ( Direct entry only for UG - Through Kaplan )', 21, 'university', 'Oklahoma', '', '', '', '', ''),
(670, ' Lawrence Technological University - LTU', 21, 'university', 'Michigan', '', '', '', '', ''),
(671, ' Saint Leo University', 21, 'university', 'Florida', '', '', '', '', ''),
(672, ' Johnson and Wales University', 21, 'university', 'Rhode Island', '', '', '', '', ''),
(673, ' Park University', 21, ' missouri university', 'Missouri', '', '', '', '', ''),
(674, ' Le Cordon Bleu Paris(Hong Kong) Ltd', 21, 'college', '', '', '', '', '', ''),
(675, ' Valparaiso University', 21, 'united states indiana', 'Indiana', '', '', '', '', ''),
(676, ' Trine University', 21, 'university', 'Indiana', '', '', '', '', ''),
(677, ' Aviation Institute of Maintenance', 21, 'college', 'Virginia', '', '', '', '', ''),
(678, ' Northern Arizona University', 21, ' usa university', 'Arizona', '', '', '', '', ''),
(679, ' Summer Discovery. Discovery Internship and Jr. Internship.', 21, 'college', '', '', '', '', '', ''),
(680, ' Manhattan College', 21, ' new york (well spring) college', 'New York', '', '', '', '', ''),
(681, ' UMass Amherst (ECE MS 1+1 Programs)', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(682, ' On Campus Boston- Curry College (UG transfer Program)', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(683, ' Florida Polytechnic University', 21, 'university', 'Florida', '', '', '', '', ''),
(684, ' Mississippi State University', 21, 'university', 'Mississippi', '', '', '', '', ''),
(685, ' Seattle Central Community College : Public', 21, 'college', 'Washington', '', '', '', '', ''),
(686, ' Shoreline Community College', 21, ' washington college', 'Washington', '', '', '', '', ''),
(687, ' Green River College', 21, 'college', 'Washington', '', '', '', '', ''),
(688, ' San Mateo Colleges of Silicon Valley (Canada College', 21, 'united states california', 'California', '', '', '', '', ''),
(689, ' Santa Ana College', 21, 'united states california', 'California', '', '', '', '', ''),
(690, ' Hillsborough Community College', 21, 'college', 'Florida', '', '', '', '', ''),
(691, ' University of Dayton', 21, 'university', 'Ohio', '', '', '', '', ''),
(692, ' INTO - George Mason University UG Pathways & PG pathways.. : Public', 21, 'university', 'Virginia', '', '', '', '', ''),
(693, ' INTO -Hofstra University (UG', 21, ' pg pathway and direct entry)', 'New York', '', '', '', '', ''),
(694, ' INTO - Oregon State University - (Tier I Research University) UG Pathways & PG pathways.. : Public', 21, 'university', 'Oregon', '', '', '', '', ''),
(695, ' INTO - Saint Louis University ( Private research University) UG Pathways: Private', 21, 'university', 'Missouri', '', '', '', '', ''),
(696, ' INTO - Marshall University UG Pathways & PG pathways. : Public', 21, 'university', 'West Virginia', '', '', '', '', ''),
(697, ' INTO- Suffolk University (Private University)', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(698, ' INTO New York at Drew University Undergraduate 2 year Standard Pathway', 21, 'university', 'New Jersey', '', '', '', '', ''),
(699, ' Pace University', 21, ' new york city. (kaplan - global pathway programs) u.g pathways & pg pathways.: private', 'New York', '', '', '', '', ''),
(700, ' Arizona State University (Kaplan- University Pathway)Global Launch Programs', 21, 'university', 'Arizona', '', '', '', '', ''),
(701, ' University of Massachusetts - MA UG pathways', 21, ' pg pathways and : public', 'Massachusetts', '', '', '', '', ''),
(702, ' University of Massachusetts- MA. (UG Pathways & PG Pathways) : Public', 21, 'university', 'Massachusetts', '', '', '', '', ''),
(703, ' Loyola University New Orleans', 21, 'university', 'Louisiana', '', '', '', '', ''),
(704, ' Navitas- Florida Atlantic University (UG Pathways): Public', 21, 'university', 'Florida', '', '', '', '', ''),
(705, ' Navitas -Richard Bland College of William & Mary', 21, 'college', 'Virginia', '', '', '', '', ''),
(706, ' Navitas- Queens College of the City University of New York', 21, 'college', 'New York', '', '', '', '', ''),
(707, ' University of Hartford', 21, 'university', 'Connecticut', '', '', '', '', ''),
(708, ' DePaul University', 21, 'university', 'Illinois', '', '', '', '', ''),
(709, ' Louisiana State University', 21, 'university', 'Louisiana', '', '', '', '', ''),
(710, ' American University Washington D.C', 21, 'university', 'Washington', '', '', '', '', ''),
(711, ' Adelphi University', 21, 'university', 'New York', '', '', '', '', ''),
(712, ' Kings College - Canisius College', 21, 'college', 'New York', '', '', '', '', ''),
(713, ' Kings College - Marymount College', 21, ' los angeles. (undergraduate pathways) : private college', 'California', '', '', '', '', ''),
(714, ' Kings College - Pine Manor', 21, ' boston. (undergraduate pathways) : private college', 'Massachusetts', '', '', '', '', ''),
(715, ' American Honors (National Transter Network)', 21, 'college', 'Washington', '', '', '', '', ''),
(716, ' CATS Academy', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(717, ' Kaplan Test Prep', 21, 'college', 'California', '', '', '', '', ''),
(718, ' University of Central Florida', 21, 'university', 'Florida', '', '', '', '', ''),
(719, ' Auburn University', 21, 'university', 'Alabama', '', '', '', '', ''),
(720, ' Auburn University at Montgomery', 21, 'university', 'Montgomery', '', '', '', '', ''),
(721, ' Lynn University', 21, 'university', 'Florida', '', '', '', '', ''),
(722, ' The University of Kansas', 21, 'university', 'Kansas', '', '', '', '', ''),
(723, ' Florida International University (FIU)', 21, 'university', 'Florida', '', '', '', '', ''),
(724, ' University of South Carolina', 21, 'university', 'South Carolina', '', '', '', '', ''),
(725, ' University of the Pacific', 21, 'university', 'California', '', '', '', '', ''),
(726, ' University of Utah', 21, 'university', 'Utah', '', '', '', '', ''),
(727, ' James Madison University', 21, 'university', 'Virginia', '', '', '', '', ''),
(728, ' University of Vermont', 21, 'university', 'Vermont', '', '', '', '', ''),
(729, ' Oglethorpe University (Only UG - Both Pathway and Direct entry)', 21, 'university', 'Georgia', '', '', '', '', ''),
(730, ' Baylor University', 21, 'university', 'Texas', '', '', '', '', ''),
(731, ' Lipscomb University', 21, 'university', 'Tennessee', '', '', '', '', ''),
(732, ' Western Washington University', 21, 'university', 'Washington', '', '', '', '', ''),
(733, ' Texas A & M University', 21, 'university', 'Texas', '', '', '', '', ''),
(734, ' Academy of Art University', 21, 'college', 'California', '', '', '', '', ''),
(735, ' University of Illinois at Chicago', 21, 'university', 'Illinois', '', '', '', '', ''),
(736, ' Wright State University', 21, 'university', 'Ohio', '', '', '', '', ''),
(737, ' International American University', 21, 'university', 'California', '', '', '', '', ''),
(738, ' Mercy College', 21, 'college', 'New York', '', '', '', '', ''),
(739, ' AMERICAN COLLEGIATE LA (ACLA)', 21, 'university', 'New York', '', '', '', '', ''),
(740, ' AMERICAN COLLEGIATE DC (ACDC)', 21, 'university', 'New York', '', '', '', '', ''),
(741, ' GONZAGA UNIVERSITY (GU)', 21, 'university', 'New York', '', '', '', '', ''),
(742, ' UNIVERSITY OF MASSACHUSETTS BOSTON (UMB)', 21, 'university', 'New York', '', '', '', '', ''),
(743, ' UNIVERSITY OF MASSACHUSETTS AMHERST (UMA)', 21, 'university', 'New York', '', '', '', '', ''),
(744, ' UNIVERSITY OF MISSISSIPPI (OMI)', 21, 'university', 'New York', '', '', '', '', ''),
(745, ' Community Colleges of Spokane', 21, 'college', 'Washington', '', '', '', '', ''),
(746, ' Edgewood College', 21, 'college', 'Wisconsin', '', '', '', '', ''),
(747, ' Montana State University Billings', 21, 'university', 'Montana', '', '', '', '', ''),
(748, ' Hilbert College', 21, 'college', 'New York', '', '', '', '', ''),
(749, ' Bay Atlantic University', 21, 'university', 'Washington', '', '', '', '', ''),
(750, ' Castleton University', 21, 'university', 'Vermont', '', '', '', '', ''),
(751, ' Hawai?i Pacific University', 21, 'university', 'Hawaii', '', '', '', '', ''),
(752, ' Paul Smith\'s College', 21, 'college', 'New York', '', '', '', '', ''),
(753, ' Nichols College', 21, 'college', 'Massachusetts', '', '', '', '', ''),
(754, ' Hartwick College', 21, 'college', 'New York', '', '', '', '', ''),
(755, ' Los Angeles Mission College', 21, 'college', 'California', '', '', '', '', ''),
(756, ' Presentation College', 21, 'college', 'South Dakota', '', '', '', '', ''),
(757, ' Seattle University', 21, 'university', 'Washington', '', '', '', '', ''),
(759, 'Deakin business school', 1, 'college', '212113232323', 'Melbourne ', 'grgeeggrgs', 'dist/uploads/institute/1712203966.png', '587358jhjfnda', 'gkdgksdgklsfgm'),
(760, 'University of Victoria', 1, 'university', 'jj545', 'Melbourne', '', '', 'adsfgdf546', 'kjgiug');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message`
--

CREATE TABLE `ggportal_tbl_message` (
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_status` varchar(1) NOT NULL DEFAULT 'Y',
  `is_read` varchar(1) NOT NULL DEFAULT 'N',
  `is_read_cu` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message`
--

INSERT INTO `ggportal_tbl_message` (`message_id`, `user_id`, `user_type`, `message`, `datetime`, `is_status`, `is_read`, `is_read_cu`) VALUES
(36, 142, 'RA', 'HI', '2024-03-18 06:35:07', 'Y', 'N', 'N'),
(37, 144, 'RA', 'hi', '2024-03-18 06:35:31', 'Y', 'Y', 'N'),
(38, 144, 'ST', 'Hello', '2024-03-18 06:37:11', 'Y', 'Y', 'N'),
(39, 144, 'ST', 'hiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiiii', '2024-04-04 06:47:44', 'Y', 'Y', 'N'),
(40, 142, 'RA', 'hello', '2024-04-09 06:03:51', 'Y', 'N', 'N'),
(41, 144, 'ST', 'hello', '2024-04-09 06:05:29', 'Y', 'Y', 'N'),
(42, 143, 'RA', 'hello', '2024-04-26 07:25:22', 'Y', 'N', 'N'),
(43, 154, 'RA', 'hello', '2024-05-16 05:49:47', 'Y', 'N', 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message_agent`
--

CREATE TABLE `ggportal_tbl_message_agent` (
  `message_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `agent_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message_agent`
--

INSERT INTO `ggportal_tbl_message_agent` (`message_id`, `student_id`, `agent_id`, `user_type`, `message`, `datetime`, `is_read`) VALUES
(1, 95, 35, 'AG', 'hi from agent', '2022-12-31 19:45:35', 'Y'),
(2, 95, 35, 'ST', 'hi from student', '2022-12-31 20:06:19', 'Y'),
(5, 52, 35, 'AG', 'how are you by agent', '2023-08-03 15:38:51', 'Y'),
(7, 95, 35, 'ST', 'im fine thanks from student', '2023-01-01 23:06:19', 'Y'),
(11, 25, 35, 'AG', 'did you submit ag', '2023-08-03 15:38:51', 'Y'),
(12, 95, 35, 'ST', 'Test Start st', '2023-09-15 16:29:35', 'Y'),
(13, 95, 35, 'AG', 'agent second message', '2023-09-16 14:40:04', 'Y'),
(14, 95, 35, 'ST', 'Malaka Second message to agent', '2023-09-16 14:40:42', 'Y'),
(15, 95, 35, 'AG', 'agent Second message', '2023-09-18 14:03:32', 'Y'),
(16, 95, 35, 'ST', 'are you there', '2023-09-18 14:13:59', 'Y'),
(17, 95, 35, 'ST', 'Malaka Thired Message', '2023-09-18 14:25:39', 'Y'),
(18, 95, 35, 'AG', 'agent Thired Message', '2023-09-18 14:26:01', 'Y'),
(0, 93, 35, 'AG', 'gju', '2023-10-02 15:02:47', 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_message_staff`
--

CREATE TABLE `ggportal_tbl_message_staff` (
  `message_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `staff_id` int(11) NOT NULL,
  `user_type` varchar(10) NOT NULL,
  `message` text NOT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_message_staff`
--

INSERT INTO `ggportal_tbl_message_staff` (`message_id`, `student_id`, `staff_id`, `user_type`, `message`, `datetime`, `is_read`) VALUES
(20, 153, 94, 'SF', 'hello ', '2024-04-24 04:58:57', 'N'),
(21, 153, 94, 'SF', 'Hiiiii.....', '2024-05-13 06:00:23', 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_program`
--

CREATE TABLE `ggportal_tbl_program` (
  `program_id` int(11) NOT NULL,
  `program_name` varchar(450) NOT NULL,
  `course_type` varchar(50) NOT NULL,
  `course_id` varchar(45) NOT NULL,
  `country_id` int(11) NOT NULL,
  `city` varchar(50) DEFAULT NULL,
  `institute_id` int(11) NOT NULL,
  `deadline` date NOT NULL,
  `currency_id` int(11) NOT NULL,
  `commission` decimal(10,2) NOT NULL,
  `tution_fee` decimal(10,2) NOT NULL,
  `application_fee` decimal(10,2) DEFAULT NULL,
  `intake` varchar(200) NOT NULL,
  `duration` varchar(90) NOT NULL,
  `ets` varchar(45) NOT NULL,
  `requirements` varchar(450) NOT NULL,
  `english_requirements` varchar(450) NOT NULL,
  `program_web_url` varchar(500) NOT NULL,
  `intake2` varchar(100) NOT NULL,
  `deadline2` date DEFAULT NULL,
  `intake3` varchar(100) NOT NULL,
  `deadline3` date DEFAULT NULL,
  `intake4` varchar(100) NOT NULL,
  `deadline4` date DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_program`
--

INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`, `intake2`, `deadline2`, `intake3`, `deadline3`, `intake4`, `deadline4`) VALUES
(191, 'Fitness and Health Promotion', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/fitness-and-health-promotion', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(190, 'Healthcare Environmental Services Management', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18219.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/healthcare-environmental-services-management/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(189, 'Esthetician', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18269.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/esthetician', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(188, 'Occupational Therapist Assistant & Physiotherapist Assistant', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18234.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/occupational-therapist-assistant-physiotherapist-assistant/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(187, 'Auto Body Repair Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/auto-body-repair-technician/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(186, 'Motorcycle and Powersports Product Repair Techniques      ', 'Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19206.74, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or 19 years or older\nApplicants to Centennial College post-secondary programs in the School of Transportation with scores of 140 or 141 on the Centennial College English Skills Assessment are advised to take this program first as a pathway to their program of choice.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/motorcycle-and-powersports-product-repair-techniques/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(185, 'Aviation Technology - Avionics Maintenance & Mgmt', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 22195.22, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or 19 years or older\nApplicants to Centennial College post-secondary programs in the School of Transportation with scores of 140 or 141 on the Centennial College English Skills Assessment are advised to take this program first as a pathway to their program of choice.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/avionics-maintenance-mgmt-aviation-technology', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(184, 'Aviation Technology - Aircraft Maintenance & Mgmt', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 22195.22, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test\nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent (minimum grade required), or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/aircraft-maintenance-mgmt-aviation-technology', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(183, 'Automotive - Motive Power Technician (optional co-op)', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18951.24, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent, or take the Centennial English Admission Test\nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent, or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automotive-motive-power-technician-technical/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(182, '(Truck and Coach) Motive Power Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19227.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent, or take the Centennial English Admission Test\nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent, or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/truck-and-coach-technician/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(181, 'Automotive Parts and Service Operations', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19147.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automotive-parts-service-operations/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(180, 'Motive Power Fundamentals', 'Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19147.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or 19 years or older\nApplicants to Centennial College post-secondary programs in the School of Transportation with scores of 140 or 141 on the Centennial College English Skills Assessment are advised to take this program first as a pathway to their program of choice.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automotive-fundamentals', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(179, 'Trades Foundation - Motive Power', 'Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19147.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or 19 years or older\nApplicants to Centennial College post-secondary programs in the School of Transportation with scores of 140 or 141 on the Centennial College English Skills Assessment are advised to take this program first as a pathway to their program of choice.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automotive-foundations', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(178, 'Motive Power Technician (Automotive) - Technical', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19227.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent, or take the Centennial English Admission Test\nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent, or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automotive-motive-power-technician-technical/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(177, 'Aviation Technician - Avionics Maintenance', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 22260.60, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test\nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent (minimum grade required), or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/avionics-maintenance-aviation-technician', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(176, 'Aviation Technician - Aircraft Maintenance', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 22260.60, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test \nGrade 11 Mathematics (M or U) or Grade 12 Mathematics (C or U), or equivalent (minimum grade required), or take the Centennial College Engineering Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/aircraft-maintenance-aviation-technician', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(175, 'Pre-Health Sciences Pathway to Certif. & Dipl.', 'Certificate', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required),\nor take the Centennial English Admission Test\nGrade 11 Math (M or U), or Grade 12 Math (C or U), or equivalent, or take a Centennial College Math Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/pre-health', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(174, 'Liberal Arts - York', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/liberal-arts-york', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(173, 'Liberal Arts - Trent', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/liberal-arts-trent', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(172, 'Liberal Arts - UTSC', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/liberal-arts-utsc', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(171, 'Liberal Arts', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/liberal-arts', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(170, 'General Arts', 'Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18134.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required),\nor take the Centennial English Admission Test', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/general-arts', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(169, 'English for Academic Purposes', 'Certificate', '21', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26834.42, 125.00, 'Sep 24', '1-3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years of age or older)  | You will require the following minimum scores:\n\nCLB - 4\nIELTS: 3.5-4.0\nTOEFL: 397-433\nibTOEFL: 30-40\niTEP: 3.5-3.6\nPearson TE (Academic): 36-40\nCambridge Exams: Preliminary English Test pass', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/english-academic-purposes/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(168, 'English Language Learning Program (ELL)', 'Certificate', '21', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 16068.00, 125.00, 'Sep 24', '1-3 Semesters / One Year', '', 'English Language Learning (ELL) is designed for International students who wish to upgrade their English skills. If you are an international student and are interested in this program, please contact the Centennial College International Centre.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/english-language-learning/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(167, 'Theatre Arts and Performance', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18264.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/theatre-arts-performance/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(166, 'Dance Performance', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18164.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/dance-performance/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(165, 'Music Industry Arts and Performance', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18204.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/music-industry-arts-performance/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(164, 'Publishing - Book, Magazine and Electronics', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/publishing-book-magazine-electronic', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(163, 'Lifestyle Media', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/lifestyle-media/', 'May 25', '2024-01-30', '', '1970-01-01', '', '1970-01-01'),
(162, 'Digital Visual Effects', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18233.90, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/digital-visual-effects/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(161, 'Game Development', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 20331.56, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/game-development', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(160, 'Arts Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18164.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or a three-year college diploma or in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/arts-management/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(159, 'Television and Film - Business', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Completion of a diploma or degree in an artistic field\nWe will also consider applicants with partial post-secondary education and substantial history of artistic practice (at least 5 years), involving some work in arts education and/or community development.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/television-film-business/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(158, 'Advanced Television and Film - Script to Screen', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18244.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or a three-year college diploma or in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/script-to-screen-advanced-television-film', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(157, 'Arts Education and Community Engagement', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18138.90, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Completion of a diploma or degree in an artistic field\nWe will also consider applicants with partial post-secondary education and substantial history of artistic practice (at least 5 years), involving some work in arts education and/or community development.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/arts-education-and-community-engagement/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(156, 'Advertising - Creative and Digital Strategy', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or a three-year college diploma or in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/advertising-creative-digital-strategy/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(155, 'Children\'s Media', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26819.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Degree or a three-year college diploma or in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/childrens-media/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(154, 'Animation - 3D', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 30230.34, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/animation-3d/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(153, 'Game - Art ', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 30219.96, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/game-art/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(152, 'Fine Arts Studio', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18248.78, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/fine-arts-studio/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(151, 'Graphic Design', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18214.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/graphic-design/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(150, 'Public Relations - Corporate Communications', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18174.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/public-relations-corporate-communications/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(149, 'Communications and Media Fundamentals', 'Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGrade 12 English (C or U), or equivalent, or take the Centennial English Admission Test', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/communications-media-fundamentals/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(148, 'Contemporary Journalism ', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26819.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Degree or a three-year college diploma in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/contemporary-journalism/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(147, 'Interactive Media Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26849.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Diploma or degree in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/interactive-media-management/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(146, 'Art and Design Fundamentals', 'Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18324.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/art-design-fundamentals/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(145, 'Photography', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18237.52, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/photography/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(144, 'Advertising and Marketing Communications Management', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\n• Grade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/advertising-and-marketing-communications-management', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(143, 'Advertising - Media Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26819.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Degree or a three-year college diploma in any discipline\nWe will consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 60+', 'https://www.centennialcollege.ca/programs-courses/full-time/advertising-media-management/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(142, 'Journalism ', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18124.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\n• Grade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/journalism/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(141, 'Broadcasting - Radio, Television, Film and Digital Media', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18244.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\n• Grade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/radio-television-film-digital-media/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(140, 'Performing Arts Fundamentals', 'Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18264.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\n• Grade 12 English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test.', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/performing-arts-fundamentals', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(139, 'Pharmacy Technician', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18189.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older).\nGrade 12 English (C or U), or equivalent (minimum grade required)\nGrade 11 Mathematics (M or U), or Grade 12 Mathematics (C or U), or equivalent (minimum grade required), or take the College GAS  Math Skills Assessment for Admission\nGrade 11 Biology (C or U) or Grade 12 Biology (C or U), or equivalent (minimum grade required)\nGrade 11 Chemistry (', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/pharmacy-technician/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(138, 'Massage Therapy', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18189.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older)\nGCE A/L English (C or U), or equivalent (minimum grade required), or take the Centennial English Admission Test\nGCE O/L Biology or GCE A/L Biology (C or U), or equivalent (minimum grade required)\nGCE O/L or GCE A/L One additional Science (Physics, Chemistry, or Exercise Science), or equivalent (minimum grade required)', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/massage-therapy/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(137, 'Environmental Technology Fast Track', 'Advanced Diploma', '28', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19311.74, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Diploma or degree in chemistry/biology, or engineering, or related discipline', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/environmental-technology-fast-track/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(136, 'Environmental Technician Fast Track', 'Diploma', '28', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19311.74, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in science, engineering, or related discipline', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/environmental-technician-fast-track', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(135, 'Environmental Technology ', 'Advanced Diploma', '28', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19417.09, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/environmental-technology/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(134, 'Environmental Technician', 'Diploma', '28', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19417.09, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/environmental-technician', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(133, 'Electro-Mechanical Engineering Technology - Automation & Robotics Fast Track', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or diploma in science, engineering, or a related discipline\nWe will consider applicants with a combination of partial diploma or degree and relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/automation-and-robotics-technology-fast-track/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(132, 'Electro-Mechanical Engineering Technician - Automation & Robotics Fast Track', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or a three-year college diploma in computer science, engineering, or a related discipline\nWe will consider applicants with a combination of partial degree or diploma, and relevant work experience', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automation-robotics-technician-fast-track', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(131, 'Electro-Mechanical Engineering Technology - Automation & Robotics ', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/automation-and-robotics-technolohttps://www.centennialcollege.ca/programs-courses/full-time/automation-and-robotics-technologhttps://www.centennialcollege.ca/programs-courses/full-time/automation-and-robotics-technology', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(130, 'Electro-Mechanical Engineering Technician - Automation & Robotics ', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/automation-and-robotics-technician', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(129, 'Electrical Engineering Technology Fast-Track', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19498.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Diploma or degree in electrical engineering or electrical technology, or in a related science and engineering field', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/electrical-engineering-technology-fast-track-4-sem/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(128, 'Electrical Engineering Technician Fast-Track', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19584.47, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in electrical engineering or electrical technology, or in a related science and engineering field', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/electrical-engineering-technician-fast-track', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(127, 'Electrical Techniques', 'Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19181.74, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/electrical-techniques', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(126, 'Heating, Refrigeration & Air Conditioning Tech', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19334.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/heating-refrigeration-and-ac-technician', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(125, 'Electrical Engineering Technology', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19504.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/electrical-engineering-technology/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(124, 'Electrical Engineering Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19584.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/electrical-engineering-technician', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(123, 'Energy Systems Engineering Technology Fast Track (OptionalCo-op)', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Diploma or degree in a related science or engineering area', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/energy-systems-engineering-technology-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(122, 'Energy Systems Engineering Technician Fast Track', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(121, 'Energy Systems Engineering Technology (Optional Co-op)', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(120, 'Energy Systems Engineering Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/energy-systems-engineering-technician/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(119, 'Mechanical Engineering Technology - Industrial Fast Track', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19227.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or a three-year college diploma in a related science or engineering area', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technology-industrial-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(118, 'Mechanical Engineering Technology - Design Fast Track', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19227.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or a three-year college diploma in a related science or engineering area. | We will consider applicants with a combination of partial diploma or degree and relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technology-design-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(117, 'Mechanical Engineering Technician - Design Fast Track', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19321.74, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or a three-year college diploma in a related science or engineering area We will consider applicants with a combination of partial diploma or degree and relevant work experience', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technology-design-fast-track/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(116, 'Aerospace Manufacturing Engineering Technology', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19254.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/aerospace-manufacturing-engineering-technology/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(115, 'Aerospace Manufacturing Engineering Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19254.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/aerospace-manufacturing-engineering-technician', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(114, 'Mechanical Engineering Technology - Industrial', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19334.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technology-industrial/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(113, 'Mechanical Engineering Technology - Design', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19334.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technology-design/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(112, 'Mechanical Engineering Technician - Design', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19334.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/mechanical-engineering-technician-design', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(110, 'Biotechnology - Advanced Fast Track', 'select', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19311.74, 125.00, 'Sep 24', '2 Years / 4 Semesters', 'tt', 'Degree or three-year college diploma in chemistry/biology, engineering, or related discipline', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/biotechnology-advanced-fast-track/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '0000-00-00'),
(111, 'Food Science Technology Fast Track', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19311.74, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or three-year college diploma in chemistry/biology, engineering, or related discipline', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/food-science-technology-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(109, 'Biotechnology Fast Track', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19311.74, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/biotechnology-advanced-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01');
INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`, `intake2`, `deadline2`, `intake3`, `deadline3`, `intake4`, `deadline4`) VALUES
(108, 'Food Science Technology (Optional Co-op)', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19417.09, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/food-science-technology/', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(107, 'Biotechnology Advanced', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19417.09, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(106, 'Biotechnology', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19417.09, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/biotechnology', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(105, 'Health Informatics Technology Fast Track', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19235.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'College diploma or university degree in computer science, information technology, software engineering, or a related discipline. | We will consider applicants with a combination of partial diploma or degree and relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/health-informatics-technology-fast-track/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(104, 'Product Design and Development', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18224.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/product-design-and-development/', 'Sep 25', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(103, 'Health Informatics Technology (Optional Co-op)', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19342.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/health-informatics-technology/', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(102, 'Software Engineering Technology (Fast Track) (Optional Co-op)', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19235.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Diploma or degree in computer science, information technology, software engineering, or a related discipline. | We will consider applicants with a combination of partial diploma or degree and relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/software-engineering-technology-fast-track/', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(101, 'Software Engineering Technician ', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19235.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(100, 'Biomedical Engineering Technology (Fast Track) (Optional Co-op)', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19460.79, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Diploma or degree in a related science or engineering area', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(99, 'Computer Systems Technology - Networking ', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or a three-year college diploma in computer science, engineering, or a related discipline. | We will consider applicants with a combination of partial degree or diploma and relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 58+', 'https://www.centennialcollege.ca/programs-courses/full-time/networking-computer-systems-technology-fast-track', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(98, 'Computer Systems Technician - Networking ', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(97, 'Software Eng Technology-Artificial Intelligence FT (Fast Track) (Optional Co-op)', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Degree or diploma in computer science, information technology, software engineering, or a related discipli', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(96, 'Software Engineering Technology', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19342.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(95, 'Software Engineering Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19342.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(94, 'Biomedical Engineering Technology (Optional Co-op)', 'Advanced Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19674.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(93, 'Computer Systems Technology - Networking', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(92, 'Computer Systems Technician - Networking', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19324.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(91, 'Software Eng Technology - Artificial Intelligence (Optional Co-op)', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19342.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(89, 'Cybersecurity', 'Graduate Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19147.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'A Diploma, Advanced Diploma or Degree in Computer Science, Computer/Electronics/Communication Engineering, IT or related discipline; or diploma with relevant work experience', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(90, 'IoT - System Architecture and Integration', 'Graduate Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 28526.68, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Completion of a diploma, advanced diploma, or degree:\nSoftware Engineering (Technology/Technician)\nNetwork Engineering (Degree/Technology/Technician)\nElectronics Engineering\nComputer Systems\nComputer Science/Computer Engineering\nConsideration for acceptance in the program will also be given to applicants who have completed 50% of a diploma, advanced diploma, or degree in these programs, with a minimum of two years of work experience in these fiel', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(88, 'Mobile Application Development', 'Graduate Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19147.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(87, 'Electronics Engineering Technology ', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19640.36, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(86, 'Electronics Engineering Technician ', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19640.36, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(85, 'Computer Repair and Maintenance', 'Certificate', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(84, 'Electronics Engineering Technology', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19624.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(83, 'Electronics Engineering Technician', 'Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19624.47, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(82, 'Construction Project Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 28458.68, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(81, 'Game - Programming Fast Track', 'Advanced Diploma', '27', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19235.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(80, 'Architectural Technology', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(79, 'Game – Programming (Optional Co-op)', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19342.47, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(78, 'Architectural Technology', 'Advanced Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(77, 'Architectural Technician', 'Diploma', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19217.12, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(76, 'Bookkeeping', 'Certificate', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(75, 'International Development', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26891.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(74, 'Insurance Management', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18308.02, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(73, 'Global Business Management ', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18907.89, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(72, 'Paralegal', 'Graduate Certificate', '21', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26891.42, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(71, 'Business Analytics and Insights', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26327.92, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(70, 'Supply Chain Management - Logistics', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19430.77, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(69, 'Business - Human Resources', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18093.01, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(68, 'Human Resources Management', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18172.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(66, 'Marketing - Digital Engagement Strategy', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18234.20, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(67, 'International Business Management', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 23393.50, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(65, 'Marketing - Corporate Account Management', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18404.77, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(64, 'Marketing - Research and Analytics', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18172.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(63, 'Financial Planning', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18284.15, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(62, 'Marketing Management', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18313.90, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(61, 'Business - International Business', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(60, 'Business - Marketing', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(59, 'Business', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(58, 'Business - Accounting', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(57, 'Business Administration - Human Resources ', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(56, 'Office Administration - Executive', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18247.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(55, 'Office Administration - Health Services', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18247.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(54, 'Office Administration - Legal', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18247.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(53, 'Paralegal - Court and Tribunal Agent', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(52, 'Project Management – Organizational Strategy', 'Graduate Certificate', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 23593.76, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(51, 'Business Admin - Supply Chain & Operations Mgmt', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18172.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(50, 'Business - Supply Chain & Operations (Busn. Oper.) (Optional Co-op)', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18172.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(49, 'Business Administration - Finance', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18084.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(48, 'Business Administration - Financial Services (Optional Co-op)', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18250.53, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(47, 'Court Support Services', 'Certificate', '21', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18247.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(46, 'Fashion Business and Management ', 'Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18414.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(45, 'Business Administration - Accounting ', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 26912.04, 125.00, 'Sep 24', '1 Year / 3 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(44, 'Business Administration - International Business ', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(43, 'Business Administration - Leadership and Management (Optional Co-op)', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(42, 'Business Administration - Accounting ', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(40, 'Food Tourism', 'Graduate Certificate', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18297.90, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'College diploma or university degree in any discipline', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(41, 'Business Administration - Marketing ', 'Advanced Diploma', '26', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18222.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older) | GCE O/L Mathematics (A, B, or C) or GCE A/L Mathematics (B or C), or equivalent (minimum grade required), or take a Math skills assessment', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(39, 'Food Media', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18346.15, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'College diploma or university degree in any discipline', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(38, 'Museum and Cultural Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18264.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Diploma or degree in any discipline and consider applicants who have successfully completed partial post-secondary education (minimum two years) and have relevant work experience.', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(37, 'Event Management', 'Graduate Certificate', '25', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18142.80, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'College diploma or university degree in any discipline', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(36, 'Hotel, Resort and Restaurant Management', 'Graduate Certificate', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18304.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Degree or diploma in any discipline', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', 'Jan 25', '2024-09-30', 'May 25', '2025-01-30', '', '1970-01-01'),
(35, 'Tourism', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18368.90, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(34, 'Baking and Pastry Arts Management', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19301.95, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(33, 'Culinary Management', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 19452.37, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(32, 'Food and Beverage Management - Restaurant Operations', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18418.51, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(31, 'Hospitality - Hotel Operations Management', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18418.51, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(30, 'Hospitality and Tourism Administration', 'Advanced Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18664.01, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(29, 'Event Planning', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18410.49, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(28, 'Nutrition and Food Service Management', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18289.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(27, 'Police and Public Safety Foundations', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18169.52, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(26, 'Addiction and Mental Health Worker', 'Diploma', '24', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18082.52, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(25, 'Community Development Work', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18136.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(24, 'Early Childhood Education - Progress', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18174.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(23, 'Workplace Wellness and Health Promotion', 'Graduate Certificate', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18199.28, 125.00, 'Sep 24', '1 Year / 2 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 60+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(22, 'Developmental Services Worker', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18136.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(21, 'Community & Justice Services', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(20, 'Child and Youth Care', 'Advanced Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18144.28, 125.00, 'Sep 24', '3 Years / 6 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.5/6.0 | PTE: 58+', '', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(19, 'Social Service Worker', 'Diploma', '23', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18154.52, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(18, 'Recreation and Leisure Services', 'Diploma', '22', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18214.28, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', '', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(17, 'Early Childhood Education- Ashtonbee', 'Diploma', '21', 2, 'Toronto', 124, '2024-06-30', 2, 5.00, 18167.90, 125.00, 'Sep 24', '2 Years / 4 Semesters', '', 'Ontario Secondary School Diploma (OSSD), or equivalent, or mature student status (19 years or older), GCE A/L English (B or C), or equivalent (minimum grade required), or take the Centennial College English Skills Assessment for Admission', 'IELTS Academic: 6.0/5.5 | PTE: 51+', 'https://www.centennialcollege.ca/programs-courses/full-time/early-childhood-education-ashtonbee', 'Jan 25', '2024-09-30', '', '1970-01-01', '', '1970-01-01'),
(14, 'Bachelor of Arts', 'Undergraduate', '19', 1, 'mathara', 498, '2024-05-30', 4, 200.00, 100.00, 2000.00, 'OCT', '4 Years', 'ETS', '6', 'Ielts', 'http://localhost/global_education/new_project/program-add.php', '', '0000-00-00', '', '0000-00-00', '', '0000-00-00'),
(16, 'Bachelor of Computer Network', 'Undergraduate', '6', 1, 'Hambantota', 19, '2024-05-31', 6, 5.00, 5500.00, 4500.00, 'MAY', '4 Years', 'dfdf', 'O/L 9 Passes', 'IELTS / PTE', 'fgdfg', '', '0000-00-00', '', '0000-00-00', '', '0000-00-00'),
(12, 'Bachelor of Engineering', 'Graduate', '19', 20, 'California', 25, '1970-01-01', 1, 8.00, 500000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JAN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(13, 'Bachelor of Engineering', 'Undergraduate', '1', 1, 'mathara', 498, '2024-05-23', 1, 200.00, 20000.00, 2000.00, 'OCT', '4 Years', 'ETS', '4', 'IELTS', 'http://localhost/global_education/new_project/program-add.php', '', '0000-00-00', '', '0000-00-00', '', '0000-00-00'),
(10, 'Bachelor of Engineering', 'Undergraduate', '17', 20, 'California', 25, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JAN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(11, 'Bachelor of Engineering', 'Graduate', '18', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JAN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(9, 'Bachelor of Engineering', 'Graduate', '16', 20, 'California', 25, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JAN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(8, 'Bachelor of Engineering', 'Graduate', '15', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(15, 'Bachelor of Physiotherapy', 'undergraduate', '20', 35, 'Kandy', 19, '1970-01-01', 1, 500.00, 1000.00, 1000.00, 'january', '4 Years', 'afrfg', 'dfdf', 'afaf', 'afadf', '', '1970-01-01', '', '1970-01-01', '', '1970-01-01'),
(7, 'Bachelor of Computer science', 'Graduate', '13', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(6, 'Bachelor of Maths', 'Graduate', '12', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(5, 'Bachelor of technology', 'Graduate', '11', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'OCT', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01');
INSERT INTO `ggportal_tbl_program` (`program_id`, `program_name`, `course_type`, `course_id`, `country_id`, `city`, `institute_id`, `deadline`, `currency_id`, `commission`, `tution_fee`, `application_fee`, `intake`, `duration`, `ets`, `requirements`, `english_requirements`, `program_web_url`, `intake2`, `deadline2`, `intake3`, `deadline3`, `intake4`, `deadline4`) VALUES
(4, 'Bachelor of scince', 'Graduate', '10', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(1, 'Bachelor of commerce', 'Undergraduate', '1', 20, 'California', 498, '1970-01-01', 6, 8.00, 10000.00, 0.00, 'SEP', '10 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JaN', '1970-01-01', 'mar', '1970-01-01', 'OCT', '1970-01-01'),
(2, 'Bachelor of Arts', 'Graduate', '8', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01'),
(3, 'Bachelor of Art', 'Graduate', '9', 20, 'California', 498, '1970-01-01', 1, 8.00, 30000.00, 0.00, 'SEP', '4 Years', 'ettts', 'requirementsss', 'IELTS 4', 'https://www.dundee.ac.uk/', 'JUN', '1970-01-01', 'SEP', '1970-01-01', 'OCT', '1970-01-01');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_program_wishlist`
--

CREATE TABLE `ggportal_tbl_program_wishlist` (
  `program_wishlist_id` int(11) NOT NULL,
  `program_id` int(11) NOT NULL,
  `user` varchar(45) NOT NULL,
  `user_id` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_program_wishlist`
--

INSERT INTO `ggportal_tbl_program_wishlist` (`program_wishlist_id`, `program_id`, `user`, `user_id`) VALUES
(50, 13, 'RA', 1);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_province`
--

CREATE TABLE `ggportal_tbl_province` (
  `province_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `province_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_province`
--

INSERT INTO `ggportal_tbl_province` (`province_id`, `country_id`, `province_name`) VALUES
(1, 22, 'Center Province'),
(2, 22, 'Eastern Province'),
(3, 22, 'Northern Province'),
(4, 22, 'North Central Province'),
(5, 22, 'Northwestern Province'),
(6, 22, 'Sabaragamuwa Province'),
(7, 22, 'Southern Province'),
(8, 22, 'Uva Province'),
(9, 22, 'Western Province'),
(10, 1, 'New South Wales'),
(11, 1, 'Victoria'),
(12, 1, 'Queensland'),
(13, 1, 'Western Australia'),
(14, 1, 'South Australia'),
(18, 2, 'Newfoundland and Labrador'),
(19, 2, 'Prince Edward Island'),
(20, 2, 'Nova Scotia'),
(21, 2, 'New Brunswick'),
(22, 2, 'Quebec'),
(23, 2, 'Ontario'),
(24, 2, 'Manitoba'),
(25, 2, 'Saskatchewan'),
(26, 2, ' British Columbia'),
(27, 2, ' Alberta'),
(28, 3, 'Auvergne-Rhône-Alpes'),
(29, 3, 'Bourgogne-Franche-Comté'),
(30, 3, 'Brittany (Bretagne)'),
(31, 3, 'Centre-Val de Loire'),
(32, 3, 'Corsica (Corse)'),
(33, 3, 'Grand Est'),
(34, 3, 'Hauts-de-France'),
(35, 3, 'Île-de-France'),
(36, 3, 'Normandy (Normandie'),
(37, 3, 'Nouvelle-Aquitaine'),
(38, 3, 'Occitanie'),
(39, 3, 'Pays de la Loire'),
(40, 3, 'Provence-Alpes-Côte d\'Azur'),
(41, 4, 'Abkhazia '),
(42, 4, 'Adjara'),
(43, 4, 'Guria'),
(44, 4, 'Imereti'),
(45, 4, 'Kakheti'),
(46, 4, 'Kvemo Kartli'),
(47, 4, 'Mtskheta-Mtianeti'),
(48, 4, 'Racha-Lechkhumi and Kvemo Svaneti'),
(49, 4, 'Samegrelo-Zemo Svaneti'),
(50, 4, 'Samtskhe-Javakheti'),
(51, 4, 'Shida Kartli'),
(52, 4, 'Tbilisi'),
(53, 5, 'Baden-Württemberg'),
(54, 5, 'Bavaria'),
(55, 5, 'Berlin'),
(56, 5, 'Brandenburg'),
(57, 5, 'Bremen'),
(58, 5, 'Hamburg'),
(59, 5, 'Hesse'),
(60, 5, 'Lower Saxony'),
(61, 5, 'Mecklenburg-Vorpommern'),
(62, 5, 'North Rhine-Westphalia '),
(63, 5, 'Rhineland-Palatinate'),
(64, 5, 'Saarland'),
(65, 5, 'Saxony'),
(66, 5, 'Saxony-Anhalt'),
(67, 5, 'Schleswig-Holstein'),
(68, 5, 'Thuringia '),
(69, 6, 'Leinster'),
(70, 6, 'Munster'),
(71, 6, 'Connacht'),
(72, 6, 'Ulster'),
(73, 7, 'Abruzzo\r\n'),
(74, 7, 'Aosta Valley '),
(75, 7, 'Apulia '),
(76, 7, 'Basilicata'),
(77, 7, 'Calabria'),
(78, 7, 'Campania'),
(79, 7, 'Emilia-Romagna'),
(80, 7, 'Friuli Venezia Giulia'),
(81, 7, 'Lazio'),
(82, 7, 'Liguria'),
(83, 7, 'Lombardy'),
(84, 7, 'Marche'),
(85, 7, 'Molise'),
(86, 7, 'Piedmont '),
(87, 7, 'Sardinia '),
(88, 7, 'Sicily'),
(89, 7, 'Trentino-Alto Adige/Südtirol'),
(90, 7, 'Tuscany '),
(91, 7, 'Umbria'),
(92, 7, 'Veneto'),
(93, 8, 'Municipalities'),
(94, 8, 'Cities'),
(95, 8, 'Rural areas '),
(96, 9, 'Counties'),
(97, 9, 'Municipalities'),
(98, 9, 'Cities (miestai) and Towns (miesteliai)'),
(99, 11, 'Port Louis'),
(100, 11, 'Pamplemousses'),
(101, 11, 'Rivière du Rempart'),
(102, 11, 'Flacq'),
(103, 11, 'Grand Port'),
(104, 11, 'Savanne'),
(105, 11, 'Plaines Wilhems'),
(106, 11, 'Moka'),
(107, 11, 'Black River'),
(108, 12, 'Drenthe'),
(109, 12, 'Flevoland'),
(110, 12, 'Friesland '),
(111, 12, 'Gelderland'),
(112, 12, 'Groningen'),
(113, 12, 'Limburg'),
(114, 12, 'North Brabant'),
(115, 12, 'North Holland '),
(116, 12, 'Overijssel'),
(117, 12, 'South Holland'),
(118, 12, 'Utrecht'),
(119, 12, 'Zeeland'),
(120, 13, 'Auckland'),
(121, 13, 'New Plymouth'),
(122, 13, 'Hawke\'s Bay'),
(123, 13, 'Wellington'),
(124, 13, 'Nelson'),
(125, 13, 'Marlborough'),
(126, 13, 'Westland'),
(127, 13, 'Canterbury'),
(128, 13, 'Otago'),
(129, 13, 'Southland'),
(130, 14, 'Lower Silesian Voivodeship'),
(131, 14, 'Kuyavian-Pomeranian Voivodeship'),
(132, 14, 'Lubusz Voivodeship'),
(133, 14, 'Łódź Voivodeship'),
(134, 14, 'Lesser Poland Voivodeship'),
(135, 14, 'Masovian Voivodeship'),
(136, 14, 'Opole Voivodeship'),
(137, 14, 'Podlaskie Voivodeship'),
(138, 14, 'Pomeranian Voivodeship'),
(139, 14, 'Silesian Voivodeship'),
(140, 14, 'Subcarpathian Voivodeship'),
(141, 14, 'Świętokrzyskie Voivodeship'),
(142, 14, 'Warmian-Masurian Voivodeship'),
(143, 14, 'Greater Poland Voivodeship'),
(144, 14, 'West Pomeranian Voivodeship '),
(145, 14, 'Lublin Voivodeship'),
(146, 15, 'Central Federal District'),
(147, 15, 'North-West Federal District'),
(148, 15, 'Southern Federal District'),
(149, 15, 'North Caucasus Federal District'),
(150, 15, 'Privolzhsky Federal District'),
(151, 15, 'Urals Federal District'),
(152, 15, 'Siberian Federal District'),
(153, 15, 'Fareast Federal District'),
(154, 16, 'Central Region'),
(155, 16, 'West Region'),
(156, 16, 'East Region'),
(157, 16, 'North East Region'),
(158, 16, 'North Region'),
(159, 17, 'Appenzell Ausserrhoden'),
(160, 17, 'Appenzell Innerrhoden'),
(161, 17, 'Basel-Landschaft'),
(162, 17, 'Basel-Stadt'),
(163, 17, 'Bern'),
(164, 17, 'Fribourg'),
(165, 17, 'Geneva'),
(166, 17, 'Glarus'),
(167, 17, 'Graubünden'),
(168, 17, 'Jura'),
(169, 17, 'Lucerne'),
(170, 17, 'Neuchâtel'),
(171, 17, 'Nidwalden'),
(172, 17, 'Obwalden'),
(173, 17, ''),
(174, 17, 'Schaffhausen'),
(175, 17, 'Schwyz'),
(176, 17, 'Solothurn'),
(177, 17, 'St. Gallen'),
(178, 17, 'Thurgau'),
(179, 17, 'Ticino'),
(180, 17, 'Uri'),
(181, 17, 'Valais'),
(182, 17, 'Vaud '),
(183, 17, 'Zug'),
(184, 17, 'Zurich'),
(185, 17, 'Aargau'),
(186, 18, 'Oblasts'),
(187, 18, 'Autonomous Republic of Crimea'),
(188, 18, 'City of Kyiv'),
(189, 18, 'City of Sevastopol'),
(190, 19, 'Abu Dhabi'),
(191, 19, 'Dubai'),
(192, 19, 'Sharjah'),
(193, 19, 'Ajman'),
(194, 19, 'Umm Al-Quwain'),
(195, 19, 'Ras Al Khaimah'),
(196, 19, 'Fujairah'),
(197, 20, 'England'),
(198, 20, 'Scotland'),
(199, 20, 'Wales'),
(200, 20, 'Northern Ireland'),
(201, 21, 'States'),
(202, 21, 'Territories'),
(203, 21, 'Federal District');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_remainder`
--

CREATE TABLE `ggportal_tbl_remainder` (
  `remainder_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `title` varchar(120) NOT NULL,
  `remainder_date` datetime NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `note` varchar(500) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff`
--

CREATE TABLE `ggportal_tbl_staff` (
  `staff_id` int(11) NOT NULL,
  `staff_no` int(11) DEFAULT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(15) NOT NULL,
  `category` varchar(20) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` varchar(20) NOT NULL,
  `country_id` varchar(100) NOT NULL,
  `state` varchar(20) NOT NULL,
  `city` varchar(20) NOT NULL,
  `password` varchar(45) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `user_privilege_id` int(11) NOT NULL,
  `profile_picture` text NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `user_active_yn` varchar(1) NOT NULL DEFAULT 'N',
  `last_seen` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp(),
  `parent_user_type` varchar(20) NOT NULL,
  `parent_user_id` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_staff`
--

INSERT INTO `ggportal_tbl_staff` (`staff_id`, `staff_no`, `first_name`, `last_name`, `username`, `email`, `mobile`, `category`, `gender`, `date_of_birth`, `marital_status`, `country_id`, `state`, `city`, `password`, `password_salt`, `user_type`, `user_privilege_id`, `profile_picture`, `email_validate_yn`, `user_active_yn`, `last_seen`, `created_by`, `created_at`, `updated_by`, `updated_at`, `parent_user_type`, `parent_user_id`) VALUES
(89, 1010, 'Queen', 'Kau', '<EMAIL>', '<EMAIL>', '0752635874', '', '', '2000-02-02', '', 'all', '0', '0', 'NEJMV0g0N3UyYlBDS1lGRHdETElQdz09', '', 'SF', 1, 'dist/uploads/staff/1710831947.jpg', '', 'Y', '2024-03-19 03:05:47', 1, '2024-03-19 03:05:47', 1, '2024-03-19 03:05:47', 'RA', 1),
(94, 1014, 'Nimsara', 'Ranaweera', '<EMAIL>', '<EMAIL>', '0773452314', '', '', '2000-02-02', '', 'all', '0', '0', 'b1hzQzBBQnlZYnpUNGJnWDRuZzFVZz09', '', 'SF', 2, 'dist/uploads/staff/1712729365.png', 'Y', 'Y', '2024-05-27 17:02:53', 1, '2024-04-10 02:09:25', 1, '2024-04-10 02:09:25', 'RA', 1),
(96, 1016, 'pasindu', 'denuka', '<EMAIL>', '<EMAIL>', '0762109986', '', '', '2000-02-02', '', 'all', '0', '0', 'MU9MbXkwWEpFWjFzSDZZSmhYcDZTQT09', '', 'SF', 1, 'dist/uploads/staff/1713952190.png', 'Y', 'Y', '2024-04-24 16:27:53', 1, '2024-04-24 05:49:50', 1, '2024-04-24 05:49:50', 'RA', 1),
(74, 1005, 'Amasha', 'Ranaweera', '<EMAIL>', '<EMAIL>', '0773452314', '', '', '2000-02-02', '', 'all', '0', '0', 'NU9MNk5rejY3dDZMOVhJWnp2TC9JQT09', '', 'SF', 0, 'dist/uploads/staff/1710474914.png', '', 'Y', '2024-03-14 23:55:14', 1, '2024-03-14 23:55:14', 1, '2024-03-14 23:55:14', 'RA', 1),
(73, 1004, 'Razan', 'Thaus', '<EMAIL>', '<EMAIL>', '+94743500150', '', '', '2000-02-02', '', 'all', '0', '0', 'YjFiaXhTcEpaREp1dW4wLzRKdDNxdz09', '', 'SF', 0, 'dist/img/user2-160x160.jpg', 'Y', 'Y', '2024-03-15 11:59:49', 1, '2024-03-14 05:18:52', 1, '2024-03-14 05:18:52', 'RA', 1),
(95, 1015, 'Bipem', 'Rartg', '<EMAIL>', '<EMAIL>', '0723658975', '', '', '2000-02-02', '', 'all', '0', '0', 'alZDWWJUaWJmc2xucmxuMVNmNU93dz09', '', 'SF', 3, 'dist/uploads/staff/1713347549.png', 'Y', 'Y', '2024-04-24 16:06:50', 1, '2024-04-17 05:52:29', 1, '2024-04-17 05:52:29', 'RA', 1),
(91, 1012, 'Khalid', 'Aman', '<EMAIL>', '<EMAIL>', '', '', '', '2000-02-02', '', 'all', '0', '0', 'Tk5sQkozb2xzT21BZGF4d3dJUnZYUT09', '', 'SF', 1, 'dist/img/user2-160x160.jpg', 'Y', 'Y', '2024-04-25 14:35:49', 1, '2024-04-01 03:20:06', 1, '2024-04-25 05:03:55', 'RA', 1),
(70, 1001, 'Umair', 'Linnas', '<EMAIL>', '<EMAIL>', '', '', '', '2000-02-02', '', 'all', '0', '0', 'SENTRkhVb0lrcHIrejFLWVIrSWZhdz09', '', 'SF', 8, 'dist/img/user2-160x160.jpg', 'Y', 'Y', '2024-03-18 14:53:05', 44, '2024-03-12 06:23:25', 44, '2024-03-12 06:23:25', 'AG', 44);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_activity`
--

CREATE TABLE `ggportal_tbl_staff_activity` (
  `user_activity_id` int(10) UNSIGNED NOT NULL,
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `activity_description` text NOT NULL,
  `created_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_login`
--

CREATE TABLE `ggportal_tbl_staff_login` (
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `date_login` datetime NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_staff_login`
--

INSERT INTO `ggportal_tbl_staff_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(1713, 70, '2024-03-13 00:21:31', '***************', '2024-03-13 00:21:31'),
(1714, 71, '2024-03-13 02:27:05', '***************', '2024-03-13 02:27:05'),
(1715, 72, '2024-03-13 06:57:03', '***************', '2024-03-13 06:57:03'),
(1716, 73, '2024-03-15 02:17:38', '***************', '2024-03-15 02:17:38'),
(1717, 73, '2024-03-15 02:29:41', '***************', '2024-03-15 02:29:41'),
(1718, 71, '2024-03-15 02:32:33', '***************', '2024-03-15 02:32:33'),
(1719, 86, '2024-03-18 03:54:52', '***************', '2024-03-18 03:54:52'),
(1720, 86, '2024-03-18 03:59:30', '***************', '2024-03-18 03:59:30'),
(1721, 70, '2024-03-18 05:15:51', '*************', '2024-03-18 05:15:51'),
(1722, 70, '2024-03-18 05:17:46', '*************', '2024-03-18 05:17:46'),
(1723, 90, '2024-03-19 03:11:16', '***************', '2024-03-19 03:11:16'),
(1724, 94, '2024-04-10 02:18:20', '**************', '2024-04-10 02:18:20'),
(1725, 94, '2024-04-10 04:41:38', '112.134.234.95', '2024-04-10 04:41:38'),
(1726, 94, '2024-04-17 02:45:43', '112.134.232.254', '2024-04-17 02:45:43'),
(1727, 94, '2024-04-17 02:52:54', '124.43.240.186', '2024-04-17 02:52:54'),
(1728, 94, '2024-04-17 03:09:12', '112.134.232.254', '2024-04-17 03:09:12'),
(1729, 94, '2024-04-17 05:34:12', '124.43.240.186', '2024-04-17 05:34:12'),
(1730, 95, '2024-04-17 05:53:52', '112.134.232.254', '2024-04-17 05:53:52'),
(1731, 95, '2024-04-17 05:57:52', '112.134.232.254', '2024-04-17 05:57:52'),
(1732, 94, '2024-04-22 04:21:16', '112.134.232.254', '2024-04-22 04:21:16'),
(1733, 94, '2024-04-23 23:33:36', '112.134.232.254', '2024-04-23 23:33:36'),
(1734, 94, '2024-04-23 23:54:09', '112.134.232.254', '2024-04-23 23:54:09'),
(1735, 95, '2024-04-23 23:59:33', '112.134.232.254', '2024-04-23 23:59:33'),
(1736, 94, '2024-04-24 01:04:55', '112.134.232.254', '2024-04-24 01:04:55'),
(1737, 94, '2024-04-24 04:17:40', '112.134.232.254', '2024-04-24 04:17:40'),
(1738, 94, '2024-04-24 04:55:11', '124.43.241.49', '2024-04-24 04:55:11'),
(1739, 94, '2024-04-24 04:55:43', '124.43.241.211', '2024-04-24 04:55:43'),
(1740, 96, '2024-04-24 05:51:31', '124.43.241.211', '2024-04-24 05:51:31'),
(1741, 94, '2024-04-24 06:00:38', '112.134.232.254', '2024-04-24 06:00:38'),
(1742, 94, '2024-04-24 06:08:44', '112.134.232.254', '2024-04-24 06:08:44'),
(1743, 95, '2024-04-24 06:33:26', '112.134.232.254', '2024-04-24 06:33:26'),
(1744, 95, '2024-04-24 06:33:51', '112.134.232.254', '2024-04-24 06:33:51'),
(1745, 95, '2024-04-24 06:36:32', '112.134.232.254', '2024-04-24 06:36:32'),
(1746, 94, '2024-04-24 06:58:19', '124.43.241.211', '2024-04-24 06:58:19'),
(1747, 94, '2024-04-25 00:52:26', '124.43.241.173', '2024-04-25 00:52:26'),
(1748, 94, '2024-04-25 02:21:51', '112.134.232.254', '2024-04-25 02:21:51'),
(1749, 94, '2024-04-25 04:44:42', '112.134.232.254', '2024-04-25 04:44:42'),
(1750, 91, '2024-04-25 05:05:11', '112.134.212.182', '2024-04-25 05:05:11'),
(1751, 94, '2024-04-26 01:10:27', '124.43.240.79', '2024-04-26 01:10:27'),
(1752, 94, '2024-04-26 01:12:35', '124.43.240.79', '2024-04-26 01:12:35'),
(1753, 94, '2024-04-26 02:46:46', '124.43.240.79', '2024-04-26 02:46:46'),
(1754, 94, '2024-04-29 07:34:08', '112.134.232.254', '2024-04-29 07:34:08'),
(1755, 94, '2024-05-13 05:58:54', '112.134.234.25', '2024-05-13 05:58:54'),
(1756, 94, '2024-05-14 04:49:00', '112.134.234.25', '2024-05-14 04:49:00'),
(1757, 94, '2024-05-14 04:53:24', '112.134.234.25', '2024-05-14 04:53:24'),
(1758, 94, '2024-05-16 06:09:27', '112.134.234.25', '2024-05-16 06:09:27'),
(1759, 94, '2024-05-27 06:09:24', '112.134.234.25', '2024-05-27 06:09:24');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_staff_privilege`
--

CREATE TABLE `ggportal_tbl_staff_privilege` (
  `staff_privilege_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `access_level` varchar(20) NOT NULL,
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_staff_privilege`
--

INSERT INTO `ggportal_tbl_staff_privilege` (`staff_privilege_id`, `name`, `access_level`, `active_yn`) VALUES
(3, 'Trainee', '80', 'Y'),
(2, 'Counselor', '85', 'Y'),
(1, 'Senior Counselor', '90', 'Y'),
(4, 'Country Relationship Managers\n', '70', 'N'),
(5, 'Accountant', '50', 'N'),
(6, 'Sales Director', '40', 'N'),
(7, 'County Head', '75', 'N'),
(8, 'Agent Staff', '90', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_state`
--

CREATE TABLE `ggportal_tbl_state` (
  `state_id` int(11) NOT NULL,
  `country_id` int(11) NOT NULL,
  `state_name` varchar(45) NOT NULL,
  `state_code` varchar(20) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_state`
--

INSERT INTO `ggportal_tbl_state` (`state_id`, `country_id`, `state_name`, `state_code`) VALUES
(1, 1, 'Victoria', ''),
(2, 1, 'New South Wales', ''),
(3, 1, 'Queensland', ''),
(4, 1, 'Australian Capital Territory', ''),
(5, 1, 'Western Australia', ''),
(6, 1, 'South Australia', ''),
(7, 1, 'New South Wales/Victoria', ''),
(8, 1, 'Tasmania', ''),
(9, 1, 'New South Wales/Western Australia', ''),
(10, 2, 'NovaScotia', ''),
(11, 2, 'British Columbia', ''),
(12, 2, 'Ontario', ''),
(13, 2, 'Manitoba', ''),
(14, 2, 'Quebec', ''),
(15, 2, 'Alberta', ''),
(16, 2, 'NewBrunswick', ''),
(17, 2, 'Saskatchewan', ''),
(18, 2, 'New Foundland', ''),
(19, 2, 'Prince Edward', ''),
(20, 2, 'Yukon', ''),
(21, 2, 'Quebec/British Columbia', ''),
(22, 2, 'New Brunswick', ''),
(23, 2, 'Montreal', ''),
(24, 2, 'Washington', ''),
(25, 3, 'Paris', ''),
(26, 3, 'Puy-de-Dome', ''),
(27, 3, 'North Carolina', ''),
(28, 4, 'Tbilisi', ''),
(29, 5, 'Hamburg', ''),
(30, 5, 'Berlin', ''),
(31, 5, 'Hannover', ''),
(32, 5, 'Bavaria', ''),
(33, 6, 'Ulster', ''),
(34, 6, 'Dublin', ''),
(35, 6, 'Westmeath', ''),
(36, 6, 'Maynooth', ''),
(37, 6, 'Galway', ''),
(38, 6, 'Cork', ''),
(39, 6, 'Limerick', ''),
(40, 6, 'Donegal', ''),
(41, 6, 'Carlow', ''),
(42, 6, 'Kilkenny', ''),
(43, 6, 'Waterford', ''),
(44, 7, 'Padova', ''),
(45, 8, 'Riga', ''),
(46, 9, 'Vilniaus', ''),
(47, 10, 'Valletta', ''),
(48, 10, 'Western', ''),
(49, 11, 'Port Louis', ''),
(50, 12, 'Noord-Holland', ''),
(51, 13, 'Auckland', ''),
(52, 13, 'New Plymouth', ''),
(53, 13, 'Christchurch', ''),
(54, 13, 'Auckland/Dunedin', ''),
(55, 13, 'Christchurch/ Auckland', ''),
(56, 13, 'North Island', ''),
(57, 13, 'Wellington', ''),
(58, 13, 'Nelson/ Richmond/Blenheim', ''),
(59, 13, 'Porirua/Aucklan', ''),
(60, 13, 'Invercargill/ Queenstown/ Christchurch/ Gore/', ''),
(61, 13, 'Hamilton', ''),
(62, 13, 'Oamaru', ''),
(63, 13, 'Dunedin', ''),
(64, 13, 'Whangarei', ''),
(65, 14, 'Warszawa', ''),
(66, 15, 'Adygeja', ''),
(67, 15, 'Moscow', ''),
(68, 15, 'Chechenija', ''),
(69, 15, 'Kabardino-Balkarija', ''),
(70, 15, 'Tatarstan', ''),
(71, 15, 'Pskov', ''),
(72, 15, 'Sankt-Peterburg', ''),
(73, 15, 'Tula', ''),
(74, 15, 'Jaroslavl', ''),
(75, 15, 'Tomsk', ''),
(76, 15, 'Idaho', ''),
(77, 15, 'Sverdlovsk', ''),
(78, 15, 'Krasnodar', ''),
(79, 15, 'Kyyiv', ''),
(80, 16, 'Singapore', ''),
(81, 17, 'Lucerne', ''),
(82, 18, 'Odessa', ''),
(83, 18, 'Kharkiv', ''),
(85, 18, 'Poltavska', ''),
(86, 19, 'Dubai', ''),
(87, 20, 'Wales', ''),
(88, 20, 'EAST OF ENGLAND', ''),
(89, 20, 'WEST MIDLANDS ENGLAND', ''),
(90, 20, 'SOUTH WEST ENGLAND', ''),
(91, 20, 'London', ''),
(92, 20, 'EAST MIDLANDS ENGLAND', ''),
(93, 20, 'Scotland', ''),
(94, 20, 'NORTH WEST ENGLAND', ''),
(95, 20, 'NORTH EAST ENGLAND', ''),
(96, 20, 'Northern Ireland', ''),
(97, 20, 'SOUTH EAST ENGLAND', ''),
(98, 20, 'YORKSHIRE AND HUMBER ENGLAND', ''),
(99, 20, 'WEST YORKSHIRE ENGLAND', ''),
(100, 20, 'YORKSHIRE AND THE HUMBER ENGLA', ''),
(101, 20, 'SOUTH YORKSHIRE ENGLAND', ''),
(102, 20, 'NORTH YORKSHIRE ENGLAND', ''),
(103, 20, 'WEST MIDLANDS ENGLAND AND LOND', ''),
(104, 20, 'NORTH WEST AND LONDON', ''),
(105, 20, 'Wrexham', ''),
(106, 20, 'Birmingham/London/Lampter/Swansea/Tileyard Lo', ''),
(107, 20, 'Tierra del Fuego', ''),
(108, 20, 'Pitcairn Island', ''),
(109, 20, 'England', ''),
(110, 21, 'Virginia', ''),
(111, 21, 'New York', ''),
(112, 21, 'Oregon', ''),
(113, 21, 'Florida', ''),
(114, 21, 'Missouri', ''),
(115, 21, 'West Virginia', ''),
(116, 21, 'Massachusetts', ''),
(117, 21, 'Alabama', ''),
(118, 21, 'Pennsylvania', ''),
(119, 21, 'Georgia', ''),
(120, 21, 'Illinois', ''),
(121, 21, 'New Jersey', ''),
(122, 21, 'New Hampshire', ''),
(123, 21, 'Connecticut', ''),
(124, 21, 'Colorado', ''),
(125, 21, 'Milwaukee', ''),
(126, 21, 'Ohio', ''),
(127, 21, 'Nevada', ''),
(128, 21, 'Arizona', ''),
(129, 21, 'DENVER', ''),
(130, 21, 'Maryland', ''),
(131, 21, 'Nebraska', ''),
(132, 21, 'California', ''),
(133, 21, 'Michigan', ''),
(134, 21, 'Indiana', ''),
(135, 21, 'Kansas', ''),
(136, 21, 'Tennessee', ''),
(137, 21, 'Texas', ''),
(138, 21, 'Kentucky', ''),
(139, 21, 'Medison', ''),
(140, 21, 'Oklahoma', ''),
(141, 21, 'New Jersy', ''),
(142, 21, 'Wisconsin', ''),
(143, 21, 'Mississippi', ''),
(144, 21, 'Iowa', ''),
(145, 21, 'New Mexico', ''),
(146, 21, 'Arkansas', ''),
(147, 21, 'South Carolina', ''),
(148, 21, 'Rhode Island', ''),
(149, 21, 'Louisiana', ''),
(150, 21, 'Montgomery', ''),
(151, 21, 'Utah', ''),
(152, 21, 'Vermont', ''),
(153, 21, 'Montana', ''),
(154, 21, 'Hawaii', ''),
(155, 21, 'South Dakota', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student`
--

CREATE TABLE `ggportal_tbl_student` (
  `student_id` int(11) NOT NULL,
  `student_no` int(11) NOT NULL,
  `first_name` varchar(45) NOT NULL,
  `last_name` varchar(45) NOT NULL,
  `username` varchar(45) NOT NULL,
  `email` varchar(45) NOT NULL,
  `mobile` varchar(15) NOT NULL,
  `category` varchar(20) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` varchar(20) NOT NULL,
  `country` varchar(100) NOT NULL,
  `state` varchar(50) NOT NULL,
  `city` varchar(50) NOT NULL,
  `password` varchar(45) NOT NULL,
  `password_salt` varchar(45) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `profile_picture` text NOT NULL,
  `email_validate_yn` varchar(1) NOT NULL DEFAULT 'N',
  `user_active_yn` varchar(1) DEFAULT 'N',
  `last_seen` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `online_status` int(1) NOT NULL DEFAULT 0,
  `emergency_contact_name` varchar(50) NOT NULL,
  `emergency_contact_relation` varchar(50) NOT NULL,
  `emergency_contact_mobile` varchar(20) NOT NULL,
  `remarks` text NOT NULL,
  `real_time_status` varchar(1) NOT NULL DEFAULT 'Y',
  `token_id` varchar(500) DEFAULT NULL,
  `token_created_time` datetime DEFAULT NULL,
  `timeline_id` int(11) NOT NULL DEFAULT 1,
  `assign_to_staff` int(11) NOT NULL DEFAULT 0,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) NOT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp(),
  `nic` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `province` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `assign_to_type` varchar(6) DEFAULT NULL,
  `assign_to_agent` int(11) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student`
--

INSERT INTO `ggportal_tbl_student` (`student_id`, `student_no`, `first_name`, `last_name`, `username`, `email`, `mobile`, `category`, `gender`, `date_of_birth`, `marital_status`, `country`, `state`, `city`, `password`, `password_salt`, `user_type`, `profile_picture`, `email_validate_yn`, `user_active_yn`, `last_seen`, `online_status`, `emergency_contact_name`, `emergency_contact_relation`, `emergency_contact_mobile`, `remarks`, `real_time_status`, `token_id`, `token_created_time`, `timeline_id`, `assign_to_staff`, `created_by`, `created_at`, `updated_by`, `updated_at`, `nic`, `address`, `province`, `postal_code`, `assign_to_type`, `assign_to_agent`) VALUES
(166, 200064, 'tashen', 'maduwantha', '<EMAIL>', '<EMAIL>', ' +9471717717717', 'IN', 'male', '2024-05-02', 'unmarried', '2', '', 'mathara', 'R2FnV3NROGc4VWJaOHRlc2F6TXlUUT09', '', 'ST', 'dist/uploads/student/1716284237.png', 'N', 'Y', '2024-05-21 05:37:17', 0, '111111', 'wwww', '111111', 'remark', 'Y', NULL, NULL, 1, 0, 1, '2024-05-21 05:37:17', 1, '2024-05-21 05:37:17', '11111111', 'mathara', '26', '1111', '', 0),
(167, 200068, 'pasindu', 'denuka', '<EMAIL>', '<EMAIL>', ' +9487867867678', 'EX', 'male', '2024-05-23', 'unmarried', '2', '', 'test', 'Uk1YbGdEalhwODc2U3B4ekZsRmpHdz09', '', 'ST', 'dist/uploads/student/1716285050.jpg', 'N', 'Y', '2024-05-21 05:50:50', 0, 'test', 'test', '435643646', 'test', 'Y', NULL, NULL, 1, 45, 45, '2024-05-21 05:50:50', 45, '2024-05-21 05:50:50', '7978978978', 'test', '24', 'test', 'AG', 45),
(165, 200060, 'tashen', 'maduwantha', '<EMAIL>', '<EMAIL>', ' +9471717717717', 'IN', 'male', '2024-05-02', 'unmarried', '2', '', 'mathara', 'elBCYThSUFlXSnBUbFU2Z1htQkRSQT09', '', 'ST', 'dist/uploads/student/1716284163.png', 'N', 'Y', '2024-05-21 05:36:03', 0, '111111', 'wwww', '111111', 'remark', 'Y', NULL, NULL, 1, 0, 1, '2024-05-21 05:36:03', 1, '2024-05-21 05:36:03', '11111111', 'mathara', '26', '1111', '', 0),
(164, 200058, 'test', 'Test12', '<EMAIL>', '<EMAIL>', ' +9467789033', 'IN', 'female', '2024-05-06', 'unmarried', '14', '', 'test', 'S1B1RGM1bllsRWJnUHBNalFidjVHdz09', '', 'ST', 'dist/uploads/student/1716284061.png', 'N', 'Y', '2024-05-21 05:34:21', 0, 'test', 'test', '123456', 'test', 'Y', NULL, NULL, 1, 0, 1, '2024-05-21 05:34:21', 1, '2024-05-21 05:34:21', '1111111111', 'test', '130', '1234', '', 0),
(163, 200055, 'pasindu', 'denuka', '<EMAIL>', '<EMAIL>', '', 'IN', 'male', '2024-05-22', 'unmarried', '4', '', 'mathara', 'Rm90WVU2a1lRWEtYVERjV2tDN3F4UT09', '', 'ST', 'dist/uploads/student/1716283506.jpg', 'N', 'Y', '2024-05-21 05:25:06', 0, 'pasi', 'no', '0762109986', 'test', 'Y', NULL, NULL, 1, 45, 45, '2024-05-21 05:25:06', 45, '2024-05-21 05:25:06', '980770', 'mathara', '43', '81200', 'AG', 45),
(162, 200052, 'maduwantha', 'hk', '<EMAIL>', '<EMAIL>', ' +94001010', 'IN', 'male', '2024-05-01', 'unmarried', '1', '', 'mathara', 'RFkxOEhSNzg4eTUzdHJIT3FUWitUdz09', '', 'ST', 'dist/uploads/student/1715752913.jfif', 'N', 'Y', '2024-05-15 02:01:53', 0, '111111', 'wwww', '111111111111', 'remark', 'Y', NULL, NULL, 1, 0, 1, '2024-05-15 02:01:53', 1, '2024-05-15 02:01:53', '1234', 'mathara', '12', '1111', '', 0),
(161, 200049, 'Amasha', 'Ranaweera', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'IN', 'female', '2009-06-13', 'married', '5', '', 'Melbourne ', 'ek9HTzJZcjlFWHJpWXFPbkN5dHMyUT09', '', 'ST', 'dist/uploads/student/1715594162.avif', 'Y', 'Y', '2024-05-13 15:28:01', 0, 'SRC Ranaweera', 'father', '54545545235253525', 'fdfdfdgfgfggdgdgdg', 'Y', NULL, NULL, 2, 45, 45, '2024-05-13 05:56:02', 45, '2024-05-13 05:56:58', '20054567890v', 'gggewefw', '58', '56000', 'AG', 45),
(159, 200045, 'birku', 'jufyi', '<EMAIL>', '<EMAIL>', ' +9487496', 'IN', 'female', '2024-05-15', 'married', '2', '', 'Matara', 'aW9hekNwNElBTlBqdUtRRy9jbjZ5QT09', '', 'ST', 'dist/uploads/student/1714717265.png', 'Y', 'Y', '2024-05-03 12:10:41', 0, 'Gandharvasen', 'Husband', '0725634598', 'fdgh', 'Y', NULL, NULL, 2, 0, 1, '2024-05-03 02:21:05', 1, '2024-05-03 02:24:48', '985624562V', 'Matara', '27', '81000', '', 0),
(158, 200042, 'rignu', 'melma', '<EMAIL>', '<EMAIL>', ' +9487496', 'IN', 'female', '2024-05-15', 'married', '2', '', 'Matara', 'eU9sdDR6b090L1E2YTZ4SkY3WjgyUT09', '', 'ST', 'dist/uploads/student/1714717124.png', 'N', 'Y', '2024-05-03 02:18:44', 0, 'Gandharvasen', 'Husband', '0725634598', 'fdgh', 'Y', NULL, NULL, 1, 0, 1, '2024-05-03 02:18:44', 1, '2024-05-03 02:18:44', '985624562V', 'Matara', '27', '81000', '', 0),
(157, 200040, 'Rashmi', 'radhika', '<EMAIL>', '<EMAIL>', '56423012', 'IN', 'female', '2024-05-01', 'unmarried', '20', '', 'Melbourne ', 'dVd1SmNaTTl1SXNROEJ1TG9tVWYrUT09', '', 'ST', 'dist/uploads/student/1714382447.avif', 'N', 'Y', '2024-04-29 05:20:47', 0, 'SRC Ranaweera', 'father', '32324343424242', 'remark', 'Y', NULL, NULL, 2, 0, 1, '2024-04-29 04:45:02', 1, '2024-04-29 05:20:47', '20054567890v', 'gggewefw', '197', '56000', '', 0),
(156, 200038, 'pasindu', 'denuka', '<EMAIL>', '<EMAIL>', ' +94762109986', 'EX', '', '1990-01-01', '', '', '', '', 'dXpFRUhZODNQRzRab0Yya0VaN1RhQT09', '', 'ST', '', 'Y', 'Y', '2024-04-26 12:15:01', 0, '', '', '', '', '', NULL, NULL, 1, 0, 0, '2024-04-26 02:11:24', 0, '2024-04-26 12:15:01', '', '', '', '', NULL, 0),
(153, 200029, 'Anne', 'Berman ', '<EMAIL>', '<EMAIL>', '456423012', 'EX', 'male', '2024-04-18', 'married', '14', '', 'Melbourne ', 'RXFoejROMWpTSVRjZUNRUC9wZWJNdz09', '', 'ST', 'dist/uploads/student/1713776338.png', 'N', 'Y', '2024-04-24 05:09:39', 0, 'SRC Ranaweera', 'father', '65657577676767676776', 'remarks', 'Y', NULL, NULL, 1, 94, 94, '2024-04-22 04:58:58', 94, '2024-04-24 05:09:39', '200545670656v', 'gggewefw', '136', '56000', 'SF', 0),
(154, 200032, 'John', 'Berman ', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'IN', 'male', '2024-04-06', 'married', '14', '', 'Melbourne ', 'eDJVWFUrSjBLTUVzVkIvVnM5OThQZz09', '', 'ST', 'dist/uploads/student/1713776387.png', 'N', 'Y', '2024-04-22 04:59:47', 0, 'SRC Ranaweera', 'father', '65657577676767676776', '', 'Y', NULL, NULL, 1, 94, 94, '2024-04-22 04:59:47', 94, '2024-04-22 04:59:47', '200545670656v', 'gggewefw', '136', '56000', 'SF', 0),
(155, 200034, 'chaumadhya', 'wanigarathna', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'IN', 'female', '2013-02-28', 'unmarried', '13', '', 'Melbourne ', 'ZVpZVGEwL01qNGpHREQ4WUgvNTZDZz09', '', 'ST', 'dist/uploads/student/1713950686.png', 'Y', 'Y', '2024-05-28 11:42:12', 0, 'SRC Ranaweera', 'father', '3433545466', 'REMARKS ', 'Y', NULL, NULL, 2, 0, 1, '2024-04-24 05:24:46', 1, '2024-04-24 06:02:48', '20054567890v', 'gggewefw', '121', '56000', '', 0),
(150, 200026, 'dinushka', 'malshan', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'IN', 'male', '2024-04-27', 'married', '16', '', 'Melbourne ', 'NlE3enE3UmZaWlBBTnlLckRiemtjUT09', '', 'ST', 'dist/uploads/student/1713424704.png', 'N', 'Y', '2024-04-18 03:18:24', 0, 'SRC Ranaweera', 'father', '1655656565656565', '', 'Y', NULL, NULL, 1, 0, 1, '2024-04-18 03:18:24', 1, '2024-04-18 03:18:24', '20054567890v', 'gggewefw', '157', '56000', '', 0),
(148, 200021, 'rartizuknu', 'gufum', '<EMAIL>', '<EMAIL>', ' +94752639855', 'EX', '', '1990-01-01', '', '', '', '', 'RXZjZjNraDFMNzAyWGs0dnRSbzRHQT09', '', 'ST', '', 'Y', 'Y', '2024-04-25 12:51:08', 0, '', '', '', '', '', NULL, NULL, 1, 0, 0, '2024-04-18 02:28:14', 0, '2024-04-18 02:29:15', '', '', '', '', NULL, 0),
(147, 200019, 'kincaid', 'cashbenties', '<EMAIL>', '<EMAIL>', '5896589', 'EX', 'male', '2024-04-22', 'married', '9', '', '', 'RW1iZWdHSjlQNjV6NmJBN2NyNEpCQT09', '', 'ST', 'dist/img/user2-160x160.jpg', 'N', 'Y', '2024-04-22 13:55:56', 0, 'Gandharvasen', 'Dad', '0712369854', 'fhk', '', NULL, NULL, 2, 0, 0, '2024-04-18 00:06:22', 147, '2024-04-22 01:14:19', '985624562V', '', '', '', '', 0),
(146, 200015, 'Amasha', 'Ranaweera', '<EMAIL>', '<EMAIL>', ' +9456423012323', 'IN', 'female', '2024-04-25', 'unmarried', '13', '', 'Melbourne ', 'eTFHMy92eW5rRVZVVXY2ZUs3a2Nvdz09', '', 'ST', 'dist/uploads/student/1712227563.png', 'N', 'Y', '2024-04-04 06:46:03', 0, 'SRC Ranaweera', 'father', '54545454542545', '', 'Y', NULL, NULL, 1, 0, 1, '2024-04-04 06:46:03', 1, '2024-04-04 06:46:03', '20054567890v', 'gggewefw', '120', '56000', '', 0),
(145, 200012, 'Nimsara', 'Ranaweera', '<EMAIL>', '<EMAIL>', '56423012', 'IN', 'male', '2024-04-03', 'unmarried', '15', '', 'Matara', 'cXRJdzM0eXVqZy8vU0pvYVRzNkxmQT09', '', 'ST', 'dist/img/user2-160x160.jpg', 'N', 'Y', '2024-04-04 05:38:09', 0, 'SRC Ranaweera', 'father', '0774563412', '', 'Y', NULL, NULL, 7, 0, 1, '2024-04-03 01:55:17', 1, '2024-04-03 01:58:12', '20054567890v', '', '150', '56000', '', 0),
(143, 200007, 'DD', 'agaA', '<EMAIL>', '<EMAIL>', ' +94787772322', 'EX', '', '1990-01-01', '', '', '', '', 'b0FGRzJDZm1GK2ZGUXJTTXlBRW5DZz09', '', 'ST', '', 'Y', 'Y', '2024-03-18 10:42:27', 0, '', '', '', '', '', NULL, NULL, 1, 0, 0, '2024-03-18 00:55:05', 0, '2024-03-18 10:42:27', '', '', '', '', NULL, 0),
(142, 200003, 'Ayzan', 'Ahamed', '<EMAIL>', '<EMAIL>', '471986578', 'EX', 'male', '0000-00-00', 'unmarried', '20', '', 'Melbourne ', 'aWhhWFNtNFpnMTlHREswODBjZmVOZz09', '', 'ST', 'dist/uploads/student/1714017197.jpg', 'N', 'N', '2024-04-24 23:57:35', 0, 'SRC Ranaweera', 'Father', '0715698456', 'This is remark', 'Y', NULL, NULL, 6, 0, 0, '2024-03-14 10:06:12', 1, '2024-04-24 23:57:35', '20054567890v', 'Matara', '197', '56000', '', 0),
(144, 200009, 'Queen', 'Padmawathi', '<EMAIL>', '<EMAIL>', '123', 'IN', 'female', '2024-04-22', 'married', '4', '', 'Matara', '123456', '', 'ST', 'dist/uploads/student/1710746938.jpg', 'N', 'Y', '2024-04-24 00:20:27', 0, 'Gandharvasen', 'Dad', '0712569958', 'hvgzjh', 'Y', '', '1971-08-15 05:45:18', 2, 0, 1, '2024-03-18 03:28:58', 1, '2024-04-22 07:25:11', '985655555V', 'Matara', '', '81000', '', 0),
(168, 200071, 'tashen', 'maduwantha', '<EMAIL>', '<EMAIL>', ' +9471717717717', 'IN', 'male', '2024-05-02', 'unmarried', '1', '', 'mathara', 'dUEyQ3FUQ0gvRnNvN2J2bGRjNmR5UT09', '', 'ST', 'dist/uploads/student/1716791219.png', 'N', 'Y', '2024-05-27 02:26:59', 0, '111111', 'wwww', '4444', 'remark', 'Y', NULL, NULL, 1, 0, 1, '2024-05-27 02:26:59', 1, '2024-05-27 02:26:59', '11111111', 'mathara', '10', '222', '', 0);

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_application`
--

CREATE TABLE `ggportal_tbl_student_application` (
  `student_application_id` int(11) NOT NULL,
  `application_no` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `my_prefer_yn` varchar(1) NOT NULL DEFAULT 'N',
  `institute_id` int(11) NOT NULL,
  `program_id` varchar(200) NOT NULL,
  `intake` varchar(20) NOT NULL,
  `year` varchar(4) NOT NULL,
  `remarks` varchar(120) NOT NULL,
  `status` varchar(50) NOT NULL,
  `application_fee` double(10,2) NOT NULL,
  `tuition_fee` double(10,2) NOT NULL,
  `tuition_fee_due_date` date NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT current_timestamp(),
  `country_code` varchar(10) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_application`
--

INSERT INTO `ggportal_tbl_student_application` (`student_application_id`, `application_no`, `student_id`, `my_prefer_yn`, `institute_id`, `program_id`, `intake`, `year`, `remarks`, `status`, `application_fee`, `tuition_fee`, `tuition_fee_due_date`, `created_by`, `created_date`, `country_code`) VALUES
(67, 4500014, 144, 'Y', 474, '6', 'jul', '2022', 'hgjgdhdfhfdhdhdhhh', '0', 2100.00, 23090.00, '2024-04-09', 0, '2024-04-09 06:08:55', '<? $countr'),
(66, 4500012, 145, 'Y', 474, '603', 'jan', '2022', 'rgrgrgw', '5', 2100.00, 23090.00, '2024-04-04', 1, '2024-04-04 05:29:59', '<? $countr'),
(65, 4500009, 144, 'Y', 474, '9', 'may', '2022', '', '0', 2100.00, 23090.00, '2024-04-04', 1, '2024-04-04 05:28:40', '<? $countr'),
(64, 4500006, 144, 'Y', 8, '604', 'jan', '2022', '', '2', 0.00, 2000.00, '2024-03-20', 1, '2024-03-20 01:28:23', '<? $countr'),
(63, 4500004, 144, 'Y', 498, '5', 'sep', '2024', 'Apply now', '0', 0.00, 24000.00, '2024-03-20', 1, '2024-03-20 00:22:28', '<? $countr'),
(62, 4500001, 142, 'Y', 140, '600', 'sep', '2024', 'With IELTS', '2', 0.00, 0.00, '2024-03-20', 1, '2024-03-20 00:21:36', '<? $countr'),
(68, 4500018, 149, 'Y', 474, '635', 'jan', '2022', '', '1', 0.00, 10000.00, '2024-04-18', 1, '2024-04-18 02:37:00', '<? $countr'),
(69, 4500021, 151, 'Y', 474, '635', 'jan', '2022', 'description ', '2', 0.00, 10000.00, '2024-04-18', 1, '2024-04-18 03:32:37', '<? $countr'),
(70, 4500023, 147, 'Y', 474, '635', 'jan', '2022', '', '0', 0.00, 10000.00, '2024-04-19', 1, '2024-04-19 05:53:57', '<? $countr'),
(71, 4500026, 142, 'Y', 474, '635', 'jan', '2022', '', '1', 0.00, 10000.00, '2024-04-19', 1, '2024-04-19 06:14:56', '<? $countr'),
(72, 4500030, 144, 'Y', 474, '635', 'sep', '2022', '', '0', 0.00, 10000.00, '2024-04-22', 1, '2024-04-22 00:34:12', '<? $countr'),
(73, 4500034, 142, 'Y', 498, '655', 'jan', '2022', 'gfgfgfd', '1', 0.00, 30000.00, '2024-04-24', 1, '2024-04-24 07:07:20', '<? $countr'),
(74, 4500038, 145, 'Y', 498, '661', 'oct', '2025', 'remark', '0', 0.00, 30000.00, '2024-05-02', 1, '2024-05-02 01:41:27', '<? $countr'),
(75, 4500042, 159, 'Y', 19, '15', 'jan', '2022', 'fgxhf', '0', 1000.00, 1000.00, '2024-05-03', 0, '2024-05-03 02:40:31', '<? $countr'),
(76, 4500046, 159, 'Y', 19, '16', 'jan', '2022', 'rtyrtyh', '0', 4500.00, 5500.00, '2024-05-03', 1, '2024-05-03 02:42:49', '<? $countr'),
(77, 4500050, 159, 'Y', 19, '16', 'aug', '2024', 'rtgrf', '0', 4500.00, 5500.00, '2024-05-03', 1, '2024-05-03 03:06:34', '<? $countr'),
(78, 4500053, 156, 'Y', 19, '15', 'jul', '2025', 'pasindu', '1', 1000.00, 1000.00, '2024-05-09', 1, '2024-05-09 02:41:11', '<? $countr'),
(79, 4500057, 161, 'Y', 498, '3', 'apr', '2024', 'Remark', '2', 0.00, 30000.00, '2024-05-21', 45, '2024-05-21 05:11:49', '<? $countr'),
(80, 4500060, 164, 'Y', 498, '2', 'jan', '2022', 'test', '1', 0.00, 30000.00, '2024-05-21', 1, '2024-05-21 05:49:15', '<? $countr');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_dependence`
--

CREATE TABLE `ggportal_tbl_student_dependence` (
  `dependence_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `dependence_type` varchar(20) NOT NULL,
  `dependence_age` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_disability`
--

CREATE TABLE `ggportal_tbl_student_disability` (
  `disability_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `description` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_document`
--

CREATE TABLE `ggportal_tbl_student_document` (
  `student_document_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `file_name` varchar(200) NOT NULL,
  `doc_url` varchar(500) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_document`
--

INSERT INTO `ggportal_tbl_student_document` (`student_document_id`, `student_id`, `file_name`, `doc_url`) VALUES
(317, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(316, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(315, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(314, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(313, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(312, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(311, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(310, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(309, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(308, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(307, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(306, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(305, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(304, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(303, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(302, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(301, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(300, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(299, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(298, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(297, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(296, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(295, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(294, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(293, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(292, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(291, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(290, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(289, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(288, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(287, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(286, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(285, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(284, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(283, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(282, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(281, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(280, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(279, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(278, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(277, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(276, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(275, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(274, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(273, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(272, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(271, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(270, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(269, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(268, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(267, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(266, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(265, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(264, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(263, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(262, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(261, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(260, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(259, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(258, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(257, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(256, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(255, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(254, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(253, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(252, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(251, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(250, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(249, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(248, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(247, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(246, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(245, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(244, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(243, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(242, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(241, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(240, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(239, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(238, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(237, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(236, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(235, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(234, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(233, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(232, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(231, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(230, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(229, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(228, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(227, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(226, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(225, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(224, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(223, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(222, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(221, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(220, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(219, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(218, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(217, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(216, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(215, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(214, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(213, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(212, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(211, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(210, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(209, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(208, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(207, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(206, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(205, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(204, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(203, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(202, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(201, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(200, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(199, 147, 'cv', 'dist/uploads/documents/1713762532-100.pdf'),
(198, 147, 'cv', 'dist/uploads/documents/1713762532-100.pdf'),
(197, 147, 'cv', 'dist/uploads/documents/1713762532-100.pdf'),
(196, 147, 'cv', 'dist/uploads/documents/1713762532-100.pdf'),
(318, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(319, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(320, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(321, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(322, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(323, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(324, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(325, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(326, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(327, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(328, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(329, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(330, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(331, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(332, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(333, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(334, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(335, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(336, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(337, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(338, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(339, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(340, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(341, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(342, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(343, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(344, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(345, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(346, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(347, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(348, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(349, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(350, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(351, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(352, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(353, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(354, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(355, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(356, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(357, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(358, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(359, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(360, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(361, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(362, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(363, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(364, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(365, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(366, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(367, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(368, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(369, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(370, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(371, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(372, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(373, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(374, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(375, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(376, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(377, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(378, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(379, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(380, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(381, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(382, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(383, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(384, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(385, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(386, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(387, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(388, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(389, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(390, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(391, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(392, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(393, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(394, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(395, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(396, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(397, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(398, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(399, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(400, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(401, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(402, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(403, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(404, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(405, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(406, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(407, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(408, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(409, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(410, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(411, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(412, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(413, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(414, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(415, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(416, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(417, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(418, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(419, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(420, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(421, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(422, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(423, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(424, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(425, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(426, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(427, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(428, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(429, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(430, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(431, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(432, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(433, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(434, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(435, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(436, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(437, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(438, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(439, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(440, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(441, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(442, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(443, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(444, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(445, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(446, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(447, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(448, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(449, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(450, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(451, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(452, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(453, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(454, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(455, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(456, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(457, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(458, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(459, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(460, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(461, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(462, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(463, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(464, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(465, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(466, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(467, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(468, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(469, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(470, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(471, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(472, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(473, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(474, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(475, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(476, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(477, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(478, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(479, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(480, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(481, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(482, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(483, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(484, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(485, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(486, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(487, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(488, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(489, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(490, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(491, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(492, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(493, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(494, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(495, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(496, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(497, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(498, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(499, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(500, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(501, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(502, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(503, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(504, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(505, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(506, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(507, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(508, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(509, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(510, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(511, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(512, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(513, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(514, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(515, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(516, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(517, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(518, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(519, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(520, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(521, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(522, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(523, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(524, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(525, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(526, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(527, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(528, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(529, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(530, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(531, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(532, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(533, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(534, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(535, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(536, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(537, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(538, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(539, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(540, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(541, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(542, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(543, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(544, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(545, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(546, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(547, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(548, 147, 'education', 'dist/uploads/documents/**********-100.pdf'),
(549, 147, 'work', 'dist/uploads/documents/**********-100.pdf'),
(550, 147, 'cv', 'dist/uploads/documents/**********-100.pdf'),
(551, 147, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(552, 147, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(553, 147, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(554, 147, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(555, 147, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(556, 147, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(557, 147, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(558, 147, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(559, 147, 'receipt', 'dist/uploads/documents/**********-100.pdf'),
(560, 144, 'cv', 'dist/uploads/documents/**********-Capture121.png'),
(561, 144, 'cv', 'dist/uploads/documents/**********-Capture16.png'),
(562, 144, 'sop', 'dist/uploads/documents/**********-100.pdf'),
(563, 144, 'lor', 'dist/uploads/documents/**********-Capture7.png'),
(564, 144, 'moi', 'dist/uploads/documents/**********-100.pdf'),
(565, 144, 'refusal', 'dist/uploads/documents/**********-100.pdf'),
(566, 144, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(567, 144, 'offer', 'dist/uploads/documents/**********-100.pdf'),
(568, 144, 'cas', 'dist/uploads/documents/**********-100.pdf'),
(569, 144, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(574, 144, 'receipt', 'dist/uploads/documents/**********-Get_Started_With_Smallpdf.pdf'),
(575, 144, 'education', 'dist/uploads/documents/**********-dummy.pdf'),
(572, 144, 'englishtest', 'dist/uploads/documents/**********-100.pdf'),
(573, 144, 'englishtest', 'dist/uploads/documents/**********-dummy.pdf'),
(576, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(577, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(578, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(579, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(580, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(581, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(582, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(583, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(584, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(585, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(586, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(587, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(588, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(589, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(590, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(591, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(592, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(593, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(594, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(595, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(596, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(597, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(598, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(599, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(600, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(601, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(602, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(603, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(604, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(605, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(606, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(607, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(608, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(609, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(610, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(611, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(612, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(613, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(614, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(615, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(616, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(617, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(618, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(619, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(620, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(621, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(622, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(623, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(624, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(625, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(626, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(627, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(628, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(629, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(630, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(631, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(632, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(633, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(634, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(635, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(636, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(637, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(638, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(639, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(640, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(641, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(642, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(643, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(644, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(645, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(646, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(647, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(648, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(649, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(650, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(651, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(652, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(653, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(654, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(655, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(656, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(657, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(658, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(659, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(660, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(661, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(662, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(663, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(664, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(665, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(666, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(667, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(668, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(669, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(670, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(671, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(672, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(673, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(674, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(675, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(676, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(677, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(678, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(679, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(680, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(681, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(682, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(683, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(684, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(685, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(686, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(687, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(688, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(689, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(690, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(691, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(692, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(693, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(694, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(695, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(696, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(697, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(698, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(699, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(700, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(701, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(702, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(703, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(704, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(705, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(706, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(707, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(708, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(709, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(710, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(711, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(712, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(713, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(714, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(715, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(716, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(717, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(718, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(719, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(720, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(721, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(722, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(723, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(724, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(725, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(726, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(727, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(728, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(729, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(730, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(731, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(732, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(733, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(734, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(735, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(736, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(737, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(738, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(739, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(740, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(741, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(742, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(743, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(744, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(745, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(746, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(747, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(748, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(749, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(750, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(751, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(752, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(753, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(754, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(755, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(756, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(757, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(758, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(759, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(760, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(761, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(762, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(763, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(764, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(765, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(766, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(767, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(768, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(769, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(770, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(771, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(772, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(773, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(774, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(775, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(776, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(777, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(778, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(779, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(780, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(781, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(782, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(783, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(784, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(785, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(786, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(787, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(788, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(789, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(790, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(791, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(792, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(793, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(794, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(795, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(796, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(797, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(798, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(799, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(800, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(801, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(802, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(803, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(804, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(805, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(806, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(807, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(808, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(809, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(810, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(811, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(812, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(813, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(814, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(815, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(816, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(817, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(818, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(819, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(820, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(821, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(822, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(823, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(824, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(825, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(826, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(827, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(828, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(829, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(830, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(831, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(832, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(833, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(834, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(835, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(836, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(837, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(838, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(839, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(840, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(841, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(842, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(843, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(844, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(845, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(846, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(847, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(848, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(849, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(850, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(851, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(852, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(853, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(854, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(855, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(856, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(857, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(858, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(859, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(860, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(861, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(862, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(863, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(864, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(865, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(866, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(867, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(868, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(869, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(870, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(871, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(872, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(873, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(874, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(875, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(876, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(877, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(878, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(879, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(880, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(881, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(882, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(883, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(884, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(885, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(886, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(887, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(888, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(889, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(890, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(891, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(892, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(893, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(894, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(895, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(896, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(897, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(898, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(899, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(900, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(901, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(902, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(903, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(904, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(905, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(906, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(907, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(908, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(909, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(910, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(911, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(912, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(913, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(914, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(915, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(916, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(917, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(918, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(919, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(920, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(921, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(922, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(923, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(924, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(925, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(926, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(927, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(928, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(929, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(930, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(931, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(932, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(933, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(934, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(935, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(936, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(937, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(938, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(939, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(940, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(941, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(942, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(943, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(944, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(945, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(946, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(947, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(948, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(949, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(950, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(951, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(952, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(953, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(954, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(955, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(956, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(957, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(958, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(959, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(960, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(961, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(962, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(963, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(964, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(965, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png');
INSERT INTO `ggportal_tbl_student_document` (`student_document_id`, `student_id`, `file_name`, `doc_url`) VALUES
(966, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(967, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(968, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(969, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(970, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(971, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(972, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(973, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(974, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(975, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(976, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(977, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(978, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(979, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(980, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(981, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(982, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(983, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(984, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(985, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(986, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(987, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(988, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(989, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(990, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(991, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(992, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(993, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(994, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(995, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(996, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(997, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(998, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(999, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1000, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1001, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1002, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1003, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1004, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1005, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1006, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1007, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1008, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1009, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1010, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1011, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1012, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1013, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1014, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1015, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1016, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1017, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1018, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1019, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1020, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1021, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1022, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1023, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1024, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1025, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1026, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1027, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1028, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1029, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1030, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1031, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1032, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1033, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1034, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1035, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1036, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1037, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1038, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1039, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1040, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1041, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1042, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1043, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1044, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1045, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1046, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1047, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1048, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1049, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1050, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1051, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1052, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1053, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1054, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1055, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1056, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1057, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1058, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1059, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1060, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1061, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1062, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1063, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1064, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1065, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1066, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1067, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1068, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1069, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1070, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1071, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1072, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1073, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1074, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1075, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1076, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1077, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1078, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1079, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1080, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1081, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1082, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1083, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1084, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1085, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1086, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1087, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1088, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1089, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1090, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1091, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1092, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1093, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1094, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1095, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1096, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1097, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1098, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1099, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1100, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1101, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1102, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1103, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1104, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1105, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1106, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1107, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1108, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1109, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1110, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1111, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1112, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1113, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1114, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1115, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1116, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1117, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1118, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1119, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1120, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1121, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1122, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1123, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1124, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1125, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1126, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1127, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1128, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1129, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1130, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1131, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1132, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1133, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1134, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1135, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1136, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1137, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1138, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1139, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1140, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1141, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1142, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1143, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1144, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1145, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1146, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1147, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1148, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1149, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1150, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1151, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1152, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1153, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1154, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1155, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1156, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1157, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1158, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1159, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1160, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1161, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1162, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1163, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1164, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1165, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1166, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1167, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1168, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1169, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1170, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1171, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1172, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1173, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1174, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1175, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1176, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1177, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1178, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1179, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1180, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1181, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1182, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1183, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1184, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1185, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1186, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1187, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1188, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1189, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1190, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1191, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1192, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1193, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1194, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1195, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1196, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1197, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1198, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1199, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1200, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1201, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1202, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1203, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1204, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1205, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1206, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1207, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1208, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1209, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1210, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1211, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1212, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1213, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1214, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1215, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1216, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1217, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1218, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1219, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1220, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1221, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1222, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1223, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1224, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1225, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1226, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1227, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1228, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1229, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1230, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1231, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1232, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1233, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1234, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1235, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1236, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1237, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1238, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1239, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1240, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1241, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1242, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1243, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1244, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1245, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1246, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1247, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1248, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1249, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1250, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1251, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1252, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1253, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1254, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1255, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1256, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1257, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1258, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1259, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1260, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1261, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1262, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1263, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1264, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1265, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1266, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1267, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1268, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1269, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1270, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1271, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1272, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1273, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1274, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1275, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1276, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1277, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1278, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1279, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1280, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1281, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1282, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1283, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1284, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1285, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1286, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1287, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1288, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1289, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1290, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1291, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1292, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1293, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1294, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1295, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1296, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1297, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1298, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1299, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1300, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1301, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1302, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1303, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1304, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1305, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1306, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1307, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1308, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1309, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1310, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1311, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1312, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1313, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1314, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1315, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1316, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1317, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1318, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1319, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1320, 153, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1321, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1322, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1323, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1324, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1325, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1326, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1327, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1328, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1329, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1330, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1331, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1332, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1333, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1334, 153, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1335, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1336, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1337, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1338, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1339, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1340, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1341, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1342, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1343, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1344, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1345, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1346, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1347, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1348, 153, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1349, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1350, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1351, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1352, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1353, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1354, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1355, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1356, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1357, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1358, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1359, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1360, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1361, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1362, 153, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1363, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1364, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1365, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1366, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1367, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1368, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1369, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1370, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1371, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1372, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1373, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1374, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1375, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1376, 153, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1377, 153, 'travel', 'dist/uploads/documents/**********-dp1.png'),
(1378, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1379, 153, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1380, 153, 'cv', 'dist/uploads/documents/**********-staff pdf.png'),
(1381, 153, 'sop', 'dist/uploads/documents/**********-staff pdf.png'),
(1382, 153, 'lor', 'dist/uploads/documents/**********-cp1.png'),
(1383, 153, 'moi', 'dist/uploads/documents/**********-dp1.png'),
(1384, 153, 'refusal', 'dist/uploads/documents/**********-vstaff v.png'),
(1385, 153, 'medical', 'dist/uploads/documents/**********-dp1.png'),
(1386, 153, 'offer', 'dist/uploads/documents/**********-excel 01.png'),
(1387, 153, 'cas', 'dist/uploads/documents/**********-vstaff v.png'),
(1388, 153, 'loa', 'dist/uploads/documents/**********-cp1.png'),
(1389, 153, 'receipt', 'dist/uploads/documents/**********-dp1.png'),
(1390, 155, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1391, 155, 'englishtest', 'dist/uploads/documents/1713950154-**********.pdf'),
(1392, 155, 'travel', 'dist/uploads/documents/1713950545-excel 01.png'),
(1393, 155, 'work', 'dist/uploads/documents/1713950182-**********.pdf'),
(1394, 155, 'work', 'dist/uploads/documents/1713950192-**********.pdf'),
(1395, 155, 'work', 'dist/uploads/documents/1713950208-API Token.pdf'),
(1396, 155, 'cv', 'dist/uploads/documents/1713950563-excel 01.png'),
(1397, 155, 'sop', 'dist/uploads/documents/1713950572-cp1.png'),
(1398, 155, 'lor', 'dist/uploads/documents/1713950606-excel 01.png'),
(1399, 155, 'moi', 'dist/uploads/documents/1713950623-**********.pdf'),
(1400, 155, 'refusal', 'dist/uploads/documents/1713950633-dp1.png'),
(1401, 155, 'medical', 'dist/uploads/documents/1713950668-dp1.png'),
(1402, 155, 'offer', 'dist/uploads/documents/1713950661-vstaff v.png'),
(1403, 155, 'cas', 'dist/uploads/documents/1713950655-dp1.png'),
(1404, 155, 'loa', 'dist/uploads/documents/1713950647-cp1.png'),
(1405, 155, 'receipt', 'dist/uploads/documents/1713950641-dp1.png'),
(1406, 142, 'education', 'dist/uploads/documents/1714016775-**********.pdf'),
(1407, 142, 'education', 'dist/uploads/documents/1714016786-**********.pdf'),
(1408, 142, 'englishtest', 'dist/uploads/documents/1714016807-**********.pdf'),
(1409, 142, 'englishtest', 'dist/uploads/documents/1714016812-**********.pdf'),
(1410, 142, 'travel', 'dist/uploads/documents/1714016951-**********.pdf'),
(1411, 142, 'travel', 'dist/uploads/documents/1714016976-cp1.png'),
(1412, 142, 'work', 'dist/uploads/documents/1714016836-**********.pdf'),
(1413, 142, 'work', 'dist/uploads/documents/1714016841-**********.pdf'),
(1414, 142, 'cv', 'dist/uploads/documents/1714017011-**********.pdf'),
(1415, 142, 'sop', 'dist/uploads/documents/1714017017-**********.pdf'),
(1416, 142, 'lor', 'dist/uploads/documents/1714017033-**********.pdf'),
(1417, 142, 'moi', 'dist/uploads/documents/1714017034-**********.pdf'),
(1418, 142, 'refusal', 'dist/uploads/documents/1714017055-**********.pdf'),
(1419, 142, 'medical', 'dist/uploads/documents/1714017056-**********.pdf'),
(1420, 142, 'offer', 'dist/uploads/documents/1714017057-**********.pdf'),
(1421, 142, 'cas', 'dist/uploads/documents/1714017075-**********.pdf'),
(1422, 142, 'loa', 'dist/uploads/documents/1714017076-**********.pdf'),
(1423, 142, 'receipt', 'dist/uploads/documents/1714017077-**********.pdf'),
(1452, 157, 'medical', 'dist/uploads/documents/1714380262-**********-**********.pdf'),
(1453, 157, 'offer', 'dist/uploads/documents/1714380269-**********-**********.pdf'),
(1451, 157, 'refusal', 'dist/uploads/documents/1714380256-**********-**********.pdf'),
(1449, 157, 'lor', 'dist/uploads/documents/1714380246-**********-**********.pdf'),
(1450, 157, 'moi', 'dist/uploads/documents/1714380251-**********-**********.pdf'),
(1430, 142, 'work', 'dist/uploads/documents/1714016836-**********.pdf'),
(1431, 142, 'work', 'dist/uploads/documents/1714016841-**********.pdf'),
(1447, 157, 'cv', 'dist/uploads/documents/1714380236-**********-**********.pdf'),
(1448, 157, 'sop', 'dist/uploads/documents/1714380240-**********-**********.pdf'),
(1446, 157, 'work', 'dist/uploads/documents/1714380180-**********.pdf'),
(1445, 157, 'work', 'dist/uploads/documents/1714380175-**********.pdf'),
(1488, 159, 'cv', 'dist/uploads/documents/**********-**********.pdf'),
(1489, 159, 'sop', 'dist/uploads/documents/**********-dummy.pdf'),
(1443, 157, 'englishtest', 'dist/uploads/documents/1714380150-**********.pdf'),
(1442, 157, 'education', 'dist/uploads/documents/1714380117-**********-**********.pdf'),
(1454, 157, 'cas', 'dist/uploads/documents/1714380275-**********-**********.pdf'),
(1455, 157, 'loa', 'dist/uploads/documents/1714380280-**********-**********.pdf'),
(1456, 157, 'receipt', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1487, 159, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1486, 159, 'travel', 'dist/uploads/documents/**********-**********.pdf'),
(1485, 159, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1484, 158, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(1483, 158, 'cas', 'dist/uploads/documents/**********-dummy.pdf'),
(1482, 158, 'offer', 'dist/uploads/documents/**********-**********.pdf'),
(1481, 158, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(1480, 158, 'refusal', 'dist/uploads/documents/**********-dummy.pdf'),
(1479, 158, 'moi', 'dist/uploads/documents/**********-**********.pdf'),
(1478, 158, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(1477, 158, 'sop', 'dist/uploads/documents/**********-dummy.pdf'),
(1476, 158, 'cv', 'dist/uploads/documents/**********-**********.pdf'),
(1475, 158, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1474, 158, 'travel', 'dist/uploads/documents/**********-**********.pdf'),
(1473, 158, 'education', 'dist/uploads/documents/**********-**********.pdf'),
(1472, 157, 'travel', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1490, 159, 'lor', 'dist/uploads/documents/**********-100.pdf'),
(1491, 159, 'moi', 'dist/uploads/documents/**********-**********.pdf'),
(1492, 159, 'refusal', 'dist/uploads/documents/**********-dummy.pdf'),
(1493, 159, 'medical', 'dist/uploads/documents/**********-100.pdf'),
(1494, 159, 'offer', 'dist/uploads/documents/**********-**********.pdf'),
(1495, 159, 'cas', 'dist/uploads/documents/**********-dummy.pdf'),
(1496, 159, 'loa', 'dist/uploads/documents/**********-100.pdf'),
(1497, 159, 'receipt', 'dist/uploads/documents/**********-**********.pdf'),
(1498, 160, 'education', 'dist/uploads/documents/**********-Sample data.pdf'),
(1499, 160, 'englishtest', 'dist/uploads/documents/**********-Sample data.pdf'),
(1500, 160, 'travel', 'dist/uploads/documents/**********-Sample data.pdf'),
(1501, 160, 'cv', 'dist/uploads/documents/**********-Sample data.pdf'),
(1502, 160, 'sop', 'dist/uploads/documents/**********-Sample data.pdf'),
(1503, 160, 'lor', 'dist/uploads/documents/**********-Sample2 - Copy.pdf'),
(1504, 160, 'moi', 'dist/uploads/documents/**********-Sample2 - Copy.pdf'),
(1505, 160, 'refusal', 'dist/uploads/documents/**********-Sample2 - Copy.pdf'),
(1506, 160, 'medical', 'dist/uploads/documents/**********-Sample2 - Copy.pdf'),
(1507, 160, 'offer', 'dist/uploads/documents/**********-Sample data.pdf'),
(1508, 160, 'cas', 'dist/uploads/documents/**********-Sample data.pdf'),
(1509, 160, 'loa', 'dist/uploads/documents/**********-Sample data.pdf'),
(1510, 160, 'receipt', 'dist/uploads/documents/**********-Sample2 - Copy.pdf'),
(1511, 161, 'education', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1512, 161, 'englishtest', 'dist/uploads/documents/**********-20240502104932.pdf'),
(1513, 161, 'travel', 'dist/uploads/documents/**********-3334344.png'),
(1514, 161, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1515, 161, 'work', 'dist/uploads/documents/**********-**********.pdf'),
(1516, 161, 'cv', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1517, 161, 'sop', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1518, 161, 'lor', 'dist/uploads/documents/**********-**********.pdf'),
(1519, 161, 'moi', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1520, 161, 'refusal', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1521, 161, 'medical', 'dist/uploads/documents/**********-**********-**********.pdf'),
(1522, 161, 'offer', 'dist/uploads/documents/**********-33553535.png'),
(1523, 161, 'cas', 'dist/uploads/documents/**********-33553535.png'),
(1524, 161, 'receipt', 'dist/uploads/documents/**********-3334344.png'),
(1525, 163, 'education', 'dist/uploads/documents/**********-Document1.pdf'),
(1526, 163, 'englishtest', 'dist/uploads/documents/**********-Document1.pdf'),
(1527, 163, 'travel', 'dist/uploads/documents/**********-Document1.pdf'),
(1528, 163, 'work', 'dist/uploads/documents/**********-Document1.pdf'),
(1529, 163, 'work', 'dist/uploads/documents/**********-Document1.pdf'),
(1530, 163, 'cv', 'dist/uploads/documents/**********-Untitled.jpg'),
(1531, 163, 'sop', 'dist/uploads/documents/**********-Untitled.jpg'),
(1532, 163, 'lor', 'dist/uploads/documents/**********-Untitled.jpg'),
(1533, 163, 'moi', 'dist/uploads/documents/**********-Untitled.jpg'),
(1534, 163, 'refusal', 'dist/uploads/documents/**********-Untitled.jpg'),
(1535, 163, 'medical', 'dist/uploads/documents/**********-Untitled.jpg'),
(1536, 163, 'offer', 'dist/uploads/documents/**********-Untitled.jpg'),
(1537, 163, 'cas', 'dist/uploads/documents/**********-Untitled.jpg'),
(1538, 163, 'loa', 'dist/uploads/documents/**********-Untitled.jpg'),
(1539, 163, 'receipt', 'dist/uploads/documents/**********-Untitled.jpg'),
(1540, 165, 'education', 'dist/uploads/documents/**********-1716270858_Sample data.pdf'),
(1541, 165, 'englishtest', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1542, 165, 'travel', 'dist/uploads/documents/**********-e1 (1).png'),
(1543, 165, 'work', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1544, 165, 'work', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1545, 165, 'cv', 'dist/uploads/documents/**********-**********-Sample data (2).pdf'),
(1546, 165, 'sop', 'dist/uploads/documents/**********-**********-Sample data (1).pdf'),
(1547, 165, 'lor', 'dist/uploads/documents/1716284097-**********-Sample data (1).pdf'),
(1548, 165, 'moi', 'dist/uploads/documents/1716284103-1716270858_Sample data-1.pdf'),
(1549, 165, 'refusal', 'dist/uploads/documents/1716284108-**********-Sample data (1).pdf'),
(1550, 165, 'medical', 'dist/uploads/documents/1716284113-1716270858_Sample data.pdf'),
(1551, 165, 'offer', 'dist/uploads/documents/1716284116-1715677255-Sample data.pdf'),
(1552, 165, 'cas', 'dist/uploads/documents/1716284121-**********-Sample data.pdf'),
(1553, 165, 'loa', 'dist/uploads/documents/1716284125-1716270858_Sample data.pdf'),
(1554, 165, 'receipt', 'dist/uploads/documents/1716284130-**********-Sample data (1).pdf'),
(1555, 166, 'education', 'dist/uploads/documents/**********-1716270858_Sample data.pdf'),
(1556, 166, 'englishtest', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1557, 166, 'travel', 'dist/uploads/documents/**********-e1 (1).png'),
(1558, 166, 'work', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1559, 166, 'work', 'dist/uploads/documents/**********-1716270858_Sample data-1.pdf'),
(1560, 166, 'cv', 'dist/uploads/documents/**********-**********-Sample data (2).pdf'),
(1561, 166, 'sop', 'dist/uploads/documents/**********-**********-Sample data (1).pdf'),
(1562, 166, 'lor', 'dist/uploads/documents/1716284097-**********-Sample data (1).pdf'),
(1563, 166, 'moi', 'dist/uploads/documents/1716284103-1716270858_Sample data-1.pdf'),
(1564, 166, 'refusal', 'dist/uploads/documents/1716284108-**********-Sample data (1).pdf'),
(1565, 166, 'medical', 'dist/uploads/documents/1716284113-1716270858_Sample data.pdf'),
(1566, 166, 'offer', 'dist/uploads/documents/1716284116-1715677255-Sample data.pdf'),
(1567, 166, 'cas', 'dist/uploads/documents/1716284121-**********-Sample data.pdf'),
(1568, 166, 'loa', 'dist/uploads/documents/1716284125-1716270858_Sample data.pdf'),
(1569, 166, 'receipt', 'dist/uploads/documents/1716284130-**********-Sample data (1).pdf'),
(1570, 167, 'education', 'dist/uploads/documents/1716284897-Document1.pdf'),
(1571, 167, 'englishtest', 'dist/uploads/documents/1716284920-Document1.pdf'),
(1572, 167, 'travel', 'dist/uploads/documents/1716284960-Untitled.jpg'),
(1573, 167, 'work', 'dist/uploads/documents/1716284930-Document1.pdf'),
(1574, 167, 'work', 'dist/uploads/documents/1716284934-Document1.pdf'),
(1575, 167, 'cv', 'dist/uploads/documents/1716284969-Document1.pdf'),
(1576, 167, 'sop', 'dist/uploads/documents/1716284973-Document1.pdf'),
(1577, 167, 'lor', 'dist/uploads/documents/1716284978-Document1.pdf'),
(1578, 167, 'moi', 'dist/uploads/documents/1716284983-Document1.pdf'),
(1579, 167, 'refusal', 'dist/uploads/documents/1716284987-Untitled.jpg'),
(1580, 167, 'medical', 'dist/uploads/documents/1716284993-4 Zero 2.png'),
(1581, 167, 'offer', 'dist/uploads/documents/1716284995-Document1.pdf'),
(1582, 167, 'cas', 'dist/uploads/documents/1716285000-4 Zero 2.png'),
(1583, 167, 'loa', 'dist/uploads/documents/1716285007-Untitled.jpg'),
(1584, 167, 'receipt', 'dist/uploads/documents/1716285016-Untitled.jpg'),
(1585, 168, 'education', 'dist/uploads/documents/1716790801-1716270967_1716270858_Sample data.pdf'),
(1586, 168, 'travel', 'dist/uploads/documents/1716790879-1716358092-Sample data.pdf'),
(1587, 168, 'cv', 'dist/uploads/documents/1716790887-1716270967_1716270858_Sample data-1.pdf'),
(1588, 168, 'sop', 'dist/uploads/documents/1716790891-1716270858_Sample data.pdf'),
(1589, 168, 'lor', 'dist/uploads/documents/1716790914-1716358092-Sample data.pdf'),
(1590, 168, 'moi', 'dist/uploads/documents/1716791159-1716270858_Sample data.pdf'),
(1591, 168, 'refusal', 'dist/uploads/documents/1716791169-1716270967_1716270858_Sample data.pdf'),
(1592, 168, 'medical', 'dist/uploads/documents/1716791174-1716270967_1716270858_Sample data (1).pdf'),
(1593, 168, 'offer', 'dist/uploads/documents/1716791179-1716270858_Sample data.pdf'),
(1594, 168, 'cas', 'dist/uploads/documents/1716791183-1716270858_Sample data-1.pdf'),
(1595, 168, 'loa', 'dist/uploads/documents/1716791188-1716270858_Sample data-1.pdf'),
(1596, 168, 'receipt', 'dist/uploads/documents/1716791193-**********-Sample data (2).pdf');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_education`
--

CREATE TABLE `ggportal_tbl_student_education` (
  `education_id` int(10) NOT NULL,
  `student_id` int(10) NOT NULL,
  `education_level` varchar(50) NOT NULL,
  `education_institute` varchar(100) NOT NULL,
  `education_program` varchar(100) NOT NULL,
  `study_language` varchar(50) NOT NULL,
  `degree_type` varchar(30) NOT NULL,
  `starting_date` date NOT NULL,
  `end_date` date NOT NULL,
  `education_country` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_education`
--

INSERT INTO `ggportal_tbl_student_education` (`education_id`, `student_id`, `education_level`, `education_institute`, `education_program`, `study_language`, `degree_type`, `starting_date`, `end_date`, `education_country`) VALUES
(85, 142, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-04-18', '2024-04-26', 'Algeria'),
(86, 144, 'Graduate', 'University of Ruhuna', 'Material Engineer', 'English', 'degree', '2019-04-01', '2023-12-18', 'Sri Lanka'),
(87, 145, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-04-03', '2024-04-16', ''),
(88, 146, '', '', '', '', 'certificate', '2024-04-04', '2024-04-25', ''),
(89, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(90, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(91, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(92, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(93, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(94, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(95, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(96, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(97, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(98, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(99, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(100, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(101, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(102, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(103, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(104, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(105, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(106, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(107, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(108, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(109, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(110, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(111, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(112, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(113, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(114, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(115, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(116, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(117, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(118, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(119, 147, '', '', '', '', 'certificate', '2024-04-10', '2024-05-03', ''),
(120, 149, 'a/l', 'univercity', 'Science', 'English', 'degree', '2020-06-18', '2024-04-25', 'Albania'),
(121, 150, 'Degree', 'UOM', 'Business Science ', 'English', 'certificate', '2024-04-17', '2024-04-02', ''),
(122, 151, 'Degree', 'UOM', 'Business Science ', 'English', 'certificate', '2024-04-17', '2024-04-02', ''),
(123, 152, 'Degree', 'UOM', 'Business Science ', 'English', 'certificate', '2024-04-17', '2024-04-02', ''),
(124, 153, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-04-17', '2024-04-25', 'China'),
(125, 154, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-04-17', '2024-04-25', 'China'),
(126, 155, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-04-24', '2024-04-30', 'Antarctica'),
(127, 157, 'Degree', 'UOM', 'Business Science ', 'English', 'diploma', '2024-04-25', '2024-05-09', 'Ethiopia'),
(128, 158, 'Graduate', 'University of Ruhuna', 'Material Engineer', 'English', 'degree', '2019-04-03', '2023-01-03', 'Australia'),
(129, 159, 'Graduate', 'University of Ruhuna', 'Material Engineer', 'English', 'degree', '2019-04-03', '2023-01-03', 'Australia'),
(130, 160, '', '', '', '', 'certificate', '0000-00-00', '0000-00-00', ''),
(131, 161, 'Degree', 'UOM', 'Business Science ', 'English', 'degree', '2024-05-07', '2024-05-30', 'Brazil'),
(132, 163, 'ol', 'nibm', 'pro', 'test', 'diploma', '2024-05-20', '2024-05-31', 'Andorra'),
(133, 164, 'test', 'test', 'test', 'test', 'diploma', '2024-05-01', '2024-05-09', 'Andorra'),
(134, 165, 'a/l', 'univercity', '', 'English', 'diploma', '2024-05-01', '2024-05-16', 'Andorra'),
(135, 166, 'a/l', 'univercity', '', 'English', 'diploma', '2024-05-01', '2024-05-16', 'Andorra'),
(136, 167, 'test', 'test', 'test', 'test', 'diploma', '2024-05-14', '2024-05-23', 'Andorra'),
(137, 168, 'a/l', 'Airways Aviation', 'Science', 'English', 'diploma', '2024-05-02', '2024-05-02', 'American Samoa');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_english_test`
--

CREATE TABLE `ggportal_tbl_student_english_test` (
  `english_test_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `test_name` varchar(100) NOT NULL,
  `test_status` varchar(50) NOT NULL,
  `speaking` int(6) DEFAULT NULL,
  `listening` int(6) DEFAULT NULL,
  `reading` int(6) DEFAULT NULL,
  `writing` int(6) DEFAULT NULL,
  `test_date` date DEFAULT NULL,
  `test_description` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_english_test`
--

INSERT INTO `ggportal_tbl_student_english_test` (`english_test_id`, `student_id`, `test_name`, `test_status`, `speaking`, `listening`, `reading`, `writing`, `test_date`, `test_description`) VALUES
(8, 144, 'pte', 'not_taking', 0, 0, 0, 0, '0000-00-00', ''),
(9, 145, 'ielts', '', 0, 0, 0, 0, '0000-00-00', ''),
(10, 149, 'ielts', 'not_taking', 0, 0, 0, 0, '0000-00-00', ''),
(11, 163, 'ielts', 'not_taking', 0, 0, 0, 0, '0000-00-00', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_passport`
--

CREATE TABLE `ggportal_tbl_student_passport` (
  `passport_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `passport_number` varchar(100) NOT NULL,
  `issued_country` varchar(100) NOT NULL,
  `passport_issue_date` date NOT NULL,
  `passport_expiry_date` date NOT NULL,
  `nationality` varchar(30) DEFAULT NULL,
  `birth_country` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_passport`
--

INSERT INTO `ggportal_tbl_student_passport` (`passport_id`, `student_id`, `passport_number`, `issued_country`, `passport_issue_date`, `passport_expiry_date`, `nationality`, `birth_country`) VALUES
(118, 142, '', '', '2024-03-15', '2024-03-15', '', ''),
(119, 144, '12365478954758', 'Sri Lanka', '2017-03-01', '2027-03-01', 'Sinhala', 'Sri Lanka'),
(120, 145, '546577668857868467474', '', '2024-04-03', '2024-04-12', '54646463', 'American Samoa'),
(153, 149, '0101100101011', 'Albania', '2024-04-03', '2025-12-18', '', 'Albania'),
(302, 163, 'test', 'Åland Islands', '2024-05-10', '2025-05-22', 'test', 'Albania');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_travel_history`
--

CREATE TABLE `ggportal_tbl_student_travel_history` (
  `travel_history_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `travel_departure` date NOT NULL,
  `travel_arrival` date NOT NULL,
  `travel_reason` varchar(100) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_travel_history`
--

INSERT INTO `ggportal_tbl_student_travel_history` (`travel_history_id`, `student_id`, `country`, `travel_departure`, `travel_arrival`, `travel_reason`) VALUES
(30, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(29, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(31, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(32, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(33, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(34, 153, 'Anguilla', '2024-04-19', '2024-04-27', 'Travel'),
(35, 142, 'Antarctica', '2024-04-18', '2024-04-27', 'Business');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_visa_refusal`
--

CREATE TABLE `ggportal_tbl_student_visa_refusal` (
  `visa_refusal_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `date` date NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_student_visa_refusal`
--

INSERT INTO `ggportal_tbl_student_visa_refusal` (`visa_refusal_id`, `student_id`, `country`, `date`) VALUES
(41, 142, 'Algeria', '2024-04-24');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_student_work`
--

CREATE TABLE `ggportal_tbl_student_work` (
  `work_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `company_name` varchar(200) NOT NULL,
  `position` varchar(100) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_student_work`
--

INSERT INTO `ggportal_tbl_student_work` (`work_id`, `student_id`, `company_name`, `position`, `start_date`, `end_date`) VALUES
(7, 144, 'LSEG', 'Engineer', '2023-01-02', '2024-03-15'),
(8, 149, 'abcd', 'aaaaa', '2024-04-02', '2024-04-22'),
(9, 163, 'test', 'test', '2024-05-20', '2024-05-28');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_timeline`
--

CREATE TABLE `ggportal_tbl_timeline` (
  `timeline_id` int(11) NOT NULL,
  `timeline_name` varchar(50) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_timeline`
--

INSERT INTO `ggportal_tbl_timeline` (`timeline_id`, `timeline_name`) VALUES
(1, 'Signup'),
(2, 'Profile Complete'),
(3, 'Course Finalization'),
(4, 'Application in Process '),
(5, 'Course Started & Application completed'),
(6, 'Application received'),
(7, 'Application Undereviewed'),
(8, 'Program apply'),
(9, 'Conditional Offer'),
(10, 'Unconditional Offer'),
(11, 'CAS/LOA issued'),
(12, 'VISA Granted'),
(13, 'University enrolled'),
(16, 'Refund'),
(15, 'Commission'),
(18, 'Reappeal'),
(19, 'Differment'),
(20, 'Processing Completed'),
(21, 'University Payment');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_training_request`
--

CREATE TABLE `ggportal_tbl_training_request` (
  `training_request_id` int(11) NOT NULL,
  `training_type` varchar(50) NOT NULL,
  `user_id` int(11) NOT NULL,
  `user_type` varchar(20) NOT NULL,
  `perfer_date_1` datetime NOT NULL,
  `perfer_date_2` datetime NOT NULL,
  `perfer_date_3` datetime NOT NULL,
  `send_yn` varchar(1) NOT NULL DEFAULT 'N',
  `active_yn` varchar(1) NOT NULL DEFAULT 'Y',
  `selected_date` varchar(20) NOT NULL,
  `meeting_link` varchar(450) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ggportal_tbl_training_request`
--

INSERT INTO `ggportal_tbl_training_request` (`training_request_id`, `training_type`, `user_id`, `user_type`, `perfer_date_1`, `perfer_date_2`, `perfer_date_3`, `send_yn`, `active_yn`, `selected_date`, `meeting_link`) VALUES
(16, 'agent', 45, 'AG', '2024-04-20 12:00:00', '1970-01-01 05:30:00', '1970-01-01 05:30:00', 'Y', 'Y', '2024-04-20 12:00:00', 'https://us06web.zoom.us/j/84814785906?pwd=WnxDf8I0y2u626gZFgFzsgfFlr4euO.1'),
(13, 'agent', 44, 'AG', '2024-04-02 13:59:00', '1970-01-01 05:30:00', '1970-01-01 05:30:00', 'Y', 'Y', '', '');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user`
--

CREATE TABLE `ggportal_tbl_user` (
  `user_id` int(11) NOT NULL,
  `user_name` varchar(45) DEFAULT NULL,
  `password` varchar(200) DEFAULT NULL,
  `password_salt` varchar(45) DEFAULT NULL,
  `email` varchar(70) DEFAULT NULL,
  `email_validated_yn` varchar(1) DEFAULT NULL,
  `user_active_yn` varchar(1) DEFAULT NULL,
  `user_type` varchar(2) DEFAULT 'T' COMMENT 'AD - admin\n',
  `user_access_level` varchar(45) DEFAULT NULL COMMENT 'may not use this field\n100 = full\n',
  `last_login_date` datetime DEFAULT NULL,
  `last_seen` datetime DEFAULT NULL,
  `online_status` int(1) NOT NULL DEFAULT 0,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `first_name` varchar(45) DEFAULT NULL,
  `last_name` varchar(45) DEFAULT NULL,
  `token_id` varchar(255) NOT NULL,
  `token_created_time` datetime DEFAULT NULL,
  `profile_picture` varchar(150) DEFAULT 'users/img/no_photo.jpg',
  `user_guid` varchar(45) DEFAULT 'rand()',
  `user_contact_no` varchar(45) DEFAULT NULL,
  `force_password_change_yn` varchar(45) DEFAULT 'N'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `ggportal_tbl_user`
--

INSERT INTO `ggportal_tbl_user` (`user_id`, `user_name`, `password`, `password_salt`, `email`, `email_validated_yn`, `user_active_yn`, `user_type`, `user_access_level`, `last_login_date`, `last_seen`, `online_status`, `last_login_ip`, `first_name`, `last_name`, `token_id`, `token_created_time`, `profile_picture`, `user_guid`, `user_contact_no`, `force_password_change_yn`) VALUES
(1, '<EMAIL>', 'Portal@8456', 'Portal@8456', '<EMAIL>', 'Y', 'Y', 'RA', '100', NULL, '2024-05-29 01:31:30', 0, NULL, 'Nashif', 'Nashif', '', '1970-01-01 00:00:00', 'dist/img/avatar5.png', 'rand()', NULL, 'N');

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user_activity`
--

CREATE TABLE `ggportal_tbl_user_activity` (
  `user_activity_id` int(10) UNSIGNED NOT NULL,
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `activity_description` text NOT NULL,
  `created_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ggportal_tbl_user_login`
--

CREATE TABLE `ggportal_tbl_user_login` (
  `user_login_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `date_login` datetime NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ggportal_tbl_user_login`
--

INSERT INTO `ggportal_tbl_user_login` (`user_login_id`, `user_id`, `date_login`, `ip_address`, `last_activity`) VALUES
(2217, 1, '2024-03-11 02:00:25', '***************', '2024-03-11 02:00:25'),
(2218, 1, '2024-03-11 02:02:30', '***************', '2024-03-11 02:02:30'),
(2219, 1, '2024-03-11 02:53:27', '**************', '2024-03-11 02:53:27'),
(2220, 1, '2024-03-11 04:04:36', '**************', '2024-03-11 04:04:36'),
(2221, 1, '2024-03-12 00:46:41', '**************', '2024-03-12 00:46:41'),
(2222, 1, '2024-03-12 05:28:31', '**************', '2024-03-12 05:28:31'),
(2223, 44, '2024-03-12 06:13:40', '***************', '2024-03-12 06:13:40'),
(2224, 1, '2024-03-12 06:31:44', '***************', '2024-03-12 06:31:44'),
(2225, 1, '2024-03-12 07:18:37', '***************', '2024-03-12 07:18:37'),
(2226, 1, '2024-03-12 19:28:39', '**************', '2024-03-12 19:28:39'),
(2227, 44, '2024-03-13 02:21:52', '***************', '2024-03-13 02:21:52'),
(2228, 44, '2024-03-13 02:28:27', '***************', '2024-03-13 02:28:27'),
(2229, 1, '2024-03-13 02:28:58', '***************', '2024-03-13 02:28:58'),
(2230, 1, '2024-03-13 02:30:39', '***************', '2024-03-13 02:30:39'),
(2231, 44, '2024-03-13 04:16:15', '***************', '2024-03-13 04:16:15'),
(2232, 1, '2024-03-13 05:33:29', '***************', '2024-03-13 05:33:29'),
(2233, 1, '2024-03-13 05:38:17', '***************', '2024-03-13 05:38:17'),
(2234, 1, '2024-03-13 05:43:46', '***************', '2024-03-13 05:43:46'),
(2235, 45, '2024-03-13 05:49:58', '***************', '2024-03-13 05:49:58'),
(2236, 45, '2024-03-13 06:50:36', '***************', '2024-03-13 06:50:36'),
(2237, 45, '2024-03-13 07:41:17', '***************', '2024-03-13 07:41:17'),
(2238, 1, '2024-03-13 22:51:29', '112.134.215.41', '2024-03-13 22:51:29'),
(2239, 45, '2024-03-14 02:09:20', '***************', '2024-03-14 02:09:20'),
(2240, 1, '2024-03-14 02:13:24', '***************', '2024-03-14 02:13:24'),
(2241, 1, '2024-03-14 05:08:09', '112.134.215.41', '2024-03-14 05:08:09'),
(2242, 1, '2024-03-14 05:08:23', '***************', '2024-03-14 05:08:23'),
(2243, 1, '2024-03-14 05:16:13', '***************', '2024-03-14 05:16:13'),
(2244, 1, '2024-03-14 05:19:20', '112.134.215.41', '2024-03-14 05:19:20'),
(2245, 1, '2024-03-14 05:19:43', '112.134.215.41', '2024-03-14 05:19:43'),
(2246, 1, '2024-03-14 05:24:39', '***************', '2024-03-14 05:24:39'),
(2247, 1, '2024-03-14 05:26:51', '***************', '2024-03-14 05:26:51'),
(2248, 1, '2024-03-14 23:48:52', '***************', '2024-03-14 23:48:52'),
(2249, 1, '2024-03-15 00:00:01', '***************', '2024-03-15 00:00:01'),
(2250, 1, '2024-03-15 00:23:05', '***************', '2024-03-15 00:23:05'),
(2251, 1, '2024-03-15 00:49:19', '***************', '2024-03-15 00:49:19'),
(2252, 1, '2024-03-15 01:08:05', '***************', '2024-03-15 01:08:05'),
(2253, 45, '2024-03-15 01:09:43', '***************', '2024-03-15 01:09:43'),
(2254, 1, '2024-03-15 01:19:48', '***************', '2024-03-15 01:19:48'),
(2255, 45, '2024-03-15 01:21:14', '***************', '2024-03-15 01:21:14'),
(2256, 45, '2024-03-15 01:28:48', '***************', '2024-03-15 01:28:48'),
(2257, 1, '2024-03-15 01:29:04', '***************', '2024-03-15 01:29:04'),
(2258, 1, '2024-03-15 02:11:24', '***************', '2024-03-15 02:11:24'),
(2259, 1, '2024-03-15 02:13:10', '***************', '2024-03-15 02:13:10'),
(2260, 1, '2024-03-15 02:27:51', '***************', '2024-03-15 02:27:51'),
(2261, 1, '2024-03-15 02:31:23', '***************', '2024-03-15 02:31:23'),
(2262, 1, '2024-03-15 02:36:01', '***************', '2024-03-15 02:36:01'),
(2263, 44, '2024-03-15 02:37:31', '***************', '2024-03-15 02:37:31'),
(2264, 1, '2024-03-15 02:39:17', '***************', '2024-03-15 02:39:17'),
(2265, 1, '2024-03-15 03:29:08', '***************', '2024-03-15 03:29:08'),
(2266, 45, '2024-03-15 03:44:04', '***************', '2024-03-15 03:44:04'),
(2267, 1, '2024-03-15 03:48:34', '***************', '2024-03-15 03:48:34'),
(2268, 1, '2024-03-15 05:51:31', '***************', '2024-03-15 05:51:31'),
(2269, 1, '2024-03-15 06:23:04', '***************', '2024-03-15 06:23:04'),
(2270, 1, '2024-03-15 07:24:14', '***************', '2024-03-15 07:24:14'),
(2271, 1, '2024-03-16 02:55:45', '112.134.213.57', '2024-03-16 02:55:45'),
(2272, 1, '2024-03-18 00:33:54', '***************', '2024-03-18 00:33:54'),
(2273, 1, '2024-03-18 01:18:54', '*************', '2024-03-18 01:18:54'),
(2274, 1, '2024-03-18 01:25:46', '***************', '2024-03-18 01:25:46'),
(2275, 1, '2024-03-18 01:31:48', '***************', '2024-03-18 01:31:48'),
(2276, 1, '2024-03-18 03:01:51', '***************', '2024-03-18 03:01:51'),
(2277, 1, '2024-03-18 03:03:29', '***************', '2024-03-18 03:03:29'),
(2278, 45, '2024-03-18 03:23:25', '***************', '2024-03-18 03:23:25'),
(2279, 1, '2024-03-18 03:23:56', '***************', '2024-03-18 03:23:56'),
(2280, 1, '2024-03-18 03:49:07', '***************', '2024-03-18 03:49:07'),
(2281, 1, '2024-03-18 03:49:42', '***************', '2024-03-18 03:49:42'),
(2282, 1, '2024-03-18 03:51:55', '***************', '2024-03-18 03:51:55'),
(2283, 1, '2024-03-18 03:57:35', '***************', '2024-03-18 03:57:35'),
(2284, 1, '2024-03-18 04:02:13', '***************', '2024-03-18 04:02:13'),
(2285, 1, '2024-03-18 04:28:17', '*************', '2024-03-18 04:28:17'),
(2286, 1, '2024-03-18 04:59:27', '***************', '2024-03-18 04:59:27'),
(2287, 1, '2024-03-18 04:59:30', '***************', '2024-03-18 04:59:30'),
(2288, 1, '2024-03-18 05:06:57', '*************', '2024-03-18 05:06:57'),
(2289, 1, '2024-03-18 05:08:44', '***************', '2024-03-18 05:08:44'),
(2290, 1, '2024-03-18 05:19:02', '*************', '2024-03-18 05:19:02'),
(2291, 1, '2024-03-18 06:32:03', '***************', '2024-03-18 06:32:03'),
(2292, 45, '2024-03-18 06:56:21', '***************', '2024-03-18 06:56:21'),
(2293, 1, '2024-03-19 01:28:24', '112.134.212.126', '2024-03-19 01:28:24'),
(2294, 1, '2024-03-19 01:28:59', '112.134.212.126', '2024-03-19 01:28:59'),
(2295, 1, '2024-03-19 02:45:56', '***************', '2024-03-19 02:45:56'),
(2296, 1, '2024-03-19 02:55:48', '***************', '2024-03-19 02:55:48'),
(2297, 1, '2024-03-19 03:04:57', '***************', '2024-03-19 03:04:57'),
(2298, 1, '2024-03-19 03:13:43', '***************', '2024-03-19 03:13:43'),
(2299, 1, '2024-03-19 03:27:13', '***************', '2024-03-19 03:27:13'),
(2300, 1, '2024-03-19 03:34:43', '124.43.241.201', '2024-03-19 03:34:43'),
(2301, 1, '2024-03-19 07:34:29', '***************', '2024-03-19 07:34:29'),
(2302, 1, '2024-03-20 00:20:08', '112.134.214.149', '2024-03-20 00:20:08'),
(2303, 1, '2024-03-20 00:37:42', '124.43.241.40', '2024-03-20 00:37:42'),
(2304, 1, '2024-03-20 00:40:41', '112.134.214.149', '2024-03-20 00:40:41'),
(2305, 1, '2024-03-20 03:25:10', '112.134.214.149', '2024-03-20 03:25:10'),
(2306, 1, '2024-03-20 20:40:43', '51.148.43.244', '2024-03-20 20:40:43'),
(2307, 1, '2024-03-22 00:22:27', '***************', '2024-03-22 00:22:27'),
(2308, 1, '2024-03-22 00:46:31', '***************', '2024-03-22 00:46:31'),
(2309, 1, '2024-03-22 01:48:46', '112.134.209.189', '2024-03-22 01:48:46'),
(2310, 1, '2024-03-22 01:48:46', '112.134.209.189', '2024-03-22 01:48:46'),
(2311, 1, '2024-03-22 05:36:42', '***************', '2024-03-22 05:36:42'),
(2312, 1, '2024-03-28 05:44:34', '112.134.209.157', '2024-03-28 05:44:34'),
(2313, 1, '2024-04-01 01:33:32', '***************', '2024-04-01 01:33:32'),
(2314, 1, '2024-04-01 03:18:51', '112.134.209.136', '2024-04-01 03:18:51'),
(2315, 1, '2024-04-01 05:56:36', '***************', '2024-04-01 05:56:36'),
(2316, 1, '2024-04-01 06:18:09', '***************', '2024-04-01 06:18:09'),
(2317, 1, '2024-04-01 07:36:58', '***************', '2024-04-01 07:36:58'),
(2318, 1, '2024-04-02 02:02:02', '***************', '2024-04-02 02:02:02'),
(2319, 1, '2024-04-02 05:48:16', '***************', '2024-04-02 05:48:16'),
(2320, 1, '2024-04-02 23:49:10', '***************', '2024-04-02 23:49:10'),
(2321, 1, '2024-04-03 04:25:23', '***************', '2024-04-03 04:25:23'),
(2322, 1, '2024-04-03 23:40:19', '***************', '2024-04-03 23:40:19'),
(2323, 1, '2024-04-04 02:37:35', '112.134.209.50', '2024-04-04 02:37:35'),
(2324, 1, '2024-04-04 02:55:59', '***************', '2024-04-04 02:55:59'),
(2325, 1, '2024-04-04 04:18:48', '***************', '2024-04-04 04:18:48'),
(2326, 1, '2024-04-04 05:28:01', '***************', '2024-04-04 05:28:01'),
(2327, 1, '2024-04-04 06:48:15', '***************', '2024-04-04 06:48:15'),
(2328, 1, '2024-04-04 07:07:03', '***************', '2024-04-04 07:07:03'),
(2329, 1, '2024-04-08 23:55:40', '112.134.234.95', '2024-04-08 23:55:40'),
(2330, 1, '2024-04-09 00:07:02', '112.134.234.95', '2024-04-09 00:07:02'),
(2331, 1, '2024-04-09 00:13:02', '112.134.234.95', '2024-04-09 00:13:02'),
(2332, 45, '2024-04-09 00:22:13', '124.43.241.76', '2024-04-09 00:22:13'),
(2333, 1, '2024-04-09 05:52:45', '112.134.234.95', '2024-04-09 05:52:45'),
(2334, 1, '2024-04-09 05:55:17', '124.43.240.44', '2024-04-09 05:55:17'),
(2335, 1, '2024-04-09 06:10:43', '112.134.234.95', '2024-04-09 06:10:43'),
(2336, 1, '2024-04-09 07:08:44', '112.134.234.95', '2024-04-09 07:08:44'),
(2337, 1, '2024-04-09 07:10:47', '112.134.234.95', '2024-04-09 07:10:47'),
(2338, 45, '2024-04-09 07:17:21', '112.134.234.95', '2024-04-09 07:17:21'),
(2339, 1, '2024-04-09 07:18:24', '112.134.234.95', '2024-04-09 07:18:24'),
(2340, 1, '2024-04-09 07:32:49', '112.134.234.95', '2024-04-09 07:32:49'),
(2341, 1, '2024-04-09 23:41:09', '112.134.234.95', '2024-04-09 23:41:09'),
(2342, 1, '2024-04-09 23:45:42', '112.134.234.95', '2024-04-09 23:45:42'),
(2343, 45, '2024-04-09 23:49:57', '124.43.240.221', '2024-04-09 23:49:57'),
(2344, 1, '2024-04-09 23:54:31', '112.134.234.95', '2024-04-09 23:54:31'),
(2345, 1, '2024-04-10 01:58:12', '112.134.234.95', '2024-04-10 01:58:12'),
(2346, 45, '2024-04-10 02:01:30', '**************', '2024-04-10 02:01:30'),
(2347, 1, '2024-04-10 02:04:36', '**************', '2024-04-10 02:04:36'),
(2348, 1, '2024-04-10 02:08:05', '112.134.234.95', '2024-04-10 02:08:05'),
(2349, 45, '2024-04-10 02:22:32', '112.134.234.95', '2024-04-10 02:22:32'),
(2350, 1, '2024-04-10 02:27:16', '112.134.234.95', '2024-04-10 02:27:16'),
(2351, 45, '2024-04-10 02:43:58', '112.134.234.95', '2024-04-10 02:43:58'),
(2352, 45, '2024-04-10 02:45:11', '112.134.234.95', '2024-04-10 02:45:11'),
(2353, 45, '2024-04-10 04:32:13', '112.134.234.95', '2024-04-10 04:32:13'),
(2354, 1, '2024-04-10 04:40:54', '112.134.234.95', '2024-04-10 04:40:54'),
(2355, 45, '2024-04-10 05:09:33', '112.134.234.95', '2024-04-10 05:09:33'),
(2356, 1, '2024-04-10 05:19:59', '112.134.234.95', '2024-04-10 05:19:59'),
(2357, 1, '2024-04-10 05:32:30', '112.134.234.95', '2024-04-10 05:32:30'),
(2358, 1, '2024-04-10 06:49:14', '124.43.240.55', '2024-04-10 06:49:14'),
(2359, 1, '2024-04-10 07:14:29', '112.134.234.95', '2024-04-10 07:14:29'),
(2360, 1, '2024-04-17 00:01:44', '112.134.232.254', '2024-04-17 00:01:44'),
(2361, 1, '2024-04-17 00:04:41', '112.134.232.254', '2024-04-17 00:04:41'),
(2362, 1, '2024-04-17 05:47:24', '112.134.232.254', '2024-04-17 05:47:24'),
(2363, 1, '2024-04-17 05:51:22', '112.134.232.254', '2024-04-17 05:51:22'),
(2364, 48, '2024-04-17 06:00:48', '124.43.240.3', '2024-04-17 06:00:48'),
(2365, 1, '2024-04-17 06:09:30', '124.43.240.186', '2024-04-17 06:09:30'),
(2366, 1, '2024-04-18 00:04:48', '112.134.232.254', '2024-04-18 00:04:48'),
(2367, 1, '2024-04-18 00:16:58', '112.134.232.254', '2024-04-18 00:16:58'),
(2368, 1, '2024-04-18 01:49:15', '112.134.232.254', '2024-04-18 01:49:15'),
(2369, 1, '2024-04-18 02:04:00', '124.43.241.237', '2024-04-18 02:04:00'),
(2370, 1, '2024-04-18 02:36:24', '124.43.241.237', '2024-04-18 02:36:24'),
(2371, 1, '2024-04-18 02:38:47', '124.43.241.237', '2024-04-18 02:38:47'),
(2372, 45, '2024-04-18 03:26:16', '124.43.240.234', '2024-04-18 03:26:16'),
(2373, 1, '2024-04-18 03:30:02', '124.43.240.234', '2024-04-18 03:30:02'),
(2374, 1, '2024-04-18 03:39:25', '112.134.232.254', '2024-04-18 03:39:25'),
(2375, 1, '2024-04-18 06:38:42', '112.134.232.254', '2024-04-18 06:38:42'),
(2376, 1, '2024-04-18 06:58:01', '124.43.240.225', '2024-04-18 06:58:01'),
(2377, 1, '2024-04-18 09:32:28', '112.134.211.116', '2024-04-18 09:32:28'),
(2378, 1, '2024-04-18 09:36:50', '112.134.211.116', '2024-04-18 09:36:50'),
(2379, 45, '2024-04-19 00:56:41', '112.134.232.254', '2024-04-19 00:56:41'),
(2380, 45, '2024-04-19 01:17:07', '112.134.232.254', '2024-04-19 01:17:07'),
(2381, 45, '2024-04-19 02:10:04', '124.43.241.75', '2024-04-19 02:10:04'),
(2382, 1, '2024-04-19 02:11:16', '124.43.241.75', '2024-04-19 02:11:16'),
(2383, 1, '2024-04-19 03:03:01', '112.134.232.254', '2024-04-19 03:03:01'),
(2384, 1, '2024-04-19 04:45:13', '124.43.240.16', '2024-04-19 04:45:13'),
(2385, 1, '2024-04-19 05:24:15', '112.134.232.254', '2024-04-19 05:24:15'),
(2386, 1, '2024-04-19 05:47:58', '112.134.232.254', '2024-04-19 05:47:58'),
(2387, 1, '2024-04-22 00:12:03', '112.134.232.254', '2024-04-22 00:12:03'),
(2388, 1, '2024-04-22 00:22:17', '112.134.232.254', '2024-04-22 00:22:17'),
(2389, 1, '2024-04-22 00:44:23', '112.134.232.254', '2024-04-22 00:44:23'),
(2390, 1, '2024-04-22 04:13:17', '112.134.232.254', '2024-04-22 04:13:17'),
(2391, 1, '2024-04-22 04:26:19', '112.134.232.254', '2024-04-22 04:26:19'),
(2392, 1, '2024-04-22 05:08:42', '112.134.232.254', '2024-04-22 05:08:42'),
(2393, 1, '2024-04-22 05:11:21', '112.134.232.254', '2024-04-22 05:11:21'),
(2394, 1, '2024-04-22 05:30:24', '112.134.214.152', '2024-04-22 05:30:24'),
(2395, 1, '2024-04-22 05:35:15', '112.134.232.254', '2024-04-22 05:35:15'),
(2396, 1, '2024-04-22 05:53:36', '112.134.232.254', '2024-04-22 05:53:36'),
(2397, 1, '2024-04-22 07:24:14', '112.134.232.254', '2024-04-22 07:24:14'),
(2398, 45, '2024-04-23 23:52:46', '112.134.232.254', '2024-04-23 23:52:46'),
(2399, 1, '2024-04-24 00:05:20', '112.134.232.254', '2024-04-24 00:05:20'),
(2400, 1, '2024-04-24 00:13:45', '112.134.232.254', '2024-04-24 00:13:45'),
(2401, 45, '2024-04-24 00:57:47', '112.134.232.254', '2024-04-24 00:57:47'),
(2402, 1, '2024-04-24 00:58:32', '112.134.232.254', '2024-04-24 00:58:32'),
(2403, 1, '2024-04-24 00:58:59', '112.134.232.254', '2024-04-24 00:58:59'),
(2404, 1, '2024-04-24 00:59:59', '112.134.232.254', '2024-04-24 00:59:59'),
(2405, 1, '2024-04-24 01:04:35', '112.134.232.254', '2024-04-24 01:04:35'),
(2406, 45, '2024-04-24 01:04:35', '112.134.232.254', '2024-04-24 01:04:35'),
(2407, 1, '2024-04-24 02:02:25', '112.134.232.254', '2024-04-24 02:02:25'),
(2408, 1, '2024-04-24 04:22:53', '112.134.232.254', '2024-04-24 04:22:53'),
(2409, 1, '2024-04-24 04:50:17', '112.134.232.254', '2024-04-24 04:50:17'),
(2410, 1, '2024-04-24 05:06:43', '112.134.232.254', '2024-04-24 05:06:43'),
(2411, 1, '2024-04-24 05:48:45', '124.43.241.211', '2024-04-24 05:48:45'),
(2412, 45, '2024-04-24 06:34:54', '112.134.232.254', '2024-04-24 06:34:54'),
(2413, 1, '2024-04-24 06:36:56', '112.134.232.254', '2024-04-24 06:36:56'),
(2414, 1, '2024-04-24 06:40:36', '112.134.232.254', '2024-04-24 06:40:36'),
(2415, 1, '2024-04-24 07:00:38', '112.134.232.254', '2024-04-24 07:00:38'),
(2416, 1, '2024-04-24 13:06:58', '175.157.84.207', '2024-04-24 13:06:58'),
(2417, 1, '2024-04-24 23:37:04', '112.134.232.254', '2024-04-24 23:37:04'),
(2418, 1, '2024-04-24 23:39:35', '112.134.232.254', '2024-04-24 23:39:35'),
(2419, 45, '2024-04-24 23:40:20', '112.134.232.254', '2024-04-24 23:40:20'),
(2420, 1, '2024-04-24 23:40:35', '112.134.232.254', '2024-04-24 23:40:35'),
(2421, 1, '2024-04-25 00:45:55', '112.134.232.254', '2024-04-25 00:45:55'),
(2422, 1, '2024-04-25 00:53:11', '124.43.241.173', '2024-04-25 00:53:11'),
(2423, 45, '2024-04-25 02:10:56', '112.134.232.254', '2024-04-25 02:10:56'),
(2424, 45, '2024-04-25 02:11:18', '112.134.232.254', '2024-04-25 02:11:18'),
(2425, 1, '2024-04-25 02:21:25', '112.134.232.254', '2024-04-25 02:21:25'),
(2426, 45, '2024-04-25 02:21:47', '112.134.232.254', '2024-04-25 02:21:47'),
(2427, 45, '2024-04-25 02:28:11', '112.134.232.254', '2024-04-25 02:28:11'),
(2428, 1, '2024-04-25 02:35:44', '112.134.232.254', '2024-04-25 02:35:44'),
(2429, 1, '2024-04-25 02:35:57', '112.134.232.254', '2024-04-25 02:35:57'),
(2430, 1, '2024-04-25 02:35:58', '112.134.232.254', '2024-04-25 02:35:58'),
(2431, 1, '2024-04-25 03:10:14', '112.134.232.254', '2024-04-25 03:10:14'),
(2432, 1, '2024-04-25 03:12:07', '112.134.232.254', '2024-04-25 03:12:07'),
(2433, 1, '2024-04-25 03:19:05', '112.134.212.182', '2024-04-25 03:19:05'),
(2434, 45, '2024-04-25 03:19:27', '112.134.232.254', '2024-04-25 03:19:27'),
(2435, 1, '2024-04-25 04:36:30', '112.134.212.182', '2024-04-25 04:36:30'),
(2436, 1, '2024-04-25 04:43:03', '112.134.212.182', '2024-04-25 04:43:03'),
(2437, 1, '2024-04-25 04:52:51', '112.134.232.254', '2024-04-25 04:52:51'),
(2438, 1, '2024-04-25 04:57:29', '112.134.212.182', '2024-04-25 04:57:29'),
(2439, 1, '2024-04-25 05:03:23', '112.134.212.182', '2024-04-25 05:03:23'),
(2440, 1, '2024-04-25 05:06:06', '112.134.212.182', '2024-04-25 05:06:06'),
(2441, 1, '2024-04-26 01:12:58', '124.43.240.79', '2024-04-26 01:12:58'),
(2442, 1, '2024-04-26 02:53:26', '124.43.240.79', '2024-04-26 02:53:26'),
(2443, 45, '2024-04-26 04:34:47', '112.134.232.254', '2024-04-26 04:34:47'),
(2444, 1, '2024-04-26 05:41:56', '112.134.232.254', '2024-04-26 05:41:56'),
(2445, 1, '2024-04-26 05:53:36', '112.134.232.254', '2024-04-26 05:53:36'),
(2446, 45, '2024-04-26 06:47:29', '112.134.232.254', '2024-04-26 06:47:29'),
(2447, 45, '2024-04-26 07:06:32', '112.134.232.254', '2024-04-26 07:06:32'),
(2448, 45, '2024-04-26 07:12:27', '112.134.232.254', '2024-04-26 07:12:27'),
(2449, 1, '2024-04-27 00:28:49', '175.157.95.240', '2024-04-27 00:28:49'),
(2450, 1, '2024-04-27 00:28:49', '175.157.95.240', '2024-04-27 00:28:49'),
(2451, 1, '2024-04-27 00:54:07', '175.157.95.240', '2024-04-27 00:54:07'),
(2452, 1, '2024-04-28 23:41:42', '112.134.232.254', '2024-04-28 23:41:42'),
(2453, 1, '2024-04-28 23:55:11', '112.134.232.254', '2024-04-28 23:55:11'),
(2454, 45, '2024-04-28 23:55:34', '112.134.232.254', '2024-04-28 23:55:34'),
(2455, 45, '2024-04-29 00:35:54', '124.43.241.67', '2024-04-29 00:35:54'),
(2456, 1, '2024-04-29 02:17:03', '112.134.232.254', '2024-04-29 02:17:03'),
(2457, 1, '2024-04-29 02:17:07', '112.134.232.254', '2024-04-29 02:17:07'),
(2458, 1, '2024-04-29 02:47:58', '112.134.232.254', '2024-04-29 02:47:58'),
(2459, 1, '2024-04-29 04:04:18', '112.134.232.254', '2024-04-29 04:04:18'),
(2460, 1, '2024-04-29 04:09:26', '124.43.241.59', '2024-04-29 04:09:26'),
(2461, 1, '2024-04-29 04:12:14', '124.43.240.248', '2024-04-29 04:12:14'),
(2462, 1, '2024-04-29 04:38:09', '124.43.240.235', '2024-04-29 04:38:09'),
(2463, 1, '2024-04-29 04:39:02', '124.43.240.248', '2024-04-29 04:39:02'),
(2464, 1, '2024-04-29 04:57:42', '124.43.240.248', '2024-04-29 04:57:42'),
(2465, 45, '2024-04-29 07:23:02', '112.134.232.254', '2024-04-29 07:23:02'),
(2466, 45, '2024-04-29 07:23:07', '112.134.232.254', '2024-04-29 07:23:07'),
(2467, 45, '2024-04-29 07:35:27', '112.134.232.254', '2024-04-29 07:35:27'),
(2468, 45, '2024-04-30 00:27:40', '112.134.232.254', '2024-04-30 00:27:40'),
(2469, 1, '2024-04-30 04:51:57', '124.43.241.248', '2024-04-30 04:51:57'),
(2470, 1, '2024-04-30 06:31:18', '112.134.232.254', '2024-04-30 06:31:18'),
(2471, 45, '2024-04-30 06:31:56', '112.134.232.254', '2024-04-30 06:31:56'),
(2472, 1, '2024-05-02 00:11:56', '112.134.239.210', '2024-05-02 00:11:56'),
(2473, 45, '2024-05-02 00:28:50', '124.43.241.176', '2024-05-02 00:28:50'),
(2474, 1, '2024-05-02 00:38:25', '112.134.239.210', '2024-05-02 00:38:25'),
(2475, 1, '2024-05-02 00:49:09', '112.134.239.210', '2024-05-02 00:49:09'),
(2476, 1, '2024-05-02 01:03:09', '112.134.239.210', '2024-05-02 01:03:09'),
(2477, 1, '2024-05-02 01:40:33', '112.134.239.210', '2024-05-02 01:40:33'),
(2478, 1, '2024-05-02 06:20:00', '112.134.239.210', '2024-05-02 06:20:00'),
(2479, 1, '2024-05-03 00:33:05', '112.134.239.210', '2024-05-03 00:33:05'),
(2480, 1, '2024-05-03 00:56:48', '124.43.240.102', '2024-05-03 00:56:48'),
(2481, 1, '2024-05-03 01:47:13', '112.134.239.210', '2024-05-03 01:47:13'),
(2482, 1, '2024-05-03 02:12:20', '124.43.240.57', '2024-05-03 02:12:20'),
(2483, 1, '2024-05-03 02:40:55', '112.134.239.210', '2024-05-03 02:40:55'),
(2484, 1, '2024-05-03 05:15:25', '112.134.209.247', '2024-05-03 05:15:25'),
(2485, 1, '2024-05-08 05:08:50', '112.134.239.210', '2024-05-08 05:08:50'),
(2486, 1, '2024-05-08 05:09:22', '124.43.240.150', '2024-05-08 05:09:22'),
(2487, 1, '2024-05-08 05:09:56', '112.134.239.210', '2024-05-08 05:09:56'),
(2488, 1, '2024-05-08 05:13:14', '112.134.239.210', '2024-05-08 05:13:14'),
(2489, 1, '2024-05-08 06:05:27', '112.134.239.210', '2024-05-08 06:05:27'),
(2490, 1, '2024-05-08 06:34:49', '112.134.239.210', '2024-05-08 06:34:49'),
(2491, 1, '2024-05-08 07:11:11', '112.134.239.210', '2024-05-08 07:11:11'),
(2492, 1, '2024-05-08 11:01:35', '175.157.223.113', '2024-05-08 11:01:35'),
(2493, 1, '2024-05-08 11:27:52', '103.87.14.16', '2024-05-08 11:27:52'),
(2494, 1, '2024-05-09 01:39:24', '112.134.239.210', '2024-05-09 01:39:24'),
(2495, 1, '2024-05-09 02:08:12', '112.134.214.98', '2024-05-09 02:08:12'),
(2496, 1, '2024-05-09 02:19:47', '112.134.214.98', '2024-05-09 02:19:47'),
(2497, 1, '2024-05-09 02:39:43', '112.134.239.210', '2024-05-09 02:39:43'),
(2498, 1, '2024-05-09 05:03:06', '112.134.239.210', '2024-05-09 05:03:06'),
(2499, 45, '2024-05-09 05:08:16', '112.134.239.210', '2024-05-09 05:08:16'),
(2500, 1, '2024-05-09 05:09:43', '112.134.239.210', '2024-05-09 05:09:43'),
(2501, 1, '2024-05-09 05:20:36', '112.134.239.210', '2024-05-09 05:20:36'),
(2502, 1, '2024-05-09 21:07:55', '112.134.205.234', '2024-05-09 21:07:55'),
(2503, 1, '2024-05-09 21:16:49', '112.134.205.234', '2024-05-09 21:16:49'),
(2504, 44, '2024-05-09 21:18:41', '112.134.205.234', '2024-05-09 21:18:41'),
(2505, 44, '2024-05-09 21:23:55', '112.134.205.234', '2024-05-09 21:23:55'),
(2506, 1, '2024-05-10 00:50:14', '112.134.215.110', '2024-05-10 00:50:14'),
(2507, 44, '2024-05-10 00:52:02', '112.134.215.110', '2024-05-10 00:52:02'),
(2508, 44, '2024-05-10 00:55:44', '112.134.215.110', '2024-05-10 00:55:44'),
(2509, 1, '2024-05-10 01:31:05', '112.134.239.210', '2024-05-10 01:31:05'),
(2510, 1, '2024-05-13 00:03:48', '112.134.234.25', '2024-05-13 00:03:48'),
(2511, 45, '2024-05-13 05:15:44', '124.43.241.226', '2024-05-13 05:15:44'),
(2512, 1, '2024-05-13 05:25:41', '112.134.234.25', '2024-05-13 05:25:41'),
(2513, 45, '2024-05-13 05:47:36', '112.134.234.25', '2024-05-13 05:47:36'),
(2514, 1, '2024-05-13 05:58:10', '112.134.234.25', '2024-05-13 05:58:10'),
(2515, 1, '2024-05-14 01:35:55', '112.134.234.25', '2024-05-14 01:35:55'),
(2516, 1, '2024-05-14 01:55:30', '112.134.211.153', '2024-05-14 01:55:30'),
(2517, 45, '2024-05-14 04:50:12', '112.134.234.25', '2024-05-14 04:50:12'),
(2518, 45, '2024-05-14 05:09:32', '112.134.234.25', '2024-05-14 05:09:32'),
(2519, 1, '2024-05-14 07:27:12', '112.134.234.25', '2024-05-14 07:27:12'),
(2520, 1, '2024-05-15 00:43:18', '112.134.234.25', '2024-05-15 00:43:18'),
(2521, 1, '2024-05-15 01:38:00', '112.134.234.25', '2024-05-15 01:38:00'),
(2522, 1, '2024-05-15 01:39:14', '112.134.234.25', '2024-05-15 01:39:14'),
(2523, 1, '2024-05-15 01:39:59', '112.134.234.25', '2024-05-15 01:39:59'),
(2524, 1, '2024-05-15 01:44:25', '112.134.234.25', '2024-05-15 01:44:25'),
(2525, 1, '2024-05-15 01:48:16', '112.134.234.25', '2024-05-15 01:48:16'),
(2526, 1, '2024-05-15 01:48:32', '112.134.234.25', '2024-05-15 01:48:32'),
(2527, 1, '2024-05-15 01:51:00', '112.134.234.25', '2024-05-15 01:51:00'),
(2528, 1, '2024-05-15 01:54:35', '112.134.234.25', '2024-05-15 01:54:35'),
(2529, 1, '2024-05-16 00:46:25', '112.134.234.25', '2024-05-16 00:46:25'),
(2530, 1, '2024-05-16 01:12:22', '112.134.213.243', '2024-05-16 01:12:22'),
(2531, 45, '2024-05-16 06:13:09', '112.134.234.25', '2024-05-16 06:13:09'),
(2532, 1, '2024-05-17 05:01:28', '112.134.234.25', '2024-05-17 05:01:28'),
(2533, 1, '2024-05-20 03:04:59', '112.134.234.25', '2024-05-20 03:04:59'),
(2534, 1, '2024-05-20 04:45:02', '112.134.234.25', '2024-05-20 04:45:02'),
(2535, 1, '2024-05-20 06:52:39', '112.134.234.25', '2024-05-20 06:52:39'),
(2536, 1, '2024-05-20 06:59:38', '112.134.234.25', '2024-05-20 06:59:38'),
(2537, 1, '2024-05-21 00:06:12', '112.134.200.159', '2024-05-21 00:06:12'),
(2538, 1, '2024-05-21 00:52:57', '124.43.241.12', '2024-05-21 00:52:57'),
(2539, 1, '2024-05-21 01:07:10', '112.134.234.25', '2024-05-21 01:07:10'),
(2540, 1, '2024-05-21 02:27:29', '112.134.234.25', '2024-05-21 02:27:29'),
(2541, 45, '2024-05-21 04:53:02', '112.134.234.25', '2024-05-21 04:53:02'),
(2542, 45, '2024-05-21 04:55:11', '112.134.234.25', '2024-05-21 04:55:11'),
(2543, 1, '2024-05-21 04:56:07', '112.134.234.25', '2024-05-21 04:56:07'),
(2544, 1, '2024-05-21 04:56:49', '112.134.234.25', '2024-05-21 04:56:49'),
(2545, 45, '2024-05-21 04:58:24', '112.134.234.25', '2024-05-21 04:58:24'),
(2546, 45, '2024-05-21 05:08:35', '112.134.234.25', '2024-05-21 05:08:35'),
(2547, 45, '2024-05-21 05:23:43', '112.134.234.25', '2024-05-21 05:23:43'),
(2548, 1, '2024-05-21 05:27:54', '112.134.234.25', '2024-05-21 05:27:54'),
(2549, 45, '2024-05-21 05:38:10', '112.134.234.25', '2024-05-21 05:38:10'),
(2550, 1, '2024-05-21 05:47:55', '112.134.234.25', '2024-05-21 05:47:55'),
(2551, 45, '2024-05-21 05:54:20', '112.134.234.25', '2024-05-21 05:54:20'),
(2552, 1, '2024-05-21 23:21:03', '112.134.234.25', '2024-05-21 23:21:03'),
(2553, 1, '2024-05-21 23:26:28', '112.134.234.25', '2024-05-21 23:26:28'),
(2554, 1, '2024-05-21 23:36:52', '112.134.234.25', '2024-05-21 23:36:52'),
(2555, 1, '2024-05-22 02:16:20', '112.134.234.25', '2024-05-22 02:16:20'),
(2556, 1, '2024-05-22 02:49:55', '112.134.234.25', '2024-05-22 02:49:55'),
(2557, 1, '2024-05-26 10:27:17', '112.135.244.21', '2024-05-26 10:27:17'),
(2558, 1, '2024-05-26 23:31:08', '112.134.234.25', '2024-05-26 23:31:08'),
(2559, 1, '2024-05-26 23:38:10', '112.134.234.25', '2024-05-26 23:38:10'),
(2560, 1, '2024-05-26 23:39:23', '112.134.234.25', '2024-05-26 23:39:23'),
(2561, 1, '2024-05-26 23:42:04', '112.134.234.25', '2024-05-26 23:42:04'),
(2562, 1, '2024-05-27 01:26:00', '112.134.212.140', '2024-05-27 01:26:00'),
(2563, 1, '2024-05-27 01:27:37', '112.134.234.25', '2024-05-27 01:27:37'),
(2564, 1, '2024-05-27 01:36:47', '112.134.234.25', '2024-05-27 01:36:47'),
(2565, 1, '2024-05-27 01:40:07', '112.134.234.25', '2024-05-27 01:40:07'),
(2566, 1, '2024-05-27 02:08:04', '112.134.234.25', '2024-05-27 02:08:04'),
(2567, 1, '2024-05-27 02:18:51', '112.134.234.25', '2024-05-27 02:18:51'),
(2568, 1, '2024-05-27 05:20:07', '112.134.234.25', '2024-05-27 05:20:07'),
(2569, 1, '2024-05-27 05:53:24', '112.134.234.25', '2024-05-27 05:53:24'),
(2570, 1, '2024-05-27 07:17:34', '112.134.234.25', '2024-05-27 07:17:34'),
(2571, 1, '2024-05-27 23:47:39', '112.134.234.25', '2024-05-27 23:47:39'),
(2572, 1, '2024-05-28 00:07:43', '112.134.234.25', '2024-05-28 00:07:43'),
(2573, 1, '2024-05-28 01:54:52', '112.134.234.25', '2024-05-28 01:54:52'),
(2574, 1, '2024-05-28 03:21:56', '112.134.234.25', '2024-05-28 03:21:56'),
(2575, 1, '2024-05-28 06:47:57', '112.134.234.25', '2024-05-28 06:47:57'),
(2576, 1, '2024-05-28 06:57:18', '112.134.234.25', '2024-05-28 06:57:18'),
(2577, 1, '2024-05-28 16:00:54', '112.134.214.217', '2024-05-28 16:00:54');

-- --------------------------------------------------------

--
-- Table structure for table `nbcourier_tbl_license`
--

CREATE TABLE `nbcourier_tbl_license` (
  `license_id` int(11) NOT NULL,
  `message` varchar(191) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `due_date` date NOT NULL,
  `status_yn` varchar(1) NOT NULL DEFAULT 'N'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ggportal_tbl_agent`
--
ALTER TABLE `ggportal_tbl_agent`
  ADD PRIMARY KEY (`agent_id`);

--
-- Indexes for table `ggportal_tbl_application_log`
--
ALTER TABLE `ggportal_tbl_application_log`
  ADD PRIMARY KEY (`application_log_id`);

--
-- Indexes for table `ggportal_tbl_application_status`
--
ALTER TABLE `ggportal_tbl_application_status`
  ADD PRIMARY KEY (`application_status_id`);

--
-- Indexes for table `ggportal_tbl_bank`
--
ALTER TABLE `ggportal_tbl_bank`
  ADD PRIMARY KEY (`bank_id`);

--
-- Indexes for table `ggportal_tbl_city`
--
ALTER TABLE `ggportal_tbl_city`
  ADD PRIMARY KEY (`city_id`);

--
-- Indexes for table `ggportal_tbl_commission`
--
ALTER TABLE `ggportal_tbl_commission`
  ADD PRIMARY KEY (`commission_id`);

--
-- Indexes for table `ggportal_tbl_content`
--
ALTER TABLE `ggportal_tbl_content`
  ADD PRIMARY KEY (`content_id`);

--
-- Indexes for table `ggportal_tbl_country`
--
ALTER TABLE `ggportal_tbl_country`
  ADD PRIMARY KEY (`country_id`);

--
-- Indexes for table `ggportal_tbl_course`
--
ALTER TABLE `ggportal_tbl_course`
  ADD PRIMARY KEY (`course_id`);

--
-- Indexes for table `ggportal_tbl_currency`
--
ALTER TABLE `ggportal_tbl_currency`
  ADD PRIMARY KEY (`currency_id`);

--
-- Indexes for table `ggportal_tbl_institute`
--
ALTER TABLE `ggportal_tbl_institute`
  ADD PRIMARY KEY (`institute_id`);

--
-- Indexes for table `ggportal_tbl_message`
--
ALTER TABLE `ggportal_tbl_message`
  ADD PRIMARY KEY (`message_id`);

--
-- Indexes for table `ggportal_tbl_message_staff`
--
ALTER TABLE `ggportal_tbl_message_staff`
  ADD PRIMARY KEY (`message_id`);

--
-- Indexes for table `ggportal_tbl_program`
--
ALTER TABLE `ggportal_tbl_program`
  ADD PRIMARY KEY (`program_id`);

--
-- Indexes for table `ggportal_tbl_program_wishlist`
--
ALTER TABLE `ggportal_tbl_program_wishlist`
  ADD PRIMARY KEY (`program_wishlist_id`);

--
-- Indexes for table `ggportal_tbl_province`
--
ALTER TABLE `ggportal_tbl_province`
  ADD PRIMARY KEY (`province_id`);

--
-- Indexes for table `ggportal_tbl_remainder`
--
ALTER TABLE `ggportal_tbl_remainder`
  ADD PRIMARY KEY (`remainder_id`);

--
-- Indexes for table `ggportal_tbl_staff`
--
ALTER TABLE `ggportal_tbl_staff`
  ADD PRIMARY KEY (`staff_id`);

--
-- Indexes for table `ggportal_tbl_staff_activity`
--
ALTER TABLE `ggportal_tbl_staff_activity`
  ADD PRIMARY KEY (`user_activity_id`);

--
-- Indexes for table `ggportal_tbl_staff_login`
--
ALTER TABLE `ggportal_tbl_staff_login`
  ADD PRIMARY KEY (`user_login_id`);

--
-- Indexes for table `ggportal_tbl_staff_privilege`
--
ALTER TABLE `ggportal_tbl_staff_privilege`
  ADD PRIMARY KEY (`staff_privilege_id`);

--
-- Indexes for table `ggportal_tbl_state`
--
ALTER TABLE `ggportal_tbl_state`
  ADD PRIMARY KEY (`state_id`);

--
-- Indexes for table `ggportal_tbl_student`
--
ALTER TABLE `ggportal_tbl_student`
  ADD PRIMARY KEY (`student_id`),
  ADD UNIQUE KEY `nic` (`student_id`);

--
-- Indexes for table `ggportal_tbl_student_application`
--
ALTER TABLE `ggportal_tbl_student_application`
  ADD PRIMARY KEY (`student_application_id`);

--
-- Indexes for table `ggportal_tbl_student_dependence`
--
ALTER TABLE `ggportal_tbl_student_dependence`
  ADD PRIMARY KEY (`dependence_id`);

--
-- Indexes for table `ggportal_tbl_student_disability`
--
ALTER TABLE `ggportal_tbl_student_disability`
  ADD PRIMARY KEY (`disability_id`);

--
-- Indexes for table `ggportal_tbl_student_document`
--
ALTER TABLE `ggportal_tbl_student_document`
  ADD PRIMARY KEY (`student_document_id`);

--
-- Indexes for table `ggportal_tbl_student_education`
--
ALTER TABLE `ggportal_tbl_student_education`
  ADD PRIMARY KEY (`education_id`);

--
-- Indexes for table `ggportal_tbl_student_english_test`
--
ALTER TABLE `ggportal_tbl_student_english_test`
  ADD PRIMARY KEY (`english_test_id`);

--
-- Indexes for table `ggportal_tbl_student_passport`
--
ALTER TABLE `ggportal_tbl_student_passport`
  ADD PRIMARY KEY (`passport_id`),
  ADD UNIQUE KEY `passport_number` (`passport_number`);

--
-- Indexes for table `ggportal_tbl_student_travel_history`
--
ALTER TABLE `ggportal_tbl_student_travel_history`
  ADD PRIMARY KEY (`travel_history_id`);

--
-- Indexes for table `ggportal_tbl_student_visa_refusal`
--
ALTER TABLE `ggportal_tbl_student_visa_refusal`
  ADD PRIMARY KEY (`visa_refusal_id`);

--
-- Indexes for table `ggportal_tbl_student_work`
--
ALTER TABLE `ggportal_tbl_student_work`
  ADD PRIMARY KEY (`work_id`);

--
-- Indexes for table `ggportal_tbl_timeline`
--
ALTER TABLE `ggportal_tbl_timeline`
  ADD PRIMARY KEY (`timeline_id`),
  ADD KEY `timeline_name` (`timeline_name`);

--
-- Indexes for table `ggportal_tbl_training_request`
--
ALTER TABLE `ggportal_tbl_training_request`
  ADD PRIMARY KEY (`training_request_id`);

--
-- Indexes for table `ggportal_tbl_user`
--
ALTER TABLE `ggportal_tbl_user`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `ggportal_tbl_user_activity`
--
ALTER TABLE `ggportal_tbl_user_activity`
  ADD PRIMARY KEY (`user_activity_id`);

--
-- Indexes for table `ggportal_tbl_user_login`
--
ALTER TABLE `ggportal_tbl_user_login`
  ADD PRIMARY KEY (`user_login_id`);

--
-- Indexes for table `nbcourier_tbl_license`
--
ALTER TABLE `nbcourier_tbl_license`
  ADD PRIMARY KEY (`license_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ggportal_tbl_agent`
--
ALTER TABLE `ggportal_tbl_agent`
  MODIFY `agent_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=52;

--
-- AUTO_INCREMENT for table `ggportal_tbl_application_log`
--
ALTER TABLE `ggportal_tbl_application_log`
  MODIFY `application_log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=529;

--
-- AUTO_INCREMENT for table `ggportal_tbl_application_status`
--
ALTER TABLE `ggportal_tbl_application_status`
  MODIFY `application_status_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=122;

--
-- AUTO_INCREMENT for table `ggportal_tbl_bank`
--
ALTER TABLE `ggportal_tbl_bank`
  MODIFY `bank_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=105;

--
-- AUTO_INCREMENT for table `ggportal_tbl_city`
--
ALTER TABLE `ggportal_tbl_city`
  MODIFY `city_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_commission`
--
ALTER TABLE `ggportal_tbl_commission`
  MODIFY `commission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=784;

--
-- AUTO_INCREMENT for table `ggportal_tbl_content`
--
ALTER TABLE `ggportal_tbl_content`
  MODIFY `content_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ggportal_tbl_country`
--
ALTER TABLE `ggportal_tbl_country`
  MODIFY `country_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `ggportal_tbl_course`
--
ALTER TABLE `ggportal_tbl_course`
  MODIFY `course_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `ggportal_tbl_currency`
--
ALTER TABLE `ggportal_tbl_currency`
  MODIFY `currency_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `ggportal_tbl_institute`
--
ALTER TABLE `ggportal_tbl_institute`
  MODIFY `institute_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=761;

--
-- AUTO_INCREMENT for table `ggportal_tbl_message`
--
ALTER TABLE `ggportal_tbl_message`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=44;

--
-- AUTO_INCREMENT for table `ggportal_tbl_message_staff`
--
ALTER TABLE `ggportal_tbl_message_staff`
  MODIFY `message_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `ggportal_tbl_program`
--
ALTER TABLE `ggportal_tbl_program`
  MODIFY `program_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=665;

--
-- AUTO_INCREMENT for table `ggportal_tbl_program_wishlist`
--
ALTER TABLE `ggportal_tbl_program_wishlist`
  MODIFY `program_wishlist_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=51;

--
-- AUTO_INCREMENT for table `ggportal_tbl_province`
--
ALTER TABLE `ggportal_tbl_province`
  MODIFY `province_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=204;

--
-- AUTO_INCREMENT for table `ggportal_tbl_remainder`
--
ALTER TABLE `ggportal_tbl_remainder`
  MODIFY `remainder_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff`
--
ALTER TABLE `ggportal_tbl_staff`
  MODIFY `staff_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=99;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_activity`
--
ALTER TABLE `ggportal_tbl_staff_activity`
  MODIFY `user_activity_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_login`
--
ALTER TABLE `ggportal_tbl_staff_login`
  MODIFY `user_login_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1760;

--
-- AUTO_INCREMENT for table `ggportal_tbl_staff_privilege`
--
ALTER TABLE `ggportal_tbl_staff_privilege`
  MODIFY `staff_privilege_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `ggportal_tbl_state`
--
ALTER TABLE `ggportal_tbl_state`
  MODIFY `state_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=156;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student`
--
ALTER TABLE `ggportal_tbl_student`
  MODIFY `student_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=169;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_application`
--
ALTER TABLE `ggportal_tbl_student_application`
  MODIFY `student_application_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=81;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_dependence`
--
ALTER TABLE `ggportal_tbl_student_dependence`
  MODIFY `dependence_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_disability`
--
ALTER TABLE `ggportal_tbl_student_disability`
  MODIFY `disability_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_document`
--
ALTER TABLE `ggportal_tbl_student_document`
  MODIFY `student_document_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1597;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_education`
--
ALTER TABLE `ggportal_tbl_student_education`
  MODIFY `education_id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=138;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_english_test`
--
ALTER TABLE `ggportal_tbl_student_english_test`
  MODIFY `english_test_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_passport`
--
ALTER TABLE `ggportal_tbl_student_passport`
  MODIFY `passport_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=308;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_travel_history`
--
ALTER TABLE `ggportal_tbl_student_travel_history`
  MODIFY `travel_history_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_visa_refusal`
--
ALTER TABLE `ggportal_tbl_student_visa_refusal`
  MODIFY `visa_refusal_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `ggportal_tbl_student_work`
--
ALTER TABLE `ggportal_tbl_student_work`
  MODIFY `work_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `ggportal_tbl_timeline`
--
ALTER TABLE `ggportal_tbl_timeline`
  MODIFY `timeline_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `ggportal_tbl_training_request`
--
ALTER TABLE `ggportal_tbl_training_request`
  MODIFY `training_request_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user`
--
ALTER TABLE `ggportal_tbl_user`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user_activity`
--
ALTER TABLE `ggportal_tbl_user_activity`
  MODIFY `user_activity_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ggportal_tbl_user_login`
--
ALTER TABLE `ggportal_tbl_user_login`
  MODIFY `user_login_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2578;

--
-- AUTO_INCREMENT for table `nbcourier_tbl_license`
--
ALTER TABLE `nbcourier_tbl_license`
  MODIFY `license_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
