<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php';
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    die("No Access");
}

$cmessages = new Messages();

if (isset($_POST['messagesSave'])) {
    $message = isset($_POST['message']) ? $_POST['message'] : null;
    $student_id = $_POST['student_id'];
    $user_type = $_POST['user_type'];
    $doc_url = null;

    if (isset($_FILES['_files']) && $_FILES['_files']['error'] == 0) {
        $upload_dir = '../dist/uploads/documents/';
        $file_name = basename($_FILES['_files']['name']);
        $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
        $time = time(); 
        $new_file_name = $time . '_' . pathinfo($file_name, PATHINFO_FILENAME) . '.' . $file_ext; 
        $target_file = $upload_dir . $new_file_name;

        if (move_uploaded_file($_FILES['_files']['tmp_name'], $target_file)) {
            // $doc_url = $target_file;
            $doc_url = "dist/uploads/documents/".$new_file_name;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Failed to upload file'
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        }
    }

    if ($message || $doc_url) {
        $criteria = array(
            'message' =>$message ?: $doc_url, 
            'user_id' => $student_id,
            'user_type' => $user_type,
            'is_read' => "N"
            // 'doc_url' => $message,
        );

        $result = $cmessages->savemessages($criteria);

        if ($result) {
            $response = [
                'status' => 'success',
                'message' => 'Message saved successfully'
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Failed to save message'
            ];
        }
    } else {
        $response = [
            'status' => 'error',
            'message' => 'Please enter a message or attach a document'
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit();
}

//save application vise messege
if (isset($_POST['applicationMessagesSave'])) {
    $message = isset($_POST['message']) ? $_POST['message'] : null;
    $student_id = $_POST['student_id'];
    $user_type = $_POST['user_type'];
    $application_id= $_POST['application_id'];
    $sender_type=$_POST['sender_type'];
    $create_user_id=$_POST['create_user_id'];

    $doc_url = null;

    if (isset($_FILES['_files']) && $_FILES['_files']['error'] == 0) {
        $upload_dir = '../dist/uploads/documents/';
        $file_name = basename($_FILES['_files']['name']);
        $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
        $time = time(); 
        $new_file_name = $time . '_' . pathinfo($file_name, PATHINFO_FILENAME) . '.' . $file_ext; 
        $target_file = $upload_dir . $new_file_name;

        if (move_uploaded_file($_FILES['_files']['tmp_name'], $target_file)) {
            // $doc_url = $target_file;
            $doc_url = "dist/uploads/documents/".$new_file_name;
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Failed to upload file'
            ];
            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        }
    }

    if ($message || $doc_url) {
        $criteria = array(
            'message' =>$message ?: $doc_url, 
            'user_id' => $create_user_id,
            'user_type' => "SF",
            'is_read' => "N",
            'application_id' => $application_id,
            'student_id' => $student_id,
            'sender_type' => $sender_type
            // 'doc_url' => $message,
        );

        $result = $cmessages->applicationMessagesSave($criteria);

        if ($result) {
            $response = [
                'status' => 'success',
                'message' => 'Message saved successfully',
                'application_id' => $application_id
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => 'Failed to save message'
            ];
        }
    } else {
        $response = [
            'status' => 'error',
            'message' => 'Please enter a message or attach a document'
        ];
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit();
}

if (isset($_POST['action']) && $_POST['action'] === 'getLastSeenApplicationVise') {
    $capplication = new Application();
    $cstaff=new Staff();
    $cagent=new Agent();

    $student_id = filter_input(INPUT_POST, 'student_id', FILTER_SANITIZE_NUMBER_INT);
    $user_type = filter_input(INPUT_POST, 'user_type', FILTER_SANITIZE_STRING);
    $application_id = filter_input(INPUT_POST, 'application_id', FILTER_SANITIZE_NUMBER_INT);
    $create_user_id= filter_input(INPUT_POST, 'create_user_id', FILTER_SANITIZE_NUMBER_INT);

    $last_seen = null;
    $last_seen_status = null;
    $first_name = null;

    $application_details = $capplication->getApplicationDetailsByID($application_id);

    
    if (!empty($application_details)) {
        
        $first_application = $application_details[0];

        $app_created_user_type=isset($first_application['created_user_type']) ? $first_application['created_user_type'] : null;

        if($app_created_user_type=="ST"){            
            $created_user_name = "Admin";
            $created_user_type="";

        }else if($app_created_user_type=="SF"){            
            $created_user_name = $cstaff->getStaffNameById($create_user_id);
            $created_user_type="staff";

        }else if($app_created_user_type=="AG"){            
            $created_user_name = $cagent->getAgentNameById($create_user_id);        
            $created_user_type="Agent";

        }else{
            $created_user_name = "Admin";
            $created_user_type="";
        }
        
        $response = array(
            'first_name' => $first_name, 
            'last_seen_status' => $last_seen_status,  
            'last_seen' => $last_seen,  
            'institute_name' => isset($first_application['institute_name']) ? $first_application['institute_name'] : null,
            'program_name' => isset($first_application['program_name']) ? $first_application['program_name'] : null,
            'application_no' => isset($first_application['application_no']) ? $first_application['application_no'] : null,
            'application_create_date' => isset($first_application['created_date']) ? $first_application['created_date'] : null,
            'intake_year' => isset($first_application['application_year']) ? $first_application['application_year'] : null,
            'intake_month' => isset($first_application['application_intake']) ? $first_application['application_intake'] : null,
            'created_user_name' => $created_user_name,
            'application_created_by' => $created_user_type,
            'student_first_name' => isset($first_application['student_first_name']) ? $first_application['student_first_name'] : null,
            'student_last_name' => isset($first_application['student_last_name']) ? $first_application['student_last_name'] : null,


        );

        echo json_encode($response);
    } else {
        
        echo json_encode(array('error' => 'No application details found'));
    }

    exit;
}





?>
