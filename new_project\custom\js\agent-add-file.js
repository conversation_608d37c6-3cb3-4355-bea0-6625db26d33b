$(document).ready(function () {

    let validate_form_1 = validate_form_2 = validate_form_3 = validate_form_4 = validate_form_5 =
        validate_form_6 = false;

    var education_files = [];
    var travel_files = [];
    var englishtest_files = [];
    var work_files = [];
    var other_files = [];

    //console.log("ready!");
    bsCustomFileInput.init();
    //Initialize Select2 Elements
    $('.select2bs4').select2({
        theme: 'bootstrap4'
    });

    // var phone = $('#mobile').val();
    // var phone = $('#mobile_no').val(phone.replace($('.country_phone_code :selected').text(), ""));
    $('#mobile_no').on('keyup', function () {
        var mobile_no = $(this).val();
        $('#mobile').val("("+$('.country_phone_code :selected').text()+")"+ mobile_no);
    });

    $('#reservation').daterangepicker({
        locale: {
            format: 'YYYY-MM-DD'
        },
        singleDatePicker: true,
        showDropdowns: true,
    }); 

    var token;


    var current_fs, next_fs, previous_fs;

    // No BACK button on first screen
    if ($(".show").hasClass("first-screen")) {
        $(".prev").css({
            'display': 'none'
        });
    }

    // Next button
    $(".next-button").click(function () {

        //validate form 1
        // console.log($('#quickform').valid());
        if ($('#quickform').valid()) {
            validate_form_1 = true;
        } else {
            validate_form_1 = false;
        }

        // console.log({
        //     validate_form_1: validate_form_1,
        //     validate_form_2: validate_form_2,
        //     validate_form_3: validate_form_3,
        //     validate_form_4: validate_form_4,
        //     validate_form_5: validate_form_5,
        //     validate_form_6: validate_form_6
        // });

        if (validate_form_1 || validate_form_2 || validate_form_3 || validate_form_4 ||
            validate_form_5 || validate_form_6) {

            current_fs = $(this).parent().parent();
            next_fs = $(this).parent().parent().next();

            $(".prev").css({
                'display': 'inline-block'
            });

            $(current_fs).removeClass("show");
            $(next_fs).addClass("show");


            $("#progressbar li").eq($(".card2").index(next_fs)).addClass("active");
            $("#progressbar li").eq($(".card2").index(current_fs)).addClass("complete_form");


            current_fs.animate({}, {
                step: function () {

                    current_fs.css({
                        'display': 'none',
                        'position': 'relative'
                    });

                    next_fs.css({
                        'display': 'block'
                    });
                }
            });
        } else {
            //toastr.info('Please complete the form first!');
        }

        validate_form_1 = validate_form_2 = validate_form_3 = validate_form_4 = validate_form_5 =
            false;
    });

    // Previous button
    $(".prev").click(function () {

        current_fs = $(".show");
        previous_fs = $(".show").prev();

        $(current_fs).removeClass("show");
        $(previous_fs).addClass("show");

        $(".prev").css({
            'display': 'inline-block'
        });

        if ($(".show").hasClass("first-screen")) {
            $(".prev").css({
                'display': 'none'
            });
        }


        $("#progressbar li").eq($(".card2").index(current_fs)).removeClass("active");
        $("#progressbar li").eq($(".card2").index(previous_fs)).removeClass("complete_form");

        current_fs.animate({}, {
            step: function () {

                current_fs.css({
                    'display': 'none',
                    'position': 'relative'
                });

                previous_fs.css({
                    'display': 'block'
                });
            }
        });
    });


    /*Save Agent*/
    $('#quickform').validate({
        rules: {
            ag_company_name: {
                required: true,
            },
            first_name: {
                required: true,
            },
            last_name: {
                required: true,
            },
            mobile: {
                required: true,
                digits: true,
            },
            email: {
                email: true,
                required: true,
            },
            country: {
                required: true,
            },
            account_name: {
                required: true,
            },
            account_no: {
                required: true,
            },
            bank_name: {
                required: true,
            },
            city: {
                required: true,
            },
            mobile_no: {
                required: true,
                digits: true,
            },
            branch_name: {
                required: true,
            },
            branch_name: {
                required: true,
            },
            // agreement_signed_yn: {
            //     required: true,
            // },
        },
        messages: {
            ag_company_name: {
                required: "Please Enter a value",
            },
            first_name: {
                required: "Please Enter a value",
            },
            last_name: {
                required: "Please Enter a value",
            },
            city: {
                required: "Please Enter a value",
            },
            mobile_no: {
                required: "Please Enter a value",
                minlength: "Please specify a valid phone number",
                maxlength: "Please specify a valid phone number"
            },
            last_name: {
                required: "Please Enter a value",
            },
            mobile: {
                required: "Please Enter a value",
                minlength: "Please specify a valid phone number",
                maxlength: "Please specify a valid phone number"
            },
            email: {
                email: "Please enter a vaild email address",
                required: "Please Enter a value",
            },
            country: {
                required: "Please  Select One",
            },
            // agreement_signed_yn: {
            //     required: "Please  Check",
            // },
        },
        errorElement: 'span',
        errorPlacement: function (error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function (form) {
            //   console.log(new FormData(form));
            var formData = new FormData(form);
            formData.append('agentSave', '1');
            $.ajax({
                type: "POST",
                url: "controller/agent-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function (data) {
                    // console.log(data);
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        //validate_form_1 = true;
                        //$('input[name="agent_id"]').val(data.data);
                        //simulate a next button click
                        var user_type = $('input[name="user_type"]').val();
                        if (user_type == 'AG') {
                            window.location.href = 'agent-profile.php';
                        } else {
                            window.location.href = 'agent-list.php';
                        }

                        //$('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });
        }
    });
    $('.country').on('change', function () {
        $(this).valid();
    });

});