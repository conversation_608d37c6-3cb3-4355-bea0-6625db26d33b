$(document).ready(function() {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });

    $('.timeline_btn').on('click', function() {
        $('#timelineChangeForm').trigger("reset");
        var student_id = $(this).data('student_id');
        var timeline_id = $(this).data('timeline_id');
        // console.log({
        //     student_id: student_id,
        //     timeline_id: timeline_id
        // });
        $('input[name=student_id]').val(student_id);
        if (timeline_id > 0) {
            $('select[name=timeline_id]').val(timeline_id);
        }
    });

    $('#modal-timeline-change').on('shown.bs.modal', function() {
        //$('#timelineChangeForm').trigger("reset");

        // console.log($('.timeline_btn').data('student_id'));
        //console.log($('.timeline_btn').data('timeline_id'));
    })


    /*Save Student*/
    $('#timelineChangeForm').validate({
        rules: {
            student_id: {
                required: true,
            },
            timeline_id: {
                required: true,
            }
        },
        messages: {

            first_name: {
                required: "Please Enter a value",
            },
            timeline_id: {
                required: "Please Please Select One",
            }
        },
        errorElement: 'span',
        errorPlacement: function(error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function(element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function(element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        },
        submitHandler: function(form) {
            //   console.log(new FormData(form));
            var formData = new FormData(form);
            formData.append('updateTimelineSave', '1');
            $.ajax({
                type: "POST",
                url: "controller/student-controller.php",
                data: formData,
                dataType: 'json',
                mimeType: "multipart/form-data",
                contentType: false,
                processData: false,
                cache: false,
                success: function(data) {
                    console.log(data);
                    if (data.data > 0) {
                        toastr.success('Saved Successfully');
                        //validate_form_1 = true;
                        //$('input[name="student_id"]').val(data.data);
                        //simulate a next button click
                        // var user_type = $('input[name="user_type"]').val();
                        // if (user_type == 'ST') {
                        //     window.location.href = 'student-profile.php';
                        // } else {
                        // }
                        window.location.href = 'student-list.php';

                        //$('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                    } else {
                        toastr.error(data.status);
                    }

                }
            });
        }
    });


    $('.delete_btn').on('click', function() {
        var student_id = $(this).data('student_id');

        Swal.fire({
            title: 'Are you sure want to delete?',
            showCancelButton: true,
            confirmButtonText: `Delete`,
            confirmButtonColor: '#d33',
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {

                $.ajax({
                    type: "POST",
                    url: "controller/student-controller.php",
                    data: {
                        "student_id": student_id,
                        "deleteStudent": 1
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);
                        if (data.data > 0) {
                            Swal.fire(
                                'Deleted!',
                                'Your file has been deleted.',
                                'success'
                            )
                            //after 1 second reload the page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });


            }
        })
    });
    // })

});