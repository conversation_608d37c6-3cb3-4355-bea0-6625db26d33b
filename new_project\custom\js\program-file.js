$(function() {
    $('.select2bs4').select2({
        theme: 'bootstrap4'
    });

    // $('.program_search').on('click', function() {
    //     //hide search page
    //     $('.search_page_start').addClass('d-none');
    //     $('.search_page_after').removeClass('d-none');
    // });

    $("[name='course_id']").change(function() {
        $("[name='course_name']").val($("[name='course_id']").find(":selected").text());
    });

    $("[name='country_id']").change(function() {
        $("[name='country_name']").val($("[name='country_id']").find(":selected").text());
    });


    $('#quickform').validate({
      rules: {
          country_id: {
              required: true,
          },
          course_id: {
              required: true,
          },
      },
      messages: {
          country_id: {
              required: "Please Choose One",
          },
          course_id: {
              required: "Please Choose One",
          },
      },
      errorElement: 'span',
      errorPlacement: function (error, element) {
          error.addClass('invalid-feedback');
          element.closest('.form-group').append(error);
      },
      highlight: function (element, errorClass, validClass) {
          $(element).addClass('is-invalid');
      },
      unhighlight: function (element, errorClass, validClass) {
          $(element).removeClass('is-invalid');
      },
  });

  // Initialize the SELECT2
  $(document).ready(function() {
    $('.js-select2-selection').select2();
  });

  $('.btn-reset').on('click', function() {
    window.location.reload();
  });



});

