<?php

// Save messages from student and staff sides
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];
$username = $_SESSION['user']['first_name'];

$access_available_for = "SF,ST";

if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cmessages = new Messages();

if(isset($_POST['messagesSave'])) {
    $message_id = filter_input(INPUT_POST, 'message_id');
    $message = filter_input(INPUT_POST, 'message');
    $student_id = filter_input(INPUT_POST, 'student_id');
    $staff_id = filter_input(INPUT_POST, 'staff_id');
    $user_type = filter_input(INPUT_POST, 'user_type');



    $criteria = array(
        'message_id' => $message_id
        ,'message' => $message
        ,'student_id' => $student_id
        ,'user_type' => $user_type
        ,'staff_id' => $staff_id
        ,'is_read' => "N"
    );

    $result = $cmessages->saveMessagesStudentStaff($criteria);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success' : 'There is an error while saving.')
    );
    echo json_encode($json);
    die();
}
