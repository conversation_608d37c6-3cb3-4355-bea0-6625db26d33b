<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';
include '../form-submit-email.php';
require_once __DIR__ . '/../class/class.sms.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
  //will automatically redirect to login page
  die("No Access");
}

$cApplication = new Application();
$cProgram = new Program();
$cStudent = new Student();
$cStaff = new Staff();
$cSms = new Sms();


// $json = array(
//    "data" =>$_POST
// );
// echo json_encode($json);
// die();


if (isset($_POST['updateSpecialNoteSave'])) {


  $application_log_id = filter_input(INPUT_POST, 'application_log_id');
  $special_note = filter_input(INPUT_POST, 'special_note');

  $criteria = array(
    "application_log_id" => $application_log_id,
    "special_note" => $special_note
  );

  $result = $cApplication->updateApplicationSplNote($criteria);

  if ($result > 0) {

    $application_log_details = $cApplication->getApplicationLog($criteria);
    $student_application_id = $application_log_details[0]['student_application_id'];

    $student_application_details = $cApplication->getApplicationDetailsByID($student_application_id);

    if ($student_application_details[0]['real_time_status'] == "Y" && $special_note != "") {
      // $json = array(
      //     "data" =>$student_application_details[0]
      //  );
      //  echo json_encode($json);
      //  die();

      //datetime now
     
      $address = $student_application_details[0]['student_email'];
      $subject = 'Application Status';

      $body = '<!DOCTYPE html>
                        <html>
                        
                        <head>
                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        
                            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
        
                            <style>
                                .header {
                                    background-color: #293C4A;
                                    padding: 20px;
                                    font-size: 25px;
                                    text-align: center;
                                    font-weight: bolder;
                                }
                        
                                .header h2 {
                                    color: #fff;
                                    margin-bottom: 0px;
                                }
                        
                                .header p {
                                   
                                    color: #fff;
                                    font-size: 16px;
                                }
                        
                            </style>
                        </head>
                        
                        <body>
                        
                        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
                        <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
                          <tbody><tr>
                            <td align="center" valign="top">
                              <table width="650" border="0" cellspacing="0" cellpadding="0">
                                <tbody><tr>
                                  <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                                    
                                      <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                                      
                                          <tbody><tr>
                                              <td class="header">
                                                  <h2 style="font-weight: 700;">Edvios</h2>
                                                  <p style="padding-top: 30px;">The Student Portal</p>
                                              </td>
                                          </tr>
                                      </tbody></table>
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                            <tbody><tr>
                                              <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr>
                                                    <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                                      Hi ' . $student_application_details[0]['first_name'] . ' ' . $student_application_details[0]['last_name'] . '</td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" style="border-bottom:#e3e3e3 1px solid">
                                            <tbody><tr>
                                              <td style="padding:15px 20px">
                                                <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr height="32px">
                                                    <td width="20%" style="color:#000000;font-size:14px">Student\'s Name 
                                                    </td>
                                                    <td style="color:#000000;padding:0 2px;font-size:14px" width="10%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px" width="70%">' . $student_application_details[0]['first_name'] . ' ' . $student_application_details[0]['last_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Application Id   </td>
                                                  <td style="color:#000000;font-size:14px;padding:0 2px;line-height:18px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['application_no'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Country  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['country'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Institution  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['institute_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Program </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['program_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Intake  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['application_intake'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Status  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['status_name'] . '</td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                      
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-bottom:#e3e3e3 1px solid">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">                     
                                              <tbody><tr>
                                              <td style="padding:12px 20px">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr>
                                                    <td style="font-size:14px;line-height:18px;color:#000000;text-align:left">
                                                    Special Note : ' . $application_log_details[0]['special_note'] . '</td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr>
                                      <td>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" style="border-bottom:#e3e3e3 1px solid">
                                          <tbody><tr>
                                            <td style="padding:15px 20px">
                                              <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                <tbody><tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px"> Creation Date</td>

                                                  <td style="color:#000000;padding:0 2px;font-size:14px" width="10%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px" width="70%">' . $student_application_details[0]['created_date'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                <td style="color:#000000;font-size:14px;line-height:18px">Collage</td>
                                                <td style="color:#000000;font-size:14px;padding:0 2px;line-height:18px" width="2%">:</td>
                                                <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['institute_name'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Expert Advise</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['my_prefer_yn'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Prefered Course </td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['program_name'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Intake</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal"> ' . $student_application_details[0]['application_intake'] . '-' . $student_application_details[0]['application_year'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px"> Update On</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['updated_date'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Remarks</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['remarks'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">date</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['date'] . '</td>
                                                </tr>
                                                
                                              </tbody></table>
                                            </td>
                                          </tr>
                                        </tbody></table>
                                      </td>
                                    </tr>
                                  </tbody></table>
                      
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tbody><tr>
                                              <td align="center" style="padding-bottom:10px">
                                                <table border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                                  <tr>
                                                  <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                            
                            <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                              <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                                  </tr>							
                                                </tbody></table>
                                              </td>
                                            </tr>
                                              
                                          
                                          </tbody></table>
                                          
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                                      <tbody><tr>
                                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                                              <tbody><tr>
                                              <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                                  Copyright &copy; 2022  <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>. All rights reserved.</td>
                                            </tr>
                                           
                                          
                                          </tbody></table>
                                          
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    
                                  </td>
                                </tr>
                              </tbody></table>
                            </td>
                          </tr>
                        </tbody></table>
            
                        </div></div>
                        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>

                        </body>
                        
                        </html>';

      send_mail($subject, $address, $body);
    

    }
  }

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}

if (isset($_POST['updateStatus'])) {


  $student_application_id = filter_input(INPUT_POST, 'student_application_id');
  $status_id = filter_input(INPUT_POST, 'selected_status_hidden_id');
  $remarks = filter_input(INPUT_POST, 'remarks');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $date = filter_input(INPUT_POST, 'date');

  $criteria = array(
    "student_application_id" => $student_application_id,
    "status_id" => $status_id,
    "remarks" => $remarks,
    "date" => $date
  );

  $result = $cApplication->updateApplicationStatus($criteria);

  if ($result > 0) {
    $student_application_details = $cApplication->getApplicationDetailsByID($student_application_id);
    $application_log_details = $cApplication->getApplicationLog($criteria);


    if ($student_application_details[0]['real_time_status'] == "Y") {

      $address = $student_application_details[0]['student_email'];
      $subject = 'Application Status';

      $body = '<!DOCTYPE html>
                        <html>
                        
                        <head>
                            <meta http-equiv="X-UA-Compatible" content="IE=edge">
                            <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        
                            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
                            <style>
                                .header {
                                    background-color: #293C4A;
                                    padding: 20px;
                                    font-size: 25px;
                                    text-align: center;
                                    font-weight: bolder;
                                }
                        
                                .header h2 {
                                    color: #fff;
                                    margin-bottom: 0px;
                                }
                        
                          
                                .header p {
                                    color: #fff;
                                    font-size: 16px;
                                }
                        
                               
                            </style>
                        </head>
                        
                        <body>
                        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
                        <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
                          <tbody><tr>
                            <td align="center" valign="top">
                              <table width="650" border="0" cellspacing="0" cellpadding="0">
                                <tbody><tr>
                                  <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                                    
                                      <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                                      
                                          <tbody><tr>
                                              <td class="header">
                                                  <h2 style="font-weight: 700;">Edvios</h2>
                                                  <p style="padding-top: 30px;">The Student Portal</p>
                                              </td>
                                          </tr>
                                      </tbody></table>
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                            <tbody><tr>
                                              <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr>
                                                    <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                                      Hi ' . $student_application_details[0]['first_name'] . ' ' . $student_application_details[0]['last_name'] . '</td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" style="border-bottom:#e3e3e3 1px solid">
                                            <tbody><tr>
                                              <td style="padding:15px 20px">
                                                <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr height="32px">
                                                    <td width="20%" style="color:#000000;font-size:14px">Student\'s Name 
                                                    </td>
                                                    <td style="color:#000000;padding:0 2px;font-size:14px" width="10%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px" width="70%">' . $student_application_details[0]['first_name'] . ' ' . $student_application_details[0]['last_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Application Id   </td>
                                                  <td style="color:#000000;font-size:14px;padding:0 2px;line-height:18px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['application_no'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Country  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['country'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Institution  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['institute_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Program </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['program_name'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Intake  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['application_intake'] . '</td>
                                                  </tr>
                                                  <tr height="32px">
                                                    <td style="color:#000000;font-size:14px;line-height:18px">Status  </td>
                                                     <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                    <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $student_application_details[0]['status_name'] . '</td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>
                      
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-bottom:#e3e3e3 1px solid">
                                      <tbody><tr>
                                        <td>
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">                     
                                              <tbody><tr>
                                              <td style="padding:12px 20px">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr>
                                                    <td style="font-size:14px;line-height:18px;color:#000000;text-align:left">
                                                    Title : ' . $student_application_details[0]['status_name'] . ' </td>
                                                  </tr>
                                                  
                                                </tbody></table>
                                              </td>
                                            </tr>
                                          </tbody></table>
                                        </td>
                                      </tr>
                                    </tbody></table>    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr>
                                      <td>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9" style="border-bottom:#e3e3e3 1px solid">
                                          <tbody><tr>
                                            <td style="padding:15px 20px">
                                              <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                <tbody><tr height="32px">
                                                <td style="color:#000000;font-size:14px;line-height:18px"> Creation Date</td>
                                                  <td style="color:#000000;padding:0 2px;font-size:14px" width="10%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px" width="70%">' . $student_application_details[0]['created_date'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                <td style="color:#000000;font-size:14px;line-height:18px">Collage</td>
                                                <td style="color:#000000;font-size:14px;padding:0 2px;line-height:18px" width="2%">:</td>
                                                <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['institute_name'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Expert Advise</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['my_prefer_yn'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Prefered Course </td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-weight:normal;font-size:14px;line-height:18px">' . $student_application_details[0]['program_name'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Intake</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal"> ' . $student_application_details[0]['application_intake'] . '-' . $student_application_details[0]['application_year'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px"> Update On</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['updated_date'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                  <td style="color:#000000;font-size:14px;line-height:18px">Remarks</td>
                                                   <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                  <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['remarks'] . '</td>
                                                </tr>
                                                <tr height="32px">
                                                <td style="color:#000000;font-size:14px;line-height:18px">date</td>
                                                 <td style="color:#000000;font-size:14px;line-height:18px;padding:0 2px" width="2%">:</td>
                                                <td style="color:#000000;font-size:14px;line-height:18px;font-weight:normal">' . $application_log_details[0]['date'] . '</td>
                                              </tr>
                                                
                                              </tbody></table>
                                            </td>
                                          </tr>
                                        </tbody></table>
                                      </td>
                                    </tr>
                                  </tbody></table>
                      
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tbody><tr>
                                              <td align="center" style="padding-bottom:10px">
                                                <table border="0" cellspacing="0" cellpadding="0">
                                                  <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                                  <tr>
                                                  <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                                  <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                                <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                                  <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                                
                                                <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                                  <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                                  </tr>							
                                                </tbody></table>
                                              </td>
                                            </tr>
                                              
                                          
                                          </tbody></table>
                                          
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                                      <tbody><tr>
                                        <td style="padding:10px 30px" bgcolor="#1b2f3e">
                                          <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                                              <tbody><tr>
                                              <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                                  Copyright &copy; 2022  <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>. All rights reserved.</td>
                                            </tr>
                                           
                                          
                                          </tbody></table>
                                          
                                        </td>
                                      </tr>
                                    </tbody></table>
                                    
                                  </td>
                                </tr>
                              </tbody></table>
                            </td>
                          </tr>
                        </tbody></table>
                        <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
                        </body>
                        
                        </html>';
            send_mail($subject, $address, $body); 
       
    
      //sms to student    
      $status_of_application=$student_application_details[0]['status_name']; 

      if($status_of_application=='Application Received'){
          $message = "Your application has been successfully received. We'll keep you updated on its progress.";
          $student_phone_number = $student_application_details[0]['student_mobile'];
          $agent_phone_number = $student_application_details[0]['mobile'];

        if($agent_phone_number!==''){

         $cSms->send_sms($message,$agent_phone_number);

          // die;
          // $agent_phone_number = str_replace(' ', '', $agent_phone_number); 

          // if (substr($agent_phone_number, 0, 3) != '+94') {                
          //   $agent_phone_number = '+94' . $agent_phone_number;

          // } else {          
          //   $agent_phone_number = str_replace("+94", "0", $agent_phone_number);
          //     $cSms->send_sms($message,$agent_phone_number);
          // }
        }

      }else if($student_application_details[0]['status']==2){
        $message = "Your Application is currently in Under-Review status. We'll keep you updated on its progress.";
        $student_phone_number = $student_application_details[0]['student_mobile'];

      }else if($status_of_application=='Program Apply'){
        $message = "Your application for the ". $student_application_details[0]['program_name'] ." has been submitted successfully.";
        $student_phone_number = $student_application_details[0]['student_mobile'];

      }else if($status_of_application=='Conditional Offer'){
        $message = "Congratulations! You've received a conditional offer for ". $student_application_details[0]['program_name'] .". Please follow the instructions to meet the conditions.";
        $student_phone_number = $student_application_details[0]['student_mobile'];

      }else if($status_of_application =='Unconditional Offer'){
      $message = "Congratulations! You've received an unconditional offer for ". $student_application_details[0]['program_name'] .".";
      $student_phone_number = $student_application_details[0]['student_mobile'];
      
      }else if($student_application_details[0]['status']==7){
      $message = "Your Confirmation of Acceptance for Studies (CAS) or Letter of Offer (LOA) has been issued. Please check your email for further details.";
      $student_phone_number = $student_application_details[0]['student_mobile'];
        
      }else if($status_of_application =='VISA Granted'){
        $message = "Congratulations! Your visa has been granted. Please check your email for further instructions.";
        $student_phone_number = $student_application_details[0]['student_mobile'];
          
      }else if($status_of_application =='University Enrolled'){
        $message = "Congratulations! You've been successfully enrolled in ". $student_application_details[0]['institute_name'] .".";
        $student_phone_number = $student_application_details[0]['student_mobile'];
          
      }else if($status_of_application =='Commission'){
        $message = "Commission details have been processed. Please review the details in your account.";
        $student_phone_number = $student_application_details[0]['mobile'];
          
      }else if($status_of_application =='Refund'){
        $message = "Your refund process has been initiated. Please check your account for updates.";
        $student_phone_number = $student_application_details[0]['student_mobile'];
          
      }else if($status_of_application =='Reappeal'){
        $message = "Congratulations! Your appeal has been successful.";
        $student_phone_number = $student_application_details[0]['student_mobile'];
          
      }else if($status_of_application =='Differment'){
        $message = "Your deferment request has been approved.";
        $student_phone_number = $student_application_details[0]['student_mobile'];    

      }else if($status_of_application =='University Payment'){       
        $message = "Your university payment has been processed successfully.";
        $student_phone_number = $student_application_details[0]['student_mobile'];
        
      }else{   
        $message ="Processing of the request has been completed. Please review the status in the system.";
        $student_phone_number=$cStaff->getStaffMobileNumberById($student_application_details[0]['created_by']);         
      }     
      
      //sms to student  
        //  $student_phone_number = $student_application_details[0]['student_mobile'];
        //remove space
        // $student_phone_number = str_replace(' ', '', $student_phone_number);
        // //check if phone number has +94 in the beginning
        // if (substr($student_phone_number, 0, 3) != '+94') {                
        //   $student_phone_number = '+94' . $student_phone_number;
        // } else {          
        //   $student_phone_number = str_replace("+94", "0", $student_phone_number);
        //   $cSms->send_sms($message,$student_phone_number);
        // }

        $cSms->send_sms($message,$student_phone_number);
    }
  }

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}



if (isset($_POST['updateEstDate'])) {

  $application_log_id = filter_input(INPUT_POST, 'application_log_id');
  $due_date = filter_input(INPUT_POST, 'new_due_date');

  $criteria = array(
    "application_log_id" => $application_log_id,
    "due_date" => $due_date
  );

  $result = $cApplication->updateApplicationLog($criteria);

  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}


// update Application
if (isset($_POST['studentApplicationUpdate'])) {

  $student_application_id = filter_input(INPUT_POST, 'student_application_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $my_prefer_yn = filter_input(INPUT_POST, 'my_prefer_yn');
  $institute_id = filter_input(INPUT_POST, 'institute_id');
  $status_id = filter_input(INPUT_POST, 'status_id');
  $intake = filter_input(INPUT_POST, 'intake');
  $year = filter_input(INPUT_POST, 'year');
  $remarks = filter_input(INPUT_POST, 'remarks');
  $created_by = filter_input(INPUT_POST, 'staff_id');
  $date = filter_input(INPUT_POST, 'date');


  $program_id_str = filter_input(INPUT_POST, 'program_id_str');
  // comma separated string to array
  $program_id_arr = explode(",", $program_id_str);

  // program_id_arr to array of objects
  foreach ($program_id_arr as $key => $value) {
    $program_id = $value;

    //get program details
    $program_dts = $cProgram->getProgramByID($program_id);

    $application_fee = $program_dts[0]['application_fee'];
    $tution_fee = $program_dts[0]['tution_fee'];
    $tution_fee_due_date = '2022-07-12'; // need to change this
    $status = $status_id;

    //add to array 
    $criteria = array(
      'student_application_id' => $student_application_id,
      'student_id' => $student_id,
      'my_prefer_yn' => $my_prefer_yn,
      'institute_id' => $institute_id,
      'program_id' => $program_id,
      'intake' => $intake,
      'year' => $year,
      'remarks' => $remarks,
      'status' => $status,
      'application_fee' => $application_fee,
      'tution_fee' => $tution_fee,
      'tution_fee_due_date' => $tution_fee_due_date,
      'created_by' => $created_by,
      'date' => $date,
    );


    //check if already 4 applications for this student

    $result = $cApplication->updateStudentApplication($criteria);
  }



  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}



// save Application
if (isset($_POST['studentApplicationSave'])) {

  $student_application_id = filter_input(INPUT_POST, 'student_application_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $my_prefer_yn = filter_input(INPUT_POST, 'my_prefer_yn');
  $institute_id = filter_input(INPUT_POST, 'institute_id');
  $intake = filter_input(INPUT_POST, 'intake');
  $year = filter_input(INPUT_POST, 'year');
  $remarks = filter_input(INPUT_POST, 'remarks');
  $created_by = filter_input(INPUT_POST, 'staff_id');
  $country_code = filter_input(INPUT_POST, 'country_code');
  $user_type = filter_input(INPUT_POST, 'user_type');


  $program_id_str = filter_input(INPUT_POST, 'program_id_str');
  // comma separated string to array
  $program_id_arr = explode(",", $program_id_str);

  
  // check all the validation for st application 
  $criteria_application_check = array(
    'student_id' => $student_id
  );
 // check if already 4 applications for this student
  $result_application_check = $cApplication->getApplications($criteria_application_check);
  
  if (count($result_application_check) < 4) { 
  //check application count if greater than 4 then show error messege
    if(count($program_id_arr) > (4-count($result_application_check))){

      $able_appilication_count = (4-count($result_application_check));

      $json = array(
        "data" => '0', "status" => 'You can only add '.$able_appilication_count.' application(s)'
      );

      echo json_encode($json);
      die();

    }

  } else {

    $json = array(
      "data" => '0', "status" => 'Max 4 applications allowed'
    );

    echo json_encode($json);
    die();

  }

  // program_id_arr to array of objects
  foreach ($program_id_arr as $key => $value) {
    $program_id = $value;

    //get program details
    $program_dts = $cProgram->getProgramByID($program_id);

    $application_fee = $program_dts[0]['application_fee'];
    $tution_fee = $program_dts[0]['tution_fee'];
    $tution_fee_due_date = 'NOW()';
    $status = '0';

    //add to array 
    $criteria = array(
      'student_application_id' => $student_application_id,
      'student_id' => $student_id,
      'my_prefer_yn' => $my_prefer_yn,
      'institute_id' => $institute_id,
      'program_id' => $program_id,
      'intake' => $intake,
      'year' => $year,
      'remarks' => $remarks,
      'status' => $status,
      'application_fee' => $application_fee,
      'tution_fee' => $tution_fee,
      'tution_fee_due_date' => $tution_fee_due_date,
      'created_by' => $created_by,
      'country_code' => $country_code,
      'user_type'=>$user_type,
    );

    // if count is less than 4 then save
    $result = $cApplication->saveApplication($criteria);

  }



  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}

//update 
if (isset($_POST['applicationUpdate'])) {

  $student_application_id = filter_input(INPUT_POST, 'student_application_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $my_prefer_yn = filter_input(INPUT_POST, 'my_prefer_yn');
  $institute_id = filter_input(INPUT_POST, 'institute_id');
  $status_id = filter_input(INPUT_POST, 'status_id');
  $intake = filter_input(INPUT_POST, 'intake');
  $year = filter_input(INPUT_POST, 'year');
  $remarks = filter_input(INPUT_POST, 'remarks');
  // $created_by = filter_input(INPUT_POST, 'staff_id');
  // $date = filter_input(INPUT_POST, 'date');
  $user_type = filter_input(INPUT_POST, 'user_type');

  $program_id_str = filter_input(INPUT_POST, 'program_id_str');
  // comma separated string to array
  $program_id_arr = explode(",", $program_id_str);

  // program_id_arr to array of objects
  foreach ($program_id_arr as $key => $value) {
    $program_id = $value;

    //get program details
    $program_dts = $cProgram->getProgramByID($program_id);

    // $application_fee = $program_dts[0]['application_fee'];
    // $tution_fee = $program_dts[0]['tution_fee'];
    // $tution_fee_due_date = '2022-07-12'; // need to change this
    // $status = $status_id;

    //add to array 
    $criteria = array(
      'student_application_id' => $student_application_id,
      'student_id' => $student_id,
      'my_prefer_yn' => $my_prefer_yn,
      'institute_id' => $institute_id,
      'program_id' => $program_id,
      'intake' => $intake,
      'year' => $year,
      'remarks' => $remarks,
      'user_type'=>$user_type,
      // 'status' => $status,
      // 'application_fee' => $application_fee,
      // 'tution_fee' => $tution_fee,
      // 'tution_fee_due_date' => $tution_fee_due_date,
      // 'created_by' => $created_by,
      // 'date' => $date,
    );


   

    $result = $cApplication->updateApplication($criteria);
  }



  $json = array(
    "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
  );
  echo json_encode($json);
  die();
}




// Delete Application
if (isset($_POST['delete_application_btn'])) {
  $student_application_id = filter_input(INPUT_POST, 'student_application_id');

  $result = $cApplication->deleteApplication($student_application_id);

  $json = array(
      "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while deleting')
  );
  echo json_encode($json);
  die();
}

if (isset($_POST['studentApplicationEdit'])) {

  $student_application_id = filter_input(INPUT_POST, 'student_application_id');
  $student_id = filter_input(INPUT_POST, 'student_id');
  $my_prefer_yn = filter_input(INPUT_POST, 'my_prefer_yn');
  $institute_id = filter_input(INPUT_POST, 'institute_id');
  $intake = filter_input(INPUT_POST, 'intake');
  $year = filter_input(INPUT_POST, 'year');
  $remarks = filter_input(INPUT_POST, 'remarks');
  $created_by = filter_input(INPUT_POST, 'staff_id');
  $country_code = filter_input(INPUT_POST, 'country_code');
  $user_type = filter_input(INPUT_POST, 'user_type');


  $program_id_str = filter_input(INPUT_POST, 'program_id_str');
  // comma separated string to array
  $program_id_arr = explode(",", $program_id_str);

  // program_id_arr to array of objects
  foreach ($program_id_arr as $key => $value) {
    $program_id = $value;

    //get program details
    $program_dts = $cProgram->getProgramByID($program_id);

    $application_fee = $program_dts[0]['application_fee'];
    $tution_fee = $program_dts[0]['tution_fee'];
    $tution_fee_due_date = 'NOW()';
    $status = '0';

    //add to array 
    $criteria = array(
      'student_application_id' => $student_application_id,
      'student_id' => $student_id,
      'my_prefer_yn' => $my_prefer_yn,
      'institute_id' => $institute_id,
      'program_id' => $program_id,
      'intake' => $intake,
      'year' => $year,
      'remarks' => $remarks,
      'status' => $status,
      'application_fee' => $application_fee,
      'tution_fee' => $tution_fee,
      'tution_fee_due_date' => $tution_fee_due_date,
      'created_by' => $created_by,
      'country_code' => $country_code,
      'user_type'=>$user_type,
    );

    $result = $cApplication->saveApplication($criteria);  
    $json = array(
      "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
    
  }
}