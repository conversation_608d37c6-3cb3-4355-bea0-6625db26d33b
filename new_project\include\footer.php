<?php

$cLicence = new License();

/** License */
$criteria_re = array(
    'status_yn'=>'Y'
);
$license_details = $cLicence->getLicense($criteria_re);
$user_type = $_SESSION['user']['user_type'];

if(isset($_SESSION['license_view'])){
    $already_viewed = $_SESSION['license_view'] ;

}else{
    $already_viewed = "N" ;
}

if($user_type == 'CU' || $user_type == 'SA' ) {
}else{
if($already_viewed == 'N'){
if(isset($license_details[0])){
    if(count($license_details[0]) > 0 ){
        if($license_details[0]['status_yn'] == 'Y' ){
            $due_date = $license_details[0]['due_date'];
            $now = date('Y-m-d'); // or your date as well
            $days = (strtotime($due_date) - strtotime($now)) / (60 * 60 * 24);


            ?>
<style>
body.modal-open .content-wrapper {
    -webkit-filter: blur(4px);
    -moz-filter: blur(4px);
    -o-filter: blur(4px);
    -ms-filter: blur(4px);
    filter: blur(4px);
}
</style>
<div class="modal fade" id="modal-license" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <?php if($days > 0 ){?>
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>
            <?php } ?>
            <div class="modal-body text-center">
                <!--                            <span><i class="fa fa-info-circle text-danger" style="font-size: 45px"></i></span>-->
                <!--                            <br>-->
                <br>
                <?php if($days > 0 ){?>
                Please make payment before the due on
                <?=$due_date?>
                <!--<?=$_SESSION['license_view']?>-->
                <br>
                <?php } else{?>
                <?=$license_details[0]['message']?>
                <?php } ?>
                <br><br>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
</div>
<?php       }
        }
    }

}
}
?>

<script>
$(document).ready(function() {
    $('#modal-license').modal('show');
    $('#modal-license').modal({
        backdrop: 'static',
        keyboard: false
    })

    $('#modal-license').on('hidden.bs.modal', function(e) {
        $.ajax({
            url: 'include/change-license-view.php',
            success: function(data) {
                console.log(data);
            }
        });
        //console.log('modalcla')
    });
});
</script>
<footer class="main-footer">
    <div class="float-right d-none d-sm-block">
        <b>Version</b> 1.2
    </div>
    <strong>Copyright &copy; 2024 <a href="https://edvios.io">edvios.io</a>.</strong> All rights reserved.
</footer>