<?php

// Save messages from student and staff sides
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];
$username = $_SESSION['user']['first_name'];

$access_available_for = "AG,ST";

if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cmessages = new Messages();

if(isset($_POST['messagesSave'])) {
    $message = filter_input(INPUT_POST, 'message');
    $student_id = filter_input(INPUT_POST, 'student_id');
    $agent_id = filter_input(INPUT_POST, 'agent_id');
    $user_type = filter_input(INPUT_POST, 'user_type');

    $criteria = array(
        'message' => $message
        ,'student_id' => $student_id
        ,'user_type' => $user_type
        ,'agent_id' => $agent_id
        ,'is_read' => "N"
    );

    $result = $cmessages->saveMessagesStudentAgent($criteria);

    $response = array("data" => $result);
    echo json_encode($response);
    die();
}
