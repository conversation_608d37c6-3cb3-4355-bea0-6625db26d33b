$(function() {
    $('.select2').select2();

    var monkeyList = new List('test-list', {
        valueNames: ['name'],
        page: 100,
        pagination: true
    });

    $('.show_results').on('change', function() {
        var searchTerm = $(this).val();

        monkeyList = new List('test-list', {
            valueNames: ['name'],
            page: searchTerm,
            pagination: true
        });
    });
    // .fav_btn button on click function
    $('.fav_btn').on('click', function() {
        var program_id = $(this).data('program_id');
        // console.log({
        //     program_id
        // });
        // var fav_btn = $(this);
        $.ajax({
            type: "POST",
            url: "controller/program-controller.php",
            data: {
                "program_id": program_id,
                "updateFav": 1
            },
            dataType: 'json',
            success: function(data) {

                if (data.data == -404) {
                    toastr.warning('Remove from favorite list');
                }
                if (data.data > 0) {
                    toastr.success('Program added to your favourite list');
                }
                // console.log(data);
                window.location.reload();
            }
        });
    });


});