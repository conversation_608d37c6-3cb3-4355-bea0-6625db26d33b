<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cInstitute = new Institute();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

// get Institute  Details
if(isset($_POST['getInstituteDetails'])){
    
    $institute_id = filter_input(INPUT_POST, 'institute_id');
    $result = $cInstitute->getInstituteByID($institute_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// save Institute
if(isset($_POST['instituteSave'])){

    $institute_id = filter_input(INPUT_POST, 'institute_id');
    $institute_name = filter_input(INPUT_POST, 'institute_name');
    $institute_type = filter_input(INPUT_POST, 'institute_type');
    $institute_country_id = filter_input(INPUT_POST, 'country_id');
    $institute_state = filter_input(INPUT_POST, 'state_id');
    $institute_city = filter_input(INPUT_POST, 'city');
    $web_url = filter_input(INPUT_POST, 'web_url');
    $logo_url = filter_input(INPUT_POST, 'logo_url');
    $email = filter_input(INPUT_POST, 'email');
    $mobile = filter_input(INPUT_POST, 'mobile');


    
    if(isset($_FILES['logo_url']) && $_FILES['logo_url']['name'] != "") {
        $file_name = $_FILES['logo_url']['name'];
        $file_size = $_FILES['logo_url']['size'];
        $file_tmp = $_FILES['logo_url']['tmp_name'];
        $file_type = $_FILES['logo_url']['type'];
        
        $exp = explode('.', $_FILES['logo_url']['name']);
        $file_ext = strtolower(end($exp));

        // timestamp
        $time = time();
        
        //remove a file already exists
        if($logo_url != ""){
            if(file_exists("../".$logo_url)) {
                unlink("../".$logo_url);
            }
        }

        //upload the file
        if( move_uploaded_file($file_tmp,"../dist/uploads/institute/".$time.'.'.$file_ext)){
            $logo_url = "dist/uploads/institute/".$time.'.'.$file_ext;
        }
        
    }
    
    //add to array 
    $criteria = array(
        'institute_id' => $institute_id,
        'institute_name' => $institute_name,
        'institute_type' => $institute_type,
        'institute_state' => $institute_state,
        'institute_country_id' => $institute_country_id,
        'institute_city' => $institute_city,
        'web_url' => $web_url,
        'logo_url' => $logo_url,
        'email' => $email,
        'mobile' => $mobile
    );

    
         

    $result = $cInstitute->saveInstitute($criteria);
    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}


// delete Institute
if(isset($_POST['deleteInstitute'])){

    $institute_id = filter_input(INPUT_POST,'institute_id');
    
    $result = $cInstitute-> deleteInstitute($institute_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//Delete Commission
if(isset($_POST['deleteCommission'])){

    $commission_id = filter_input(INPUT_POST,'commission_id');
    
    $result = $cInstitute-> deleteCommission($commission_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while deleting')
    );
    echo json_encode($json);
    die();
}

//Delete All Commission
if(isset($_POST['deleteAllCommission'])){

    
    $result = $cInstitute-> deleteAllCommission();

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while deleting')
    );
    echo json_encode($json);
    die();
}

// save Institute
if(isset($_POST['commissionSave'])){

    $institute_id = filter_input(INPUT_POST, 'institute_id');
    $commission_id = filter_input(INPUT_POST, 'commission_id');
    $rate = filter_input(INPUT_POST, 'rate');
    
    //add to array 
    $criteria = array(
        'institute_id' => $institute_id,
        'commission_id' => $commission_id,
        'rate' => $rate
    );

    
    $result = $cInstitute->saveCommission($criteria);
    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

//upload list from excel file
if (isset($_POST['uploadProgramList'])) {
    $allowed_ext = ['csv', 'xls', 'xlsx'];
    $file = $_FILES['file'];
    $file_name = $_FILES['file']['name'];
    $file_size = $_FILES['file']['size'];
    $file_error = $_FILES['file']['error'];
    $file_type = $_FILES['file']['type'];

    $file_ext = explode('.', $file_name);
    $file_actual_ext = strtolower(end($file_ext));

    if (in_array($file_actual_ext, $allowed_ext)) {
        if ($file_error === 0) {
            $file_tmp_name = $_FILES['file']['tmp_name'];
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file_tmp_name);
            $data = $spreadsheet->getActiveSheet()->toArray();
            //skip first row
            array_shift($data);

            foreach ($data as $row) {
                $institute_name = $row[0];
                $institute_id = $cInstitute->getInstituteIDByName($institute_name);
                $commission_id=filter_input(INPUT_POST, 'commission_id');
                $rate = $row[1];

                $criteria = array(
                    'institute_id' => $institute_id,
                    'commission_id' => $commission_id,
                    'rate' => $rate,
                );

                $result = $cInstitute->saveCommission($criteria);
            }
            if ($result !== false) {
                $response = array(
                    'status' => 'success',
                    'data' => $result,
                );
                echo json_encode($response);
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Error inserting data into the database.',
                );
                echo json_encode($response);
            }
        } else {
            $_SESSION['error'] = "There was an error uploading your file!";
            header("Location: /global_education/new_project/commission-list.php");
            exit();
        }
    } else {
        $_SESSION['error'] = "You cannot upload files of this type!";
        header("Location: /global_education/new_project/commission-list.php");
        exit();
    }
}
