<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of user
 *
 * <AUTHOR>
 */

include_once 'connection.php';

class user extends connection {
    //put your code here
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }
    
    function user(){
        
         self::__construct();
        
    }

    private $_conn;
    
    function getLogin($email) {
        $conn = $this->_conn;
        
        $cond=" and u.email = '". mysqli_real_escape_string($conn, $email)."' ";
        $query = "select u.*, password as password1 from ggportal_tbl_user u where 1=1 $cond;";
//        echo $query;
        
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }
    
    function getUserDetails($user_id, $user_type=""){
        $conn = $this->_conn;
        $user_id = intval($user_id);
        $user_type = mysqli_real_escape_string($conn, $user_type);
        $cond="";
        if($user_type!=""){
            $cond .= " and user_type='$user_type' ";
        }
        $cond .= " and u.user_id = $user_id  ";
        $query = "select * from ggportal_tbl_user u where 1=1 $cond;";
        //echo $query;
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }
    
    function searchUser($criteria){
        $conn = $this->_conn;
        $cond="";
        if(isset($criteria['email']) && $criteria['email']!=""){
            $email=  mysqli_real_escape_string($conn,$criteria['email']);
            $cond.=" and u.email='$email' ";
        }
        if(isset($criteria['email']) && $criteria['email']!=""){
            $email=  mysqli_real_escape_string($conn,$criteria['email']);
            $cond.=" and (u.email='$email' or u.user_name='$email' ) ";
        }
        if($cond==""){
            $cond=" and 1=2 "; //dont show any
        }
        
        $query="Select * from ggportal_tbl_user u 
            where 1=1 $cond
                ;";
        $res=  mysqli_query($conn, $query);
        return db_to_array($res);
                
    }
    
    
    
    function getUserList($criteria){
        $conn = $this->_conn;
        $cond="";
        if(isset($criteria['name']) && $criteria['name']!=""){
            $name=  mysqli_real_escape_string($conn, $criteria['name']);
            $cond.=" and concat(first_name,' ',last_name) like '%$name%' ";
        }
        if(isset($criteria['user_type']) && $criteria['user_type']!=""){
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond.=" and u.user_type in ($user_type) ";
        }
        
        
        $query="select * from ggportal_tbl_user u 
                 where 1=1 $cond;";
        //echo $query;
        $res=  mysqli_query($conn, $query);
        return db_to_array($res);
    }
    
    
    function saveUser($user_details,$user_id=0){
        $conn = $this->_conn;
        $user_id = intval($user_id);
        $user_name = mysqli_real_escape_string($conn, $user_details['user_name']);
        $password = mysqli_real_escape_string($conn, $user_details['password']);
        $user_email = mysqli_real_escape_string($conn, $user_details['email']);
        $user_type = mysqli_real_escape_string($conn, $user_details['user_type']);
        $user_access_level = mysqli_real_escape_string($conn, $user_details['user_access_level']);
        $first_name = mysqli_real_escape_string($conn, $user_details['first_name']);
        $last_name = mysqli_real_escape_string($conn, $user_details['last_name']);
        $user_guid = mysqli_real_escape_string($conn, $user_details['user_guid']);
        
        $user_contact_no = mysqli_real_escape_string($conn, $user_details['user_contact_no']);
        $user_active_yn = mysqli_real_escape_string($conn, $user_details['user_active_yn']);
        $email_validated_yn = mysqli_real_escape_string($conn, $user_details['email_validated_yn']);
        $force_password_change = mysqli_real_escape_string($conn, $user_details['force_password_change']); //enabled when admin create a temp password
        
        if($user_id==0){
            $query="INSERT INTO ggportal_tbl_user SET
                user_name='$user_name'
                ,password='$password'
                ,password_salt='$password'
                ,email='$user_email'
                ,email_validated_yn='$email_validated_yn'
                ,user_active_yn='$user_active_yn'
                ,user_type='$user_type'
                ,user_access_level='$user_access_level'                
                ,first_name='$first_name'
                ,last_name='$last_name'
                ,profile_picture=''
                ,user_guid='$user_guid'                
                ,user_contact_no='$user_contact_no'
                ,force_password_change_yn='$force_password_change'
                ,date_password_changed = now()
            ;";           
            
        }else{
            //edit
            //if current user is admin , then no problem otherwise only update certain
            if($_SESSION['user']['user_type']=="RA"){
                 $query="update ggportal_tbl_user SET            
                      user_name='$user_name'
                    ,user_active_yn='$user_active_yn'                        
                    ,user_type='$user_type'
                    ,user_access_level='$user_access_level'                
                    ,first_name='$first_name'
                    ,last_name='$last_name'                                                            
                    ,user_contact_no='$user_contact_no'
                    ,force_password_change_yn='$force_password_change'    
                    ";
                    if($password!=""){
             $query.=",password='$password'
                    ,password_salt='$password'";
                    }       
             $query.=" where user_id=$user_id
                ;";
            }else{
                //user editing his/her profile
                $query="update ggportal_tbl_user SET
                    first_name='$first_name'
                    ,last_name='$last_name'                    
                    ,user_contact_no='$user_contact_no'
                    where user_id=$user_id
                ;";
            }
        }
        //echo $query;
        //die();
        $res=  mysqli_query($conn, $query);
        if($res){
            if($user_id==0){
                $user_id=  mysqli_insert_id($conn);
            }
        }else{
            $user_id=0;
        }
        return $user_id;
    }
    

    function saveUserLogin($user_id,$login_id=0){
        global $_conn;
        $user_id=intval($user_id);
        $login_id=intval($login_id);
        if($login_id>0){
            $query="update ggportal_tbl_user_login set last_activity=now() where user_login_id=$login_id;";
        }else{
            //update tbl_user and reset the login try
            $query="update ggportal_tbl_user SET last_login_date=now()
            ,login_failure_try=0
            ,login_failure_ip=''
            ,login_failure_date=null
            where user_id=$user_id;";
            //$res=mysqli_query($_conn,$query);
            
            //insert to tbl_user_login
            $query="Insert into ggportal_tbl_user_login SET
                user_id=$user_id
                ,date_login=now()
                ,ip_address='".getIP()."'
                ,last_activity=now(); ";
        }

        $res=mysqli_query($_conn,$query);
        //echo $query;
        //die();
        if($res){
            if($login_id==0){
                $login_id=mysqli_insert_id($_conn);
            }
        }else{
            $login_id=0;
        }
        return $login_id;
    }

    function saveFailureAttempt($user_name,$password){
        global $_conn;
        $user_name=mysqli_real_escape_string($_conn,$user_name);
        $password=mysqli_real_escape_string($_conn,$password);
        $query="Insert into ggportal_tbl_login_invalid SET
             username='$user_name'
            , password='$password'
            , ip_address='".getIP()."'
            , date_tried=now();";
        $res=mysqli_query($_conn,$query);

        //now update the user table;
        $query="Update ggportal_tbl_user SET
            login_failure_try=login_failure_try+1
            ,login_failure_ip='".getIP()."'
            ,login_failure_date=now()
            where username='$user_name';";
        //echo $query;
       // die();
        $res=mysqli_query($_conn,$query);

    }
    
    function saveUserPicture($user_id,$profile_picture){
        global $_conn;
        $user_id = intval($user_id);
        $query="update ggportal_tbl_user set profile_picture='$profile_picture' where user_id=$user_id;";
        $res=mysqli_query($_conn,$query);
    }
    
    function saveActivateUser($user_id){
        global $_conn;
        $user_id = intval($user_id);
        $query="update ggportal_tbl_user set user_active_yn='Y', email_validated_yn='Y' where user_id=$user_id;";
        $res=  mysqli_query($_conn, $query);
        if($res){
            return 1;
        }else{
            return 0;
        }
    }

    function getCusDetails($email) {
        // print_r($email);
        // die();
        $conn = $this->_conn;

        $cond=" and u.email = '". mysqli_real_escape_string($conn, $email)."' ";
        $query = "SELECT u.* FROM ggportal_tbl_user u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);

    }

    function saveTokenDetails($tokendetails){
        $conn = $this->_conn;

        $user_id = intval($tokendetails['user_id']);
        $token_id=mysqli_real_escape_string($conn,$tokendetails['token_id']);
        $token_created_time=mysqli_real_escape_string($conn,$tokendetails['token_created_time']);

        if ($user_id == 0) {
            $query = "INSERT INTO `ggportal_tbl_user` SET
            `token_id`='$token_id'
            ,`token_created_time`='$token_created_time'
            ";

        } else { //update
            $query = "UPDATE `ggportal_tbl_user` SET
            `token_id`='$token_id'
            ,`token_created_time`='$token_created_time'
            Where user_id = '$user_id'
            ";
        }
        //echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            if ($user_id == 0) {
                $user_id = mysqli_insert_id($conn);
            }
            return $user_id;
        } else {
            return 0;
        }

    }

    function getTokenDetails($token_id) {
        // print_r($username);
        // die();
        $conn = $this->_conn;

        $cond=" and u.token_id = '". mysqli_real_escape_string($conn, $token_id)."' ";
        $query = "SELECT u.* FROM ggportal_tbl_user u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);

    }

    function savePassDetails($passdetails){
        $conn = $this->_conn;

        $token_id=mysqli_real_escape_string($conn,$passdetails['token_id']);
        $password=mysqli_real_escape_string($conn,$passdetails['password']);

        // print_r($token_id);
        // print_r($password);
        // die();

        $query = "UPDATE `ggportal_tbl_user` SET
        `password`='$password'
        ,`token_id`=''
        ,`token_created_time`='0000-00-00 00:00:00'
        Where token_id = '$token_id'
        ";

        //echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            if ($token_id == 0) {
                $token_id = mysqli_insert_id($conn);
            }
            return $token_id;
        } else {
            return 0;
        }

    }

    function savePassDetails2($passdetails){
        $conn = $this->_conn;

        $token_id=mysqli_real_escape_string($conn,$passdetails['token_id']);

        // print_r($token_id);
        // die();

        $query = "UPDATE `ggportal_tbl_user` SET
        `token_id`=''
        ,`token_created_time`='0000-00-00 00:00:00'
        Where token_id = '$token_id'
        ";

        //echo $query;
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        if ($res) {
            if ($token_id == 0) {
                $token_id = mysqli_insert_id($conn);
            }
            return $token_id;
        } else {
            return 0;
        }

    }

    public function calAdmiOnlineStatus()
    {
        $conn = $this->_conn;

        $query = "SELECT  last_seen FROM ggportal_tbl_user";
        $res = mysqli_query($conn, $query);
        if ($res === false) {
            error_log("Query failed: " . mysqli_error($conn));
            return null;
        }
        $resultData = mysqli_fetch_assoc($res);

        return $resultData;
    }
    
}
