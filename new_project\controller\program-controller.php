<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cProgram = new Program();
$cLoadCombo = new LoadCombo();
$cCourse = new Course();
$cCountry = new Country();
$cInstitute = new Institute();




//Codeaisys#Sithum#GetDynamicUniversitylist
//Changes related to program finder start here
if (isset($_POST['action']) && $_POST['action'] === 'getInstituteCombo') {

    $country_to_apply_idPHP = filter_input(INPUT_POST, 'country_to_apply_id');
    $country_to_apply_namePHP= filter_input(INPUT_POST, 'country_to_apply_name');
    $course_typePHP = filter_input(INPUT_POST, 'course_type');
    $intake_namePHP = filter_input(INPUT_POST, 'intake_name');
    $course_namePHP = filter_input(INPUT_POST, 'course_name');
    $course_name_idPHP= filter_input(INPUT_POST, 'course_name_id');

    $comboData = $cLoadCombo->getStudyInstituteComboValueByFilter($country_to_apply_idPHP,$country_to_apply_namePHP,$course_typePHP,$intake_namePHP,$course_namePHP,$course_name_idPHP);
    // Output the data as JSON
    //header("Content-Type: application/json");
    echo json_encode($comboData);
    exit; // Terminate the script after sending the response
}
//Changes related to program finder end here

// filter and retrieve programs based on criteria
if (isset($_POST['action']) && $_POST['action'] === 'getPrograms') {

    $country_to_apply_idPHP = filter_input(INPUT_POST, 'country_to_apply_id');
    $country_to_apply_namePHP = filter_input(INPUT_POST, 'country_to_apply_name');
    $course_typePHP = filter_input(INPUT_POST, 'course_type');
    $intake_namePHP = filter_input(INPUT_POST, 'intake_name');
    $course_namePHP = filter_input(INPUT_POST, 'course_name');
    $course_name_idPHP = filter_input(INPUT_POST, 'course_name_id');
    $duration=filter_input(INPUT_POST, 'duration');

    $max_tuition_feePHP = filter_input(INPUT_POST, 'max_tuition_fee', FILTER_VALIDATE_FLOAT);
    $min_application_feePHP = filter_input(INPUT_POST, 'min_application_fee', FILTER_VALIDATE_FLOAT);
    $max_application_feePHP = filter_input(INPUT_POST, 'max_application_fee', FILTER_VALIDATE_FLOAT);

    // Assuming getFilteredPrograms is a method in your Program class that accepts these parameters and returns the filtered programs
    $programs = $cProgram->getFilteredPrograms($country_to_apply_idPHP, $country_to_apply_namePHP, $course_typePHP, $intake_namePHP, $course_namePHP, $course_name_idPHP,$duration, $max_tuition_fee = null, $min_application_fee = null, $max_application_fee = null);
    
    // Output the data as JSON
    echo json_encode($programs);
    exit; // Terminate the script after sending the response
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'getFilteredPrograms') {
    $country_to_apply_idPHP = filter_input(INPUT_POST, 'country_to_apply_id');
    $country_to_apply_namePHP = filter_input(INPUT_POST, 'country_to_apply_name');
    $course_typePHP = filter_input(INPUT_POST, 'program_type');
    $intake_namePHP = filter_input(INPUT_POST, 'intake_name');
    $institute_name = filter_input(INPUT_POST, 'institute_name');
    $course_name_idPHP = filter_input(INPUT_POST, 'course_name_id');
    $duration=filter_input(INPUT_POST, 'duration');

    $max_tuition_fee = filter_input(INPUT_POST, 'max_tuition_fee', FILTER_VALIDATE_FLOAT);
    $min_tuition_fee = filter_input(INPUT_POST, 'min_tuition_fee', FILTER_VALIDATE_FLOAT);
    $min_application_fee = filter_input(INPUT_POST, 'min_application_fee', FILTER_VALIDATE_FLOAT);
    $max_application_fee = filter_input(INPUT_POST, 'max_application_fee', FILTER_VALIDATE_FLOAT);

    // Assuming getFilteredPrograms is a method in your Program class that accepts these parameters and returns the filtered programs
    $institute_idPHP=$cInstitute->getInstituteIDByName($institute_name);

    $cprogram = new Program();   
    $programs = $cProgram->getFilteredPrograms($country_to_apply_idPHP,$course_typePHP, $intake_namePHP,
                        $institute_idPHP, $course_name_idPHP,$duration,$min_tuition_fee,$max_tuition_fee,
                        $min_application_fee,$max_application_fee);

    echo json_encode($programs);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'getAllPrograms') {
    
    $cprogram = new Program();   
    $programs = $cProgram->getAllPrograms();

    echo json_encode($programs);
    exit;
}




// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

// get course  Combo
if (isset($_POST['getProgramMultipleCombo'])) {


    $program_id = filter_input(INPUT_POST, 'program_id');
    $institute_id = filter_input(INPUT_POST, 'institute_id');

    $program_combo_dts = array(
        'id_match' => ($program_id > 0 ? $program_id : 0),
        'institute_id' => ($institute_id > 0 ? $institute_id : 0)
    );

    $result = $cLoadCombo->getProgramMultipleCombo($program_combo_dts);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}
// return single selection combo
if (isset($_POST['getProgramCombo'])) {


    $program_id = filter_input(INPUT_POST, 'program_id');
    $institute_id = filter_input(INPUT_POST, 'institute_id');

    $program_combo_dts = array(
        'id_match' => ($program_id > 0 ? $program_id : 0),
        'institute_id' => ($institute_id > 0 ? $institute_id : 0)
    );

    $result = $cLoadCombo->getProgramComboNew($program_combo_dts);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// get course  Details
if (isset($_POST['getProgramMultipleDetails'])) {

    $program_id_str = filter_input(INPUT_POST, 'program_id_str');

    $all_data = 1;
    if ($program_id_str == "") {
        $all_data = 0;
    }

    $criteria = array(
        "program_id_str" => $program_id_str,
        "all_data" => $all_data,
    );

    $result = $cProgram->getPrograms($criteria);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}

// get program Details 
if (isset($_POST['getProgramDetails'])) {

    $program_id = filter_input(INPUT_POST, 'program_id');


    $result = $cProgram->getProgramByID($program_id);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// delete Program
if (isset($_POST['deleteProgram'])) {

    $program_id = filter_input(INPUT_POST, 'program_id');

    $result = $cProgram->deleteProgram($program_id);

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

// update Program Wishlist
if (isset($_POST['updateFav'])) {
    $program_id = filter_input(INPUT_POST, 'program_id');


    //add to array 

    $criteria = array(
        'program_wishlist_id' => 0,
        'program_id' => $program_id,
        'user' => $user_type,
        'user_id' => $user_id,
    );

    //check for existing record
    $res = $cProgram->getProgramWhishlist($criteria);

    if (count($res) > 0) {

        $criteria = array(
            'program_wishlist_id' => $res[0]['program_wishlist_id'],
            'program_id' => $program_id,
            'user' => $user_type,
            'user_id' => $user_id,
        );
        $result = $cProgram->updateProgramWishlist($criteria);


        $json = array(
            "data" => -404, "status" => 'Already in Favorite'
        );
        echo json_encode($json);
        die();
    } else {

        $result = $cProgram->updateProgramWishlist($criteria);

        $json = array(
            "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
        );
        echo json_encode($json);
        die();
    }
}

// save Program
if (isset($_POST['programSave'])) {

    $program_id = filter_input(INPUT_POST, 'program_id');
    $country_id = filter_input(INPUT_POST, 'country_id');
    $city = filter_input(INPUT_POST, 'city');
    $course_id = filter_input(INPUT_POST, 'course_id');
    $institute_id = filter_input(INPUT_POST, 'institute_id');
    $deadline = filter_input(INPUT_POST, 'deadline');
    $deadline2 = filter_input(INPUT_POST, 'deadline2');
    $deadline3 = filter_input(INPUT_POST, 'deadline3');
    $deadline4 = filter_input(INPUT_POST, 'deadline4');
    $currency_id = filter_input(INPUT_POST, 'currency_id');
    $program_name = filter_input(INPUT_POST, 'program_name');
    $commission = filter_input(INPUT_POST, 'commission');
    $tution_fee = filter_input(INPUT_POST, 'tution_fee');
    $application_fee = filter_input(INPUT_POST, 'application_fee');
    $duration = filter_input(INPUT_POST, 'duration');
    $intake = filter_input(INPUT_POST, 'intake');
    $intake2 = filter_input(INPUT_POST, 'intake2');
    $intake3 = filter_input(INPUT_POST, 'intake3');
    $intake4 = filter_input(INPUT_POST, 'intake4');
    $ets = filter_input(INPUT_POST, 'ets');
    $requirements = filter_input(INPUT_POST, 'requirements');
    $english_requirements = filter_input(INPUT_POST, 'english_requirements');
    $program_web_url = filter_input(INPUT_POST, 'program_web_url');
    $course_type = filter_input(INPUT_POST, 'course_type');

    //add to array 

    $criteria = array(
        'program_id' => $program_id,
        'country_id' => $country_id,
        'course_id' => $course_id,
        'city' => $city,
        'institute_id' => $institute_id,
        'deadline' => $deadline,
        'currency_id' => $currency_id,
        'program_name' => $program_name,
        'commission' => $commission,
        'tution_fee' => $tution_fee,
        'application_fee' => $application_fee,
        'duration' => $duration,
        'intake' => $intake,
        'ets' => $ets,
        'requirements' => $requirements,
        'english_requirements' => $english_requirements,
        'program_web_url' => $program_web_url,
        'course_type' => $course_type,
        'deadline2' => $deadline2,
        'deadline3' => $deadline3,
        'deadline4' => $deadline4,
        'intake2' => $intake2,
        'intake3' => $intake3,
        'intake4' => $intake4
    );


    $result = $cProgram->saveProgram($criteria);
    // $json = array(
    //     "data" =>$_POST
    //  );
    //  echo json_encode($json);
    //  die();

    $json = array(
        "data" => $result, "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}

if (isset($_POST['download_btn'])) {    
    // Get program data
    // $fileName = "program-list-" . time();
    $data = $cProgram->getProgramData();   
    // Check if there is data to export
    if ($data !== null) {
        // Step 2: Create Excel File
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        
        $sheet->setCellValue('A1','program_id');
        $sheet->setCellValue('B1','program_name');
        $sheet->setCellValue('C1','course_type');
        $sheet->setCellValue('D1','course_name');
        $sheet->setCellValue('E1','country_name');
        $sheet->setCellValue('F1','city');
        $sheet->setCellValue('G1','institute_name');
        $sheet->setCellValue('H1','deadline');
        $sheet->setCellValue('I1','currency_name');
        $sheet->setCellValue('J1','commission');
        $sheet->setCellValue('K1','tution_fee');
        $sheet->setCellValue('L1','application_fee');
        $sheet->setCellValue('M1','intake');
        $sheet->setCellValue('N1','duration');
        $sheet->setCellValue('O1','ets');
        $sheet->setCellValue('P1','requirements');
        $sheet->setCellValue('Q1','english_requirements');
        $sheet->setCellValue('R1','program_web_url');
        $sheet->setCellValue('S1','intake2');
        $sheet->setCellValue('T1','deadline2');
        $sheet->setCellValue('U1','intake3');
        $sheet->setCellValue('V1','deadline3');
        $sheet->setCellValue('W1','intake4');
        $sheet->setCellValue('X1','deadline4');
       

       
        $row = 2;
        foreach ($data as $rowData) {
            $course_name = $cCourse->getCourseNameById($rowData['course_id']);           
            $country_name = $cCountry-> getCountryNameByID($rowData['country_id']);
            $institute_name = $cInstitute->  getInstituteNameById($rowData['institute_id']);
            
            $curency_name = $cLoadCombo->  getCurrencyNameById($rowData['currency_id']);

            $sheet->setCellValue('A' . $row,$rowData['program_id']);
            $sheet->setCellValue('B' . $row,$rowData['program_name']);
            $sheet->setCellValue('C' . $row,$rowData['course_type']);
            $sheet->setCellValue('D' . $row,$course_name);
            $sheet->setCellValue('E' . $row,$country_name);
            $sheet->setCellValue('F' . $row,$rowData['city']);
            $sheet->setCellValue('G' . $row,$institute_name);
            $sheet->setCellValue('H' . $row,$rowData['deadline']);
            $sheet->setCellValue('I' . $row,$curency_name);
            $sheet->setCellValue('J' . $row,$rowData['commission']);
            $sheet->setCellValue('K' . $row,$rowData['tution_fee']);
            $sheet->setCellValue('L' . $row,$rowData['application_fee']);
            $sheet->setCellValue('M' . $row,$rowData['intake']);
            $sheet->setCellValue('N' . $row,$rowData['duration']);
            $sheet->setCellValue('O' . $row,$rowData['ets']);
            $sheet->setCellValue('P' . $row,$rowData['requirements']);
            $sheet->setCellValue('Q' . $row,$rowData['english_requirements']);
            $sheet->setCellValue('R' . $row,$rowData['program_web_url']);
            $sheet->setCellValue('S' . $row,$rowData['intake2']);
            $sheet->setCellValue('T' . $row,$rowData['deadline2']);
            $sheet->setCellValue('U' . $row,$rowData['intake3']);
            $sheet->setCellValue('V' . $row,$rowData['deadline3']);
            $sheet->setCellValue('W' . $row,$rowData['intake4']);
            $sheet->setCellValue('X' . $row,$rowData['deadline4']);
            $row++;
        }

        // Create a writer object
        $writer = new Xlsx($spreadsheet);
        $final_fileName = 'programList.xlsx';

        // Step 3: Offer Download
        // Set headers for download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="'.urlencode($final_fileName).'"');
        header('Cache-Control: max-age=0');

        // Write the Excel file to the output buffer
        $writer->save('php://output');
        
        // Stop script execution
        exit();
    } else {
        // No data found, display an error message or perform any other action
        echo "No data found to export.";
    }
}


//upload program list from excel file
if (isset($_POST['uploadProgramList'])) {
    $allowed_ext = ['csv', 'xls', 'xlsx'];
    $file = $_FILES['file'];
    $file_name = $_FILES['file']['name'];
    $file_size = $_FILES['file']['size'];
    $file_error = $_FILES['file']['error'];
    $file_type = $_FILES['file']['type'];

    $file_ext = explode('.', $file_name);
    $file_actual_ext = strtolower(end($file_ext));

    if (in_array($file_actual_ext, $allowed_ext)) {
        if ($file_error === 0) {
            $file_tmp_name = $_FILES['file']['tmp_name'];
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file_tmp_name);
            $data = $spreadsheet->getActiveSheet()->toArray();
            //skip first row
            array_shift($data);

            foreach ($data as $row) {
                $program_id=$row[0];
                $program_name = $row[1];
                $course_type = $row[2];
                $course_name = $row[3];
                $course_id = $cCourse->getCourseIDByName($course_name);
                $country_name = $row[4];
                $country_id = $cCountry->getCountryIDByName($country_name);
                $city = $row[5];
                $institute_name = $row[6];
                $institute_id = $cInstitute->getInstituteIDByName($institute_name);
                $deadline = date('Y-m-d', strtotime($row[7]));
                $currency_code = $row[8];
                $currency_id = $cLoadCombo->getCurrencyName($currency_code);
                $commission = $row[9];
                $tution_fee = $row[10];
                $application_fee = $row[11];
                $intake = $row[12];
                $duration = $row[13];
                $ets = $row[14];
                $requirements = $row[15];
                $english_requirements = $row[16];
                $program_web_url = $row[17];
                $intake2 = $row[18];
                $deadline2 = date('Y-m-d', strtotime($row[19]));
                $intake3 = $row[20];
                $deadline3 = date('Y-m-d', strtotime($row[21]));
                $intake4 = $row[22];
                $deadline4 = date('Y-m-d', strtotime($row[23]));

                $criteria = array(
                    'program_id' => $program_id,
                    'program_name' => $program_name,
                    'course_type' => $course_type,
                    'course_name'=>$course_name,
                    'course_id' => $course_id,
                    'country_id' => $country_id,
                    'city' => $city,
                    'institute_id' => $institute_id,
                    'deadline' => $deadline,
                    'currency_id' => $currency_id,
                    'commission' => $commission,
                    'tution_fee' => $tution_fee,
                    'application_fee' => $application_fee,
                    'intake' => $intake,
                    'duration' => $duration,
                    'ets' => $ets,
                    'requirements' => $requirements,
                    'english_requirements' => $english_requirements,
                    'program_web_url' => $program_web_url,
                    'intake2' => $intake2,
                    'deadline2' => $deadline2,
                    'intake3' => $intake3,
                    'deadline3' => $deadline3,
                    'intake4' => $intake4,
                    'deadline4' => $deadline4
                );
                 // Check if the program already exists in the database
                 $check_result = $cProgram->checkProgramExists($program_id);
                 if($country_id==null || $institute_id==null || $currency_id==null){

                 }
               
                 else if (($check_result) > 0) {                    
                    //  $result = $cProgram->insertOrUpdateProgram($criteria);
                    $result = $cProgram->updateProgram($program_id,$course_id,$institute_id, $criteria);
                 } else {
                     // Program does not exist, insert a new record
                    //  $result = $cProgram->insertOrUpdateProgram($criteria);
                    $result = $cProgram->insertPrograms($criteria);
                 }          
 
                

            }
            if ($result !== false) {
                $response = array(
                    'status' => 'success',
                    'data' => $result,
                );
                echo json_encode($response);
            } else {
                $response = array(
                    'status' => 'error',
                    'message' => 'Error inserting data into the database.',
                );
                echo json_encode($response);
            }
        } else {
            $_SESSION['error'] = "There was an error uploading your file!";
            header("Location: /global_education/new_project/program-list.php");
            exit();
        }
    } else {
        $_SESSION['error'] = "You cannot upload files of this type!";
        header("Location: /global_education/new_project/program-list.php");
        exit();
    }
}
