<?php
/**
 * Copyright (c) 2021.  @aashif
 */

session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/config-ggportal.php';
include_once $include_path . 'common_functions.php';
require_once $include_path . "email_functions.php";
include_once $class_path . 'class.student.php';
include '../form-submit-email.php';

$redirect_url = filter_input(INPUT_POST, "redirect", FILTER_SANITIZE_URL); //after loggedin, redirect.
if ($redirect_url == "") {
  $redirect_url = "../dash-student.php";
}

//echo "redirect=".$redirect_url.";";
$login_session = (isset($_SESSION['user']['login_session']) ? $_SESSION['user']['login_session'] : "");
unset($_SESSION['user']['login_session']); //regenerate in login.php
unset($_SESSION['user']);


$session_variable = filter_input(INPUT_POST, "session_variable", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$username = filter_input(INPUT_POST, "username", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$password = filter_input(INPUT_POST, "password", FILTER_SANITIZE_SPECIAL_CHARS); //session variable from login session
$user_type = filter_input(INPUT_POST, "user_type", FILTER_SANITIZE_SPECIAL_CHARS);
// print_r($username);
// print_r($password);
// print_r($user_type);
// print_r($redirect_url);
// die();
$profile_picture = "";

if ($username != "" && $password != "") {
  $cuser = new Student();
  $user_details = $cuser->getLogin($username);
  
  $userId = $user_details[0]['student_id'];
  //     print_r($user_details);
//     die();
  if (isset($user_details[0]['student_id']) && $user_details[0]['student_id'] > 0) {
    $student_id = $user_details[0]['student_id'];
    //check if password correct
    $pass_correct = $user_details[0]['password'];

    $password = convert_string('encrypt', $password); //encrypt the password
    echo $password;
    if ($password == $pass_correct) {
      //check if user active        
      $student_active_yn = strtoupper($user_details[0]['user_active_yn']);
      //check if email active
//            $email_validated_yn = strtoupper($user_details[0]['email_validated_yn']);
      // if($email_validated_yn=="Y"){
      if ($student_active_yn == "Y") {
        extract($user_details[0]); //all fields to variables
        $user_login_id = 0; //$cuser->saveStudentLogin($user_details[0]['student_id']);
        switch ($user_type) {
          case "RA":
            $user_type_lable = "Admin";
            break;
          case "CU":
            $user_type_lable = "Member";
            break;
          default:
            $user_type_lable = "Please Login";
        }
        if ($profile_picture == "") {
          $profile_picture = "dist/img/avatar5.png";
        }
        $_SESSION['user'] = array(
          'user_id' => $student_id
          ,
          'user_login_id' => $user_login_id
          ,
          'user_name' => $email
          ,
          'email' => $email
          ,
          'first_name' => $first_name
          ,
          "last_name" => $last_name
          ,
          "user_contact_no" => ''
          ,
          "last_login" => date("Y-m-d H:i")
          ,
          "user_type" => $user_type //RA - Rubiks admin, AM = Ambassador, SA - School admin
          ,
          "user_type_label" => $user_type_lable
          ,
          "user_photo" => $profile_picture
          ,
          "payout_type" => $user_details[0]['payout_type']
          ,
          'current_session_key' => CURRENT_SESSION_KEY //session key, used if require force logout of all users
        );


        //header("location:../$redirect_url");

        setcookie("student_mail", $email, time() + 60 * 60 * 24 * 7, '/');
        setcookie("student_password", $password, time() + 60 * 60 * 24 * 7, '/');
        header("location:$redirect_url");
        die();
      } else {
        //Resend the verification email
        $token_id = uniqid();
        $address = $username;
        $subject = 'Activate your account';
        $body = '<!DOCTYPE html>
            <html>
            
            <head>
                <meta http-equiv="X-UA-Compatible" content="IE=edge">
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
            
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/brands.min.css"  type="text/css"/>
                <style>
                    .header {
                        background-color: #293C4A;
                        padding: 20px;
                        font-size: 25px;
                        text-align: center;
                        font-weight: bolder;
                    }
            
                    .header h2 {
                        color: #fff;
                        margin-bottom: 0px;
                    }
            
             
            
                    .header p {
                        padding-top: 0px;
                        margin-top: 0px;
                        color: #fff;
                        font-size: 16px;
                    }
            
                
                </style>
            </head>
            
            <body>
            <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
            <div style="padding:0!important;margin:0!important;display:block!important;min-width:100%!important;width:100%!important;background:#f4f4f4">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f4f4f4">
              <tbody><tr>
                <td align="center" valign="top">
                  <table width="650" border="0" cellspacing="0" cellpadding="0">
                    <tbody><tr>
                      <td style="width:650px;min-width:650px;font-size:0pt;line-height:0pt;margin:0;font-weight:normal;padding:0px 0px 30px 0px">
                        
                          <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#1b2f3e" style="text-align:center">
                                          
                              <tbody><tr>
                                  <td class="header">
                                      <h2 style="font-weight: 700;">Edvios</h2>
                                      <p style="padding-top: 30px;">The Student Portal</p>
                                  </td>
                              </tr>
                          </tbody></table>
                        
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr>
                            <td>
                              <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                <tbody><tr>
                                  <td style="padding:15px 20px 0px 20px;background:#f9f9f9">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr>
                                        <td style="font-size:18px;line-height:24px;color:#000000;text-align:left;font-weight:bold">
                                          Hi ' . $last_name . '</td>
                                      </tr>
                                      
                                    </tbody></table>
                                  </td>
                                </tr>
                              </tbody></table>
                            </td>
                          </tr>
                        </tbody></table>
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tbody><tr>
                                      <td>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f9f9f9">
                                          <tbody><tr>
                                            <td style="padding:0px 20px">
                                              <table width="100%" height="auto" bgcolor="#f9f9f9" border="0" cellspacing="0" cellpadding="0">
                                                <tbody><tr height="5px">
                                                  <td width="100%">
                                                      &nbsp;                              
                                                  </td>
                                                </tr>
                                                
                                                <tr height="50px">
                                                   <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                                    <p> You have been registered to the Edvios. Please click on the link below to verify your account.</p>                             
                                                  </td>                             
                                                </tr>
                                                  
                                                <tr height="50px">
                                                   <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                                   <a class="primary-btn" href="' . $base_url . 'student-activate-account.php?token_id=' . $token_id . '&st=' . $userId . '">Verify Account</a>                            
                                                  </td>                             
                                                </tr>
            
                                                <tr height="50px">
                                                <td width="100%" style="color:#000000;text-align:left;font-weight:500;font-size:14px;line-height:20px">                             
                                                If you did not register to the Edvios, please ignore this email.                            
                                               </td>                             
                                             </tr>
                                                <tr>
                                                  <td width="100%" style="color:#000000;text-align:left;font-weight:bold;font-size:14px;line-height:22px">                              
                                                    <p>Thank you.<br> Kind Regards,<br> Edvios.io</p>
                                                  </td>                             
                                                </tr> 
                                                    <tr height="15px">
                                                        <td width="100%">&nbsp;</td>
                                                    </tr>
                                                                     
                                                                           
                                              </tbody></table>
                                            </td>
                                          </tr>
                                        </tbody></table>
                                      </td>
                                    </tr>
                                  </tbody></table>
            
                        
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                          <tbody><tr>
                            <td style="padding:10px 30px" bgcolor="#1b2f3e">
                              <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody><tr>
                                  <td align="center" style="padding-bottom:10px">
                                    <table border="0" cellspacing="0" cellpadding="0">
                                      <tbody><tr><td colspan=" 4" height="20px">&nbsp;</td></tr>
                                      <tr>
                                        <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                          <a href="https://www.facebook.com/globalguidancelk"><img style="max-width:30px;height:auto" src="https://ci4.googleusercontent.com/proxy/_uzETlfK02JqE1U8swHt6PbWZKvWmfkjnjH5-LhpdZKKYsfQObqfiBhKm9vRRbhI8fqr6-D6dEdA9ypdk_q73REkNbPBz7iUYuLnReTd=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/facebook-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                        <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                          <a href="https://www.twitter.com/globalguidance_"><img style="max-width:30px;height:auto" src="https://ci6.googleusercontent.com/proxy/3BDtH0NxTOhKu2S0VWDYkINHBsxjvEaJg2qRV0JNz0UsdYJgpdm7SGBgcRPx4GyuymL76TzfVXE6inJdRZ54KIle59u8LG-JVy2CV1s=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/twitter-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                        
                                        <td width="40" style="font-size:0pt;line-height:0pt;text-align:left">
                                          <a href="https://www.linkedin.com/company/global-guidance"><img style="max-width:30px;height:auto" src="https://ci3.googleusercontent.com/proxy/Gyg4e-Vluua55EblssT5hYGehsKZwSovnmMk8xT37Q2xpLVFi3F-yJqo_4f2w7nkjPJbchcr_HF0UZ-mtjdiMJ7W20NdJTSSY2zx4BET=s0-d-e1-ft#https://app.dfavo.com/assets/email_images/linkedIn-icon.png" width="30" height="30" border="0" alt="" class="CToWUd" data-bit="iit"></a></td>
                                      </tr>							
                                    </tbody></table>
                                  </td>
                                </tr>
                                  
                              
                              </tbody></table>
                              
                            </td>
                          </tr>
                        </tbody></table>
                        
                        <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-top:#3f5667 1px solid">
                          <tbody><tr>
                            <td style="padding:10px 30px" bgcolor="#1b2f3e">
                              <table width="100%" border="0" cellspacing="0" cellpadding="0">   
                                  <tbody><tr>
                                  <td style="color:#ffffff;font-size:12px;line-height:26px;text-align:center">
                                      Copyright &copy; 2022   <a href="https://www.edvios.io" target="_blank">Edvios</a>. All rights reserved.</td>
                                </tr>
                               
                              
                              </tbody></table>
                              
                            </td>
                          </tr>
                        </tbody></table>
                        
                      </td>
                    </tr>
                  </tbody></table>
                </td>
              </tr>
            </tbody></table>
            <span style="opacity: 0"> ' . date("Y-m-d H:i:s") . '</span>
            
            </body>
            
            </html>';
        $errorMsg = "Your email is not validated! Please validate your email by clicking on the link found in your activation email. We have re-send the activation email for your convenience. Please do check in your Junk/Spam folder as well.";
        send_mail($subject, $address, $body);
        errorRedirect($errorMsg, "../login.php");
      }
      // }else{
      //     emailValidationEmail($user_id);
      //     errorRedirect("Your email is not validated! Please validate your email by clicking on the link found in your activation email. We have re-send the activation email for your convenience. Please do check in your Junk/Spam folder as well.","../login.php");
      // }
    } else {
      errorRedirect("Invalid Username/Password.", "../login.php");
    }
  } else {
    errorRedirect("Invalid Login.", "../login.php");
  }
} else {
  header("location:../login.php");
  die();
}
function clearUser()
{
  $_SESSION['user'] = array(
    'student_id' => 0
    ,
    'user_login_id' => 0
    ,
    'name' => ""
    ,
    'email' => ""
    ,
    "last_login" => date("Y-m-d H:i")
    ,
    "user_access_level" => ""
    ,
    "user_type" => ""
    ,
    "user_type_label" => ""
    ,
    "user_photo" => ""
  );
}
?>