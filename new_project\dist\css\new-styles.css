body {
  font-family: "Poppins", sans-serif !important;
}

/* <PERSON><PERSON> */
.newLoginCard {
  border-top: none !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(13, 6, 112, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
}
.newCardHeader {
  border-bottom: none !important;
}
.newCardBody {
  padding-top: 0px !important;
}
.newCardTitle {
  color: #95BC2E !important;
}
.newFormControl {
  text-align: center !important;
  background: transparent !important;
  border-radius: 8px !important;
  border: 1px #d1d1d1 solid !important;
  width: 100% !important;
  box-shadow: 0 0 5px #d1d1d1;
}
.newFormControl:focus{
  border: 1px #95BC2E solid !important;
  box-shadow: 0 0 5px #95BC2E;
}
.newActionButton {
  border-radius: 8px !important;
  padding: 8px 20px !important;
  background-color: #1C82E5 !important;
  margin-top: 0px !important;
  font-weight: bold !important;
  border: 2px solid #1C82E5 !important;
  color: #fff !important;
}

.newActionButton:hover {
  color: #1C82E5 !important;
  background-color: #fff !important;
  border: 2px solid #1C82E5 !important;
}

.newContent {
  height: 100vh !important;
}
/* Admin Login Ends */

/* ---------------------------------------------------------------------- */
/* Admin Dashboard Begins */
.dashboardWrapper {
  padding-right: 5% !important;
  padding-left: 5% !important;
}
.pageTitle {
  font-size: 32px !important;
}
.newCard {
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(13, 6, 112, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  margin-bottom: 40px !important;
  padding-bottom: 20px !important;
}
.newCardTitle {
  margin-top: 10px;
  font-size: 18px !important;
  text-align: center !important;
  width: 100% !important;
  text-transform: uppercase !important;
  color: #fff !important;
}
.newCardTitle b{
  color: #95BC2E !important;
}
.newInfoBox {
  max-width: 250px !important;
  height: 250px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(13, 6, 112, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  background: #fafcff !important;
}
.newInfoBoxNumber {
  font-size: 80px !important;
  text-align: center;
  color: #95BC2E !important;
}
.newInfoBoxText {
  text-align: center;
}
.newTable > thead > tr > th {
  font-size: 14px;
  text-align: center;
}

.newTable > tbody > tr > td {
  text-align: center !important;
}

.newDropdownFilter {
  border-radius: 12px !important;
  padding: 0px 20px;
  background-color: #95BC2E;
  color: white !important;
  width: 140px !important;
  border: none !important;
}
/* Admin Dashboard Ends */

/* -------------------------------------------------------------- */
/* Program Begins */
.programForm {
  border-radius: 20px !important;
  padding: 20px !important;
  box-shadow: 0 4px 16px rgba(13, 6, 112, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
  background: rgb(248, 248, 255) !important;
}
/* Program Ends */

/* ------------------------------------------------------------------ */
/* Training List Begins*/
.sheduledBtn {
  background: rgb(202, 255, 202) !important;
  color: black !important;
  font-weight: 500 !important;
  border-radius: 12px !important;
  padding: 3px 12px !important;
}

/* Training List Ends */

/* --------------------------------------------------------------------- */
/* Student List Begins */
.notAssignedBadge {
  background: rgb(218, 218, 218) !important;
  color: red !important;
  font-weight: 500 !important;
  border-radius: 12px !important;
  padding: 3px 12px !important;
}
.chatButton {
  background: rgb(157, 255, 165) !important;
  color: green !important;
  font-weight: 500 !important;
  border-radius: 12px !important;
  padding: 3px 12px !important;
  border: none;
}
.assignToButton {
  background: rgb(245, 255, 157) !important;
  color: black !important;
  font-weight: 500 !important;
  border-radius: 12px !important;
  padding: 3px 12px !important;
  border: none;
}
/* Student List Ends */

/* ------------------------------------------------------------------*/
/* Student Profile Begins */
.profileNameDiv {
  border: none !important;
  box-shadow: none !important;
}
.profileDiv {
  max-width: 1200px;
}
/* Student Profile Ends */
.newFormControlStudent {
  text-align: start !important;
  background: transparent !important;
  border-radius: 0px !important;
  border: none !important;
  border-bottom: 1px #d1d1d1 solid !important;
  width: 100% !important;
  margin-top: -10px !important;
}
/* ------------------------------------------------------------------- */
.bg-color1 {
  background: #ffa4a4 !important;
}

body:not(.layout-fixed) .main-sidebar {
  background: #fff !important;
}

.ctm-brand-link:hover{
  color: #1C82E5;
  font-weight: bold;
} 