<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA,AG,ST,SF";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cCountry = new Country();
$cLoadCombo = new LoadCombo();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();

if(isset($_POST['getAllCountryCombo'])){
    
    
    $country_id = filter_input(INPUT_POST, 'country_id');
    $name = filter_input(INPUT_POST, 'name');

    $combo_dts = array(
        'id_match' => ( $country_id > 0 ? $country_id : 0 )
    );

    $result = $cLoadCombo->getAllCountryCombo($combo_dts,$name);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Retriving')
    );
    echo json_encode($json);
    die();
}


// delete Country
if(isset($_POST['deleteCountry'])){

    $country_id = filter_input(INPUT_POST,'country_id');
    
    $result = $cCountry-> deleteCountry($country_id);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}



// save Country
if(isset($_POST['countrySave'])){
    $country_id = filter_input(INPUT_POST, 'country_id');
    $country_name = filter_input(INPUT_POST, 'country_name');
    $country_code = filter_input(INPUT_POST, 'country_code');

    // Check if the country name already exists
    $existingCountryName = $cCountry->getCountryIDByName($country_name);
    if ($existingCountryName && $existingCountryName['country_id'] != $country_id) {
        // $json = array(
        //     "message" => "Country Name already exists"
        // );
        $_SESSION['message_toast'] = 'Saved Successfully.';
        echo json_encode($json);
        die();
    }

    // Check if the country code already exists
    $existingCountryCode = $cCountry->getCountryIDByName($country_code);
    if ($existingCountryCode && $existingCountryCode['country_id'] != $country_id) {
        // $json = array(
        //     "message" => "Country Code already exists"
        // );
        echo json_encode($json);
        die();
    }

    // If no duplicates found, proceed to save the country
    $criteria = array(
        'country_id' => $country_id,
        'country_name' => $country_name,
        'country_code' => $country_code,
    );

    $result = $cCountry->saveCountry($criteria);

    // if($result > 0){
    //     $_SESSION['message_toast'] = 'Saved Successfully.';
    // }
    
    $json = array(
        "data" => $result,
        "status" => ($result > 0 ? 'Success' : 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}
