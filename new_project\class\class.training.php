<?php

include_once 'connection.php';

class Training extends connection
{

    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Training()
    {
        self::__construct();
    }

    private $_conn;


    function getTrainingByID($training_request_id)
    {
        $conn = $this->_conn;
        $training_request_id = intval($training_request_id);
        if ($training_request_id > 0) {
            $cond = "and c.training_request_id=$training_request_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    FROM ggportal_tbl_training_request c
                    WHERE 1=1 $cond;";
        //        echo $query;
        //        die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    function getTrainings($criteria)
    {
        $conn = $this->_conn;
        $cond = "";


        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $cond .= " and s.user_type = '$user_type' ";
        }

        if (isset($criteria['active_yn']) && strlen($criteria['active_yn']) > 0) {
            $active_yn = mysqli_real_escape_string($conn, $criteria['active_yn']);
            $cond .= " and s.active_yn = '$active_yn' ";
        }

        if (isset($criteria['today_date']) && strlen($criteria['today_date']) > 0) {
            $today_date = mysqli_real_escape_string($conn, $criteria['today_date']);
            $cond .= " and s.training_date <= '$today_date' ";
        }


        if (isset($criteria['user_id']) && strlen($criteria['user_id']) > 0) {
            $user_id = intval($criteria['user_id']);
            $cond .= " and s.user_id  = $user_id ";
        }

        $query = "select s.*,concat(u.first_name,' ',u.last_name) as name from ggportal_tbl_training_request s
        left join ggportal_tbl_agent u on u.agent_id=s.user_id
         where 1=1  $cond order by s.active_yn DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }



    function saveTraining($_details)
    {
        $conn = $this->_conn;
        $training_request_id = intval($_details['training_request_id']);


        if ($training_request_id == 0) {

            $query = "INSERT INTO ggportal_tbl_training_request SET
                  training_type = '" . mysqli_real_escape_string($conn, $_details['training_type']) . "'              
                    ,user_id = " . mysqli_real_escape_string($conn, $_details['user_id']) . "          
                    ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                    ,perfer_date_1 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_1']) . "'  
                    ,perfer_date_2 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_2']) . "'  
                    ,perfer_date_3 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_3']) . "'  
                    ,send_yn = '" . mysqli_real_escape_string($conn, $_details['send_yn']) . "'     
                    ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'            
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_training_request SET     
                     training_type = '" . mysqli_real_escape_string($conn, $_details['training_type']) . "'              
                    ,user_id = " . mysqli_real_escape_string($conn, $_details['user_id']) . "          
                    ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                    ,perfer_date_1 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_1']) . "'  
                    ,perfer_date_2 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_2']) . "'  
                    ,perfer_date_3 = '" . mysqli_real_escape_string($conn, $_details['perfer_date_3']) . "'  
                    ,send_yn = '" . mysqli_real_escape_string($conn, $_details['send_yn']) . "'     
                    ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'    
            WHERE training_request_id = $training_request_id;        
            ";
        }

        // return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($training_request_id == 0) {
                $training_request_id = mysqli_insert_id($conn);
            }
            return $training_request_id;
        } else {
            return mysqli_error($conn);
        }
    }

    //update training
    function updateTraining($_details)
    {
        $conn = $this->_conn;
        $training_request_id = intval($_details['training_request_id']);
        $query = "update ggportal_tbl_training_request SET      
        active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'  
            WHERE training_request_id = $training_request_id;        
            ";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $training_request_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function updateTrainingLink($_details)
    {
        $conn = $this->_conn;
        $training_request_id = intval($_details['training_request_id']);
        $query = "update ggportal_tbl_training_request SET      
         selected_date = '" . mysqli_real_escape_string($conn, $_details['selected_date']) . "' 
        ,meeting_link = '" . mysqli_real_escape_string($conn, $_details['meeting_link']) . "'  
            WHERE training_request_id = $training_request_id;        
            ";
        $res = mysqli_query($conn, $query);
        if ($res) {
            return $training_request_id;
        } else {
            return mysqli_error($conn);
        }
    }


    function deleteTraining($training_request_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_training_request` WHERE `training_request_id` = $training_request_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $training_request_id;
        } else {
            return mysqli_error($conn);
        }
    }
}
