$(document).ready(function () {

    //console.log("ready!");
      bsCustomFileInput.init();
      //Initialize Select2 Elements
      $('.select2bs4').select2({
            theme: 'bootstrap4'
      });

    $('.select2bs4').each(function(index) {
        if (!$(this).attr('id')) {
            $(this).attr('id', 'select2bs4-' + index);
        }
    });

    // Capture initial values from the select elements
    var initialValues = {};
    $('.select2bs4').each(function() {
        var id = $(this).attr('id');
        var initialValue = $(this).val();
        initialValues[id] = initialValue;
    });

    // Handle reset button click
    $('#reset-button').click(function() {
        $('.select2bs4').each(function() {
            var id = $(this).attr('id');
            var initialValue = initialValues[id];
            $(this).val(initialValue).trigger('change');
        });
        $('#quickform').validate().resetForm();  
    });
        /*Save training*/
      $('#quickform').validate({
          rules: {
            title: {
                  required: true,
              },
              perfer_date_1: {
                required: true,
            },
              agent_id: {
                  required: true,
              },
              perfer_date_2: {
                  required: true,
              },
              perfer_date_3: {
                required: true,
            },
            send_yn: {
                required: true,
            },
            active_yn: {
                required: true,
            },
            training_type: {
                required: true,
            },
          },
          messages: {

            title: {
                  required: "Please Enter a value",
              },
              perfer_date_1: {
                required: "Please Enter a value",
            },
              perfer_date_2: {
                  required: "Please Enter a value",
              },
              perfer_date_3: {
                required: "Please Enter a value",
            },
            send_yn: {
                required: "Please Enter a value",
            },
            active_yn: {
                required: "Please Enter a value",
            },
            training_type: {
                required: "Please Enter a value",
            },
          },
          errorElement: 'span',
          errorPlacement: function (error, element) {
              error.addClass('invalid-feedback');
              element.closest('.form-group').append(error);
          },
          highlight: function (element, errorClass, validClass) {
              $(element).addClass('is-invalid');
          },
          unhighlight: function (element, errorClass, validClass) {
              $(element).removeClass('is-invalid');
          },
          submitHandler: function (form){
              //console.log(new FormData(form));
              var formData = new FormData(form);
              formData.append('trainingSave', '1');
              $.ajax({
                  type:"POST",
                  url:"controller/training-controller.php",
                  data:formData,
                  dataType: 'json',
                  mimeType: "multipart/form-data",
                  contentType: false,
                  processData: false,
                  cache: false,
                  success:function(data){
                      //console.log(data);
                      if(data.data>0){
                          toastr.success('Saved Successfully');
                          window.location.href = 'training-request-list.php';
                        //   $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                      }else {
                        toastr.error(data.status);
                      }

                  }
              });
          }
      });
      $('.agent_id').on('change', function () {
        $(this).valid();
    });

  });