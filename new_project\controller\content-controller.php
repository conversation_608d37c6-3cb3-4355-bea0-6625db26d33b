<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-ggportal.php';
require_once $include_path . 'header-include.php'; //functions and class
require_once $include_path . 'validate-session.php';

$user_type = $_SESSION['user']['user_type'];
$user_id = $_SESSION['user']['user_id'];

$username = $_SESSION['user']['first_name'];
$access_available_for = "RA";
if (!validate_page_access($access_available_for)) {
    //will automatically redirect to login page
    die("No Access");
}

$cContent = new Content();


// $json = array(
//     "data" =>$_POST
//  );
//  echo json_encode($json);
//  die();



// save content
if(isset($_POST['contentSave'])){

    $content_id = filter_input(INPUT_POST, 'content_id');
    $content_body = filter_input(INPUT_POST, 'content_body');

    //add to array 
    
    $criteria = array(
        'content_id' => $content_id,
        'content_body' => $content_body,
    );
    

    $result = $cContent->saveContent($criteria);

    $json = array(
        "data" =>$result
        ,"status" => ($result > 0 ? 'Success': 'There is an error while Saving')
    );
    echo json_encode($json);
    die();
}
