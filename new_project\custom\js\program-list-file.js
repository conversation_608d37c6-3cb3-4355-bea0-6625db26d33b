$(document).ready(function () {
  // $('#example2').DataTable({
  //     "paging": true,
  //     "lengthChange": true,
  //     "searching": true,
  //     "ordering": true,
  //     "info": true,
  //     "autoWidth": false,
  //     "responsive": true,
  // });

  $("#example2 tfoot th").each(function () {
    var title = $(this).text();
    if (title == "ID" || title == "Action") {
      $(this).html("");
    } else {
      $(this).html(
        '<input class="form-control form-control-sm" type="text" placeholder="Search ' +
          title +
          '" />'
      );
    }
  });

  // DataTable
  var table = $("#example2").DataTable({
    paging: true,
    lengthChange: true,
    pageLength: 100,
    searching: true,
    ordering: true,
    info: true,
    autoWidth: false,
    responsive: true,
    initComplete: function () {
      // Apply the search
      this.api()
        .columns()
        .every(function () {
          var that = this;

          $("input", this.footer()).on("keyup change clear", function () {
            if (that.search() !== this.value) {
              that.search(this.value).draw();
            }
          });
        });
    },
  });
  $("#example2_filter").addClass("d-none");
  $("#example2 tfoot tr").appendTo("#example2 thead");

  $(document).on("click", ".delete_btn", function () {
    var program_id = $(this).data("program_id");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/program-controller.php",
          data: {
            program_id: program_id,
            deleteProgram: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire(
                "Deleted!",
                "Your program has been deleted.",
                "success"
              );
              window.location.reload();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  /*Upload program list*/
  $("#bulkuploadform").submit(function (e) {    
    e.preventDefault();
    var formData = new FormData(this);
    formData.append("uploadProgramList", "1");
    $.ajax({
      type: "POST",
      url: "controller/program-controller.php",
      data: formData,
      dataType: "json",
      mimeType: "multipart/form-data",
      contentType: false,
      processData: false,
      cache: false,
      success: function (data) {
        console.log("Data received from server:", data);
        if (data.data > 0) {
          toastr.success("Saved Successfully");
          window.location.href = "program-list.php";
        } else {
          toastr.error(data.status);
        }
      },
      error: function (xhr, status, error) {
        console.log("Error:", xhr);
        toastr.error("An error occurred during the AJAX request.");
      },
    });
  });


document.querySelector(".download_btn").addEventListener("click", function() {    
  // Create a form element
  var form = document.createElement('form');
  form.setAttribute('method', 'post');
  form.setAttribute('action', 'controller/program-controller.php');
  
  // Add an input element for the download button
  var input = document.createElement('input');
  input.setAttribute('type', 'hidden');
  input.setAttribute('name', 'download_btn');
  input.setAttribute('value', 'true');
  
  // Append the input to the form
  form.appendChild(input);
  
  // Append the form to the document body
  document.body.appendChild(form);
  
  // Submit the form
  form.submit();
});



 
 
});
