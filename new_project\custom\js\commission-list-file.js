$(document).ready(function () {
  // $('#example2').DataTable({
  //     "paging": true,
  //     "lengthChange": true,
  //     "searching": true,
  //     "ordering": true,
  //     "info": true,
  //     "autoWidth": false,
  //     "responsive": true,
  // });

  $("#example2 tfoot th").each(function () {
    var title = $(this).text();
    if (title == "ID") {
      $(this).html("");
    } 
    else if (title == "Action") {
        $(this).html(
            '<button type="button" class="btn btn-danger btn-sm delete_all_btn">Delete All Records</button>'
        );
      }else {
      $(this).html(
        '<input class="form-control form-control-sm" type="text" placeholder="Search ' +
          title +
          '" />'
      );
    }
  });

  // DataTable
  var table = $("#example2").DataTable({
    paging: true,
    lengthChange: true,
    searching: true,
    ordering: true,
    info: true,
    autoWidth: false,
    responsive: true,
    initComplete: function () {
      // Apply the search
      this.api()
        .columns()
        .every(function () {
          var that = this;

          $("input", this.footer()).on("keyup change clear", function () {
            if (that.search() !== this.value) {
              that.search(this.value).draw();
            }
          });
        });
    },
  });
  $("#example2_filter").addClass("d-none");
  $("#example2 tfoot tr").appendTo("#example2 thead");

  $(document).on("click", ".delete_btn", function () {
    var commission_id = $(this).data("commission_id");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/institute-controller.php",
          data: {
            commission_id: commission_id,
            deleteCommission: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your file has been deleted.", "success");
              window.location.reload();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  /*Upload commision list*/
  $("#bulkuploadform").submit(function (e) {
    e.preventDefault();
    var formData = new FormData(this);
    formData.append("uploadProgramList", "1");
    $.ajax({
      type: "POST",
      url: "controller/institute-controller.php",
      data: formData,
      dataType: "json",
      mimeType: "multipart/form-data",
      contentType: false,
      processData: false,
      cache: false,
      success: function (data) {
        console.log("Data received from server:", data);
        if (data.data > 0) {
          toastr.success("Saved Successfully");
          window.location.href = "commission-list.php";
        } else {
          toastr.error(data.status);
        }
      },
      error: function (xhr, status, error) {
        console.log("Error:", xhr);
        toastr.error("An error occurred during the AJAX request.");
      },
    });
  });

  $(document).on("click", ".delete_all_btn", function () {
    Swal.fire({
      title: "Are you sure want to delete all records?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/institute-controller.php",
          data: {
            deleteAllCommission: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("All Data Deleted!", "Your data has been deleted.", "success");
              window.location.reload();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });
});
