





<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark">
  <head>
    <meta charset="utf-8">
  <link rel="dns-prefetch" href="https://github.githubassets.com">
  <link rel="dns-prefetch" href="https://avatars.githubusercontent.com">
  <link rel="dns-prefetch" href="https://github-cloud.s3.amazonaws.com">
  <link rel="dns-prefetch" href="https://user-images.githubusercontent.com/">



  <link crossorigin="anonymous" media="all" integrity="sha512-7KjiGvJiLLy6LJPGf3m67ejAdgQsgDdnxZYoaI6+Agd0ZxHKTCjoKZgaf3PgUjURCcVceAwySJJJWgitRskDiA==" rel="stylesheet" href="https://github.githubassets.com/assets/frameworks-eca8e21af2622cbcba2c93c67f79baed.css" />
  <link crossorigin="anonymous" media="all" integrity="sha512-RzgqLoeS1c1AYAWGmmE3oaCD775kLmGtx6yBw9V7hQ8W3SDwXPyb/+eq+DWBPKaeGkjWpCWc4HBZpHTwImx+JA==" rel="stylesheet" href="https://github.githubassets.com/assets/site-47382a2e8792d5cd406005869a6137a1.css" />
    <link crossorigin="anonymous" media="all" integrity="sha512-dDsAoT3mMaA8gyLZkshXL3vrnDAuIv4cNq2iN06+o44rOFIngYNNiTehUUzNuMoBXMaDg0MLhEaZNumoCiLJkw==" rel="stylesheet" href="https://github.githubassets.com/assets/behaviors-743b00a13de631a03c8322d992c8572f.css" />
    
    
    
    <link crossorigin="anonymous" media="all" integrity="sha512-ZALWtv1kOaT0UBdNi8Fv4ZmZmV8HfkIPYkQWilW0B4Yg/R4+W3No2FSZ4brvptofB42qt/Zfqczsq9TdzA6HIw==" rel="stylesheet" href="https://github.githubassets.com/assets/github-6402d6b6fd6439a4f450174d8bc16fe1.css" />

  <script crossorigin="anonymous" defer="defer" integrity="sha512-CzeY4A6TiG4fGZSWZU8FxmzFFmcQFoPpArF0hkH0/J/S7UL4eed/LKEXMQXfTwiG5yEJBI+9BdKG8KQJNbhcIQ==" type="application/javascript" src="https://github.githubassets.com/assets/environment-0b3798e0.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-E+MNpR1dyNE0hrMBGq4WFHHe3UIkcQU59XV5Pdzb3m0JUo5PcRrFRSWQTHDA5388RHiBHVOcm5EaaoRNa86bLw==" type="application/javascript" src="https://github.githubassets.com/assets/chunk-frameworks-13e30da5.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-EYEym3yUb3yPcpRqldUV2Qre2K0A+k6WClEe50zbp8J2pkDMBVM5gWbX2K5x6oTK0Lsg6LjzQs5Dj7UseJK3Bg==" type="application/javascript" src="https://github.githubassets.com/assets/chunk-vendor-1181329b.js"></script>
  
  <script crossorigin="anonymous" defer="defer" integrity="sha512-mde+OBNm8p9eFyRKnB002Gib9pR0q74EOv0leiKK3foulLUU3biNIMm3LWlHlm1wfjI2hMEqsTLQne6hm7O/4g==" type="application/javascript" src="https://github.githubassets.com/assets/behaviors-99d7be38.js"></script>
  
    <script crossorigin="anonymous" defer="defer" integrity="sha512-tjuNQcwhEIXGvVIG4XM/Aj4z+Od+NBRkbgWNwEMjGL3nsnAmdoBdbzsn/WTvl3hk+TPt1D0BvBLHLBPgiwvT2w==" type="application/javascript" data-module-id="./chunk-animate-on-scroll.js" data-src="https://github.githubassets.com/assets/chunk-animate-on-scroll-b63b8d41.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-+BKEtK9JkmJ52jKSoX6+SBrGV6kJxB8J/iAPkFQ/oeq8YekNcz7IZlJgM5Tddyx1RrkL3+sdG0tAy3YuFbYqfA==" type="application/javascript" data-module-id="./chunk-codemirror.js" data-src="https://github.githubassets.com/assets/chunk-codemirror-f81284b4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-EKOvqJ9uTatAt87WxU+OSS4mi7gMUszFbGo4aPErQkjpWLXnrPSeZvK5ngU8OYoIoiVOq+v8dA3C6MF/z2d/kA==" type="application/javascript" data-module-id="./chunk-color-modes.js" data-src="https://github.githubassets.com/assets/chunk-color-modes-10a3afa8.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-zkYZSjUFqSifB+Lt76jclFMrfqpcPqevT801RZcoBNCZHRTBKcFrW9OyJoPOzKFv+fZVDRnqdqGsuIv5KOIgZg==" type="application/javascript" data-module-id="./chunk-contributions-spider-graph.js" data-src="https://github.githubassets.com/assets/chunk-contributions-spider-graph-ce46194a.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-6j/oSF+kbW+yetNPvI684VzAu9pzug6Vj2h+3u1LdCuRhR4jnuiHZfeQKls3nxcT/S3H+oIt7FtigE/aeoj+gg==" type="application/javascript" data-module-id="./chunk-drag-drop.js" data-src="https://github.githubassets.com/assets/chunk-drag-drop-ea3fe848.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-N+ziqJjVMfWiqeVHdayDHpNRlG5HsF+cgV+pFnMDoTJuvBzgw+ndsepe4NcKAxIS3WMvzMaQcYmd2vrIaoAJVg==" type="application/javascript" data-module-id="./chunk-edit.js" data-src="https://github.githubassets.com/assets/chunk-edit-37ece2a8.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-aiqMIGGZGo8AQMjcoImKPMTsZVVRl6htCSY7BpRmpGPG/AF+Wq+P/Oj/dthWQOIk9cCNMPEas7O2zAR6oqn0tA==" type="application/javascript" data-module-id="./chunk-emoji-picker-element.js" data-src="https://github.githubassets.com/assets/chunk-emoji-picker-element-6a2a8c20.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-DAk56F8lz8k6kg6vf15oE4tu4MTIPDT9DUo3VwO8SLYyb3ws4QU433BG7eVXOS50wzl7dUuMFRfTP1rHlHi45g==" type="application/javascript" data-module-id="./chunk-filter-input.js" data-src="https://github.githubassets.com/assets/chunk-filter-input-0c0939e8.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-XwuQdORq1W9Z+a/i72pH+NfR1rhwlGdxIlaIBbTciscGc/+McxMNLixGBp8e6Td4W1zzHvQ1Jyryl5gUfEr76g==" type="application/javascript" data-module-id="./chunk-insights-graph.js" data-src="https://github.githubassets.com/assets/chunk-insights-graph-5f0b9074.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-guegRI58Rv3iFAfIPmB7r5EMnJN8YavE7gaPDH1bXf48eRgA4GtM2bgd0Luxu1zmIbSCIiultO3tXizNlApljQ==" type="application/javascript" data-module-id="./chunk-jump-to.js" data-src="https://github.githubassets.com/assets/chunk-jump-to-82e7a044.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-supZkxo+OPYLNtLXxI+e1dkCqVySu4FOWX1fiVVFs2ZggygppNsEz4F9wVp4YtH2TjFsZW8r/75uDUhPneO2sA==" type="application/javascript" data-module-id="./chunk-notification-list-focus.js" data-src="https://github.githubassets.com/assets/chunk-notification-list-focus-b2ea5993.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ma0OOy3nj0c1cqBx0BkcmIFsLqcSZ+MIukQxyEFM/OWTzZpG+QMgOoWPAHZz43M6fyjAUG1jH6c/6LPiiKPCyw==" type="application/javascript" data-module-id="./chunk-profile-pins-element.js" data-src="https://github.githubassets.com/assets/chunk-profile-pins-element-99ad0e3b.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ZLYfbPLIHR6OTusF09GG1MbJsUXh+0W3ccu0EcIiU9QklF4bCaBaGKC6jOXWk4juJRideyWJ0Y2mkKFdZmkIcQ==" type="application/javascript" data-module-id="./chunk-ref-selector.js" data-src="https://github.githubassets.com/assets/chunk-ref-selector-64b61f6c.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-9yFUsOHjJzYPFPiEllzLQ+moFYxDf4kgPb4JtE6U2HkuQGhk13jLBkjRfFiR21uDHuH+aqLZBHrYN43T1jtWRA==" type="application/javascript" data-module-id="./chunk-responsive-underlinenav.js" data-src="https://github.githubassets.com/assets/chunk-responsive-underlinenav-f72154b0.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-9WNXtB07IyWypiPmkuucspwog4mme9q5GKGMSgd7FI0DPimmg/pEw+aaAofFV1vuWMt9I8H5QpsVtlbHGg1YBA==" type="application/javascript" data-module-id="./chunk-runner-groups.js" data-src="https://github.githubassets.com/assets/chunk-runner-groups-f56357b4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-fIq9Mn7jY/bHQXnsmh+VejpDnaO+d/FDxsp+4CuZtdNLrLuO+dQCjh+m6Yd8GCYD2Cy6DWbCEyM+mH2dkB2H9A==" type="application/javascript" data-module-id="./chunk-sortable-behavior.js" data-src="https://github.githubassets.com/assets/chunk-sortable-behavior-7c8abd32.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-WK8VXw3lfUQ/VRW0zlgKPhcMUqH0uTnB/KzePUPdZhCm/HpxfXXHKTGvj5C0Oex7+zbIM2ECzULbtTCT4ug3yg==" type="application/javascript" data-module-id="./chunk-toast.js" data-src="https://github.githubassets.com/assets/chunk-toast-58af155f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-vgHJEmEJxNmHucGbVY8bEUoOYo5/ZwpQ69rU8Dld89daWJ54uad9lNptxq32F8pnbHhdngw9lohNEbMbjmj5AQ==" type="application/javascript" data-module-id="./chunk-tweetsodium.js" data-src="https://github.githubassets.com/assets/chunk-tweetsodium-be01c912.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ucVNkKwIqy4+ze/ypsWpn7ndabcdwyPboAy0Z0S4heVwr2dS/RF4hWDZcLs9IYoLD8h8oL1E65uSM+GncDsTZA==" type="application/javascript" data-module-id="./chunk-user-status-submit.js" data-src="https://github.githubassets.com/assets/chunk-user-status-submit-b9c54d90.js"></script>
  
  <script crossorigin="anonymous" defer="defer" integrity="sha512-eaBSia5AjX+v5LUREpt1HOhD+0aLjK91awB5RmwIEB/2FWELPH1z2wyJtogNJU1H7/99sk/t0/ODf2BxW57coQ==" type="application/javascript" src="https://github.githubassets.com/assets/repositories-79a05289.js"></script>
<script crossorigin="anonymous" defer="defer" integrity="sha512-xcx3BdsrK77G2iC9ccUI/KOoDGIZIl9ORL25ClBvmQdnxEQoXQuNjkunyOreNdY0+eR0pMcyIv3ziV0nGci6Fg==" type="application/javascript" src="https://github.githubassets.com/assets/diffs-c5cc7705.js"></script>

  <meta name="viewport" content="width=device-width">
  
  <title>jQuery-Autocomplete/jquery.autocomplete.min.js at master · devbridge/jQuery-Autocomplete · GitHub</title>
    <meta name="description" content="Ajax Autocomplete for jQuery allows you to easily create autocomplete/autosuggest boxes for text input fields - devbridge/jQuery-Autocomplete">
    <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="GitHub">
  <link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub">
  <meta property="fb:app_id" content="1401488693436528">
  <meta name="apple-itunes-app" content="app-id=1477376905" />
    <meta name="twitter:image:src" content="https://opengraph.githubassets.com/04f2f2a7406f24d97633efce43a8b1d50c08c55573e22fa3ac398f5f27162087/devbridge/jQuery-Autocomplete" /><meta name="twitter:site" content="@github" /><meta name="twitter:card" content="summary_large_image" /><meta name="twitter:title" content="devbridge/jQuery-Autocomplete" /><meta name="twitter:description" content="Ajax Autocomplete for jQuery allows you to easily create autocomplete/autosuggest boxes for text input fields - devbridge/jQuery-Autocomplete" />
    <meta property="og:image" content="https://opengraph.githubassets.com/04f2f2a7406f24d97633efce43a8b1d50c08c55573e22fa3ac398f5f27162087/devbridge/jQuery-Autocomplete" /><meta property="og:site_name" content="GitHub" /><meta property="og:type" content="object" /><meta property="og:title" content="devbridge/jQuery-Autocomplete" /><meta property="og:url" content="https://github.com/devbridge/jQuery-Autocomplete" /><meta property="og:description" content="Ajax Autocomplete for jQuery allows you to easily create autocomplete/autosuggest boxes for text input fields - devbridge/jQuery-Autocomplete" />



    

  <link rel="assets" href="https://github.githubassets.com/">
  

  <meta name="request-id" content="9CF8:9A32:2421F2D:26EF252:607FB7A2" data-pjax-transient="true"/><meta name="html-safe-nonce" content="201bc6948122981623e079cd0a2be56409057fa60a26b5c1544d8540552f1939" data-pjax-transient="true"/><meta name="visitor-payload" content="eyJyZWZlcnJlciI6Imh0dHBzOi8vZ2l0aHViLmNvbS9kZXZicmlkZ2UvalF1ZXJ5LUF1dG9jb21wbGV0ZS90cmVlL21hc3Rlci9kaXN0IiwicmVxdWVzdF9pZCI6IjlDRjg6OUEzMjoyNDIxRjJEOjI2RUYyNTI6NjA3RkI3QTIiLCJ2aXNpdG9yX2lkIjoiODY2MDIyNzI0ODEzNzc5NzQ1MCIsInJlZ2lvbl9lZGdlIjoic2VhIiwicmVnaW9uX3JlbmRlciI6InNlYSJ9" data-pjax-transient="true"/><meta name="visitor-hmac" content="326e4146486d6d813ef4cca2f3c91be5c085d1ea3369cf5dc0bd8a9dd402b2a7" data-pjax-transient="true"/>

    <meta name="hovercard-subject-tag" content="repository:688157" data-pjax-transient>


  <meta name="github-keyboard-shortcuts" content="repository,source-code" data-pjax-transient="true" />

  

  <meta name="selected-link" value="repo_source" data-pjax-transient>

    <meta name="google-site-verification" content="c1kuD-K2HIVF635lypcsWPoD4kilo5-jA_wBFyT4uMY">
  <meta name="google-site-verification" content="KT5gs8h0wvaagLKAVWq8bbeNwnZZK1r1XQysX3xurLU">
  <meta name="google-site-verification" content="ZzhVyEFwb7w3e0-uOTltm8Jsck2F5StVihD0exw2fsA">
  <meta name="google-site-verification" content="GXs5KoUUkNCoaAZn7wPN-t01Pywp9M3sEjnt_3_ZWPc">

  <meta name="octolytics-host" content="collector.githubapp.com" /><meta name="octolytics-app-id" content="github" /><meta name="octolytics-event-url" content="https://collector.githubapp.com/github-external/browser_event" />

  <meta name="analytics-location" content="/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show" data-pjax-transient="true" />

  



  <meta name="optimizely-datafile" content="{&quot;version&quot;: &quot;4&quot;, &quot;rollouts&quot;: [], &quot;typedAudiences&quot;: [], &quot;anonymizeIP&quot;: true, &quot;projectId&quot;: &quot;***********&quot;, &quot;variables&quot;: [], &quot;featureFlags&quot;: [], &quot;experiments&quot;: [{&quot;status&quot;: &quot;Running&quot;, &quot;audienceIds&quot;: [], &quot;variations&quot;: [{&quot;variables&quot;: [], &quot;id&quot;: &quot;20155488945&quot;, &quot;key&quot;: &quot;control&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20132706682&quot;, &quot;key&quot;: &quot;show_missions&quot;}], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;dashboard_missions&quot;, &quot;layerId&quot;: &quot;20134196434&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;20132706682&quot;, &quot;endOfRange&quot;: 1250}, {&quot;entityId&quot;: &quot;&quot;, &quot;endOfRange&quot;: 5000}, {&quot;entityId&quot;: &quot;20155488945&quot;, &quot;endOfRange&quot;: 6250}, {&quot;entityId&quot;: &quot;&quot;, &quot;endOfRange&quot;: 10000}], &quot;forcedVariations&quot;: {}}], &quot;audiences&quot;: [{&quot;conditions&quot;: &quot;[\&quot;or\&quot;, {\&quot;match\&quot;: \&quot;exact\&quot;, \&quot;name\&quot;: \&quot;$opt_dummy_attribute\&quot;, \&quot;type\&quot;: \&quot;custom_attribute\&quot;, \&quot;value\&quot;: \&quot;$opt_dummy_value\&quot;}]&quot;, &quot;id&quot;: &quot;$opt_dummy_audience&quot;, &quot;name&quot;: &quot;Optimizely-Generated Audience for Backwards Compatibility&quot;}], &quot;groups&quot;: [{&quot;policy&quot;: &quot;random&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;***********&quot;, &quot;endOfRange&quot;: 10000}], &quot;experiments&quot;: [{&quot;status&quot;: &quot;Running&quot;, &quot;audienceIds&quot;: [], &quot;variations&quot;: [{&quot;variables&quot;: [], &quot;id&quot;: &quot;20061181493&quot;, &quot;key&quot;: &quot;control&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20046091568&quot;, &quot;key&quot;: &quot;most_popular&quot;}], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;pricing_page&quot;, &quot;layerId&quot;: &quot;20047761391&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;20061181493&quot;, &quot;endOfRange&quot;: 5000}, {&quot;entityId&quot;: &quot;20046091568&quot;, &quot;endOfRange&quot;: 10000}], &quot;forcedVariations&quot;: {&quot;890b7acea08c1711c74beff6bd78b5e7&quot;: &quot;control&quot;, &quot;235830406.1616679911&quot;: &quot;control&quot;, &quot;167363014.1617810094&quot;: &quot;most_popular&quot;, &quot;f7d5ee986ba8bcc155e2393401c920f7&quot;: &quot;most_popular&quot;, &quot;2022915492.1615428687&quot;: &quot;most_popular&quot;, &quot;1006574531.1617036769&quot;: &quot;control&quot;, &quot;1693726779.1607624005&quot;: &quot;most_popular&quot;, &quot;b3d9f4f9910bc46c43a8d65ab83d8570&quot;: &quot;most_popular&quot;, &quot;1800070736.1616613011&quot;: &quot;control&quot;}}], &quot;id&quot;: &quot;19972601768&quot;}], &quot;attributes&quot;: [{&quot;id&quot;: &quot;16822470375&quot;, &quot;key&quot;: &quot;user_id&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;spammy&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;organization_plan&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;is_logged_in&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;geo&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;requestedCurrency&quot;}], &quot;botFiltering&quot;: false, &quot;accountId&quot;: &quot;***********&quot;, &quot;events&quot;: [{&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;hydro_click.dashboard.teacher_toolbox_cta&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.organizations.complete_sign_up&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;no_metric.tracked_outside_of_optimizely&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.add_repo&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.repository_imports.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.help.learn_more_about_repository_creation&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18188530140&quot;, &quot;key&quot;: &quot;test_event.do_not_use_in_production&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18191963644&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.transfer_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18195612788&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.import_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18210945499&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.invite_members&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18211063248&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.create_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18215721889&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.update_profile&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18224360785&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.dismiss&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18234832286&quot;, &quot;key&quot;: &quot;submit.organization_activation.complete&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18252392383&quot;, &quot;key&quot;: &quot;submit.org_repository.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18257551537&quot;, &quot;key&quot;: &quot;submit.org_member_invitation.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18259522260&quot;, &quot;key&quot;: &quot;submit.organization_profile.update&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18564603625&quot;, &quot;key&quot;: &quot;view.classroom_select_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18568612016&quot;, &quot;key&quot;: &quot;click.classroom_sign_in_click&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18572592540&quot;, &quot;key&quot;: &quot;view.classroom_name&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18574203855&quot;, &quot;key&quot;: &quot;click.classroom_create_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18582053415&quot;, &quot;key&quot;: &quot;click.classroom_select_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18589463420&quot;, &quot;key&quot;: &quot;click.classroom_create_classroom&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.classroom_create_first_classroom&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.classroom_grant_access&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;view.classroom_creation&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;upgrade_account_plan&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.view_account_billing_page&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.dismiss_signup_prompt&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.contact_sales&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.compare_account_plans&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.upgrade_account_cta&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.open_account_switcher&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.visit_account_profile&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.switch_account_context&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.homepage_signup&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.homepage_signup&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_enterprise_trial&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_organization_team&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.input_enterprise_trial_form&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.continue_with_team&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_organization_free&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.username&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.create_account&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.email&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.password&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;view.pricing_page&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.create_account&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.upgrade_payment_form&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.create_organization&quot;}], &quot;revision&quot;: &quot;606&quot;}" />
  <!-- To prevent page flashing, the optimizely JS needs to be loaded in the
    <head> tag before the DOM renders -->
  <script crossorigin="anonymous" defer="defer" integrity="sha512-+B/sUnOMqB/HP7ia2/8Aepv6fNkZvIfzJNXwL2TkiqXfDN8qoKdkapxxB92k5CDwOMdoGmfBn16nK+Xm5Zyg8A==" type="application/javascript" src="https://github.githubassets.com/assets/optimizely-f81fec52.js"></script>



  

      <meta name="hostname" content="github.com">
    <meta name="user-login" content="">


      <meta name="expected-hostname" content="github.com">


    <meta name="enabled-features" content="MARKETPLACE_PENDING_INSTALLATIONS,AUTOCOMPLETE_EMOJIS_IN_MARKDOWN_EDITOR,ACTIONS_CONCURRENCY_UI">

  <meta http-equiv="x-pjax-version" content="e099f0bea0fd348f39654d2cbdd5e016130b3809a166712ffde2f2e3b1d7be7b">
  

    
  <meta name="go-import" content="github.com/devbridge/jQuery-Autocomplete git https://github.com/devbridge/jQuery-Autocomplete.git">

  <meta name="octolytics-dimension-user_id" content="1701896" /><meta name="octolytics-dimension-user_login" content="devbridge" /><meta name="octolytics-dimension-repository_id" content="688157" /><meta name="octolytics-dimension-repository_nwo" content="devbridge/jQuery-Autocomplete" /><meta name="octolytics-dimension-repository_public" content="true" /><meta name="octolytics-dimension-repository_is_fork" content="false" /><meta name="octolytics-dimension-repository_network_root_id" content="688157" /><meta name="octolytics-dimension-repository_network_root_nwo" content="devbridge/jQuery-Autocomplete" />



    <link rel="canonical" href="https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js" data-pjax-transient>


  <meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats">

  <meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors">

  <meta name="browser-optimizely-client-errors-url" content="https://api.github.com/_private/browser/optimizely_client/errors">

  <link rel="mask-icon" href="https://github.githubassets.com/pinned-octocat.svg" color="#000000">
  <link rel="alternate icon" class="js-site-favicon" type="image/png" href="https://github.githubassets.com/favicons/favicon.png">
  <link rel="icon" class="js-site-favicon" type="image/svg+xml" href="https://github.githubassets.com/favicons/favicon.svg">

<meta name="theme-color" content="#1e2327">
<meta name="color-scheme" content="light dark" />


  <link rel="manifest" href="/manifest.json" crossOrigin="use-credentials">

  </head>

  <body class="logged-out env-production page-responsive page-blob" style="word-wrap: break-word;">
    

    <div class="position-relative js-header-wrapper ">
      <a href="#start-of-content" class="px-2 py-4 color-bg-info-inverse color-text-white show-on-focus js-skip-to-content">Skip to content</a>
      <span class="progress-pjax-loader width-full js-pjax-loader-bar Progress position-fixed">
    <span style="background-color: #79b8ff;width: 0%;" class="Progress-item progress-pjax-loader-bar "></span>
</span>      
      


            <header class="Header-old header-logged-out js-details-container Details position-relative f4 py-2" role="banner">
  <div class="container-xl d-lg-flex flex-items-center p-responsive">
    <div class="d-flex flex-justify-between flex-items-center">
        <a class="mr-4" href="https://github.com/" aria-label="Homepage" data-ga-click="(Logged out) Header, go to homepage, icon:logo-wordmark">
          <svg height="32" class="octicon octicon-mark-github color-text-white" viewBox="0 0 16 16" version="1.1" width="32" aria-hidden="true"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path></svg>
        </a>

          <div class="d-lg-none css-truncate css-truncate-target width-fit p-2">
            

          </div>

        <div class="d-flex flex-items-center">
              <a href="/join?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo"
                class="d-inline-block d-lg-none f5 color-text-white no-underline border color-border-tertiary rounded-2 px-2 py-1 mr-3 mr-sm-5 js-signup-redesign-control js-signup-redesign-target"
                data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="add3accf6e689527b09737188ac1a91b7c74da95497637cb727280335113227f"
              >
                Sign&nbsp;up
              </a>
              <a href="/join_next?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo"
                class="d-inline-block d-lg-none f5 color-text-white no-underline border color-border-tertiary rounded-2 px-2 py-1 mr-3 mr-sm-5 js-signup-redesign-variation js-signup-redesign-target"
                hidden
                data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="add3accf6e689527b09737188ac1a91b7c74da95497637cb727280335113227f"
              >
                Sign&nbsp;up
              </a>

          <button class="btn-link d-lg-none mt-1 js-details-target" type="button" aria-label="Toggle navigation" aria-expanded="false">
            <svg height="24" class="octicon octicon-three-bars color-text-white" viewBox="0 0 16 16" version="1.1" width="24" aria-hidden="true"><path fill-rule="evenodd" d="M1 2.75A.75.75 0 011.75 2h12.5a.75.75 0 110 1.5H1.75A.75.75 0 011 2.75zm0 5A.75.75 0 011.75 7h12.5a.75.75 0 110 1.5H1.75A.75.75 0 011 7.75zM1.75 12a.75.75 0 100 1.5h12.5a.75.75 0 100-1.5H1.75z"></path></svg>
          </button>
        </div>
    </div>

    <div class="HeaderMenu HeaderMenu--logged-out position-fixed top-0 right-0 bottom-0 height-fit position-lg-relative d-lg-flex flex-justify-between flex-items-center flex-auto">
      <div class="d-flex d-lg-none flex-justify-end border-bottom color-bg-secondary p-3">
        <button class="btn-link js-details-target" type="button" aria-label="Toggle navigation" aria-expanded="false">
          <svg height="24" class="octicon octicon-x color-text-secondary" viewBox="0 0 24 24" version="1.1" width="24" aria-hidden="true"><path fill-rule="evenodd" d="M5.72 5.72a.75.75 0 011.06 0L12 10.94l5.22-5.22a.75.75 0 111.06 1.06L13.06 12l5.22 5.22a.75.75 0 11-1.06 1.06L12 13.06l-5.22 5.22a.75.75 0 01-1.06-1.06L10.94 12 5.72 6.78a.75.75 0 010-1.06z"></path></svg>
        </button>
      </div>

        <nav class="mt-0 px-3 px-lg-0 mb-5 mb-lg-0" aria-label="Global">
          <ul class="d-lg-flex list-style-none">
              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Why GitHub?
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                      <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>
                  <div class="dropdown-menu flex-auto rounded px-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <a href="/features" class="py-2 lh-condensed-ultra d-block Link--primary no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Features">Features <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a>
                    <ul class="list-style-none f5 pb-3">
                        <li class="edge-item-fix"><a href="/mobile" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Mobile <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/actions" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Actions <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/codespaces" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Codespaces <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/packages" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Packages <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/security" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Security <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/code-review/" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Code review <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/project-management/" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Project management <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/features/integrations" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">Integrations <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>

                    <ul class="list-style-none mb-0 border-lg-top pt-lg-3">
                      <li class="edge-item-fix"><a href="/sponsors" class="py-2 lh-condensed-ultra d-block no-underline Link--primary no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Sponsors">GitHub Sponsors <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="/customer-stories" class="py-2 lh-condensed-ultra d-block no-underline Link--primary no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Customer stories">Customer stories<span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>
                  </div>
                </details>
              </li>
              <li class="border-bottom border-lg-bottom-0 mr-0 mr-lg-3">
                <a href="/team" class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-ga-click="(Logged out) Header, go to Team">Team</a>
              </li>
              <li class="border-bottom border-lg-bottom-0 mr-0 mr-lg-3">
                <a href="/enterprise" class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-ga-click="(Logged out) Header, go to Enterprise">Enterprise</a>
              </li>

              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Explore
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                      <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>

                  <div class="dropdown-menu flex-auto rounded px-0 pt-2 pb-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/explore" class="py-2 lh-condensed-ultra d-block Link--primary no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Explore">Explore GitHub <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>

                    <h4 class="color-text-tertiary text-normal text-mono f5 mb-2 border-lg-top pt-lg-3">Learn and contribute</h4>
                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/topics" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Topics">Topics <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                        <li class="edge-item-fix"><a href="/collections" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Collections">Collections <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="/trending" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Trending">Trending <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://lab.github.com/" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Learning lab">Learning Lab <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://opensource.guide" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Open source guides">Open source guides <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>

                    <h4 class="color-text-tertiary text-normal text-mono f5 mb-2 border-lg-top pt-lg-3">Connect with others</h4>
                    <ul class="list-style-none mb-0">
                      <li class="edge-item-fix"><a href="https://github.com/readme" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover">The ReadME Project <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://github.com/events" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Events">Events <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://github.community" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Community forum">Community forum <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://education.github.com" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to GitHub Education">GitHub Education <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://stars.github.com" class="py-2 pb-0 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to GitHub Stars Program">GitHub Stars program <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>
                  </div>
                </details>
              </li>

              <li class="border-bottom border-lg-bottom-0 mr-0 mr-lg-3">
                <a href="/marketplace" class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-ga-click="(Logged out) Header, go to Marketplace">Marketplace</a>
              </li>

              <li class="d-block d-lg-flex flex-lg-nowrap flex-lg-items-center border-bottom border-lg-bottom-0 mr-0 mr-lg-3 edge-item-fix position-relative flex-wrap flex-justify-between d-flex flex-items-center ">
                <details class="HeaderMenu-details details-overlay details-reset width-full">
                  <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
                    Pricing
                    <svg x="0px" y="0px" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative">
                       <path d="M1,1l6.2,6L13,1"></path>
                    </svg>
                  </summary>

                  <div class="dropdown-menu flex-auto rounded px-0 pt-2 pb-4 mt-0 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
                    <a href="/pricing" class="pb-2 lh-condensed-ultra d-block Link--primary no-underline h5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Pricing">Plans <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a>

                    <ul class="list-style-none mb-3">
                      <li class="edge-item-fix"><a href="/pricing#feature-comparison" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Compare plans">Compare plans <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                      <li class="edge-item-fix"><a href="https://enterprise.github.com/contact" class="py-2 lh-condensed-ultra d-block Link--secondary no-underline f5 Bump-link--hover" data-ga-click="(Logged out) Header, go to Contact Sales">Contact Sales <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>

                    <ul class="list-style-none mb-0 border-lg-top pt-lg-3">
                      <li class="edge-item-fix"><a href="https://education.github.com" class="py-2 pb-0 lh-condensed-ultra d-block no-underline Link--primary no-underline h5 Bump-link--hover"  data-ga-click="(Logged out) Header, go to Education">Education <span class="Bump-link-symbol float-right text-normal color-text-tertiary pr-3">&rarr;</span></a></li>
                    </ul>
                  </div>
                </details>
              </li>
          </ul>
        </nav>

      <div class="d-lg-flex flex-items-center px-3 px-lg-0 text-center text-lg-left">
          <div class="d-lg-flex min-width-0 mb-3 mb-lg-0">
            <div class="header-search flex-auto js-site-search position-relative flex-self-stretch flex-md-self-auto mb-3 mb-md-0 mr-0 mr-md-3 scoped-search site-scoped-search js-jump-to"
  role="combobox"
  aria-owns="jump-to-results"
  aria-label="Search or jump to"
  aria-haspopup="listbox"
  aria-expanded="false"
>
  <div class="position-relative">
    <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="js-site-search-form" role="search" aria-label="Site" data-scope-type="Repository" data-scope-id="688157" data-scoped-search-url="/devbridge/jQuery-Autocomplete/search" data-owner-scoped-search-url="/orgs/devbridge/search" data-unscoped-search-url="/search" action="/devbridge/jQuery-Autocomplete/search" accept-charset="UTF-8" method="get">
      <label class="form-control input-sm header-search-wrapper p-0 js-chromeless-input-container header-search-wrapper-jump-to position-relative d-flex flex-justify-between flex-items-center">
        <input type="text"
          class="form-control input-sm header-search-input jump-to-field js-jump-to-field js-site-search-focus js-site-search-field is-clearable"
          data-hotkey="s,/"
          name="q"
          value=""
          placeholder="Search"
          data-unscoped-placeholder="Search GitHub"
          data-scoped-placeholder="Search"
          autocapitalize="off"
          aria-autocomplete="list"
          aria-controls="jump-to-results"
          aria-label="Search"
          data-jump-to-suggestions-path="/_graphql/GetSuggestedNavigationDestinations"
          spellcheck="false"
          autocomplete="off"
          >
          <input type="hidden" data-csrf="true" class="js-data-jump-to-suggestions-path-csrf" value="Oy/7JolGfzaOHfOyIhcOEKwxg2fvTJ3ZeifNpy1eotm36/dW/kIM74Lb/X/raP7swJlBJYPQL6xG3JoKJoFMfA==" />
          <input type="hidden" class="js-site-search-type-field" name="type" >
            <img src="https://github.githubassets.com/images/search-key-slash.svg" alt="" class="mr-2 header-search-key-slash">

            <div class="Box position-absolute overflow-hidden d-none jump-to-suggestions js-jump-to-suggestions-container">
              
<ul class="d-none js-jump-to-suggestions-template-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-suggestion" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="suggestion">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

</ul>

<ul class="d-none js-jump-to-no-results-template-container">
  <li class="d-flex flex-justify-center flex-items-center f5 d-none js-jump-to-suggestion p-2">
    <span class="color-text-secondary">No suggested jump to results</span>
  </li>
</ul>

<ul id="jump-to-results" role="listbox" class="p-0 m-0 js-navigation-container jump-to-suggestions-results-container js-jump-to-suggestions-results-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-scoped-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="scoped_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-owner-scoped-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="owner_scoped_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this organization">
        In this organization
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-global-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="global_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg height="16" width="16" class="octicon octicon-repo flex-shrink-0 js-jump-to-octicon-repo d-none" title="Repository" aria-label="Repository" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-project flex-shrink-0 js-jump-to-octicon-project d-none" title="Project" aria-label="Project" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path></svg>
      <svg height="16" width="16" class="octicon octicon-search flex-shrink-0 js-jump-to-octicon-search d-none" title="Search" aria-label="Search" viewBox="0 0 16 16" version="1.1" role="img"><path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path></svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-tertiary px-1 color-text-tertiary ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>


</ul>

            </div>
      </label>
</form>  </div>
</div>

          </div>

        <a href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete%2Fblob%2Fmaster%2Fdist%2Fjquery.autocomplete.min.js"
          class="HeaderMenu-link flex-shrink-0 no-underline mr-3"
          data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="6d11142ae78ed89c05276ace264369b4b63174fea122a8649c3f19c0513687d7"
          data-ga-click="(Logged out) Header, clicked Sign in, text:sign-in">
          Sign in
        </a>
            <a href="/join?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo&amp;source_repo=devbridge%2FjQuery-Autocomplete"
              class="HeaderMenu-link flex-shrink-0 d-inline-block no-underline border color-border-tertiary rounded px-2 py-1 js-signup-redesign-target js-signup-redesign-control"
              data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="6d11142ae78ed89c05276ace264369b4b63174fea122a8649c3f19c0513687d7"
              data-hydro-click="{&quot;event_type&quot;:&quot;analytics.click&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Sign up&quot;,&quot;action&quot;:&quot;click to sign up for account&quot;,&quot;label&quot;:&quot;ref_page:/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show;ref_cta:Sign up;ref_loc:header logged out&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="24f632ea7a6c6478946c4abb5f377488c008c0d22705740ed92b43149f522ada"
            >
              Sign up
            </a>
            <a href="/join_next?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo&amp;source_repo=devbridge%2FjQuery-Autocomplete"
              class="HeaderMenu-link flex-shrink-0 d-inline-block no-underline border color-border-tertiary rounded-1 px-2 py-1 js-signup-redesign-target js-signup-redesign-variation"
              hidden
              data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="6d11142ae78ed89c05276ace264369b4b63174fea122a8649c3f19c0513687d7"
              data-hydro-click="{&quot;event_type&quot;:&quot;analytics.click&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Sign up&quot;,&quot;action&quot;:&quot;click to sign up for account&quot;,&quot;label&quot;:&quot;ref_page:/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show;ref_cta:Sign up;ref_loc:header logged out&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="24f632ea7a6c6478946c4abb5f377488c008c0d22705740ed92b43149f522ada"
            >
              Sign up
            </a>
      </div>
    </div>
  </div>
</header>

    </div>

  <div id="start-of-content" class="show-on-focus"></div>





    <div data-pjax-replace id="js-flash-container">


  <template class="js-flash-template">
    <div class="flash flash-full  {{ className }}">
  <div class=" px-2" >
    <button class="flash-close js-flash-close" type="button" aria-label="Dismiss this message">
      <svg class="octicon octicon-x" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg>
    </button>
    
      <div>{{ message }}</div>

  </div>
</div>
  </template>
</div>


    

  <include-fragment class="js-notification-shelf-include-fragment" data-base-src="https://github.com/notifications/beta/shelf"></include-fragment>




  <div
    class="application-main "
    data-commit-hovercards-enabled
    data-discussion-hovercards-enabled
    data-issue-and-pr-hovercards-enabled
  >
        <div itemscope itemtype="http://schema.org/SoftwareSourceCode" class="">
    <main id="js-repo-pjax-container" data-pjax-container >
      

      
    






  


  <div class="color-bg-secondary pt-3 hide-full-screen mb-5">

      <div class="d-flex mb-3 px-3 px-md-4 px-lg-5">

        <div class="flex-auto min-width-0 width-fit mr-3">
            <h1 class=" d-flex flex-wrap flex-items-center break-word f3 text-normal">
    <svg class="octicon octicon-repo color-text-secondary mr-2" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path></svg>
  <span class="author flex-self-stretch" itemprop="author">
    <a class="url fn" rel="author" data-hovercard-type="organization" data-hovercard-url="/orgs/devbridge/hovercard" href="/devbridge">devbridge</a>
  </span>
  <span class="mx-1 flex-self-stretch color-text-secondary">/</span>
  <strong itemprop="name" class="mr-2 flex-self-stretch">
    <a data-pjax="#js-repo-pjax-container" href="/devbridge/jQuery-Autocomplete">jQuery-Autocomplete</a>
  </strong>
  
</h1>


        </div>

          <ul class="pagehead-actions flex-shrink-0 d-none d-md-inline" style="padding: 2px 0;">

  <li>
      <a class="tooltipped tooltipped-s btn btn-sm" aria-label="You must be signed in to change notification settings" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;notification subscription menu watch&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="aad4777b642cd173fa37ddf84312087fa169d2de78c41b861a7f48b741f354ad" href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete">
    <svg class="octicon octicon-bell" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path d="M8 16a2 2 0 001.985-1.75c.017-.137-.097-.25-.235-.25h-3.5c-.138 0-.252.113-.235.25A2 2 0 008 16z"></path><path fill-rule="evenodd" d="M8 1.5A3.5 3.5 0 004.5 5v2.947c0 .346-.102.683-.294.97l-1.703 2.556a.018.018 0 00-.003.01l.001.006c0 .***************.006a.017.017 0 00.006.004l.007.001h10.964l.007-.001a.016.016 0 00.006-.004.016.016 0 00.004-.006l.001-.007a.017.017 0 00-.003-.01l-1.703-2.554a1.75 1.75 0 01-.294-.97V5A3.5 3.5 0 008 1.5zM3 5a5 5 0 0110 0v2.947c0 .**************.139l1.703 2.555A1.518 1.518 0 0113.482 13H2.518a1.518 1.518 0 01-1.263-2.36l1.703-2.554A.25.25 0 003 7.947V5z"></path></svg>
    Notifications
</a>
  </li>

  <li>
          <a class="btn btn-sm btn-with-count  tooltipped tooltipped-s" aria-label="You must be signed in to star a repository" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;star button&quot;,&quot;repository_id&quot;:688157,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="5d9449b4ab699c4fde4dcddb2f065ac2d16f1f4eafcb995b489addf4b2b21cf1" href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete">
      <svg class="octicon octicon-star v-align-text-bottom mr-1" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M8 .25a.75.75 0 01.673.418l1.882 3.815 4.21.612a.75.75 0 01.416 1.279l-3.046 2.97.719 4.192a.75.75 0 01-1.088.791L8 12.347l-3.766 1.98a.75.75 0 01-1.088-.79l.72-4.194L.818 6.374a.75.75 0 01.416-1.28l4.21-.611L7.327.668A.75.75 0 018 .25zm0 2.445L6.615 5.5a.75.75 0 01-.564.41l-3.097.45 2.24 2.184a.75.75 0 01.216.664l-.528 3.084 2.769-1.456a.75.75 0 01.698 0l2.77 1.456-.53-3.084a.75.75 0 01.216-.664l2.24-2.183-3.096-.45a.75.75 0 01-.564-.41L8 2.694v.001z"></path></svg>
      <span>
        Star
</span></a>
    <a class="social-count js-social-count" href="/devbridge/jQuery-Autocomplete/stargazers"
      aria-label="3418 users starred this repository">
      3.4k
    </a>

  </li>

  <li>
        <a class="btn btn-sm btn-with-count tooltipped tooltipped-s" aria-label="You must be signed in to fork a repository" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;repo details fork button&quot;,&quot;repository_id&quot;:688157,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="db3e37d8685775ccabe97524e0c8c44e1ce170865d122503274a82490783d900" href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete">
          <svg class="octicon octicon-repo-forked" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M5 3.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm0 2.122a2.25 2.25 0 10-1.5 0v.878A2.25 2.25 0 005.75 8.5h1.5v2.128a2.251 2.251 0 101.5 0V8.5h1.5a2.25 2.25 0 002.25-2.25v-.878a2.25 2.25 0 10-1.5 0v.878a.75.75 0 01-.75.75h-4.5A.75.75 0 015 6.25v-.878zm3.75 7.378a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm3-8.75a.75.75 0 100-1.5.75.75 0 000 1.5z"></path></svg>
          Fork
</a>
      <a href="/devbridge/jQuery-Autocomplete/network/members" class="social-count"
         aria-label="1700 users forked this repository">
        1.7k
      </a>
  </li>
</ul>

      </div>
        

  <nav data-pjax="#js-repo-pjax-container" aria-label="Repository" class="js-repo-nav js-sidenav-container-pjax js-responsive-underlinenav overflow-hidden UnderlineNav px-3 px-md-4 px-lg-5 color-bg-secondary">

    <ul class="UnderlineNav-body list-style-none ">
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete" data-tab-item="i0code-tab" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages repo_deployments /devbridge/jQuery-Autocomplete" data-hotkey="g c" data-ga-click="Repository, Navigation click, Code tab" aria-current="page" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item selected ">
    
                  <svg class="octicon octicon-code UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06l-4.25-4.25z"></path></svg>
          <span data-content="Code">Code</span>
            <span title="Not available" class="Counter "></span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/issues" data-tab-item="i1issues-tab" data-selected-links="repo_issues repo_labels repo_milestones /devbridge/jQuery-Autocomplete/issues" data-hotkey="g i" data-ga-click="Repository, Navigation click, Issues tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-issue-opened UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M8 1.5a6.5 6.5 0 100 13 6.5 6.5 0 000-13zM0 8a8 8 0 1116 0A8 8 0 010 8zm9 3a1 1 0 11-2 0 1 1 0 012 0zm-.25-6.25a.75.75 0 00-1.5 0v3.5a.75.75 0 001.5 0v-3.5z"></path></svg>
          <span data-content="Issues">Issues</span>
            <span title="56" class="Counter ">56</span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/pulls" data-tab-item="i2pull-requests-tab" data-selected-links="repo_pulls checks /devbridge/jQuery-Autocomplete/pulls" data-hotkey="g p" data-ga-click="Repository, Navigation click, Pull requests tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-git-pull-request UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.177 3.073L9.573.677A.25.25 0 0110 .854v4.792a.25.25 0 01-.427.177L7.177 3.427a.25.25 0 010-.354zM3.75 2.5a.75.75 0 100 1.5.75.75 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122v5.256a2.251 2.251 0 11-1.5 0V5.372A2.25 2.25 0 011.5 3.25zM11 2.5h-1V4h1a1 1 0 011 1v5.628a2.251 2.251 0 101.5 0V5A2.5 2.5 0 0011 2.5zm1 10.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0zM3.75 12a.75.75 0 100 1.5.75.75 0 000-1.5z"></path></svg>
          <span data-content="Pull requests">Pull requests</span>
            <span title="32" class="Counter ">32</span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/actions" data-tab-item="i3actions-tab" data-selected-links="repo_actions /devbridge/jQuery-Autocomplete/actions" data-hotkey="g a" data-ga-click="Repository, Navigation click, Actions tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-play UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0zM8 0a8 8 0 100 16A8 8 0 008 0zM6.379 5.227A.25.25 0 006 5.442v5.117a.25.25 0 00.379.214l4.264-2.559a.25.25 0 000-.428L6.379 5.227z"></path></svg>
          <span data-content="Actions">Actions</span>
            <span title="Not available" class="Counter "></span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/projects" data-tab-item="i4projects-tab" data-selected-links="repo_projects new_repo_project repo_project /devbridge/jQuery-Autocomplete/projects" data-hotkey="g b" data-ga-click="Repository, Navigation click, Projects tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-project UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path></svg>
          <span data-content="Projects">Projects</span>
            <span title="0" hidden="hidden" class="Counter ">0</span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/wiki" data-tab-item="i5wiki-tab" data-selected-links="repo_wiki /devbridge/jQuery-Autocomplete/wiki" data-hotkey="g w" data-ga-click="Repository, Navigation click, Wikis tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-book UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M0 1.75A.75.75 0 01.75 1h4.253c1.227 0 2.317.59 3 1.501A3.744 3.744 0 0111.006 1h4.245a.75.75 0 01.75.75v10.5a.75.75 0 01-.75.75h-4.507a2.25 2.25 0 00-1.591.659l-.622.621a.75.75 0 01-1.06 0l-.622-.621A2.25 2.25 0 005.258 13H.75a.75.75 0 01-.75-.75V1.75zm8.755 3a2.25 2.25 0 012.25-2.25H14.5v9h-3.757c-.71 0-1.4.201-1.992.572l.004-7.322zm-1.504 7.324l.004-5.073-.002-2.253A2.25 2.25 0 005.003 2.5H1.5v9h3.757a3.75 3.75 0 011.994.574z"></path></svg>
          <span data-content="Wiki">Wiki</span>
            <span title="Not available" class="Counter "></span>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/security" data-tab-item="i6security-tab" data-selected-links="security overview alerts policy token_scanning code_scanning /devbridge/jQuery-Autocomplete/security" data-hotkey="g s" data-ga-click="Repository, Navigation click, Security tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-shield UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M7.467.133a1.75 1.75 0 011.066 0l5.25 1.68A1.75 1.75 0 0115 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.7 1.7 0 01-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 011.217-1.667l5.25-1.68zm.61 1.429a.25.25 0 00-.153 0l-5.25 1.68a.25.25 0 00-.174.238V7c0 1.358.275 2.666 1.057 3.86.784 1.194 2.121 2.34 4.366 3.297a.2.2 0 00.154 0c2.245-.956 3.582-2.104 4.366-3.298C13.225 9.666 13.5 8.36 13.5 7V3.48a.25.25 0 00-.174-.237l-5.25-1.68zM9 10.5a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.75a.75.75 0 10-1.5 0v3a.75.75 0 001.5 0v-3z"></path></svg>
          <span data-content="Security">Security</span>
            <include-fragment src="/devbridge/jQuery-Autocomplete/security/overall-count" accept="text/fragment+html"></include-fragment>

    
</a></li>
        <li class="d-flex">
  <a href="/devbridge/jQuery-Autocomplete/pulse" data-tab-item="i7insights-tab" data-selected-links="repo_graphs repo_contributors dependency_graph dependabot_updates pulse people community /devbridge/jQuery-Autocomplete/pulse" data-ga-click="Repository, Navigation click, Insights tab" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item ">
    
                  <svg class="octicon octicon-graph UnderlineNav-octicon d-none d-sm-inline" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M1.5 1.75a.75.75 0 00-1.5 0v12.5c0 .414.336.75.75.75h14.5a.75.75 0 000-1.5H1.5V1.75zm14.28 2.53a.75.75 0 00-1.06-1.06L10 7.94 7.53 5.47a.75.75 0 00-1.06 0L3.22 8.72a.75.75 0 001.06 1.06L7 7.06l2.47 2.47a.75.75 0 001.06 0l5.25-5.25z"></path></svg>
          <span data-content="Insights">Insights</span>
            <span title="Not available" class="Counter "></span>

    
</a></li>
</ul>
      <div style="visibility:hidden;" class="UnderlineNav-actions  js-responsive-underlinenav-overflow position-absolute pr-3 pr-md-4 pr-lg-5 right-0">      <details class="details-overlay details-reset position-relative">
  <summary role="button">          <div class="UnderlineNav-item mr-0 border-0">
            <svg class="octicon octicon-kebab-horizontal" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path></svg>
            <span class="sr-only">More</span>
          </div>
</summary>
  <div>          <details-menu role="menu" class="dropdown-menu dropdown-menu-sw ">
  
            <ul>
                <li data-menu-item="i0code-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete" href="/devbridge/jQuery-Autocomplete">
                    Code
</a>                </li>
                <li data-menu-item="i1issues-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/issues" href="/devbridge/jQuery-Autocomplete/issues">
                    Issues
</a>                </li>
                <li data-menu-item="i2pull-requests-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/pulls" href="/devbridge/jQuery-Autocomplete/pulls">
                    Pull requests
</a>                </li>
                <li data-menu-item="i3actions-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/actions" href="/devbridge/jQuery-Autocomplete/actions">
                    Actions
</a>                </li>
                <li data-menu-item="i4projects-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/projects" href="/devbridge/jQuery-Autocomplete/projects">
                    Projects
</a>                </li>
                <li data-menu-item="i5wiki-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/wiki" href="/devbridge/jQuery-Autocomplete/wiki">
                    Wiki
</a>                </li>
                <li data-menu-item="i6security-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/security" href="/devbridge/jQuery-Autocomplete/security">
                    Security
</a>                </li>
                <li data-menu-item="i7insights-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links=" /devbridge/jQuery-Autocomplete/pulse" href="/devbridge/jQuery-Autocomplete/pulse">
                    Insights
</a>                </li>
            </ul>

</details-menu></div>
</details></div>
</nav>

  </div>


<div class="container-xl clearfix new-discussion-timeline px-3 px-md-4 px-lg-5">
  <div id="repo-content-pjax-container" class="repository-content " >

    
      
    
<div>
  


    <a class="d-none js-permalink-shortcut" data-hotkey="y" href="/devbridge/jQuery-Autocomplete/blob/8138252b4c6fa994a0d438630ea375308e7f3b11/dist/jquery.autocomplete.min.js">Permalink</a>

    <!-- blob contrib key: blob_contributors:v22:c2512eb0ca56aa748fd495b4e10bedd3c973d1b125857b3dc19bdc74782ea9fb -->

    <div class="d-flex flex-items-start flex-shrink-0 pb-3 flex-wrap flex-md-nowrap flex-justify-between flex-md-justify-start">
      
<div class="position-relative">
  <details class="details-reset details-overlay mr-0 mb-0 " id="branch-select-menu">
    <summary class="btn css-truncate"
            data-hotkey="w"
            title="Switch branches or tags">
      <svg class="octicon octicon-git-branch text-gray" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M11.75 2.5a.75.75 0 100 1.5.75.75 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122V6A2.5 2.5 0 0110 8.5H6a1 1 0 00-1 1v1.128a2.251 2.251 0 11-1.5 0V5.372a2.25 2.25 0 111.5 0v1.836A2.492 2.492 0 016 7h4a1 1 0 001-1v-.628A2.25 2.25 0 019.5 3.25zM4.25 12a.75.75 0 100 1.5.75.75 0 000-1.5zM3.5 3.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0z"></path></svg>
      <span class="css-truncate-target" data-menu-button>master</span>
      <span class="dropdown-caret"></span>
    </summary>

      
<div class="SelectMenu">
  <div class="SelectMenu-modal">
    <header class="SelectMenu-header">
      <span class="SelectMenu-title">Switch branches/tags</span>
      <button class="SelectMenu-closeButton" type="button" data-toggle-for="branch-select-menu"><svg aria-label="Close menu" aria-hidden="false" class="octicon octicon-x" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg></button>
    </header>

    <input-demux data-action="tab-container-change:input-demux#storeInput tab-container-changed:input-demux#updateInput">
      <tab-container class="d-flex flex-column js-branches-tags-tabs" style="min-height: 0;">
        <div class="SelectMenu-filter">
          <input data-target="input-demux.source"
                 id="context-commitish-filter-field"
                 class="SelectMenu-input form-control"
                 aria-owns="ref-list-branches"
                 data-controls-ref-menu-id="ref-list-branches"
                 autofocus
                 autocomplete="off"
                 aria-label="Filter branches/tags"
                 placeholder="Filter branches/tags"
                 type="text"
          >
        </div>

        <div class="SelectMenu-tabs" role="tablist" data-target="input-demux.control" >
          <button class="SelectMenu-tab" type="button" role="tab" aria-selected="true">Branches</button>
          <button class="SelectMenu-tab" type="button" role="tab">Tags</button>
        </div>

        <div role="tabpanel" id="ref-list-branches" data-filter-placeholder="Filter branches/tags" class="d-flex flex-column flex-auto overflow-auto" tabindex="">
          <ref-selector
            type="branch"
            data-targets="input-demux.sinks"
            data-action="
              input-entered:ref-selector#inputEntered
              tab-selected:ref-selector#tabSelected
              focus-list:ref-selector#focusFirstListMember
            "
            query-endpoint="/devbridge/jQuery-Autocomplete/refs"
            
            cache-key="v0:1430696260.0"
            current-committish="bWFzdGVy"
            default-branch="bWFzdGVy"
            name-with-owner="ZGV2YnJpZGdlL2pRdWVyeS1BdXRvY29tcGxldGU="
          >

              <template data-target="ref-selector.noMatchTemplate">
    <div class="SelectMenu-message">Nothing to show</div>
</template>


            <!-- TODO: this max-height is necessary or else the branch list won't scroll.  why? -->
            <div data-target="ref-selector.listContainer" role="menu" class="SelectMenu-list " style="max-height: 330px">
              <div class="SelectMenu-loading pt-3 pb-0" aria-label="Menu is loading">
                <svg style="box-sizing: content-box; color: var(--color-icon-primary);" viewBox="0 0 16 16" fill="none" width="32" height="32" class="anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
              </div>
            </div>

              <template data-target="ref-selector.itemTemplate">
  <a href="https://github.com/devbridge/jQuery-Autocomplete/blob/{{ urlEncodedRefName }}/dist/jquery.autocomplete.min.js" class="SelectMenu-item" role="menuitemradio" rel="nofollow" aria-checked="{{ isCurrent }}" data-index="{{ index }}">
    <svg class="octicon octicon-check SelectMenu-icon SelectMenu-icon--check" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path></svg>
    <span class="flex-1 css-truncate css-truncate-overflow {{ isFilteringClass }}">{{ refName }}</span>
    <span hidden="{{ isNotDefault }}" class="Label Label--secondary flex-self-start">default</span>
  </a>
</template>


              <footer class="SelectMenu-footer"><a href="/devbridge/jQuery-Autocomplete/branches">View all branches</a></footer>
          </ref-selector>

        </div>

        <div role="tabpanel" id="tags-menu" data-filter-placeholder="Find a tag" class="d-flex flex-column flex-auto overflow-auto" tabindex="" hidden>
          <ref-selector
            type="tag"
            data-action="
              input-entered:ref-selector#inputEntered
              tab-selected:ref-selector#tabSelected
              focus-list:ref-selector#focusFirstListMember
            "
            data-targets="input-demux.sinks"
            query-endpoint="/devbridge/jQuery-Autocomplete/refs"
            cache-key="v0:1430696260.0"
            current-committish="bWFzdGVy"
            default-branch="bWFzdGVy"
            name-with-owner="ZGV2YnJpZGdlL2pRdWVyeS1BdXRvY29tcGxldGU="
          >

            <template data-target="ref-selector.noMatchTemplate">
              <div class="SelectMenu-message" data-index="{{ index }}">Nothing to show</div>
            </template>

              <template data-target="ref-selector.itemTemplate">
  <a href="https://github.com/devbridge/jQuery-Autocomplete/blob/{{ urlEncodedRefName }}/dist/jquery.autocomplete.min.js" class="SelectMenu-item" role="menuitemradio" rel="nofollow" aria-checked="{{ isCurrent }}" data-index="{{ index }}">
    <svg class="octicon octicon-check SelectMenu-icon SelectMenu-icon--check" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path></svg>
    <span class="flex-1 css-truncate css-truncate-overflow {{ isFilteringClass }}">{{ refName }}</span>
    <span hidden="{{ isNotDefault }}" class="Label Label--secondary flex-self-start">default</span>
  </a>
</template>


            <div data-target="ref-selector.listContainer" role="menu" class="SelectMenu-list" style="max-height: 330px">
              <div class="SelectMenu-loading pt-3 pb-0" aria-label="Menu is loading">
                <svg style="box-sizing: content-box; color: var(--color-icon-primary);" viewBox="0 0 16 16" fill="none" width="32" height="32" class="anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
              </div>
            </div>
              <footer class="SelectMenu-footer"><a href="/devbridge/jQuery-Autocomplete/tags">View all tags</a></footer>
          </ref-selector>
        </div>
      </tab-container>
    </input-demux>
  </div>
</div>

  </details>

</div>

      <h2 id="blob-path" class="breadcrumb flex-auto flex-self-center min-width-0 text-normal mx-2 width-full width-md-auto flex-order-1 flex-md-order-none mt-3 mt-md-0">
        <span class="js-repo-root text-bold"><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="true" href="/devbridge/jQuery-Autocomplete"><span>jQuery-Autocomplete</span></a></span></span><span class="separator">/</span><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="true" href="/devbridge/jQuery-Autocomplete/tree/master/dist"><span>dist</span></a></span><span class="separator">/</span><strong class="final-path">jquery.autocomplete.min.js</strong>
          <span class="separator">/</span><details class="details-reset details-overlay d-inline" id="jumpto-symbol-select-menu">
  <summary class="btn-link Link--secondary css-truncate" aria-haspopup="true" data-hotkey="r" data-hydro-click="{&quot;event_type&quot;:&quot;code_navigation.click_on_blob_definitions&quot;,&quot;payload&quot;:{&quot;action&quot;:&quot;click_on_blob_definitions&quot;,&quot;repository_id&quot;:688157,&quot;ref&quot;:&quot;master&quot;,&quot;language&quot;:&quot;JavaScript&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="04d997b946371ac63633d4797b1711057823ec3a88fac8b920acebf1e84f3726">
      <svg class="octicon octicon-code" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06l-4.25-4.25z"></path></svg>
    <span data-menu-button>Jump to</span>
    <span class="dropdown-caret"></span>
  </summary>
  <details-menu class="SelectMenu SelectMenu--hasFilter" role="menu">
    <div class="SelectMenu-modal">
      <header class="SelectMenu-header">
        <span class="SelectMenu-title">Code definitions</span>
        <button class="SelectMenu-closeButton" type="button" data-toggle-for="jumpto-symbol-select-menu">
          <svg aria-label="Close menu" class="octicon octicon-x" viewBox="0 0 16 16" version="1.1" width="16" height="16" role="img"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg>
        </button>
      </header>
      <div class="SelectMenu-list">
          <div class="SelectMenu-blankslate">
            <p class="mb-0 color-text-secondary">
              No definitions found in this file.
            </p>
          </div>
        <div data-filterable-for="jumpto-symbols-filter-field" data-filterable-type="substring">
        </div>
      </div>
      <footer class="SelectMenu-footer">
        <div class="d-flex flex-justify-between">
          Code navigation not available for this commit
          <svg class="octicon octicon-dot-fill text-light-gray" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8z"></path></svg>
        </div>
      </footer>
    </div>
  </details-menu>
</details>

      </h2>
      <a href="/devbridge/jQuery-Autocomplete/find/master"
            class="js-pjax-capture-input btn mr-2 d-none d-md-block"
            data-pjax
            data-hotkey="t">
        Go to file
      </a>

      <details id="blob-more-options-details" class="details-overlay details-reset position-relative">
  <summary role="button" class="btn ">          <svg aria-label="More options" class="octicon octicon-kebab-horizontal" height="16" viewBox="0 0 16 16" version="1.1" width="16" role="img"><path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path></svg>
</summary>
  <div>          <ul class="dropdown-menu dropdown-menu-sw">
            <li class="d-block d-md-none">
              <a class="dropdown-item d-flex flex-items-baseline" data-hydro-click="{&quot;event_type&quot;:&quot;repository.click&quot;,&quot;payload&quot;:{&quot;target&quot;:&quot;FIND_FILE_BUTTON&quot;,&quot;repository_id&quot;:688157,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="ced969f2f54c88702076b3a7535cfd5e42367c26535e3e3986639d5e4e3dc0cd" data-ga-click="Repository, find file, location:repo overview" data-hotkey="t" data-pjax="true" href="/devbridge/jQuery-Autocomplete/find/master">
                <span class="flex-auto">Go to file</span>
                <span class="text-small color-text-secondary" aria-hidden="true">T</span>
</a>            </li>
            <li data-toggle-for="blob-more-options-details">
              <button type="button" data-toggle-for="jumpto-line-details-dialog" class="btn-link dropdown-item">
                <span class="d-flex flex-items-baseline">
                  <span class="flex-auto">Go to line</span>
                  <span class="text-small color-text-secondary" aria-hidden="true">L</span>
                </span>
              </button>
            </li>
            <li data-toggle-for="blob-more-options-details">
              <button type="button" data-toggle-for="jumpto-symbol-select-menu" class="btn-link dropdown-item">
                <span class="d-flex flex-items-baseline">
                  <span class="flex-auto">Go to definition</span>
                  <span class="text-small color-text-secondary" aria-hidden="true">R</span>
                </span>
              </button>
            </li>
            <li class="dropdown-divider" role="none"></li>
            <li>
              <clipboard-copy value="dist/jquery.autocomplete.min.js" class="dropdown-item cursor-pointer" data-toggle-for="blob-more-options-details">
                Copy path
              </clipboard-copy>
            </li>
            <li>
              <clipboard-copy value="https://github.com/devbridge/jQuery-Autocomplete/blob/8138252b4c6fa994a0d438630ea375308e7f3b11/dist/jquery.autocomplete.min.js" class="dropdown-item cursor-pointer" data-toggle-for="blob-more-options-details" >
                <span class="d-flex flex-items-baseline">
                  <span class="flex-auto">Copy permalink</span>
                </span>
              </clipboard-copy>
            </li>
          </ul>
</div>
</details>    </div>



    <div class="Box d-flex flex-column flex-shrink-0 mb-3">
      
  <div class="Box-header Box-header--blue Details js-details-container">
      <div class="d-flex flex-items-center">
        <span class="flex-shrink-0 ml-n1 mr-n1 mt-n1 mb-n1">
          <a rel="contributor" data-skip-pjax="true" data-hovercard-type="user" data-hovercard-url="/users/geoffrosen/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/geoffrosen"><img class="avatar avatar-user" src="https://avatars.githubusercontent.com/u/11847434?s=48&amp;v=4" width="24" height="24" alt="@geoffrosen" /></a>
        </span>
        <div class="flex-1 d-flex flex-items-center ml-3 min-width-0">
          <div class="css-truncate css-truncate-overflow">
            <a class="text-bold Link--primary" rel="contributor" data-hovercard-type="user" data-hovercard-url="/users/geoffrosen/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/geoffrosen">geoffrosen</a>

              <span>
                <a data-pjax="true" title="Updated onHint" class="Link--secondary" href="/devbridge/jQuery-Autocomplete/commit/75fd1b391de8a5aac82e3c0863d129a466388997">Updated onHint</a>
              </span>
          </div>


          <span class="ml-2">
            <include-fragment accept="text/fragment+html" src="/devbridge/jQuery-Autocomplete/commit/75fd1b391de8a5aac82e3c0863d129a466388997/rollup?direction=e" class="d-inline"></include-fragment>
          </span>
        </div>
        <div class="ml-3 d-flex flex-shrink-0 flex-items-center flex-justify-end color-text-secondary no-wrap">
          <span class="d-none d-md-inline">
            <span>Latest commit</span>
            <a class="text-small text-mono Link--secondary" href="/devbridge/jQuery-Autocomplete/commit/75fd1b391de8a5aac82e3c0863d129a466388997" data-pjax>75fd1b3</a>
            <span itemprop="dateModified"><relative-time datetime="2021-01-24T20:11:59Z" class="no-wrap">Jan 24, 2021</relative-time></span>
          </span>

          <a data-pjax href="/devbridge/jQuery-Autocomplete/commits/master/dist/jquery.autocomplete.min.js" class="ml-3 no-wrap Link--primary no-underline">
            <svg class="octicon octicon-history text-gray" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M1.643 3.143L.427 1.927A.25.25 0 000 2.104V5.75c0 .138.112.25.25.25h3.646a.25.25 0 00.177-.427L2.715 4.215a6.5 6.5 0 11-1.18 4.458.75.75 0 10-1.493.154 8.001 8.001 0 101.6-5.684zM7.75 4a.75.75 0 01.75.75v2.992l2.028.812a.75.75 0 01-.557 1.392l-2.5-1A.75.75 0 017 8.25v-3.5A.75.75 0 017.75 4z"></path></svg>
            <span class="d-none d-sm-inline">
              <strong>History</strong>
            </span>
          </a>
        </div>
      </div>

  </div>

  <div class="Box-body d-flex flex-items-center flex-auto border-bottom-0 flex-wrap" >
    <details class="details-reset details-overlay details-overlay-dark lh-default color-text-primary float-left mr-3" id="blob_contributors_box">
      <summary class="Link--primary">
        <svg class="octicon octicon-people text-gray" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M5.5 3.5a2 2 0 100 4 2 2 0 000-4zM2 5.5a3.5 3.5 0 115.898 2.549 5.507 5.507 0 013.034 4.084.75.75 0 11-1.482.235 4.001 4.001 0 00-7.9 0 .75.75 0 01-1.482-.236A5.507 5.507 0 013.102 8.05 3.49 3.49 0 012 5.5zM11 4a.75.75 0 100 1.5 1.5 1.5 0 01.666 2.844.75.75 0 00-.416.672v.352a.75.75 0 00.574.73c1.2.289 2.162 1.2 2.522 2.372a.75.75 0 101.434-.44 5.01 5.01 0 00-2.56-3.012A3 3 0 0011 4z"></path></svg>
        <strong>3</strong>
        
        contributors
      </summary>
      <details-dialog
        class="Box Box--overlay d-flex flex-column anim-fade-in fast"
        aria-label="Users who have contributed to this file"
        src="/devbridge/jQuery-Autocomplete/contributors-list/master/dist/jquery.autocomplete.min.js" preload>
        <div class="Box-header">
          <button class="Box-btn-octicon btn-octicon float-right" type="button" aria-label="Close dialog" data-close-dialog>
            <svg class="octicon octicon-x" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg>
          </button>
          <h3 class="Box-title">
            Users who have contributed to this file
          </h3>
        </div>
        <include-fragment>
          <svg style="box-sizing: content-box; color: var(--color-icon-primary);" viewBox="0 0 16 16" fill="none" width="32" height="32" class="my-3 mx-auto d-block anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
        </include-fragment>
      </details-dialog>
    </details>
      <span class="">
    <a class="avatar-link" data-hovercard-type="user" data-hovercard-url="/users/tkirda/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/devbridge/jQuery-Autocomplete/commits/master/dist/jquery.autocomplete.min.js?author=tkirda">
      <img class="avatar mr-2 avatar-user" src="https://avatars.githubusercontent.com/u/282935?s=48&amp;v=4" width="24" height="24" alt="@tkirda" /> 
</a>    <a class="avatar-link" data-hovercard-type="user" data-hovercard-url="/users/swey/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/devbridge/jQuery-Autocomplete/commits/master/dist/jquery.autocomplete.min.js?author=swey">
      <img class="avatar mr-2 avatar-user" src="https://avatars.githubusercontent.com/u/568574?s=48&amp;v=4" width="24" height="24" alt="@swey" /> 
</a>    <a class="avatar-link" data-hovercard-type="user" data-hovercard-url="/users/geoffrosen/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/devbridge/jQuery-Autocomplete/commits/master/dist/jquery.autocomplete.min.js?author=geoffrosen">
      <img class="avatar mr-2 avatar-user" src="https://avatars.githubusercontent.com/u/11847434?s=48&amp;v=4" width="24" height="24" alt="@geoffrosen" /> 
</a>
</span>

  </div>
    </div>








  <div data-target="readme-toc.content" class="Box mt-3 position-relative
">
  
      
<div class="Box-header py-2 d-flex flex-column flex-shrink-0 flex-md-row flex-md-items-center">
  <div class="text-mono f6 flex-auto pr-3 flex-order-2 flex-md-order-1 mt-2 mt-md-0">

      8 lines (8 sloc)
      <span class="file-info-divider"></span>
    13 KB
  </div>

  <div class="d-flex py-1 py-md-0 flex-auto flex-order-1 flex-md-order-2 flex-sm-grow-0 flex-justify-between">

    <div class="BtnGroup">
      <a href="/devbridge/jQuery-Autocomplete/raw/master/dist/jquery.autocomplete.min.js" id="raw-url" role="button" class="btn-sm btn BtnGroup-item ">Raw</a>
        <a href="/devbridge/jQuery-Autocomplete/blame/master/dist/jquery.autocomplete.min.js" data-hotkey="b" role="button" class="js-update-url-with-hash btn-sm btn BtnGroup-item ">Blame</a>
    </div>

    <div>
          <a class="btn-octicon tooltipped tooltipped-nw js-remove-unless-platform"
             data-platforms="windows,mac"
             href="https://desktop.github.com"
             aria-label="Open this file in GitHub Desktop"
             data-ga-click="Repository, open with desktop">
              <svg class="octicon octicon-device-desktop" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M1.75 2.5h12.5a.25.25 0 01.25.25v7.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25v-7.5a.25.25 0 01.25-.25zM14.25 1H1.75A1.75 1.75 0 000 2.75v7.5C0 11.216.784 12 1.75 12h3.727c-.1 1.041-.52 1.872-1.292 2.757A.75.75 0 004.75 16h6.5a.75.75 0 00.565-1.243c-.772-.885-1.193-1.716-1.292-2.757h3.727A1.75 1.75 0 0016 10.25v-7.5A1.75 1.75 0 0014.25 1zM9.018 12H6.982a5.72 5.72 0 01-.765 2.5h3.566a5.72 5.72 0 01-.765-2.5z"></path></svg>
          </a>

          <a href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete%2Fblob%2Fmaster%2Fdist%2Fjquery.autocomplete.min.js" class="btn-octicon disabled tooltipped tooltipped-nw"
            aria-label="You must be signed in to make or propose changes">
            <svg class="octicon octicon-pencil" height="16" viewBox="0 0 16 16" version="1.1" width="16" aria-hidden="true"><path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path></svg>
          </a>
          <a href="/login?return_to=%2Fdevbridge%2FjQuery-Autocomplete%2Fblob%2Fmaster%2Fdist%2Fjquery.autocomplete.min.js" class="btn-octicon btn-octicon-danger disabled tooltipped tooltipped-nw"
            aria-label="You must be signed in to make or propose changes">
            <svg class="octicon octicon-trash" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M6.5 1.75a.25.25 0 01.25-.25h2.5a.25.25 0 01.25.25V3h-3V1.75zm4.5 0V3h2.25a.75.75 0 010 1.5H2.75a.75.75 0 010-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75zM4.496 6.675a.75.75 0 10-1.492.15l.66 6.6A1.75 1.75 0 005.405 15h5.19c.9 0 1.652-.681 1.741-1.576l.66-6.6a.75.75 0 00-1.492-.149l-.66 6.6a.25.25 0 01-.249.225h-5.19a.25.25 0 01-.249-.225l-.66-6.6z"></path></svg>
          </a>
    </div>
  </div>
</div>


    
  <div itemprop="text" class="Box-body p-0 blob-wrapper data type-javascript  gist-border-0">
      
<table class="highlight tab-size js-file-line-container" data-tab-size="8" data-paste-markdown-skip>
      <tr>
        <td id="L1" class="blob-num js-line-number" data-line-number="1"></td>
        <td id="LC1" class="blob-code blob-code-inner js-file-line"><span class=pl-c>/**</span></td>
      </tr>
      <tr>
        <td id="L2" class="blob-num js-line-number" data-line-number="2"></td>
        <td id="LC2" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*  Ajax Autocomplete for jQuery, version 1.4.11</span></td>
      </tr>
      <tr>
        <td id="L3" class="blob-num js-line-number" data-line-number="3"></td>
        <td id="LC3" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*  (c) 2017 Tomas Kirda</span></td>
      </tr>
      <tr>
        <td id="L4" class="blob-num js-line-number" data-line-number="4"></td>
        <td id="LC4" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*</span></td>
      </tr>
      <tr>
        <td id="L5" class="blob-num js-line-number" data-line-number="5"></td>
        <td id="LC5" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*  Ajax Autocomplete for jQuery is freely distributable under the terms of an MIT-style license.</span></td>
      </tr>
      <tr>
        <td id="L6" class="blob-num js-line-number" data-line-number="6"></td>
        <td id="LC6" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*  For details, see the web site: https://github.com/devbridge/jQuery-Autocomplete</span></td>
      </tr>
      <tr>
        <td id="L7" class="blob-num js-line-number" data-line-number="7"></td>
        <td id="LC7" class="blob-code blob-code-inner js-file-line"><span class=pl-c>*/</span></td>
      </tr>
      <tr>
        <td id="L8" class="blob-num js-line-number" data-line-number="8"></td>
        <td id="LC8" class="blob-code blob-code-inner js-file-line"><span class=pl-c1>!</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s>&quot;use strict&quot;</span><span class=pl-kos>;</span><span class=pl-s>&quot;function&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>define</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>define</span><span class=pl-kos>.</span><span class=pl-c1>amd</span>?<span class=pl-en>define</span><span class=pl-kos>(</span><span class=pl-kos>[</span><span class=pl-s>&quot;jquery&quot;</span><span class=pl-kos>]</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>)</span>:<span class=pl-s>&quot;object&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>exports</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s>&quot;function&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-en>require</span>?<span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-en>require</span><span class=pl-kos>(</span><span class=pl-s>&quot;jquery&quot;</span><span class=pl-kos>)</span><span class=pl-kos>)</span>:<span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s1>jQuery</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s>&quot;use strict&quot;</span><span class=pl-kos>;</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-en>escapeRegExChars</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span> <span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span><span class=pl-kos>[</span>|<span class=pl-cce>\\</span>{}()[<span class=pl-cce>\]</span>^$+*?.<span class=pl-kos>]</span><span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;\\$&amp;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>createNode</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>document</span><span class=pl-kos>.</span><span class=pl-en>createElement</span><span class=pl-kos>(</span><span class=pl-s>&quot;div&quot;</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>className</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>style</span><span class=pl-kos>.</span><span class=pl-c1>position</span><span class=pl-c1>=</span><span class=pl-s>&quot;absolute&quot;</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>style</span><span class=pl-kos>.</span><span class=pl-c1>display</span><span class=pl-c1>=</span><span class=pl-s>&quot;none&quot;</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-c1>27</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-c1>9</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-c1>13</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-c1>38</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-c1>39</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-c1>=</span><span class=pl-c1>40</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>noop</span><span class=pl-kos>;</span><span class=pl-k>function</span> <span class=pl-en>r</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>badQueries</span><span class=pl-c1>=</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>timeoutId</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>cachedResponse</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>onChange</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>isLocal</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>noSuggestionsContainer</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>extend</span><span class=pl-kos>(</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-c1>defaults</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-c1>selected</span>:<span class=pl-s>&quot;autocomplete-selected&quot;</span><span class=pl-kos>,</span><span class=pl-c1>suggestion</span>:<span class=pl-s>&quot;autocomplete-suggestion&quot;</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hint</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hintValue</span><span class=pl-c1>=</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>initialize</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>setOptions</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-c1>utils</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>Autocomplete</span><span class=pl-c1>=</span><span class=pl-s1>r</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-c1>defaults</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-c1>ajaxSettings</span>:<span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-c1>autoSelectFirst</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>appendTo</span>:<span class=pl-s>&quot;body&quot;</span><span class=pl-kos>,</span><span class=pl-c1>serviceUrl</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>lookup</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>onSelect</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>onHint</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>width</span>:<span class=pl-s>&quot;auto&quot;</span><span class=pl-kos>,</span><span class=pl-c1>minChars</span>:<span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>maxHeight</span>:<span class=pl-c1>300</span><span class=pl-kos>,</span><span class=pl-c1>deferRequestBy</span>:<span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-c1>params</span>:<span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>formatResult</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>!</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-k>return</span> <span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>;</span><span class=pl-k>var</span> <span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s>&quot;(&quot;</span><span class=pl-c1>+</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>escapeRegExChars</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-c1>+</span><span class=pl-s>&quot;)&quot;</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-k>new</span> <span class=pl-v>RegExp</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-s>&quot;gi&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s>&quot;&lt;strong&gt;$1&lt;/strong&gt;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span>&amp;<span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;&amp;amp;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span>&lt;<span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;&amp;lt;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span>&gt;<span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;&amp;gt;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span>&quot;<span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;&amp;quot;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>replace</span><span class=pl-kos>(</span><span class=pl-pds><span class=pl-c1>/</span>&amp;lt;<span class=pl-kos>(</span><span class=pl-cce>\/</span>?strong<span class=pl-kos>)</span>&amp;gt;<span class=pl-c1>/</span>g</span><span class=pl-kos>,</span><span class=pl-s>&quot;&lt;$1&gt;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>formatGroup</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span><span class=pl-s>&#39;&lt;div class=&quot;autocomplete-group&quot;&gt;&#39;</span><span class=pl-c1>+</span><span class=pl-s1>e</span><span class=pl-c1>+</span><span class=pl-s>&quot;&lt;/div&gt;&quot;</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-c1>delimiter</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>zIndex</span>:<span class=pl-c1>9999</span><span class=pl-kos>,</span><span class=pl-c1>type</span>:<span class=pl-s>&quot;GET&quot;</span><span class=pl-kos>,</span><span class=pl-c1>noCache</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>onSearchStart</span>:<span class=pl-s1>l</span><span class=pl-kos>,</span><span class=pl-c1>onSearchComplete</span>:<span class=pl-s1>l</span><span class=pl-kos>,</span><span class=pl-c1>onSearchError</span>:<span class=pl-s1>l</span><span class=pl-kos>,</span><span class=pl-c1>preserveInput</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>containerClass</span>:<span class=pl-s>&quot;autocomplete-suggestions&quot;</span><span class=pl-kos>,</span><span class=pl-c1>tabDisabled</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>dataType</span>:<span class=pl-s>&quot;text&quot;</span><span class=pl-kos>,</span><span class=pl-c1>currentRequest</span>:<span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-c1>triggerSelectOnValidInput</span>:<span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-c1>preventBadQueries</span>:<span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-en>lookupFilter</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>!==</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>indexOf</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-c1>paramName</span>:<span class=pl-s>&quot;query&quot;</span><span class=pl-kos>,</span><span class=pl-en>transformResult</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span><span class=pl-s>&quot;string&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>e</span>?<span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>parseJSON</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span>:<span class=pl-s1>e</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-c1>showNoSuggestionNotice</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-c1>noSuggestionNotice</span>:<span class=pl-s>&quot;No results&quot;</span><span class=pl-kos>,</span><span class=pl-c1>orientation</span>:<span class=pl-s>&quot;bottom&quot;</span><span class=pl-kos>,</span><span class=pl-c1>forceFixPosition</span>:<span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-c1>prototype</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-en>initialize</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>suggestion</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>selected</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>.</span><span class=pl-en>setAttribute</span><span class=pl-kos>(</span><span class=pl-s>&quot;autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-s>&quot;off&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>noSuggestionsContainer</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s>&#39;&lt;div class=&quot;autocomplete-no-suggestion&quot;&gt;&lt;/div&gt;&#39;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>html</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>noSuggestionNotice</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>get</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-c1>=</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-c1>utils</span><span class=pl-kos>.</span><span class=pl-en>createNode</span><span class=pl-kos>(</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-c1>containerClass</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>appendTo</span><span class=pl-kos>(</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-c1>appendTo</span><span class=pl-c1>||</span><span class=pl-s>&quot;body&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s>&quot;auto&quot;</span><span class=pl-c1>!==</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;width&quot;</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;mouseover.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>activate</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>data</span><span class=pl-kos>(</span><span class=pl-s>&quot;index&quot;</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;mouseout.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>children</span><span class=pl-kos>(</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>removeClass</span><span class=pl-kos>(</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;click.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>data</span><span class=pl-kos>(</span><span class=pl-s>&quot;index&quot;</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;click.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-en>clearTimeout</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>blurTimeoutId</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>fixPositionCapture</span><span class=pl-c1>=</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>fixPosition</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>window</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;resize.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>fixPositionCapture</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;keydown.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onKeyPress</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;keyup.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onKeyUp</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;blur.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onBlur</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;focus.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onFocus</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;change.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onKeyUp</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>on</span><span class=pl-kos>(</span><span class=pl-s>&quot;input.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>onKeyUp</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onFocus</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-c1>||</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>fixPosition</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&gt;=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>minChars</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onValueChange</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onBlur</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>getQuery</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>blurTimeoutId</span><span class=pl-c1>=</span><span class=pl-en>setTimeout</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>!==</span><span class=pl-s1>n</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>onInvalidateSelection</span><span class=pl-c1>||</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>noop</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-c1>200</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>abortAjax</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentRequest</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentRequest</span><span class=pl-kos>.</span><span class=pl-en>abort</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentRequest</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>setOptions</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>extend</span><span class=pl-kos>(</span><span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>isLocal</span><span class=pl-c1>=</span><span class=pl-v>Array</span><span class=pl-kos>.</span><span class=pl-en>isArray</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>lookup</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>isLocal</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>lookup</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>verifySuggestionsFormat</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>lookup</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>orientation</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>validateOrientation</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>orientation</span><span class=pl-kos>,</span><span class=pl-s>&quot;bottom&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-kos>{</span><span class=pl-s>&quot;max-height&quot;</span>:<span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>maxHeight</span><span class=pl-c1>+</span><span class=pl-s>&quot;px&quot;</span><span class=pl-kos>,</span><span class=pl-c1>width</span>:<span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-c1>+</span><span class=pl-s>&quot;px&quot;</span><span class=pl-kos>,</span><span class=pl-s>&quot;z-index&quot;</span>:<span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>zIndex</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>clearCache</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>cachedResponse</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>badQueries</span><span class=pl-c1>=</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>clear</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>clearCache</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>=</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>disable</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-en>clearTimeout</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>abortAjax</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>enable</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>fixPosition</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>parent</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>get</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-c1>===</span><span class=pl-smi>document</span><span class=pl-kos>.</span><span class=pl-c1>body</span><span class=pl-c1>||</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>forceFixPosition</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>orientation</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>outerHeight</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>outerHeight</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>offset</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-c1>top</span>:<span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-kos>,</span><span class=pl-c1>left</span>:<span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>left</span><span class=pl-kos>}</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-s>&quot;auto&quot;</span><span class=pl-c1>===</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>l</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>window</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>height</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>window</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>scrollTop</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>h</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-s1>r</span><span class=pl-c1>+</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-c1>-</span><span class=pl-s1>n</span><span class=pl-kos>,</span><span class=pl-s1>c</span><span class=pl-c1>=</span><span class=pl-s1>r</span><span class=pl-c1>+</span><span class=pl-s1>l</span><span class=pl-c1>-</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-c1>+</span><span class=pl-s1>o</span><span class=pl-c1>+</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-v>Math</span><span class=pl-kos>.</span><span class=pl-en>max</span><span class=pl-kos>(</span><span class=pl-s1>h</span><span class=pl-kos>,</span><span class=pl-s1>c</span><span class=pl-kos>)</span><span class=pl-c1>===</span><span class=pl-s1>h</span>?<span class=pl-s>&quot;top&quot;</span>:<span class=pl-s>&quot;bottom&quot;</span><span class=pl-kos>}</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-c1>+=</span><span class=pl-s>&quot;top&quot;</span><span class=pl-c1>===</span><span class=pl-s1>i</span>?<span class=pl-c1>-</span><span class=pl-s1>n</span>:<span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>!==</span><span class=pl-smi>document</span><span class=pl-kos>.</span><span class=pl-c1>body</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>g</span><span class=pl-kos>,</span><span class=pl-s1>d</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;opacity&quot;</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>||</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;opacity&quot;</span><span class=pl-kos>,</span><span class=pl-c1>0</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>show</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>g</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>offsetParent</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>offset</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-c1>-=</span><span class=pl-s1>g</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>top</span><span class=pl-c1>+=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>scrollTop</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>left</span><span class=pl-c1>-=</span><span class=pl-s1>g</span><span class=pl-kos>.</span><span class=pl-c1>left</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>||</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;opacity&quot;</span><span class=pl-kos>,</span><span class=pl-s1>d</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-s>&quot;auto&quot;</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>outerWidth</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-c1>+</span><span class=pl-s>&quot;px&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>isCursorAtEnd</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>.</span><span class=pl-c1>selectionStart</span><span class=pl-kos>;</span><span class=pl-k>return</span><span class=pl-s>&quot;number&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>s</span>?<span class=pl-s1>s</span><span class=pl-c1>===</span><span class=pl-s1>e</span>:<span class=pl-c1>!</span><span class=pl-smi>document</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>||</span><span class=pl-kos>(</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-c1>=</span><span class=pl-smi>document</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-kos>.</span><span class=pl-en>createRange</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>moveStart</span><span class=pl-kos>(</span><span class=pl-s>&quot;character&quot;</span><span class=pl-kos>,</span><span class=pl-c1>-</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-c1>===</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>text</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onKeyPress</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-c1>||</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>||</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>which</span><span class=pl-c1>!==</span><span class=pl-s1>u</span><span class=pl-c1>||</span><span class=pl-c1>!</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>!</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>switch</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>which</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>case</span> <span class=pl-s1>s</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>;</span><span class=pl-k>case</span> <span class=pl-s1>a</span>:<span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hint</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onHint</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>isCursorAtEnd</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>selectHint</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>}</span><span class=pl-k>return</span><span class=pl-kos>;</span><span class=pl-k>case</span> <span class=pl-s1>i</span>:<span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hint</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onHint</span><span class=pl-kos>)</span><span class=pl-k>return</span> <span class=pl-k>void</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>selectHint</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-k>return</span> <span class=pl-k>void</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>tabDisabled</span><span class=pl-kos>)</span><span class=pl-k>return</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>;</span><span class=pl-k>case</span> <span class=pl-s1>n</span>:<span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-k>return</span> <span class=pl-k>void</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>;</span><span class=pl-k>case</span> <span class=pl-s1>o</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>moveUp</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>;</span><span class=pl-k>case</span> <span class=pl-s1>u</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>moveDown</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>break</span><span class=pl-kos>;</span><span class=pl-k>default</span>:<span class=pl-k>return</span><span class=pl-kos>}</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>stopImmediatePropagation</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>preventDefault</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-k>else</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>suggest</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onKeyUp</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>!</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>disabled</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>switch</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>which</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>case</span> <span class=pl-s1>o</span>:<span class=pl-k>case</span> <span class=pl-s1>u</span>:<span class=pl-k>return</span><span class=pl-kos>}</span><span class=pl-en>clearTimeout</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>!==</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>findBestHint</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>deferRequestBy</span><span class=pl-c1>&gt;</span><span class=pl-c1>0</span>?<span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-c1>=</span><span class=pl-en>setTimeout</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>onValueChange</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>deferRequestBy</span><span class=pl-kos>)</span>:<span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>onValueChange</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onValueChange</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>ignoreValueChange</span><span class=pl-kos>)</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>ignoreValueChange</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>;</span><span class=pl-k>else</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getQuery</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>!==</span><span class=pl-s1>i</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>onInvalidateSelection</span><span class=pl-c1>||</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>noop</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-en>clearTimeout</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>triggerSelectOnValidInput</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>isExactMatch</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span>?<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>)</span>:<span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&lt;</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>minChars</span>?<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getSuggestions</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>isExactMatch</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>e</span><span class=pl-kos>[</span><span class=pl-c1>0</span><span class=pl-kos>]</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-c1>===</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>getQuery</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>delimiter</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>i</span>?<span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>split</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>trim</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>[</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>]</span><span class=pl-kos>)</span><span class=pl-kos>)</span>:<span class=pl-s1>e</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>getSuggestionsLocal</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>lookupFilter</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-en>parseInt</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>lookupLimit</span><span class=pl-kos>,</span><span class=pl-c1>10</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-c1>suggestions</span>:<span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>grep</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>lookup</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span> <span class=pl-s1>o</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&gt;</span><span class=pl-s1>a</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>.</span><span class=pl-en>slice</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>getSuggestions</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-c1>=</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-c1>=</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>serviceUrl</span><span class=pl-kos>;</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>params</span><span class=pl-kos>[</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>paramName</span><span class=pl-kos>]</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-c1>!==</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchStart</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>params</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>ignoreParams</span>?<span class=pl-c1>null</span>:<span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>params</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>lookup</span><span class=pl-kos>)</span>?<span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-en>lookup</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>suggest</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchComplete</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span>:<span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>isLocal</span>?<span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>getSuggestionsLocal</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span>:<span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>l</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>l</span><span class=pl-c1>=</span><span class=pl-s1>l</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>l</span><span class=pl-c1>+</span><span class=pl-s>&quot;?&quot;</span><span class=pl-c1>+</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>param</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-c1>||</span><span class=pl-kos>{</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>cachedResponse</span><span class=pl-kos>[</span><span class=pl-s1>n</span><span class=pl-kos>]</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>&amp;&amp;</span><span class=pl-v>Array</span><span class=pl-kos>.</span><span class=pl-en>isArray</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span>?<span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>suggest</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchComplete</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>)</span>:<span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>isBadQuery</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span>?<span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchComplete</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>)</span>:<span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>abortAjax</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-kos>{</span><span class=pl-c1>url</span>:<span class=pl-s1>l</span><span class=pl-kos>,</span><span class=pl-c1>data</span>:<span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-c1>type</span>:<span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>type</span><span class=pl-kos>,</span><span class=pl-c1>dataType</span>:<span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>dataType</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>extend</span><span class=pl-kos>(</span><span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>ajaxSettings</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>currentRequest</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>ajax</span><span class=pl-kos>(</span><span class=pl-s1>o</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>done</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-kos>;</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>currentRequest</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-en>transformResult</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-en>processResponse</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchComplete</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>fail</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>u</span><span class=pl-kos>.</span><span class=pl-c1>onSearchError</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-s1>a</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>isBadQuery</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>!</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>preventBadQueries</span><span class=pl-kos>)</span><span class=pl-k>return</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>;</span><span class=pl-k>for</span><span class=pl-kos>(</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>badQueries</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>;</span><span class=pl-s1>s</span><span class=pl-c1>--</span><span class=pl-kos>;</span><span class=pl-kos>)</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-c1>===</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>indexOf</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>[</span><span class=pl-s1>s</span><span class=pl-kos>]</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-k>return</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>;</span><span class=pl-k>return</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>hide</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onHide</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onHide</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-en>clearTimeout</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>onChangeTimeout</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onHint</span><span class=pl-kos>(</span><span class=pl-c1>null</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>suggest</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>groupBy</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>formatResult</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getQuery</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>suggestion</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>selected</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>noSuggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>h</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>beforeRender</span><span class=pl-kos>,</span><span class=pl-s1>c</span><span class=pl-c1>=</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>triggerSelectOnValidInput</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>isExactMatch</span><span class=pl-kos>(</span><span class=pl-s1>o</span><span class=pl-kos>)</span>?<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>)</span>:<span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>each</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>u</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-s1>i</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>c</span><span class=pl-c1>+=</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>data</span><span class=pl-kos>[</span><span class=pl-s1>i</span><span class=pl-kos>]</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>e</span><span class=pl-c1>===</span><span class=pl-s1>o</span>?<span class=pl-s>&quot;&quot;</span>:<span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>formatGroup</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>,</span><span class=pl-c1>0</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>c</span><span class=pl-c1>+=</span><span class=pl-s>&#39;&lt;div class=&quot;&#39;</span><span class=pl-c1>+</span><span class=pl-s1>a</span><span class=pl-c1>+</span><span class=pl-s>&#39;&quot; data-index=&quot;&#39;</span><span class=pl-c1>+</span><span class=pl-s1>t</span><span class=pl-c1>+</span><span class=pl-s>&#39;&quot;&gt;&#39;</span><span class=pl-c1>+</span><span class=pl-s1>n</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-c1>+</span><span class=pl-s>&quot;&lt;/div&gt;&quot;</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>adjustContainerWidth</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>r</span><span class=pl-kos>.</span><span class=pl-en>detach</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-kos>.</span><span class=pl-en>html</span><span class=pl-kos>(</span><span class=pl-s1>c</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>h</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>h</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>fixPosition</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-kos>.</span><span class=pl-en>show</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>autoSelectFirst</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-kos>.</span><span class=pl-en>scrollTop</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>l</span><span class=pl-kos>.</span><span class=pl-en>children</span><span class=pl-kos>(</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-s1>a</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>first</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>addClass</span><span class=pl-kos>(</span><span class=pl-s1>u</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>findBestHint</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-k>else</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>showNoSuggestionNotice</span>?<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>noSuggestions</span><span class=pl-kos>(</span><span class=pl-kos>)</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>noSuggestions</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>beforeRender</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>noSuggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>adjustContainerWidth</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-en>detach</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>empty</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>append</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>fixPosition</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>show</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>visible</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>adjustContainerWidth</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s>&quot;auto&quot;</span><span class=pl-c1>===</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>width</span>?<span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>outerWidth</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;width&quot;</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-c1>&gt;</span><span class=pl-c1>0</span>?<span class=pl-s1>e</span>:<span class=pl-c1>300</span><span class=pl-kos>)</span><span class=pl-kos>)</span>:<span class=pl-s>&quot;flex&quot;</span><span class=pl-c1>===</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>width</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-en>css</span><span class=pl-kos>(</span><span class=pl-s>&quot;width&quot;</span><span class=pl-kos>,</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>findBestHint</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-c1>null</span><span class=pl-kos>;</span><span class=pl-s1>e</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>each</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-c1>0</span><span class=pl-c1>===</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>indexOf</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>n</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-c1>!</span><span class=pl-s1>n</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onHint</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onHint</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onHint</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>;</span><span class=pl-s1>e</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>+</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>.</span><span class=pl-en>substr</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hintValue</span><span class=pl-c1>!==</span><span class=pl-s1>i</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hintValue</span><span class=pl-c1>=</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hint</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>verifySuggestionsFormat</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span> <span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s>&quot;string&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>e</span><span class=pl-kos>[</span><span class=pl-c1>0</span><span class=pl-kos>]</span>?<span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>map</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span><span class=pl-kos>{</span><span class=pl-c1>value</span>:<span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-c1>data</span>:<span class=pl-c1>null</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>)</span>:<span class=pl-s1>e</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>validateOrientation</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>trim</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>||</span><span class=pl-s>&quot;&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>toLowerCase</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>inArray</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-kos>[</span><span class=pl-s>&quot;auto&quot;</span><span class=pl-kos>,</span><span class=pl-s>&quot;bottom&quot;</span><span class=pl-kos>,</span><span class=pl-s>&quot;top&quot;</span><span class=pl-kos>]</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>processResponse</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>;</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>verifySuggestionsFormat</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>noCache</span><span class=pl-c1>||</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>cachedResponse</span><span class=pl-kos>[</span><span class=pl-s1>s</span><span class=pl-kos>]</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>preventBadQueries</span><span class=pl-c1>&amp;&amp;</span><span class=pl-c1>!</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>badQueries</span><span class=pl-kos>.</span><span class=pl-en>push</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getQuery</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>suggest</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>activate</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>selected</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-s1>n</span><span class=pl-kos>.</span><span class=pl-en>find</span><span class=pl-kos>(</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>suggestion</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>n</span><span class=pl-kos>.</span><span class=pl-en>find</span><span class=pl-kos>(</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>removeClass</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>!==</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>&gt;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span>?<span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-s1>o</span><span class=pl-kos>.</span><span class=pl-en>get</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>addClass</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span>:<span class=pl-c1>null</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>selectHint</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>inArray</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>hint</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>select</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>select</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>hide</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onSelect</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>moveUp</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>!==</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-kos>)</span><span class=pl-k>return</span> <span class=pl-c1>0</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span>?<span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>children</span><span class=pl-kos>(</span><span class=pl-s>&quot;.&quot;</span><span class=pl-c1>+</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>suggestion</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>first</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>removeClass</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>classes</span><span class=pl-kos>.</span><span class=pl-c1>selected</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>=</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>ignoreValueChange</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>1</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-k>void</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>findBestHint</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span>:<span class=pl-k>void</span> <span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>adjustScroll</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>moveDown</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>!==</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-c1>&amp;&amp;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>adjustScroll</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selectedIndex</span><span class=pl-c1>+</span><span class=pl-c1>1</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>adjustScroll</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>activate</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-k>if</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-kos>,</span><span class=pl-s1>a</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>outerHeight</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>offsetTop</span><span class=pl-kos>,</span><span class=pl-s1>o</span><span class=pl-c1>=</span><span class=pl-kos>(</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>scrollTop</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-c1>+</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>maxHeight</span><span class=pl-c1>-</span><span class=pl-s1>a</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>&lt;</span><span class=pl-s1>n</span>?<span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>scrollTop</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span>:<span class=pl-s1>i</span><span class=pl-c1>&gt;</span><span class=pl-s1>o</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>scrollTop</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-c1>-</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>maxHeight</span><span class=pl-c1>+</span><span class=pl-s1>a</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>preserveInput</span><span class=pl-c1>||</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>ignoreValueChange</span><span class=pl-c1>=</span><span class=pl-c1>!</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getValue</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>[</span><span class=pl-s1>e</span><span class=pl-kos>]</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onHint</span><span class=pl-kos>(</span><span class=pl-c1>null</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>onSelect</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>onSelect</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-kos>[</span><span class=pl-s1>e</span><span class=pl-kos>]</span><span class=pl-kos>;</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>getValue</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-c1>value</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-c1>===</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-c1>||</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>preserveInput</span><span class=pl-c1>||</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>val</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>onHint</span><span class=pl-kos>(</span><span class=pl-c1>null</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestions</span><span class=pl-c1>=</span><span class=pl-kos>[</span><span class=pl-kos>]</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>selection</span><span class=pl-c1>=</span><span class=pl-s1>i</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-en>isFunction</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-en>call</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>element</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>getValue</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>options</span><span class=pl-kos>.</span><span class=pl-c1>delimiter</span><span class=pl-kos>;</span><span class=pl-k>return</span> <span class=pl-s1>i</span>?<span class=pl-c1>1</span><span class=pl-c1>===</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-c1>=</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-c1>=</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>currentValue</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>split</span><span class=pl-kos>(</span><span class=pl-s1>i</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-c1>length</span>?<span class=pl-s1>t</span>:<span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-en>substr</span><span class=pl-kos>(</span><span class=pl-c1>0</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>-</span><span class=pl-s1>s</span><span class=pl-kos>[</span><span class=pl-s1>s</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-c1>-</span><span class=pl-c1>1</span><span class=pl-kos>]</span><span class=pl-kos>.</span><span class=pl-c1>length</span><span class=pl-kos>)</span><span class=pl-c1>+</span><span class=pl-s1>t</span>:<span class=pl-s1>t</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-en>dispose</span>:<span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>el</span><span class=pl-kos>.</span><span class=pl-en>off</span><span class=pl-kos>(</span><span class=pl-s>&quot;.autocomplete&quot;</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>removeData</span><span class=pl-kos>(</span><span class=pl-s>&quot;autocomplete&quot;</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>window</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>off</span><span class=pl-kos>(</span><span class=pl-s>&quot;resize.autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>fixPositionCapture</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-c1>suggestionsContainer</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>remove</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>fn</span><span class=pl-kos>.</span><span class=pl-en>devbridgeAutocomplete</span><span class=pl-c1>=</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-s1>e</span><span class=pl-kos>,</span><span class=pl-s1>s</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>return</span> <span class=pl-smi>arguments</span><span class=pl-kos>.</span><span class=pl-c1>length</span>?<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>each</span><span class=pl-kos>(</span><span class=pl-k>function</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>{</span><span class=pl-k>var</span> <span class=pl-s1>i</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-en>data</span><span class=pl-kos>(</span><span class=pl-s>&quot;autocomplete&quot;</span><span class=pl-kos>)</span><span class=pl-kos>;</span><span class=pl-s>&quot;string&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>e</span>?<span class=pl-s1>n</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s>&quot;function&quot;</span><span class=pl-c1>==</span><span class=pl-k>typeof</span> <span class=pl-s1>n</span><span class=pl-kos>[</span><span class=pl-s1>e</span><span class=pl-kos>]</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>n</span><span class=pl-kos>[</span><span class=pl-s1>e</span><span class=pl-kos>]</span><span class=pl-kos>(</span><span class=pl-s1>s</span><span class=pl-kos>)</span>:<span class=pl-kos>(</span><span class=pl-s1>n</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>n</span><span class=pl-kos>.</span><span class=pl-c1>dispose</span><span class=pl-c1>&amp;&amp;</span><span class=pl-s1>n</span><span class=pl-kos>.</span><span class=pl-en>dispose</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-c1>=</span><span class=pl-k>new</span> <span class=pl-s1>r</span><span class=pl-kos>(</span><span class=pl-smi>this</span><span class=pl-kos>,</span><span class=pl-s1>e</span><span class=pl-kos>)</span><span class=pl-kos>,</span><span class=pl-s1>i</span><span class=pl-kos>.</span><span class=pl-en>data</span><span class=pl-kos>(</span><span class=pl-s>&quot;autocomplete&quot;</span><span class=pl-kos>,</span><span class=pl-s1>n</span><span class=pl-kos>)</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span>:<span class=pl-smi>this</span><span class=pl-kos>.</span><span class=pl-en>first</span><span class=pl-kos>(</span><span class=pl-kos>)</span><span class=pl-kos>.</span><span class=pl-en>data</span><span class=pl-kos>(</span><span class=pl-s>&quot;autocomplete&quot;</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>,</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>fn</span><span class=pl-kos>.</span><span class=pl-c1>autocomplete</span><span class=pl-c1>||</span><span class=pl-kos>(</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>fn</span><span class=pl-kos>.</span><span class=pl-c1>autocomplete</span><span class=pl-c1>=</span><span class=pl-s1>t</span><span class=pl-kos>.</span><span class=pl-c1>fn</span><span class=pl-kos>.</span><span class=pl-c1>devbridgeAutocomplete</span><span class=pl-kos>)</span><span class=pl-kos>}</span><span class=pl-kos>)</span><span class=pl-kos>;</span></td>
      </tr>
</table>

  <details class="details-reset details-overlay BlobToolbar position-absolute js-file-line-actions dropdown d-none" aria-hidden="true">
    <summary class="btn-octicon ml-0 px-2 p-0 color-bg-primary border color-border-tertiary rounded-1" aria-label="Inline file action toolbar">
      <svg class="octicon octicon-kebab-horizontal" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path></svg>
    </summary>
    <details-menu>
      <ul class="BlobToolbar-dropdown dropdown-menu dropdown-menu-se mt-2" style="width:185px">
        <li>
          <clipboard-copy role="menuitem" class="dropdown-item" id="js-copy-lines" style="cursor:pointer;">
            Copy lines
          </clipboard-copy>
        </li>
        <li>
          <clipboard-copy role="menuitem" class="dropdown-item" id="js-copy-permalink" style="cursor:pointer;">
            Copy permalink
          </clipboard-copy>
        </li>
        <li><a class="dropdown-item js-update-url-with-hash" id="js-view-git-blame" role="menuitem" href="/devbridge/jQuery-Autocomplete/blame/8138252b4c6fa994a0d438630ea375308e7f3b11/dist/jquery.autocomplete.min.js">View git blame</a></li>
          <li><a class="dropdown-item" id="js-new-issue" role="menuitem" href="/devbridge/jQuery-Autocomplete/issues/new">Reference in new issue</a></li>
      </ul>
    </details-menu>
  </details>

  </div>


</div>

  

  <details class="details-reset details-overlay details-overlay-dark" id="jumpto-line-details-dialog">
    <summary data-hotkey="l" aria-label="Jump to line"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast linejump" aria-label="Jump to line">
      <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="js-jump-to-line-form Box-body d-flex" action="" accept-charset="UTF-8" method="get">
        <input class="form-control flex-auto mr-3 linejump-input js-jump-to-line-field" type="text" placeholder="Jump to line&hellip;" aria-label="Jump to line" autofocus>
        <button type="submit" class="btn" data-close-dialog>Go</button>
</form>    </details-dialog>
  </details>

    <div class="Popover anim-scale-in js-tagsearch-popover"
     hidden
     data-tagsearch-url="/devbridge/jQuery-Autocomplete/find-definition"
     data-tagsearch-ref="master"
     data-tagsearch-path="dist/jquery.autocomplete.min.js"
     data-tagsearch-lang="JavaScript"
     data-hydro-click="{&quot;event_type&quot;:&quot;code_navigation.click_on_symbol&quot;,&quot;payload&quot;:{&quot;action&quot;:&quot;click_on_symbol&quot;,&quot;repository_id&quot;:688157,&quot;ref&quot;:&quot;master&quot;,&quot;language&quot;:&quot;JavaScript&quot;,&quot;originating_url&quot;:&quot;https://github.com/devbridge/jQuery-Autocomplete/blob/master/dist/jquery.autocomplete.min.js&quot;,&quot;user_id&quot;:null}}"
     data-hydro-click-hmac="05d4c6ef9b27d2a40b724c97558ea95681ecb3b1014ab5243fab72989f69b0cc">
  <div class="Popover-message Popover-message--large Popover-message--top-left TagsearchPopover mt-1 mb-4 mx-auto Box color-shadow-large">
    <div class="TagsearchPopover-content js-tagsearch-popover-content overflow-auto" style="will-change:transform;">
    </div>
  </div>
</div>


</div>



  </div>
</div>

    </main>
  </div>

  </div>

          
<div class="footer container-xl width-full p-responsive" role="contentinfo">
  <div class="position-relative d-flex flex-row-reverse flex-lg-row flex-wrap flex-lg-nowrap flex-justify-center flex-lg-justify-between pt-6 pb-2 mt-6 f6 color-text-secondary border-top color-border-secondary ">
    <ul class="list-style-none d-flex flex-wrap col-12 col-lg-5 flex-justify-center flex-lg-justify-between mb-2 mb-lg-0">
      <li class="mr-3 mr-lg-0">&copy; 2021 GitHub, Inc.</li>
        <li class="mr-3 mr-lg-0"><a href="https://docs.github.com/en/github/site-policy/github-terms-of-service" data-ga-click="Footer, go to terms, text:terms">Terms</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://docs.github.com/en/github/site-policy/github-privacy-statement" data-ga-click="Footer, go to privacy, text:privacy">Privacy</a></li>
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to security, text:security" href="https://github.com/security">Security</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://www.githubstatus.com/" data-ga-click="Footer, go to status, text:status">Status</a></li>
        <li><a data-ga-click="Footer, go to help, text:Docs" href="https://docs.github.com">Docs</a></li>
    </ul>

    <a aria-label="Homepage" title="GitHub" class="footer-octicon d-none d-lg-block mx-lg-4" href="https://github.com">
      <svg height="24" class="octicon octicon-mark-github" viewBox="0 0 16 16" version="1.1" width="24" aria-hidden="true"><path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path></svg>
</a>
    <ul class="list-style-none d-flex flex-wrap col-12 col-lg-5 flex-justify-center flex-lg-justify-between mb-2 mb-lg-0">
        <li class="mr-3 mr-lg-0"><a href="https://support.github.com" data-ga-click="Footer, go to contact, text:contact">Contact GitHub</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.com/pricing" data-ga-click="Footer, go to Pricing, text:Pricing">Pricing</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://docs.github.com" data-ga-click="Footer, go to api, text:api">API</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://services.github.com" data-ga-click="Footer, go to training, text:training">Training</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.blog" data-ga-click="Footer, go to blog, text:blog">Blog</a></li>
        <li><a data-ga-click="Footer, go to about, text:about" href="https://github.com/about">About</a></li>
    </ul>
  </div>
  <div class="d-flex flex-justify-center pb-6">
    <span class="f6 color-text-tertiary"></span>
  </div>

  
</div>



  <div id="ajax-error-message" class="ajax-error-message flash flash-error" hidden>
    <svg class="octicon octicon-alert" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8.22 1.754a.25.25 0 00-.44 0L1.698 13.132a.25.25 0 00.22.368h12.164a.25.25 0 00.22-.368L8.22 1.754zm-1.763-.707c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0114.082 15H1.918a1.75 1.75 0 01-1.543-2.575L6.457 1.047zM9 11a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.25a.75.75 0 00-1.5 0v2.5a.75.75 0 001.5 0v-2.5z"></path></svg>
    <button type="button" class="flash-close js-ajax-error-dismiss" aria-label="Dismiss error">
      <svg class="octicon octicon-x" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg>
    </button>
    You can’t perform that action at this time.
  </div>

  <div class="js-stale-session-flash flash flash-warn flash-banner" hidden
    >
    <svg class="octicon octicon-alert" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M8.22 1.754a.25.25 0 00-.44 0L1.698 13.132a.25.25 0 00.22.368h12.164a.25.25 0 00.22-.368L8.22 1.754zm-1.763-.707c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0114.082 15H1.918a1.75 1.75 0 01-1.543-2.575L6.457 1.047zM9 11a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.25a.75.75 0 00-1.5 0v2.5a.75.75 0 001.5 0v-2.5z"></path></svg>
    <span class="js-stale-session-flash-signed-in" hidden>You signed in with another tab or window. <a href="">Reload</a> to refresh your session.</span>
    <span class="js-stale-session-flash-signed-out" hidden>You signed out in another tab or window. <a href="">Reload</a> to refresh your session.</span>
  </div>
    <template id="site-details-dialog">
  <details class="details-reset details-overlay details-overlay-dark lh-default color-text-primary hx_rsm" open>
    <summary role="button" aria-label="Close dialog"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast hx_rsm-dialog hx_rsm-modal">
      <button class="Box-btn-octicon m-0 btn-octicon position-absolute right-0 top-0" type="button" aria-label="Close dialog" data-close-dialog>
        <svg class="octicon octicon-x" viewBox="0 0 16 16" version="1.1" width="16" height="16" aria-hidden="true"><path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path></svg>
      </button>
      <div class="octocat-spinner my-6 js-details-dialog-spinner"></div>
    </details-dialog>
  </details>
</template>

    <div class="Popover js-hovercard-content position-absolute" style="display: none; outline: none;" tabindex="0">
  <div class="Popover-message Popover-message--bottom-left Popover-message--large Box color-shadow-large" style="width:360px;">
  </div>
</div>




  </body>
</html>

