$(document).ready(function () {
  function animateNumber($element) {
    var startNumber = 0;
    var endNumber = parseInt($element.text(), 10);

    $({ countNum: startNumber }).animate(
      { countNum: endNumber },
      {
        duration: 3000,
        easing: "linear",
        step: function () {
          $element.text(Math.floor(this.countNum));
        },
        complete: function () {
          $element.text(endNumber);
        },
      }
    );
  }

  animateNumber($("#application_count"));
  animateNumber($("#paid_count"));
  animateNumber($("#student_count"));
  animateNumber($("#channel_partner_count"));
  animateNumber($("#offer_received"));
});
