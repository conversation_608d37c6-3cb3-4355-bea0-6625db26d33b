$(document).ready(function () {

    //console.log("ready!");
      bsCustomFileInput.init();
      //Initialize Select2 Elements
      $('.select2bs4').select2({
            theme: 'bootstrap4'
      });

        /*Save agent*/
      $('#quickform').validate({
          rules: {
              first_name: {
                  required: true,
              },
              country_id: {
                  required: true,
              },
              last_name: {
                  required: true,
              },
              email: {
                  required: true,
              },
              user_type: {
                  required: true,
              }
          },
          messages: {

            first_name: {
                  required: "Please Enter a value",
              },
              last_name: {
                  required: "Please Enter a value",
              },
              email: {
                  required: "Please Enter a value",
              },
              user_type: {
                  required: "Please Please Select One",
              },
          },
          errorElement: 'span',
          errorPlacement: function (error, element) {
              error.addClass('invalid-feedback');
              element.closest('.form-group').append(error);
          },
          highlight: function (element, errorClass, validClass) {
              $(element).addClass('is-invalid');
          },
          unhighlight: function (element, errorClass, validClass) {
              $(element).removeClass('is-invalid');
          },
          submitHandler: function (form){
              //console.log(new FormData(form));
              var formData = new FormData(form);
              formData.append('agentSave', '1');
              $.ajax({
                  type:"POST",
                  url:"controller/agent-controller.php",
                  data:formData,
                  dataType: 'json',
                  mimeType: "multipart/form-data",
                  contentType: false,
                  processData: false,
                  cache: false,
                  success:function(data){
                      //console.log(data);
                      if(data.data>0){
                          toastr.success('Saved Successfully');
                          window.location.href = 'agent-list.php';
                        //   $('#form').trigger("reset");
                        //   window.location.href = window.location.pathname + '?savedSuccess=1';
                      }else {
                        toastr.error(data.status);
                      }

                  }
              });
          }
      });

  });