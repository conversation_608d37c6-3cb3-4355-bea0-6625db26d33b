$(document).ready(function() {
    // $('#example2').DataTable({
    //     "paging": true,
    //     "lengthChange": true,
    //     "searching": true,
    //     "ordering": true,
    //     "info": true,
    //     "autoWidth": false,
    //     "responsive": true,
    // });


    $('#example2 tfoot th').each(function() {
        var title = $(this).text();
        if (title == 'ID' || title == 'Action') {
            $(this).html('')
        } else {

            $(this).html(
                '<input class="form-control form-control-sm" type="text" placeholder="Search ' +
                title + '" />');
        }
    });

    // DataTable
    var table = $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "pageLength": 100,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        initComplete: function() {
            // Apply the search
            this.api()
                .columns()
                .every(function() {
                    var that = this;

                    $('input', this.footer()).on('keyup change clear', function() {
                        if (that.search() !== this.value) {
                            that.search(this.value).draw();
                        }
                    });
                });
        },
    });
    $('#example2_filter').addClass('d-none');
    $('#example2 tfoot tr').appendTo('#example2 thead');

    $('.delete_btn').on('click', function() {
        var institute_id = $(this).data('institute_id');

        Swal.fire({
            title: 'Are you sure want to delete?',
            showCancelButton: true,
            confirmButtonText: `Delete`,
            confirmButtonColor: '#d33',
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {

                $.ajax({
                    type: "POST",
                    url: "controller/institute-controller.php",
                    data: {
                        "institute_id": institute_id,
                        "deleteInstitute": 1
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);
                        if (data.data > 0) {
                            Swal.fire(
                                'Deleted!',
                                'Your file has been deleted.',
                                'success'
                            )
                            window.location.reload();
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });


            }
        })
    });

});