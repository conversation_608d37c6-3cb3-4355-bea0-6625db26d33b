<?php

//this is an included function, so no session start
require_once "".$class_path."class.phpmailer.php";

global $ycdtrc_logo_path;
//$ycdtrc_logo_path = "http://sldb.youcandothecube.com/web/images/ycdtrc-logo.png";
$ycdtrc_logo_path = "https://speedleague.youcandothecube.com/web/images/ycdtrc-logo.png";
global $ycdtrc_base_url;
$ycdtrc_base_url = "https://speedleague.youcandothecube.com/web/";
global $ycdtrc_parent_url;
$ycdtrc_parent_url = "https://speedleague.youcandothecube.com/parent/";

function sendParentConsentEmail($student_id, $competition_id) {
    global $ycdtrc_parent_url;
    //echo '<br>email function started';
    /**
     * Dear parent/guardian of ___(autofill Student Name),
      Please review and sign the following consent form to allow your child’s <PERSON><PERSON><PERSON>’s Cube solving time to be
      recorded in the You CAN Do the Rubik’s Cube Speed League database.
      Thank you,
      ___(Coach name/ autofill)
     */
    $mail = new PHPMailer;
    $cstudent = new student();
    $ccompetition = new competition();
    $criteria['competition_id'] = $competition_id;
    $competition_list = $ccompetition->getDetailsForParentConsent($student_id, $competition_id); // getCompetitionByID($competition_id);
    //print_r($competition_list);
    extract($competition_list[0]);
    //die();


    $coach_details = $cstudent->getCoachDetails($coach_id);
    $coach_f_name = $coach_details[0]['first_name'];
    $coach_l_name = $coach_details[0]['last_name'];

    $consent_url = $ycdtrc_parent_url . "consent-form.php?ref=" . $parent_consent_guid;

    $email_text = "<!DOCTYPE html>
				<html>
				<head>
					<style>
						
					</style>
				</head>
				<body>";
    $email_text .= "Dear parent/guardian of $first_name $last_name,";

    $email_text .= "<br><br>Please review and sign the following consent form to allow your child's Rubik's Cube solving time to be 
                    recorded in the You CAN Do the Rubik's Cube Speed League database.
                    <br><br>
                    In addition, photographs and/or videos of students taken at these competitions may be used to promote the event 
                    and other similar events, at educational workshops, classrooms, and conferences, or shared online. Student names will 
                    not be included with photos. In order for our school to share student information , we need to abide by federal 
                    regulations that require a parental signature
                    <br><br>
                    Please click the <a href='$consent_url'>link here to review the consent details.</a>
                    <br>
                    <br>
                    If you are having trouble clicking on the url above, please copy and past the link in your web browser.
                    <br>
                    <br>
                    $consent_url
                    <br>
                    <br>
Thank you,
<br><br>
$coach_f_name $coach_l_name";

    //echo "<br><br><br>".$email_text;
    //die();

    $email_plain_text = $email_text; //need to amend this for plain text

    $mail->From = '<EMAIL>';
    $mail->FromName = 'YCDTRC';
    $mail->AddAddress($parent_email, $parent_f_name);     // Add a recipient
    $mail->AddBCC('<EMAIL>');
    $mail->AddBCC("<EMAIL>");
    $mail->AddBCC("<EMAIL>");
    $mail->AddBCC('<EMAIL>');
    //$mail->AddBCC("<EMAIL>");

    //$mail->addAttachment('');         // Add attachments
    //$mail->addAttachment('/tmp/image.jpg', 'new.jpg');    // Optional name
    $mail->isHTML(true);                                  // Set email format to HTML

    $mail->Subject = "Parent Consent Request for $first_name $last_name";
    $mail->Body = $email_text;
    $mail->AltBody = $email_plain_text;

    if (!$mail->send()) {
        //echo '<br>Message could not be sent.';
        return 'Mailer Error: ' . $mail->ErrorInfo;
    } else {
        return 'Success';
    }
}

function send_email_transfer_request($student_id, $student_guid) {
    global $ycdtrc_parent_url;
    /**
     * Dear parent/guardian of ___(autofill Student Name),
      We need your permission to transfer ___(Student first name/ autofill) from my Rubik’s Cube team to their new team at ___(Organization/ autofill) with ___(New Coach Name/ autofill).  Please check the appropriate box so we can transfer his/her record of Rubik’s Cube solving times.
      Thank you,
      ___(Coach name/ autofill)
     */
    $cstudent = new student();
    $cuser = new user();
    $corganisation = new organisation();
    
    $criteria['student_id'] = $student_id;
    $criteria['student_guid'] = $student_guid;
    $student_list = $cstudent->getStudent($criteria);
    //print_r($student_list);
    $parent_name = $student_list[0]['parent_f_name'] . ' ' . $student_list[0]['parent_l_name'];
    $parent_email = $student_list[0]['parent_email'];
    $student_name = $student_list[0]['first_name'] . ' ' . $student_list[0]['last_name'];
    $transfer_request_guid = $student_list[0]['transfer_request_guid'];
    $domain=$ycdtrc_parent_url."student-transfer-approve.php?ref=$transfer_request_guid&id=$student_id";
    
    $student_coach_id = $student_list[0]['coach_id'];
    $student_organisation_name = $student_list[0]['organisation_name'];
    
    $student_coach_details = $cuser->getUserDetails($student_coach_id, "");
    $student_coach_name = $student_coach_details[0]['first_name'] . ' ' . $student_coach_details[0]['last_name'];
    $student_coach_email = $student_coach_details[0]['email'];

    $today = date("Y-m-d H:i");
    //print_r($_SESSION['user']);
    $organisation_details = $corganisation->getOrganisation(array('organisation_id'=>$_SESSION['user']['organisation_id']));
    //print_r($organisation_details);
    
    $new_coach_name = $_SESSION['user']['first_name'] . ' ' . $_SESSION['user']['last_name']; // 
    $new_coach_organisation = $organisation_details[0]['organisation_name'];
    
    $mail = new PHPMailer;
    


    $email_text = "<!DOCTYPE html>
				<html>
				<head>
					
				</head>
				<body>";
    $email_text .= "Dear parent/guardian of $student_name,";

    $email_text .= "<br><br>We need your permission to transfer $student_name
                from my Rubik's Cube team to their new team at $new_coach_organisation with $new_coach_name.  
                Please check the appropriate box so we can transfer his/her record of Rubik's Cube solving times.
                <br>
                <br><a href='$domain'>Click here</a> to approve or decline the transfer.
                <br>
                <br>If the above link not working, plesae copy and paste this link in your browser
                <br>$domain
                <br>
                <br>If you have any queries, please feel free to contact me via my email: $student_coach_email
		<br><br>
		Regards,
		<br><br>
                <p style='font-size: 14px;'>$student_coach_name</p>
		<br><p style='font-size: 14px;'>Coach</p>
                <br>Date: $today
		</body>
		</html>
		";


    $email_plain_text = $email_text; //need to amend this for plain text
    //echo $email_plain_text;
    //die();
    
    $mail->From = '<EMAIL>';
    $mail->FromName = 'You can do the cube';
    
    $mail->AddAddress($parent_email, $parent_f_name);     // Add a recipient

    //$mail->AddBCC('<EMAIL>');
    //$mail->AddBCC("<EMAIL>");
    //$mail->AddBCC("<EMAIL>");
    $mail->AddBCC('<EMAIL>');


    //$mail->addAttachment('');         // Add attachments
    //$mail->addAttachment('/tmp/image.jpg', 'new.jpg');    // Optional name
    $mail->isHTML(true);                                  // Set email format to HTML

    $mail->Subject = "Transfer request for $student_name";
    $mail->Body = $email_text;
    $mail->AltBody = $email_plain_text;

    if (!$mail->send()) {
        //echo '<br>Message could not be sent.';
        return '<br>Mailer Error: ' . $mail->ErrorInfo;
    } else {
        return 1;// '<br>Email Message has been sent';
    }
}

function sendForgotPassword($username) {
    global $ycdtrc_logo_path;
    $cuser = new user();
    $mail = new PHPMailer;
    $user_details = $cuser->getLogin($username);
    if (isset($user_details[0]['user_id']) && $user_details['0']['user_id'] > 0) {
        $pass_correct = $user_details[0]['password1'];
        $first_name = $user_details[0]['first_name'];
        $last_name = $user_details[0]['last_name'];

        $email = $user_details[0]['email'];
        $string_message = "<!DOCTYPE html>
				<html>
				<head>
					<style>
					</style>
				</head>
				<body>
					Dear ($first_name $last_name),
					<br>Thank you for requesting the password. Please find your password.
					<br>password: $pass_correct
					<br>
					<br>Please use the above password to login to YCDTRC admin portal.
					<br>
					<br>
					<br>Thank you 
					<br>
					<br>YCDTRC Online Team
					<br>
                                        <br><img src='$ycdtrc_logo_path' alt='You Can Do The Rubiks Cube Logo' >
				</body>
				</html>
					";
        $mail->From = '<EMAIL>';
        $mail->FromName = 'YCDTRC';
        $mail->addAddress($email, $first_name);     // Add a recipient
        //$mail->AddAddress('<EMAIL>');
        //$mail->AddAddress("<EMAIL>");
        //$mail->AddAddress("<EMAIL>");
        $mail->addBCC('<EMAIL>');

        $mail->addReplyTo('<EMAIL>', 'Noreply');

        //$mail->addAttachment('');         // Add attachments
        //$mail->addAttachment('/tmp/image.jpg', 'new.jpg');    // Optional name
        $mail->isHTML(true);                                  // Set email format to HTML

        $mail->Subject = 'YCDTRC Password reset';
        $mail->Body = $string_message;
        if (!$mail->send()) {
            echo '<br>Message could not be sent.';
            echo '<br>Mailer Error: ' . $mail->ErrorInfo;
        } else {
            //echo '<br>Email Message has been sent';
        }
    } else {
        echo "Invalid user!";
    }
}

function send_email_notifications($id, $email_type = 'public competition') {
    global $ycdtrc_base_url;
    /**
     * send email notification to rubiks admin & ambassador about this new public competition
     */
    $cuser = new user();
    $mail = new PHPMailer;
    $ccompetition = new competition();
    $email_to = "<EMAIL>";
    $email_subject = "Function call";
    $email_from = "<EMAIL>";
    $message = "Nothing to Show!";
    $email_type = strtolower($email_type);

    if ($email_type == "public competition") {
        //$to = "<EMAIL>, <EMAIL>";
        $email_to = "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>";
        $email_subject = "New Competition Created $comp_title";
        $email_from = "<EMAIL>";
        $competition_id = $id;
        $competition_list = $ccompetition->getCompetitionByID($competition_id);
        // print_R($competition_list);
        if (!isset($competition_list[0]['competition_id'])) {
            errorRedirect("Invalid Competition to send email!", "index.php");
        }
        extract($competition_list[0]);

        //$comp_contact_person = $competition_list[$x]['comp_contact_person'];
        //$comp_contact_email = $competition_list[$x]['comp_contact_email'];
        $full_address = $comp_address1;
        $full_address.=($comp_address2 != "" ? "<br>" . $comp_address2 : "");
        $full_address.=($comp_address3 != "" ? "<br>" . $comp_address3 : "");
        $full_address.=($comp_city != "" ? "<br>" . $comp_city : "");
        $full_address.=($state_name != "" ? "<br>" . $state_name : "");
        $full_address.=($comp_zip != "" ? " " . $comp_zip : "");
        $full_address.=($comp_contact_person != "" ? "<br>" . $comp_contact_person : "");
        $full_address.=($comp_contact_email != "" ? "<br>" . $comp_contact_email : "");

        //$comp_date_start = $competition_list[$x]['comp_date_start'];
        //$comp_date_end = $competition_list[$x]['comp_date_end'];

        $message = "<html>
        <head>
            <title>Email Rubik's Competition</title>
            <style>
                p{padding-bottom: 4px;}
            </style>
        </head>
        <body>";
        $message.="<p>Dear Admin, </p>";
        $message.="<p>There is a new Public Competition registered by the coach $comp_contact_person. Please login to the system to approve the competition<p>";
        $message.="<p>Competition details as follow</p>";
        $message.="<br>Competition Title: $comp_title 
            <br>Competition Date: $comp_date_start
            <br>Description: $comp_description
            <br>Start Date: $comp_date_start
            <br>Location: $full_address
            <br>Contact Person: $comp_contact_person
            <br>Email: $comp_contact_email    
                ";
        $message.="<br><br>Please <a href='" . $ycdtrc_base_url . "competition-add.php?competition=$competition_id'>click here to view Competition</a>";
    }
    $ip_address = getIP();

    $date = date("m/d/Y H:i");

    $message.="<br><br>YouCanDoTheRubiksCube Team";
    $message.="<br><br>Date: $date";
    $message.="<br>IP Address: $ip_address";
    $message.="</body></html>";


    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";


    //echo $message;
    return mail($email_to, $email_subject, $message, $headers);
}

function send_competition_registration_email($team_id, $email_id = 1) {
    global $ycdtrc_base_url;

    $cuser = new user();
    $mail = new PHPMailer;
    $ccompetition = new competition();
    $team_id = intval($team_id);
    $competition_list = $ccompetition->getCompetitionTeamForEmail($team_id);

    if (!isset($competition_list[0]['competition_id'])) {
        errorRedirect("Invalid Competition!", "index.php");
    }
    //print_R($competition_list);
    //die();
    $comp_title = $competition_list[0]['comp_title'];
    $coach_id = $competition_list[0]['team_coach_id'];
    $competition_id = $competition_list[0]['team_competition_id'];
    $division_id = $competition_list[0]['team_division_id'];
    $comp_start_date = format_date($competition_list[0]['comp_date_start'], 2);
    $application_closing_date = format_date($competition_list[0]['application_closing_date'], 2);
    $last_adjustment_date = format_date(dateAdd("d", -2, $comp_start_date), 2);

    $comp_address1 = $competition_list[0]['comp_address1'];
    $comp_address2 = $competition_list[0]['comp_address2'];
    $comp_address3 = $competition_list[0]['comp_address3'];
    $comp_city = $competition_list[0]['comp_city'];
    $state_name = $competition_list[0]['state_name'];
    $comp_zip = $competition_list[0]['comp_zip'];
    $comp_borrow_code = $competition_list[0]['comp_borrow_code'];
    $comp_purchase_code = $competition_list[0]['comp_purchase_code'];
    $coach_name = $competition_list[0]['first_name'] . " " . $competition_list[0]['last_name'];
    $coach_email = $competition_list[0]['email'];
    $team_title = $competition_list[0]['team_title'];
    $moreinfo_link = $competition_list[0]['moreinfo_link'];
    $eligibility_link = $competition_list[0]['eligibility_link'];
    $comp_contact_person = $competition_list[0]['comp_contact_person'];
    $comp_contact_email = $competition_list[0]['comp_contact_email'];
    $comp_ycdtrc_admin_contact = $competition_list[0]['comp_ycdtrc_admin_contact'];
    $comp_ycdtrc_admin_email = $competition_list[0]['comp_ycdtrc_admin_email'];
    $roster_closing_date = $competition_list[0]['roster_closing_date'];
    $ambassador_name = $competition_list[0]['ambassador_name'];
    $ambassador_email = $competition_list[0]['ambassador_email'];
    $full_address = $comp_address1;
    $full_address.=($comp_address2 != "" ? "<br>" . $comp_address2 : "");
    $full_address.=($comp_address3 != "" ? "<br>" . $comp_address3 : "");
    $full_address.=($comp_city != "" ? "<br>" . $comp_city : "");
    $full_address.=($state_name != "" ? "<br>" . $state_name : "");
    $full_address.=($comp_zip != "" ? " " . $comp_zip : "");
    //will add the url for direct editing!!

    $url = $ycdtrc_base_url . "competition-team-enroll.php?coach=<?=$coach_id?>&team=<?=$team_id?>&competition=<?=$competition_id?>&division=<?=$division_id?>";
    $url_parent_consent = $ycdtrc_base_url . "parent-consent-list.php";
    $message = "<html>
            <head>
                <title></title>
                <style>
                    p{padding-bottom: 4px;}
                </style>

            </head>
            <body>";

    if ($email_id == 1) {
        //once registration
        $message.="
            
                    Dear $coach_name,
        <br>Thanks for registering your team for the <a href='$url'>$comp_title</a> on $comp_start_date.
        <br>
        <br>Important! Complete your team roster by $application_closing_date to confirm your team's participation. If you need to make changes after that, 
            you can make adjustments until $last_adjustment_date. Be sure to get <a href='$url_parent_consent'>parent permission through the online consent form</a> for all of your students before the competition date.
        <br>
        <br>You will need to bring a set of 25 Rubik's Cubes to the competition for your team to use. If you need cubes, you can borrow them through our 
        Lending Library at <a href='www.youcandothecube.com/compset'>www.youcandothecube.com/compset</a> using code $comp_borrow_code. Please submit your request ASAP as supplies are limited.  You can also purchase a set of cubes online -see the order form at <a href='http://tinyurl.com/RubiksComp'>http://tinyurl.com/RubiksComp</a> for more information. 
        <br>
        <br>Make sure you read the <a href='$moreinfo_link'>More Info page</a> and familiarize yourself with the rules at <a href='www.tinyurl.com/RubiksRules'>www.tinyurl.com/RubiksRules</a>.
        <br>
        <br>Please print a Solo Scorecard for each member of your team who will be competing in the solo event and bring them with you to the competition. There are 4 scorecards to a sheet. Each soloist needs 1 scorecard. Please make sure each soloist includes their first and last name, school, and grade on the scorecard prior to approaching the solo table. <a href='http://tinyurl.com/soloscorecard'>http://tinyurl.com/soloscorecard</a>
        <br>
        <br>Please let the event Ambassador, $ambassador_name at $ambassador_email 
        or YCDTRC Rep, $comp_ycdtrc_admin_contact at $comp_ycdtrc_admin_email know if you have any questions. 
            
        

        ";
        $email_subject = "Registration for Rubik's Cube Competition";
    } elseif ($email_id == 2) {
        //after registration closes, about 4 weeks to competition, we send an email like this:




        $message.="We are now 4 weeks away from the <a href='$url'>$comp_title</a> on $comp_start_date!  Please let us know if your plans have changed and you will not be bringing a team. 
        <br>
        <br>Complete your team roster by $roster_closing_date to confirm your team will be attending and so we can plan on the number of soloists that will be competing. You will input all your competitors and alternates and select whether they will or will not compete solo. 
        Be sure to get <a href='$url_parent_consent'>parent permission through the online consent form</a> for all of your students before the competition date.
        <br>
        <br>Remember, you will need to bring the following to the competition:
        <br>	Team set of 25 Rubik's Cubes (Request from our Lending Library with code $comp_borrow_code: https://www.youcandothecube.com/compset/ or purchase: http://tinyurl.com/RubiksComp)
        <br>	Soloist scorecard for each soloist (4 on a page, just need 1 of the 4 per student) <a href='http://tinyurl.com/soloscorecard'>http://tinyurl.com/soloscorecard</a>

        <br>$ambassador_name will be sending you a more specific schedule closer to the competition date. 
            If you have any questions please email event Ambassador, $ambassador_name at $ambassador_email or YCDTRC Rep, $comp_ycdtrc_admin_contact at $comp_ycdtrc_admin_email.

        ";
        $email_subject = "Countdown... 4 weeks until Rubik's Cube Competition";
    } elseif ($email_id == 3) {
        //2weeks before

        $message.="
            Hello Rubik's Cube Coaches!
            <br>
            <br>There are two weeks left until the <a href='$url'>$comp_title</a> on $comp_start_date, which will be held at 
            <br>$full_address.  
            <br>If your plans have changed and you will not be competing this year, please let us know.  
            <br>
            <br><b>All rosters should be complete at this point - but if not, and you still plan on attending the competition - 
            please complete this task asap. Be sure to also get parent permission through the online consent form for all of your students before the competition date.</b>
            <br>
            <br>You will be receiving a tentative schedule from $ambassador_name, please contact them if you have any day-of-competition questions $ambassador_email
            ";
        $email_subject = "Countdown... 2 weeks until Rubik's Cube Competition";
    } elseif ($email_id == 4) {
        //1 week before
        $message.="The big day is just 1 week away!   I know you are all looking forward to the 
                <br><a href='$url'>$comp_title</a> 
                   <br> at 
                   $full_address 
                       <br>on (AUTOFILL date). 
                        <br>
                       <br>A few important details:
                       <br>	This is a Rubik's Cube sponsored event, so only Rubik's Brand Cubes may be used.
                       <br>	Please bring your own set of 25 cubes... either the kits you have purchased, the lending library set you have borrowed or your students' cubes from home.
                       <br>	Please bring a solo scorecard for each student competing in the solo event.  <a href='http://tinyurl.com/soloscorecard'>http://tinyurl.com/soloscorecard</a>
                       <br>	The Coach only will check in the team at the registration table. 
                       <br>
                       <br>If you have any questions, please don't hesitate to ask!  Contact event Ambassador, $ambassador_name at $ambassador_email 
                           or YCDTRC Rep, $comp_ycdtrc_admin_contact at $comp_ycdtrc_admin_email.
                       ";
        $email_subject = "Countdown... 4 weeks until Rubik's Cube Competition";
    }
    //echo "<br><br>$message";

    $date = date("m/d/Y H:i");
    $message.="<br><br>Date: $date";
    $message.="</body>
        </html>";

	$email_to = $coach_email;
    $email = "<EMAIL>";//, <EMAIL>, <EMAIL>, <EMAIL>";


    $email_from = "<EMAIL>";

    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";


    //echo $message;
    return mail($email_to, $email_subject, $message, $headers);
    return mail($email, $email_subject, $message, $headers);
}

/**
 * 
 * @param type $id (competiton_id or team_id)
 * @param type $email_type (1=prize pack email during competition creation, 2=heat patch email during team enrollment)
 * @return type
 */
function send_email_to_shipping_department($id, $email_type = 1) {
    //email_type 1=prize pack, 2=heat transfer
    //email_type 1=competition_id, 2=team_id
    $cuser = new user();

    $ccompetition = new competition();


    $shipping_address = "";
    if ($email_type == 1) { //load the details from competition
        $request_type = "Prize Pack";
        $competition_list = $ccompetition->getCompetition(array("competition_id" => $id));

        if (!isset($competition_list[0]['competition_id'])) {
            errorRedirect("Invalid Competition!", "index.php");
        }
        $shipping_address = nl2br($competition_list[0]['prize_pack_shipping_address']);
    } else { //load the details from team
        $request_type = "Heat Transfer Patch";
        $competition_list = $ccompetition->getTeamEnrolledEvents($id);

        if (!isset($competition_list[0]['competition_id'])) {
            //errorRedirect("Invalid Competition!", "index.php");
        }
        $shipping_address = nl2br($competition_list[0]['heat_patch_shipping_address']);
        $team_title = $competition_list[0]['team_title'];
        $coach_name = $competition_list[0]['first_name'] . ' ' . $competition_list[0]['last_name'];
        $comp_title = $competition_list[0]['comp_title'];
        $comp_start_date = format_date($competition_list[0]['comp_date_start'], 2);
        $application_closing_date = format_date($competition_list[0]['application_closing_date'], 2);

        $message = "
        $coach_name has requested a team set of heat transfers for the $comp_title on $comp_start_date.
        <br>
        <br>Mail to:
        <br>$shipping_address
        <br>
        <br>
        Best regards
        <br>
        YCDTRC Online Team
        <br>
        Note: This is an automated email!

        ";
        $email = "<EMAIL>";
    }
    //print_R($competition_list);
    //die("tye=$request_type id=$id");


    $email_to = "<EMAIL>,<EMAIL>"; // <EMAIL>, <EMAIL>, <EMAIL>";

    $email_from = "<EMAIL>";

    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";

    $email_subject = "$request_type Request by ";

    return mail($email, $email_subject, $message, $headers); //to shipping
    return mail($email_to, $email_subject, $message, $headers); // to ycdtrc team
}

function send_email_welcome($user_id) {
    $cuser = new user();
    $user_id = intval($user_id);

    $mail = new PHPMailer;

    $user_id = intval($user_id);
    $user_details = $cuser->getUserDetails($user_id);
    if (!isset($user_details[0]['user_id'])) {
        echo "Invalid user!";
        die();
    }
    $email = $user_details[0]['email'];
    $first_name = $user_details[0]['first_name'];
    $last_name = $user_details[0]['last_name'];
    $user_name = $user_details[0]['user_name'];
    $user_guid = $user_details[0]['user_guid'] . '-' . $user_id;
}

function emailValidationEmail($user_id) {
    global $ycdtrc_base_url;

    $cuser = new user();
    $mail = new PHPMailer;

    $user_id = intval($user_id);
    $user_details = $cuser->getUserDetails($user_id);
    if (!isset($user_details[0]['user_id'])) {
        echo "Invalid user!";
        die();
    }
    $email = $user_details[0]['email'];
    $first_name = $user_details[0]['first_name'];
    $last_name = $user_details[0]['last_name'];
    $user_name = $user_details[0]['user_name'];
    $user_guid = $user_details[0]['user_guid'] . '-' . $user_id;

    $message = "Welcome $first_name $last_name to the You CAN Do the Rubik's Cube Competition Registration and Results Portal    
<br>
<br>Your account name is $email. Please click on the link below to activate your account. <a href='" . $ycdtrc_base_url . "email-activate.php?key=$user_guid'>CLICK HERE TO ACTIVATE Link </a>
<br>
<br>If you cannot click on the link please copy and paste the following link into your address bar. Link: ".$ycdtrc_base_url."email-activate.php?key=$user_guid
<br>
<br>Now that you have registered, you can choose to join a public competition or host a private competition for your students. If recorded through the database, your students’ scores will be listed and ranked among other students on the You CAN Do the Rubik’s Cube Speed League Leaderboard.
<br>
<br>Instructions for joining a competition or posting your own are in the Competition Registration and Results Portal portal, but if you have any questions or need assistance, 
feel free to let us know <NAME_EMAIL>.
<br>
<br>
Best regards
<br>
YCDTRC Team
<br>
E: <EMAIL>

";
    $email_to = "<EMAIL>";//,<EMAIL>"; // <EMAIL>, <EMAIL>, <EMAIL>";

    $email_from = "<EMAIL>";

    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";

    $email_subject = "Email Activation";

    mail($email_to, $email_subject, $message, $headers); //to rubiks
    mail($email, $email_subject, $message, $headers); //to user
//	die();
}

function send_email_private_competition_approved($competition_id) {

    $message = "NAME) has requested a competition prize pack for their competition on (DATE OF COMPETITION).

Mail to:
(ADDRESS)
";
}

function send_email_public_competition_approved($competition_id) {

    global $ycdtrc_base_url;


    $mail = new PHPMailer;
    $ccompetition = new competition();
    $criteria['competition_id'] = $competition_id;
    $competition_list = $ccompetition->getCompetition($criteria);

    if (!isset($competition_list[0]['competition_id'])) {
        errorRedirect("Invalid Competition!", "index.php");
    }
    //print_R($competition_list);
    //die();
    $comp_title = $competition_list[0]['comp_title'];
    $coach_id = $competition_list[0]['team_coach_id'];
    $competition_id = $competition_list[0]['team_competition_id'];
    $division_id = $competition_list[0]['team_division_id'];
    $comp_start_date = format_date($competition_list[0]['comp_date_start'], 2);
    $application_closing_date = format_date($competition_list[0]['application_closing_date'], 2);
    $last_adjustment_date = dateAdd("d", -2, $comp_start_date);

    $comp_address1 = $competition_list[0]['comp_address1'];
    $comp_address2 = $competition_list[0]['comp_address2'];
    $comp_address3 = $competition_list[0]['comp_address3'];
    $comp_city = $competition_list[0]['comp_city'];
    $state_name = $competition_list[0]['state_name'];
    $comp_zip = $competition_list[0]['comp_zip'];
    $comp_borrow_code = $competition_list[0]['comp_borrow_code'];
    $comp_purchase_code = $competition_list[0]['comp_purchase_code'];
    $coach_name = $competition_list[0]['first_name'] . " " . $competition_list[0]['last_name'];
    $coach_email = $competition_list[0]['email'];
    $team_title = $competition_list[0]['team_title'];
    $moreinfo_link = $competition_list[0]['moreinfo_link'];
    $eligibility_link = $competition_list[0]['eligibility_link'];
    $comp_contact_person = $competition_list[0]['comp_contact_person'];
    $comp_contact_email = $competition_list[0]['comp_contact_email'];
    $comp_ycdtrc_admin_contact = $competition_list[0]['comp_ycdtrc_admin_contact'];
    $comp_ycdtrc_admin_email = $competition_list[0]['comp_ycdtrc_admin_email'];
    $roster_closing_date = $competition_list[0]['roster_closing_date'];
    $ambassador_name = $competition_list[0]['ambassador_name'];
    $ambassador_email = $competition_list[0]['ambassador_email'];
    $full_address = $comp_address1;
    $full_address.=($comp_address2 != "" ? "<br>" . $comp_address2 : "");
    $full_address.=($comp_address3 != "" ? "<br>" . $comp_address3 : "");
    $full_address.=($comp_city != "" ? "<br>" . $comp_city : "");
    $full_address.=($state_name != "" ? "<br>" . $state_name : "");
    $full_address.=($comp_zip != "" ? " " . $comp_zip : "");

    $message = "
        Dear Ambassador $ambassador_name,
        <br>
        <br>Your competition- $comp_title on $comp_start_date has been approved and is now posted at https://www.youcandothecube.com/you-can-do-the-rubiks-cube-competitions/upcoming-competitions/
        <br>
        <br>Your next steps are to:
        <br>Oversee registration for your competition
        <br>	Register your own team(s) for your competition. 
        <bR>	Follow up with coaches who don’t meet deadline for rosters.
        <br>	Check that all students have parent approval via electronic consent form. 
        <br> 
        <br>Prepare for Competition Day
        <br>	Confirm supply list with YCDTRC rep
        <br>	Create a schedule including order of play for team heats and solo rounds. Share with registered coaches before the competition.
        <br>	Remind coaches about reading rules (including flipped corners and missing tiles) and share venue specific info (parking, food, etc).
        <br>
        <br>Please let your YCDTRC rep know if you have any questions.

        ";

    $email_to = "<EMAIL>";//,<EMAIL>"; // <EMAIL>, <EMAIL>, <EMAIL>";
	$email = $coach_email;
    $email_from = "<EMAIL>";

    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";

    $email_subject = "Rubiks Cube Competition Approval";

    mail($email_to, $email_subject, $message, $headers);
    mail($email, $email_subject, $message, $headers);
}

function send_email_results_published($competition_id) {
    $competition_id = intval($competition_id);

    $ccompetition = new competition();

    $competition_list = $ccompetition->getCompetitionTeamForEmail(0, $competition_id);

    if (!isset($competition_list[0]['competition_id'])) {
        errorRedirect("Invalid Competition!", "index.php");
    }
    //print_R($competition_list);
    //die();
    $comp_title = $competition_list[0]['comp_title'];
    $coach_id = $competition_list[0]['team_coach_id'];
    $competition_id = $competition_list[0]['team_competition_id'];
    $division_id = $competition_list[0]['team_division_id'];
    $comp_start_date = format_date($competition_list[0]['comp_date_start'], 2);
    $application_closing_date = format_date($competition_list[0]['application_closing_date'], 2);
    $last_adjustment_date = dateAdd("d", -2, $comp_start_date);

    $comp_address1 = $competition_list[0]['comp_address1'];
    $comp_address2 = $competition_list[0]['comp_address2'];
    $comp_address3 = $competition_list[0]['comp_address3'];
    $comp_city = $competition_list[0]['comp_city'];
    $state_name = $competition_list[0]['state_name'];
    $comp_zip = $competition_list[0]['comp_zip'];
    $comp_borrow_code = $competition_list[0]['comp_borrow_code'];
    $comp_purchase_code = $competition_list[0]['comp_purchase_code'];
    $coach_name = $competition_list[0]['first_name'] . " " . $competition_list[0]['last_name'];
    $coach_email = $competition_list[0]['email'];
    $team_title = $competition_list[0]['team_title'];
    $moreinfo_link = $competition_list[0]['moreinfo_link'];
    $eligibility_link = $competition_list[0]['eligibility_link'];
    $comp_contact_person = $competition_list[0]['comp_contact_person'];
    $comp_contact_email = $competition_list[0]['comp_contact_email'];
    $comp_ycdtrc_admin_contact = $competition_list[0]['comp_ycdtrc_admin_contact'];
    $comp_ycdtrc_admin_email = $competition_list[0]['comp_ycdtrc_admin_email'];
    $roster_closing_date = $competition_list[0]['roster_closing_date'];
    $ambassador_name = $competition_list[0]['ambassador_name'];
    $ambassador_email = $competition_list[0]['ambassador_email'];

    $message = "Dear Ambassador $ambassador_name,
        <br>
        <br>Thank you for all of your great work on the $comp_title on $comp_start_date.  
            The scores you entered have now been posted and can be viewed online <a href='https://www.youcandothecube.com/you-can-do-the-rubiks-cube-competitions/competition-results/'>https://www.youcandothecube.com/you-can-do-the-rubiks-cube-competitions/competition-results/</a>  
            If you haven't already, please make sure you have sent photos from your competition to your YCDTRC rep.
        <br>
        <br>Thanks again! 
        ";

    $email_to = "<EMAIL>";//,<EMAIL>"; // <EMAIL>, <EMAIL>, <EMAIL>";
	$email = $coach_email;
	
    $email_from = "<EMAIL>";

    // Always set content-type when sending HTML email
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $email_from" . "\r\n";

    $email_subject = "Email Activation";

    mail($email_to, $email_subject, $message, $headers);
    mail($email, $email_subject, $message, $headers);
}

?>