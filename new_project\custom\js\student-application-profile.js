
$(document).ready(function () {

    /*On here to anime end is progress animation*/
    var progress_count = $('.progress_count').val();
    var pickup_order_driver_id = $('.pickup_order_driver_id').val();
    var delivery_order_driver_id = $('.delivery_order_driver_id').val();
    var i = 1;
    animateProgress($('.step01  .progress'), 100, progress_count);

    function animateProgress($progressBar, val, stepDone, currentVal) {
        currentVal = currentVal || 0;
        var step = val * 15 / 300;

        function animate(currentVal) {
            currentVal += step;
            $progressBar.val(currentVal);
            currentVal < val && requestAnimationFrame(function () {
                animate(currentVal);
            });
        }

        animate(currentVal);
        stepDone--;
        i++;
        if (stepDone > 0)
            animateProgress($('.step0' + i + '  .progress'), 100, stepDone);
    }

    /*anime end*/

});