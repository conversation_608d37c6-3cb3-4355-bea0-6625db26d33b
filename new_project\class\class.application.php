<?php

include_once 'connection.php';

class Application extends connection
{

    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    function Application()
    {
        self::__construct();
    }

    private $_conn;


    function updateApplicationSplNote($_details)
    {
        $conn = $this->_conn;
        $application_log_id = intval($_details['application_log_id']);

        $query = "update ggportal_tbl_application_log set
        special_note = '" . mysqli_real_escape_string($conn, $_details['special_note']) . "' 
        WHERE application_log_id = $application_log_id;
        ";


        $result = mysqli_query($conn, $query);

        if ($result) {

            return 1;
        } else {
            return mysqli_error($conn);
        }
    }

    function updateApplicationStatus($_details)
    {
        $conn = $this->_conn;
        $student_application_id = intval($_details['student_application_id']);

        $query = "update ggportal_tbl_student_application set
        status = '" . mysqli_real_escape_string($conn, $_details['status_id']) . "' 
        WHERE student_application_id = $student_application_id;
        ";


        $result = mysqli_query($conn, $query);

        if ($result) {
            $log_query = "update ggportal_tbl_application_log set
            active_yn = 'Y'
            ,remarks ='" . mysqli_real_escape_string($conn, $_details['remarks']) . "'  
            ,date ='" . mysqli_real_escape_string($conn, $_details['date']) . "'  
            ,updated_date = NOW()  
            WHERE status_id = '" . mysqli_real_escape_string($conn, $_details['status_id']) . "' 
            and student_application_id = $student_application_id;
            ";
            $log_res = mysqli_query($conn, $log_query);


            return 1;
        } else {
            return mysqli_error($conn);
        }
    }


    function getApplicationByID($student_application_id)
    {
        $conn = $this->_conn;
        $student_application_id = intval($student_application_id);
        if ($student_application_id > 0) {
            $cond = "and c.student_application_id=$student_application_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT * ,intake AS intake1
                    FROM ggportal_tbl_student_application c
                    WHERE 1=1 $cond;";
        //    echo $query;
        //    die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getApplicationDetailsByID($student_application_id)
    {
        $conn = $this->_conn;
        $student_application_id = intval($student_application_id);
        if ($student_application_id > 0) {
            $cond = "and s.student_application_id=$student_application_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "SELECT *
                    ,s.intake as application_intake
                    ,s.year as application_year
                    ,c1.mobile as student_mobile
                    ,c1.email as student_email
                    ,c1.first_name as student_first_name
                    ,c1.last_name as student_last_name
                    FROM ggportal_tbl_student_application s
                    left join ggportal_tbl_student c1 on c1.student_id=s.student_id
                    left join ggportal_tbl_institute i on i.institute_id=s.institute_id
                    left join ggportal_tbl_program p on p.program_id=s.program_id
                    left join ggportal_tbl_application_status st on st.application_status_id = s.status
                    left join ggportal_tbl_agent a on a.agent_id = s.created_by  
                    WHERE 1=1 $cond;";
        //    echo $query;
        //    die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }


    function getApplications($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['student_application_id']) && strlen($criteria['student_application_id']) > 0) {
            $student_application_id = intval($criteria['student_application_id']);
            $cond .= " and s.student_application_id = $student_application_id ";
        }
    

        if (isset($criteria['name']) && strlen($criteria['name']) > 0) {
            $name = mysqli_real_escape_string($conn, $criteria['name']);
            $cond .= " and r.first_name like '%$name%' ";
        }

        if (isset($criteria['service_provider_id']) && strlen($criteria['service_provider_id']) > 0) {
            $service_provider_id = mysqli_real_escape_string($conn, $criteria['service_provider_id']);
            $cond .= " and r.service_provider_id = '$service_provider_id' ";
        }

        if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0) {
            $is_active = mysqli_real_escape_string($conn, $criteria['is_active']);
            $cond .= " and r.is_active like '%$is_active%' ";
        }

        if (isset($criteria['student_id']) && strlen($criteria['student_id']) > 0) {
            $student_id = intval($criteria['student_id']);
            $cond .= " and c1.student_id  = $student_id ";
        }
        // if ($student_id !== null && strlen($student_id) > 0) {
        //     $student_id = intval($student_id);
        //     $cond .= " and c1.student_id  = $student_id ";
        // }

        if (isset($criteria['staff_id']) && strlen($criteria['staff_id']) > 0) {
            $assign_to_staff = intval($criteria['staff_id']);
            $cond .= " and c1.assign_to_staff  = $assign_to_staff ";
        }

        if (isset($criteria['country_id']) && strlen($criteria['country_id']) > 0) {
            $country_id = mysqli_real_escape_string($conn, $criteria['country_id']);
            $cond .= " and c1.country  = '$country_id' ";
        }

        if (isset($criteria['user_type']) && strlen($criteria['user_type']) > 0) {
            $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);
            $agent_id = mysqli_real_escape_string($conn, $criteria['agent_id']);

            if ($user_type == 'AG') {
                $cond .= " and c1.assign_to_staff  in ( select staff_id from ggportal_tbl_staff where parent_user_id = $agent_id and parent_user_type = '$user_type') ";
            }
        }

        $query = "SELECT s.*, p.*, c1.*, i.*, st.*, cu.*, 
        CONCAT(sf.first_name,' ',sf.last_name) AS staff_name, 
        c1.email AS student_mail, c1.mobile AS student_mobile, 
        s.intake AS application_intake, s.year AS application_year 
            FROM ggportal_tbl_student_application s
            LEFT JOIN ggportal_tbl_student c1 ON c1.student_id = s.student_id        
            LEFT JOIN ggportal_tbl_institute i ON i.institute_id = s.institute_id
            LEFT JOIN ggportal_tbl_program p ON p.program_id = s.program_id
            LEFT JOIN ggportal_tbl_currency cu ON cu.currency_id = p.currency_id
            LEFT JOIN ggportal_tbl_application_status st ON st.application_status_id = s.status  
            LEFT JOIN ggportal_tbl_staff sf ON sf.staff_id = s.created_by 
            LEFT JOIN ggportal_tbl_staff sf2 ON sf2.staff_id = c1.assign_to_staff 
            WHERE 1=1 $cond 
            ORDER BY s.student_application_id DESC";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getApplicationCount($criteria)
    {
        $conn = $this->_conn;
        $query = "select count(*) as total from ggportal_tbl_student_application;";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getOfferReceivedCount($criteria)
    {
        $conn = $this->_conn;
        $query = "select count(*) as total from ggportal_tbl_student_application a where a.status='4';";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getPaidApplicationStat()
    {
        $conn = $this->_conn;
        $query = "SELECT status, COUNT(*) as total FROM ggportal_tbl_student_application GROUP BY status;";
        $result = mysqli_query($conn, $query);

        $total = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $total[$row['status']] = $row['total'];
        }

        return $total;
    }

    function getPaidApplicationStatFilter($value)
    {
        $conn = $this->_conn;
        $value = mysqli_real_escape_string($conn, $value);
        if ($value == 'thisMonth') {
            $query = "SELECT status, COUNT(*) as total FROM ggportal_tbl_student_application where MONTH(created_date) = MONTH(CURRENT_DATE()) GROUP BY status;";
        } else if ($value == 'lastMonth') {
            $query = "SELECT status, COUNT(*) as total FROM ggportal_tbl_student_application where MONTH(created_date) = MONTH(CURRENT_DATE())-1 GROUP BY status;";
        } else {
            $query = "SELECT status, COUNT(*) as total FROM ggportal_tbl_student_application a where a.student_id=$value GROUP BY status;";
        }
        $result = mysqli_query($conn, $query);


        $total = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $total[$row['status']] = $row['total'];
        }

        return $total;
    }

    function getApplicationCountFilterAgent($criteria)
    {
        $conn = $this->_conn;
        $value = mysqli_real_escape_string($conn, $criteria['value']);
        $user_id = mysqli_real_escape_string($conn, $criteria['user_id']);
        $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);

        $query = '';

        if ($value == 'monthly') {
            $query = "SELECT CONCAT(YEAR(a.created_date), '-', MONTH(a.created_date)) AS yearmonth, COUNT(*) AS total 
            FROM ggportal_tbl_student_application a WHERE a.student_id IN (
                SELECT student_id FROM ggportal_tbl_student c1 
                WHERE c1.assign_to_staff IN (
                    SELECT staff_id FROM ggportal_tbl_staff sf 
                    WHERE (sf.parent_user_id = $user_id AND sf.parent_user_type = '$user_type') 
                    OR (c1.assign_to_staff = $user_id AND c1.assign_to_type = '$user_type') 
                    OR (c1.assign_to_agent = $user_id AND c1.assign_to_type = 'SF')
                )
            )
            AND a.created_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 4 MONTH) 
            GROUP BY yearmonth 
            ORDER BY yearmonth;";
        } elseif ($value == 'weekly') {
            $query = "SELECT WEEK(a.created_date) AS week_number, COUNT(*) AS total FROM ggportal_tbl_student_application a
            WHERE a.student_id IN (
                SELECT student_id FROM ggportal_tbl_student c1
                WHERE c1.assign_to_staff IN (
                    SELECT staff_id FROM ggportal_tbl_staff sf
                    WHERE sf.parent_user_id = $user_id AND sf.parent_user_type = '$user_type'
                ) OR c1.assign_to_staff = $user_id AND c1.assign_to_type = '$user_type' 
                OR c1.assign_to_agent = $user_id AND c1.assign_to_type = 'SF'
            )
            AND a.created_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 4 WEEK)
            GROUP BY week_number
            ORDER BY week_number;";
        }

        $result = mysqli_query($conn, $query);
        $total = array();

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                if ($value == 'weekly' && isset($row['week_number'])) {
                    $total[$row['week_number']] = $row['total'];
                } elseif ($value == 'monthly' && isset($row['yearmonth'])) {
                    $total[$row['yearmonth']] = $row['total'];
                }
            }
        }

        return $total;
    }




    function getApplicationStatStaff($user_id, $user_type)
    {
        $conn = $this->_conn;
        $user_id = mysqli_real_escape_string($conn, $user_id);
        $user_type = mysqli_real_escape_string($conn, $user_type);

        $query = " SELECT status, COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
        ( SELECT student_id FROM ggportal_tbl_student WHERE assign_to_staff = $user_id AND assign_to_type = '$user_type' ) GROUP BY status;";

        $result = mysqli_query($conn, $query);
        $total = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $total[$row['status']] = $row['total'];
        }

        return $total;
    }

    function getApplicationStatAgent($user_id, $user_type)
    {
        $conn = $this->_conn;
        $user_id = mysqli_real_escape_string($conn, $user_id);
        $user_type = mysqli_real_escape_string($conn, $user_type);

        $query = "SELECT a.status, COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
        ( SELECT student_id FROM ggportal_tbl_student c1 WHERE c1.assign_to_staff IN ( SELECT staff_id from ggportal_tbl_staff where parent_user_id = $user_id and parent_user_type = '$user_type')
         or c1.assign_to_staff = $user_id and c1.assign_to_type = 'AG' or c1.assign_to_agent = $user_id and c1.assign_to_type = 'SF') GROUP BY a.status;";

        $result = mysqli_query($conn, $query);
        $total = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $total[$row['status']] = $row['total'];
        }
        return $total;
    }

    function getDestinations($user_id, $user_type)
    {
        $conn = $this->_conn;
        $user_id = mysqli_real_escape_string($conn, $user_id);
        $user_type = mysqli_real_escape_string($conn, $user_type);

        $query = "SELECT a.country_code, COUNT(*) as total FROM ggportal_tbl_student_application a 
            WHERE a.student_id IN ( SELECT student_id FROM ggportal_tbl_student c1 WHERE c1.assign_to_staff 
            IN ( SELECT staff_id from ggportal_tbl_staff where parent_user_id = $user_id and parent_user_type = '$user_type') OR
             c1.assign_to_staff = $user_id and c1.assign_to_type = '$user_type' or c1.assign_to_agent = $user_id and c1.assign_to_type = 'SF')
              GROUP BY a.country_code ORDER BY total DESC LIMIT 4;";

        $result = mysqli_query($conn, $query);

        // Create an array to store the results
        $destinations = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $destinations[] = $row;
        }

        // Encode the array as JSON and return it
        return json_encode($destinations);
    }





    function getApplicationCountAgent($criteria)
    {
        $conn = $this->_conn;
        $user_id = mysqli_real_escape_string($conn, $criteria['user_id']);
        $user_type = mysqli_real_escape_string($conn, $criteria['user_type']);

        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
        (SELECT student_id FROM ggportal_tbl_student c1 WHERE c1.assign_to_staff IN 
        (SELECT staff_id FROM ggportal_tbl_staff sf WHERE sf.parent_user_id = ? AND sf.parent_user_type = ?)
        OR c1.assign_to_staff = ? AND c1.assign_to_type = ? 
        OR c1.assign_to_agent = ? AND c1.assign_to_type = 'SF')");
        $stmt->bind_param("isiss", $user_id, $user_type, $user_id, $user_type, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $total = $row['total'];
        $stmt->close();
        return $total;
    }

    function allApplicationCountStaff($user_id, $user_type)
    {
        $conn = $this->_conn;
        $user_id = mysqli_real_escape_string($conn, $user_id);
        $user_type = mysqli_real_escape_string($conn, $user_type);
       

        $query = " SELECT COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
        ( SELECT student_id FROM ggportal_tbl_student WHERE assign_to_staff = $user_id AND assign_to_type = '$user_type' );";
        
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);

        return $result[0]['total'];
    }

    function getPaidApplicationCount($criteria)
    {
        $conn = $this->_conn;
        $query = "select count(*) as total from ggportal_tbl_student_application a where a.status='6';";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getApplicationCountByStaff($critera,$value)
    {
        $conn = $this->_conn;
        $staff_id = intval($critera['staff_id']);
        $value = mysqli_real_escape_string($conn, $value);

        if ($value == 'thismonth') {
            $query = "SELECT COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
            ( SELECT s.student_id FROM ggportal_tbl_student s  WHERE s.assign_to_staff = $staff_id AND s.assign_to_type='SF' ) AND MONTH(a.created_date) = MONTH(CURRENT_DATE());";
        } else if ($value == 'lastmonth') {
            $query = "SELECT COUNT(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN 
            ( SELECT s.student_id FROM ggportal_tbl_student s  WHERE s.assign_to_staff = $staff_id AND s.assign_to_type='SF' ) AND MONTH(a.created_date) = MONTH(CURRENT_DATE())-1;";
        } else {
        $query = "SELECT count(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN (SELECT s.student_id FROM ggportal_tbl_student s  WHERE s.assign_to_staff = $staff_id AND s.assign_to_type='SF');";
        }
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getOfferReceivedCountByStaff($critera)
    {
        $conn = $this->_conn;
        $staff_id = intval($critera['staff_id']);
        $query = "SELECT count(*) as total FROM ggportal_tbl_student_application a WHERE a.student_id IN (SELECT s.student_id FROM ggportal_tbl_student s  WHERE s.assign_to_staff = $staff_id AND s.assign_to_type='SF') AND a.status='4';";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getApplicationLog($criteria)
    {
        $conn = $this->_conn;
        $cond = "";
        $cond2 = "";

        if (isset($criteria['application_log_id']) && strlen($criteria['application_log_id']) > 0) {
            $application_log_id = intval($criteria['application_log_id']);
            $cond2 .= " and l1.application_log_id  = $application_log_id ";
        }

        if (isset($criteria['student_application_id']) && strlen($criteria['student_application_id']) > 0) {
            $student_application_id = intval($criteria['student_application_id']);
            $cond2 .= " and l1.student_application_id  = $student_application_id ";
        }

        if (isset($criteria['status_id']) && strlen($criteria['status_id']) > 0) {
            $status_id = intval($criteria['status_id']);
            $cond2 .= " and l1.status_id  = $status_id ";
        }

        $query = "SELECT * from ggportal_tbl_application_log l1
        left join ggportal_tbl_application_status s on s.application_status_id=l1.status_id
         where 1=1 $cond2  order by l1.status_id ASC;";
        //    echo $query;
        //    die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function updateApplicationLog($_details)
    {
        $conn = $this->_conn;
        $application_log_id = intval($_details['application_log_id']);

        $query = "update ggportal_tbl_application_log set
         due_date='" . $_details['due_date'] . "'
         WHERE application_log_id = $application_log_id;
         ";

        $result = mysqli_query($conn, $query);

        if ($result) {
            return 1;
        } else {
            return mysqli_error($conn);
        }
    }
    function updateStudentApplication($_details)
    {
        $conn = $this->_conn;
        $student_application_id = intval($_details['student_application_id']);
        // return $_details;
        // die();
        if ($student_application_id == 0) {

            $query = "INSERT INTO ggportal_tbl_student_application SET
                    student_id = " . intval($_details['student_id']) . "                    
                    ,status = '" . mysqli_real_escape_string($conn, $_details['status']) . "'                         
                    ,application_fee = " . mysqli_real_escape_string($conn, $_details['application_fee']) . "                 
                    ,tuition_fee = " . mysqli_real_escape_string($conn, $_details['tution_fee']) . "                   
                    ,tuition_fee_due_date = " . mysqli_real_escape_string($conn, $_details['tution_fee_due_date']) . "                  
                    ,created_by = '" . mysqli_real_escape_string($conn, $_details['created_by']) . "'                   
                    ,created_date = NOW()                   
               ;";
            //return $query;
        } else {
            //update
            $query = "update ggportal_tbl_student_application SET     
                    student_id = " . intval($_details['student_id']) . "                  
                    ,my_prefer_yn = '" . mysqli_real_escape_string($conn, $_details['my_prefer_yn']) . "'              
                    ,institute_id = " . intval($_details['institute_id']) . "              
                    ,program_id = '" . mysqli_real_escape_string($conn, $_details['program_id']) . "'              
                    ,intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "'
                    ,year = '" . mysqli_real_escape_string($conn, $_details['year']) . "'                         
                    ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'                         
                    ,status = '" . mysqli_real_escape_string($conn, $_details['status']) . "'                         
                    ,application_fee = '" . mysqli_real_escape_string($conn, $_details['application_fee']) . "'                   
                    ,tuition_fee = '" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "'                   
                    ,tuition_fee_due_date = '" . mysqli_real_escape_string($conn, $_details['tution_fee_due_date']) . "'                   
                    ,created_by = '" . mysqli_real_escape_string($conn, $_details['created_by']) . "'  
                    ,date = '" . mysqli_real_escape_string($conn, $_details['date']) . "'  
            WHERE student_application_id = $student_application_id;        
            ";
        }

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($student_application_id == 0) {
                $student_application_id = mysqli_insert_id($conn);
            }

            // change is status changed to log
            //    $log_query = "update ggportal_tbl_application_log set
            //    active_yn = 'Y'
            //    ,updated_date = NOW()  
            //    WHERE status_id = '".mysqli_real_escape_string($conn, $_details['status'])."' 
            //    and student_application_id = $student_application_id;
            //    "; 
            //       $log_res = mysqli_query($conn, $log_query);


            return $student_application_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function saveStudentApplication($_details)
    {
        $conn = $this->_conn;
        $student_application_id = intval($_details['student_application_id']);
        $order_no_check = "SELECT * FROM `ggportal_tbl_student_application` WHERE 1 ORDER BY application_no DESC LIMIT 1";
        $result = mysqli_query($conn, $order_no_check);
        $latest_order = db_to_array($result);
        if (count($latest_order) > 0) {
            //random number generator between 1-10
            $random_number = rand(2, 4);
            $application_no = $latest_order[0]['application_no'] + $random_number;
        } else {
            $application_no = '4500001';
        }


        if ($student_application_id == 0) {

            $query = "INSERT INTO ggportal_tbl_student_application SET
                    student_id = " . intval($_details['student_id']) . "               
                    ,application_no = " . intval($application_no) . "               
                    ,my_prefer_yn = '" . mysqli_real_escape_string($conn, $_details['my_prefer_yn']) . "'              
                    ,institute_id = " . intval($_details['institute_id']) . "              
                    ,program_id = '" . mysqli_real_escape_string($conn, $_details['program_id']) . "'              
                    ,intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "'
                    ,year = '" . mysqli_real_escape_string($conn, $_details['year']) . "'                         
                    ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'                         
                    ,status = '" . mysqli_real_escape_string($conn, $_details['status']) . "'                         
                    ,application_fee = " . mysqli_real_escape_string($conn, $_details['application_fee']) . "                 
                    ,tuition_fee = " . mysqli_real_escape_string($conn, $_details['tution_fee']) . "                   
                    ,tuition_fee_due_date = " . mysqli_real_escape_string($conn, $_details['tution_fee_due_date']) . "                  
                    ,created_by = '" . mysqli_real_escape_string($conn, $_details['created_by']) . "'                    
                    ,created_date = NOW()   
                    ,country_code= '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'                
               ;";
            //return $query;
        } else {
            //update          

            $query = "update ggportal_tbl_student_application SET     
                    student_id = " . intval($_details['student_id']) . "            
                    ,application_no = " . intval($_details['application_no']) . "         
                    ,my_prefer_yn = '" . mysqli_real_escape_string($conn, $_details['my_prefer_yn']) . "'              
                    ,institute_id = " . intval($_details['institute_id']) . "              
                    ,program_id = '" . mysqli_real_escape_string($conn, $_details['program_id']) . "'              
                    ,intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "'
                    ,year = '" . mysqli_real_escape_string($conn, $_details['year']) . "'                         
                    ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'                         
                    ,status = '" . mysqli_real_escape_string($conn, $_details['status']) . "'                         
                    ,application_fee = '" . mysqli_real_escape_string($conn, $_details['application_fee']) . "'                   
                    ,tuition_fee = '" . mysqli_real_escape_string($conn, $_details['tution_fee']) . "'                   
                    ,tuition_fee_due_date = '" . mysqli_real_escape_string($conn, $_details['tution_fee_due_date']) . "'                   
                    ,created_by = '" . mysqli_real_escape_string($conn, $_details['created_by']) . "'                     
                    ,created_date = NOW()
                    ,country_code= '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'                   
            WHERE student_application_id = $student_application_id;        
            ";

        }

        //return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($student_application_id == 0) {
                $student_application_id = mysqli_insert_id($conn);
            }
            // get all status
            $query_status = "SELECT * FROM ggportal_tbl_application_status";
            $result_status = mysqli_query($conn, $query_status);
            $result_status = db_to_array($result_status);

            //echo $query_status;
            //echo $result_status;
            //die();

            foreach ($result_status as $status) {
                $query_log = "INSERT INTO ggportal_tbl_application_log SET
                        student_application_id = " . intval($student_application_id) . "               
                        ,status_id = " . intval($status['application_status_id']) . "               
                        ,updated_date =  null              
                        ,remarks = ''    
                        ,date = ''                               
                        ,due_date = NOW()                   
                        ,active_yn = 'N'                   
                ;";
                $res_log = mysqli_query($conn, $query_log);
            }


            return $student_application_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteApplication($student_application_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_student_application` WHERE `student_application_id` = $student_application_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $student_application_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function getApplicationsList($user_type, $user_id)
    {
        $conn = $this->_conn;
        if ($user_type == 'RA') {
            $query = "WITH RankedRecords AS ( SELECT a.*, c1.first_name, ROW_NUMBER() OVER (PARTITION BY a.student_id ORDER BY a.student_id) 
            AS rn FROM ggportal_tbl_student_application a LEFT JOIN ggportal_tbl_student c1 ON c1.student_id = a.student_id ) SELECT * FROM RankedRecords WHERE rn = 1;";
        } else if ($user_type == 'AG') {
            $query = "WITH RankedRecords AS ( SELECT a.*, c1.first_name, ROW_NUMBER() OVER (PARTITION BY a.student_id ORDER BY a.student_id) 
            AS rn FROM ggportal_tbl_student_application a LEFT JOIN ggportal_tbl_student c1 ON c1.student_id = a.student_id AND a.student_id IN 
               (SELECT student_id FROM ggportal_tbl_student c1 WHERE c1.assign_to_staff IN 
                  (SELECT staff_id FROM ggportal_tbl_staff sf WHERE sf.parent_user_id =$user_id AND sf.parent_user_type = '$user_type')
                       OR c1.assign_to_staff = $user_id AND c1.assign_to_type = '$user_type' 
                       OR c1.assign_to_agent = $user_id AND c1.assign_to_type = 'SF') 
             ) SELECT * FROM RankedRecords WHERE rn = 1;";
        } else {
            $query = "SELECT * FROM `ggportal_tbl_student_application a` WHERE a.created_by = $user_id;";
        }
        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getApplicationCountByStudent($student_id)
    {
        $conn = $this->_conn;
        $query = "select count(*) as total from ggportal_tbl_student_application a where a.student_id=$student_id;";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

  
    function getApplicationListByStatus($status)
    {
        $conn = $this->_conn;
        
        
        $query = "SELECT a.*, s.* FROM ggportal_tbl_student_application a
                left join ggportal_tbl_student s ON a.student_id = s.student_id
                WHERE a.status = ?";
        
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $status); 
        
       
        mysqli_stmt_execute($stmt);
        
       
        $result = mysqli_stmt_get_result($stmt);
        
       
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
        
        
        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
        
        return $data;
    }

    function getAllStudentApplications()
    {
        $conn = $this->_conn;

        $query = "SELECT sa.*, p.*, s.*, sa.created_by AS application_created_by 
                FROM ggportal_tbl_student_application sa
                LEFT JOIN ggportal_tbl_program p ON sa.program_id = p.program_id
                LEFT JOIN ggportal_tbl_student s ON sa.student_id = s.student_id";
           
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_execute($stmt);
        
        $result = mysqli_stmt_get_result($stmt);
        
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }

        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
        
        return $data;
    }

    public function getIntakeById($student_application_id)
    {
    $conn = $this->_conn;

    // Prepare the query
    $query = "SELECT intake
              FROM ggportal_tbl_student_application
              WHERE student_application_id = ?
              LIMIT 1"; // Limit the result to one row

    // Prepare the statement
    $stmt = mysqli_prepare($conn, $query);

    // Bind the parameter
    mysqli_stmt_bind_param($stmt, "i", $student_application_id);

    // Execute the statement
    mysqli_stmt_execute($stmt);

    // Get the result
    $result = mysqli_stmt_get_result($stmt);

    // Fetch the row
    $row = mysqli_fetch_assoc($result);

    // Close the statement
    mysqli_stmt_close($stmt);

    // Check if a row was fetched
    if ($row) {
        return $row['intake'];
    } else {
        // No intake found, return null or handle as needed
        return null;
    }
}

function updateApplication($_details)
{
    $conn = $this->_conn;

    // Sanitize input
    $student_application_id = intval($_details['student_application_id']);
    $institute_id = intval($_details['institute_id']);
    $year = mysqli_real_escape_string($conn, $_details['year']);
    $program_id = mysqli_real_escape_string($conn, $_details['program_id']);
    $user_type=mysqli_real_escape_string($conn, $_details['user_type']);
    // Prepare the SET clause of the update query
    $set_clause = "institute_id = $institute_id,
                   year = '$year',
                   program_id = '$program_id'
                   user_type = '$user_type'";

    // Check if optional fields are provided and append them to the SET clause if so
    if (isset($_details['intake'])) {
        $intake = mysqli_real_escape_string($conn, $_details['intake']);
        $set_clause .= ", intake = '$intake'";
    }

    if (isset($_details['remarks'])) {
        $remarks = mysqli_real_escape_string($conn, $_details['remarks']);
        $set_clause .= ", remarks = '$remarks'";
    }

    // Prepare and execute the update query
    $query = "UPDATE ggportal_tbl_student_application 
              SET $set_clause
              WHERE student_application_id = $student_application_id";

    $res = mysqli_query($conn, $query);

    if ($res) {
        // Check if any rows were affected
        if (mysqli_affected_rows($conn) > 0) {
            return "Student application updated successfully";
        } else {
            return "No changes made to the student application";
        }
    } else {
        return mysqli_error($conn);
    }
}

function saveApplication($_details)
    {
        $conn = $this->_conn;
        $student_application_id = intval($_details['student_application_id']);
        $order_no_check = "SELECT * FROM `ggportal_tbl_student_application` WHERE 1 ORDER BY application_no DESC LIMIT 1";
        $result = mysqli_query($conn, $order_no_check);
        $latest_order = db_to_array($result);
        if (count($latest_order) > 0) {
            //random number generator between 1-10
            $random_number = rand(2, 4);
            $application_no = $latest_order[0]['application_no'] + $random_number;
        } else {
            $application_no = '4500001';
        }


        if ($student_application_id == 0) {

            $query = "INSERT INTO ggportal_tbl_student_application SET
                    student_id = " . intval($_details['student_id']) . "               
                    ,application_no = " . intval($application_no) . "               
                    ,my_prefer_yn = '" . mysqli_real_escape_string($conn, $_details['my_prefer_yn']) . "'              
                    ,institute_id = " . intval($_details['institute_id']) . "              
                    ,program_id = '" . mysqli_real_escape_string($conn, $_details['program_id']) . "'              
                    ,intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "'
                    ,year = '" . mysqli_real_escape_string($conn, $_details['year']) . "'                         
                    ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'                         
                    ,status = '" . mysqli_real_escape_string($conn, $_details['status']) . "'                         
                    ,application_fee = " . mysqli_real_escape_string($conn, $_details['application_fee']) . "                 
                    ,tuition_fee = " . mysqli_real_escape_string($conn, $_details['tution_fee']) . "                   
                    ,tuition_fee_due_date = " . mysqli_real_escape_string($conn, $_details['tution_fee_due_date']) . "                  
                    ,created_by = '" . mysqli_real_escape_string($conn, $_details['created_by']) . "'                    
                    ,created_date = NOW()   
                    ,country_code= '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'  
                    ,created_user_type= '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'  

               ;";
            $res = mysqli_query($conn, $query);
            if ($res) {
                $student_application_id = mysqli_insert_id($conn);
            
                // Insert logs for each status
                $query_status = "SELECT * FROM ggportal_tbl_application_status";
                $result_status = mysqli_query($conn, $query_status);
                $result_status = db_to_array($result_status);

                foreach ($result_status as $status) {
                    $query_log = "INSERT INTO ggportal_tbl_application_log SET
                                student_application_id = " . intval($student_application_id) . "               
                                ,status_id = " . intval($status['application_status_id']) . "               
                                ,updated_date =  null              
                                ,remarks = ''    
                                ,date = ''                               
                                ,due_date = NOW()                   
                                ,active_yn = 'N'                   
                        ;";
                    mysqli_query($conn, $query_log);
                }

                return $student_application_id;
            } else {
                return mysqli_error($conn);
            }
            //return $query;
        } else {
            $fetchQuery = "SELECT * FROM ggportal_tbl_student_application WHERE student_application_id = $student_application_id";
            $fetchResult = mysqli_query($conn, $fetchQuery);
            $oldData = mysqli_fetch_assoc($fetchResult);
            //update
            $query = "update ggportal_tbl_student_application SET     
                    student_id = " . intval($_details['student_id']) . "   
                    ,my_prefer_yn = '" . mysqli_real_escape_string($conn, $_details['my_prefer_yn']) . "'              
                    ,institute_id = " . intval($_details['institute_id']) . "              
                    ,program_id = '" . mysqli_real_escape_string($conn, $_details['program_id']) . "'              
                    ,intake = '" . mysqli_real_escape_string($conn, $_details['intake']) . "'
                    ,year = '" . mysqli_real_escape_string($conn, $_details['year']) . "'                         
                    ,remarks = '" . mysqli_real_escape_string($conn, $_details['remarks']) . "'  
                    ,created_date = NOW()
                    ,country_code= '" . mysqli_real_escape_string($conn, $_details['country_code']) . "'                   
            WHERE student_application_id = $student_application_id;        
            ";
        }

        //return $query;
        // die();

        $res = mysqli_query($conn, $query);

        if ($res) {
            if ($student_application_id == 0) {
                $student_application_id = mysqli_insert_id($conn);
            } else {
                // Log changes for update
                foreach ($_details as $field => $newValue) {
                    if ($field != 'status' && $field != 'application_fee' && $field != 'created_by'  && $field != 'country_code' && isset($oldData[$field]) && $oldData[$field] != $newValue) {
                        $logQuery = "INSERT INTO ggportal_tbl_application_history (student_application_id, field_name, old_value, new_value, changed_by,change_user_type)
                                     VALUES ($student_application_id, 
                                             '" . mysqli_real_escape_string($conn, $field) . "', 
                                             '" . mysqli_real_escape_string($conn, $oldData[$field]) . "', 
                                             '" . mysqli_real_escape_string($conn, $newValue) . "', 
                                             '" . mysqli_real_escape_string($conn, $_details['created_by']) . "',
                                             '" . mysqli_real_escape_string($conn, $_details['user_type']) . "')";
                        mysqli_query($conn, $logQuery);
                    }
                }
            }            
            return $student_application_id;
        } else {
            return mysqli_error($conn);
        }
    }

function getAllStudentApplicationByStaffId($staff_id)
    {
        $conn = $this->_conn;

        $query = "SELECT sa.*, p.*, s.*, sa.created_by AS application_created_by
          FROM ggportal_tbl_student_application sa
          LEFT JOIN ggportal_tbl_program p ON sa.program_id = p.program_id
          LEFT JOIN ggportal_tbl_student s ON sa.student_id = s.student_id
          WHERE s.created_by = ?";
    
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "i", $staff_id);
        mysqli_stmt_execute($stmt);
        
        $result = mysqli_stmt_get_result($stmt);
        
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }

        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
        
        return $data;
    }

function getApplicationListByUserId($status,$userId)
    {
        $conn = $this->_conn;
    
        $query = "SELECT a.*, s.* FROM ggportal_tbl_student_application a
                  LEFT JOIN ggportal_tbl_student s ON a.student_id = s.student_id
                  WHERE a.status = ? AND s.created_by = ?";
    
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ii', $status, $userId);
    
        mysqli_stmt_execute($stmt);
    
        $result = mysqli_stmt_get_result($stmt);
    
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
    
        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
    
        return $data;
    }
    

function getAllStudentApplicationByStudentId($student_id)
    {
        $conn = $this->_conn;

        $query = "SELECT sa.*, p.*, s.* 
                FROM ggportal_tbl_student_application sa
                LEFT JOIN ggportal_tbl_program p ON sa.program_id = p.program_id
                LEFT JOIN ggportal_tbl_student s ON sa.student_id = s.student_id
                WHERE s.student_id = ?";
    
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "i", $student_id);
        mysqli_stmt_execute($stmt);
        
        $result = mysqli_stmt_get_result($stmt);
        
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }

        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
        
        return $data;
    }


    function getApplicationHistoryByApplicationId($student_application_id)
    {
        $conn = $this->_conn;

       $query = "SELECT h.*, 
                 p_old.program_name AS old_program_name, 
                 p_new.program_name AS new_program_name, 
                 i_old.institute_name AS old_institute_name, 
                 i_new.institute_name AS new_institute_name
            FROM ggportal_tbl_application_history h
            LEFT JOIN ggportal_tbl_program p_old ON h.old_value = p_old.program_id
            LEFT JOIN ggportal_tbl_program p_new ON h.new_value = p_new.program_id
            LEFT JOIN ggportal_tbl_institute i_old ON h.old_value = i_old.institute_id                
            LEFT JOIN ggportal_tbl_institute i_new ON h.new_value = i_new.institute_id
            WHERE h.student_application_id = ?
            ORDER BY h.changed_at DESC";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, "i", $student_application_id);
        mysqli_stmt_execute($stmt);
        
        $result = mysqli_stmt_get_result($stmt);
        
        $data = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }

        mysqli_free_result($result);
        mysqli_stmt_close($stmt);
        
        return $data;
    }

    public function getCreatedByStudentApplicationId($student_application_id)
    {
        $conn = $this->_conn;

        // Prepare the query
        $query = "SELECT created_by
                  FROM ggportal_tbl_student_application
                  WHERE student_application_id = ?
                  LIMIT 1"; // Limit the result to one row
    
        // Initialize a statement
        $stmt = mysqli_prepare($conn, $query);
    
        if ($stmt) {
            // Bind parameters
            mysqli_stmt_bind_param($stmt, "i", $student_application_id);
    
            // Execute the statement
            mysqli_stmt_execute($stmt);
    
            // Bind the result variables
            mysqli_stmt_bind_result($stmt, $created_by);
    
            // Fetch the result
            mysqli_stmt_fetch($stmt);
    
            // Close the statement
            mysqli_stmt_close($stmt);
    
            // Return the created_by value
            return $created_by;
        } else {
            
            return null;
        }


    

}
}