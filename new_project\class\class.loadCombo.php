<?php


include_once 'connection.php';



class LoadCombo extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    public function LoadCombo()
    {
        self::__construct();
    }

    private $_conn;

    public function getAgentCombo($criteria, $combo_name = 'agent_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];


        $query = "select c.agent_id,concat(c.first_name ,' ','(',c.email,')') from ggportal_tbl_agent c  where 1=1  $cond order by c.agent_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Agent", true);
    }

    public function getTimelineCombo($criteria, $combo_name = 'timeline_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];


        $query = "select c.timeline_id,concat(c.timeline_name ,' ') from ggportal_tbl_timeline c  where 1=1   $cond order by c.timeline_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Timeline", true);
    }


    public function getStaffPrivilegeCombo($criteria, $combo_name = 'staff_privilege_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];


        $query = "select c.staff_privilege_id,concat(c.name ,' ') from ggportal_tbl_staff_privilege c  where 1=1  and c.active_yn = 'Y' AND c.name <> 'Agent Staff' $cond order by c.staff_privilege_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Type", true);
    }


    public function getStaffCombo($criteria, $combo_name = 'staff_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['parent_user_id']) && strlen($criteria['parent_user_id']) > 0) {
            $parent_user_id = intval($criteria['parent_user_id']);
            $cond .= " and c.parent_user_id  = $parent_user_id ";
        }

        if (isset($criteria['parent_user_type']) && strlen($criteria['parent_user_type']) > 0) {
            $parent_user_type = $criteria['parent_user_type'];
            $cond .= " and c.parent_user_type = '$parent_user_type' ";
        }



        $query = "select c.staff_id,concat(c.first_name ,' ',c.last_name,' ','(',c.email,')') from ggportal_tbl_staff c 
        left join ggportal_tbl_staff_privilege sp on sp.staff_privilege_id = c.user_privilege_id
        where 1=1 and sp.active_yn = 'Y'  $cond order by c.staff_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Staff", true);
    }

    public function getStatusCombo($criteria, $combo_name = 'status_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $query = "select c.application_status_id,concat(c.status_name ,' ',c.status_code)AS application_status_name from ggportal_tbl_application_status c  where 1=1 $cond order by c.application_status_id;";
        //echo $query;
        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
		
    }


    public function getStudentCombo($criteria, $combo_name = 'student_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['student_id']) && strlen($criteria['student_id']) > 0) {
            $student_id = $criteria['student_id'];
            $cond .= " and c.student_id like '%$student_id%' ";
        }

        if (isset($criteria['staff_id']) && strlen($criteria['staff_id']) > 0) {
            $assign_to_staff = intval($criteria['staff_id']);
            $cond .= " and c.assign_to_staff  = $assign_to_staff ";
        }

        if (isset($criteria['agent_id']) && strlen($criteria['agent_id']) > 0) {           
            $assign_to_staff = array_map('intval', (array)$criteria['staffs_id']);
            $assign_to_agent = intval($criteria['agent_id']);            
            $assign_to_staff_str = implode(',', $assign_to_staff);                  
            $cond .= " and c.assign_to_agent = $assign_to_agent";
            $cond .= " or c.assign_to_staff IN ($assign_to_staff_str)";
        }

        if (isset($criteria['country_id']) && strlen($criteria['country_id']) > 0) {
            $country_id = $criteria['country_id'];
            $cond .= " and c.country = '$country_id' ";
        }

        $query = "select c.student_id,concat(c.first_name ,' ',c.last_name,' ','(',c.email,')') from ggportal_tbl_student c  where 1=1 $cond order by c.student_id;";
        //echo $query;       

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Student", true);
    }

    public function getCurrencyCombo($criteria, $combo_name = 'currency_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['currency_id']) && strlen($criteria['currency_id']) > 0) {
            $currency_id = $criteria['currency_id'];
            $cond .= " and c.currency_id like '%$currency_id%' ";
        }

        $query = "select c.currency_id,concat(c.currency_code ,' ',c.currency_symbol) from ggportal_tbl_currency c  where 1=1 $cond order by c.currency_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Currency", true);
    }

    function getCurrencyName($currency_code)
    {
        $conn = $this->_conn;

        $cond = "c.currency_code = '$currency_code' OR c.currency_name = '$currency_code';";
        $query = "SELECT c.currency_id FROM ggportal_tbl_currency c
                    WHERE $cond;";
        $res = mysqli_query($conn, $query);

        if (!$res) {
            return false;
        }

        $row = mysqli_fetch_assoc($res);
        if (!$row || !isset($row['currency_id'])) {
            return false;
        }
        return (int)$row['currency_id'];
    }
    function getCurrencyNameById($currency_id)
    {
        $conn = $this->_conn;
        $currency_id = intval($currency_id);
    
        // Initialize the currency name variable
        $currency_name = "";
    
        // Check if the currency_id is valid
        if ($currency_id > 0) {
            // Use prepared statement to prevent SQL injection
            $query = "SELECT currency_name FROM ggportal_tbl_currency WHERE currency_id = ?";
            $stmt = mysqli_prepare($conn, $query);
    
            if ($stmt) {
                // Bind the currency_id parameter
                mysqli_stmt_bind_param($stmt, "i", $currency_id);
                
                // Execute the query
                mysqli_stmt_execute($stmt);
                
                // Bind the result
                mysqli_stmt_bind_result($stmt, $currency_name);
                
                // Fetch the result
                mysqli_stmt_fetch($stmt);
                
                // Close the statement
                mysqli_stmt_close($stmt);
            } else {
                // Error handling for failed prepared statement
                $error_message = mysqli_error($conn);
                echo "Error creating prepared statement: $error_message";
                // You can log or handle this error as needed
            }
        }
        
        // Return the currency name
        return $currency_name;
    }
    

    public function getStateCombo($criteria, $combo_name = 'state_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['country_id']) && strlen($criteria['country_id']) > 0) {
            $country_id = $criteria['country_id'];
            $cond .= " and c.country_id like '%$country_id%' ";
        }

        $query = "select c.state_id,concat(c.state_name ,' ') from ggportal_tbl_state c  where 1=1 $cond order by c.state_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " State", true);
    }

    public function getCountryCombo($criteria, $combo_name = 'country_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.country_id,concat(c.country_name ,' ') from ggportal_tbl_country c  where 1=1 $cond order by c.country_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Country", true);
    }

    public function getprovinceCombo($criteria, $combo_name = 'id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.id,concat(c.province ,' ') from ggportal_tbl_province c  where 1=1 $cond order by c.id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, "province", true);
    }

    public function getAllprovinceCombo($criteria, $combo_name = 'id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.id,concat(c.province ,' ') from ggportal_tbl_province c  where 1=1 $cond order by c.id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, "  Province", true);
    }



    //Codeaisys#Sithum#Load Country To Apply And country of resident ComboBox
    //Changes related to program finder start here
    public function getCountryToApplyAndCountryOfStudent()
    {

        $query = " SELECT c.country_id, CONCAT(c.country_name, ' (', c.country_code, ')') As country_name FROM ggportal_tbl_country c WHERE 1=1  ORDER BY
                CASE
                WHEN c.country_code IN ('AU', 'CA', 'UK') THEN 1
                ELSE 2
                END,   c.country_id;";


        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
    }


    //Codeaisys#Sithum#Load program type ComboBox
    public function getProgramTypeComboValues()
    {
        $query = "select DISTINCT p.course_type,concat(p.course_type ,' ') from ggportal_tbl_program p  where 1=1 and p.course_type<>'' order by p.program_id;";

        $result = mysqli_query($this->_conn, $query);

        $courseTypes = array();

        while ($row = mysqli_fetch_assoc($result)) {
            $courseTypes[] = $row['course_type'];
        }
        return $courseTypes;
    }


    //Codeaisys#Sithum#Load course type ComboBox
    public function getStudyCourseComboValues()
    {
        $query = "select c.course_id,concat(c.course_name ,' ')As course_name from ggportal_tbl_course c order by c.course_id;";

        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
    }

    //Codeaisys#Sithum#Load course type ComboBox
    public function getStudyIntakeComboValues()
    {
        $query = "select  DISTINCT p.intake,concat(p.intake ,' ')As intake_name from ggportal_tbl_program p  order by p.intake ASC;";

        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
    }
    //return duration
    public function getDurationComboValues()
    {
        $query = "SELECT DISTINCT duration FROM ggportal_tbl_program;";

        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
    }
    //return institute_name
    public function getUniversityComboValues()
    {
        $query = "SELECT DISTINCT institute_id, institute_name FROM ggportal_tbl_institute;";

        $result = mysqli_query($this->_conn, $query);
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }
        if (!empty($resultset)) {
            return $resultset;
        }
    }



    //Codeaisys#Sithum#Load Institute type ComboBox according to the filter criteria
    public function getStudyInstituteComboValueByFilter($country_to_apply_idPHP, $country_to_apply_namePHP, $course_typePHP, $intake_namePHP, $course_namePHP, $course_name_idPHP)
    {

        $query = "select i.institute_id,i.institute_name from ggportal_tbl_institute i 
     join ggportal_tbl_program p
     ON p.institute_id = i.institute_id
     where p.course_type = '$course_typePHP' AND p.country_id = $country_to_apply_idPHP AND p.course_id =$course_name_idPHP  AND p.intake = '$intake_namePHP' ";

        $result = mysqli_query($this->_conn, $query);

        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }

        if (!empty($resultset)) {
            return $resultset;
        }
    }
    //Changes related to program finder end here

    public function getStudyCountryCombo($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        // $query = "select c.country_id,concat(c.country_name ,'(', c.country_code, ') ') from ggportal_tbl_country c  where 1=1 $cond order by c.country_id;";
        $query = "SELECT c.country_id, CONCAT(c.country_name, ' (', c.country_code, ')') FROM ggportal_tbl_country c WHERE 1=1 $cond ORDER BY
                  CASE
                    WHEN c.country_code IN ('AU', 'CA', 'UK') THEN 1
                    ELSE 2
                   END,   c.country_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, " Country To Apply", true);
    }

    public function getStudyPassportCountryCombo($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.country_id,concat(c.country_name ,'(', c.country_code, ') ') from ggportal_tbl_country c  where 1=1 $cond order by c.country_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, " Country Of Student's Passport", true);
    }

    public function getStudyCourseCombo($criteria, $combo_name = 'course_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.course_id,concat(c.course_name ,' ') from ggportal_tbl_course c  where 1=1 $cond order by c.course_id;";
        //echo $query;


        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, " Course To Apply", true);
    }

    public function getStudyProgramCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select p.program_id,concat(p.program_name ,' ') from ggportal_tbl_program p  where 1=1 $cond order by p.program_id;";
        //echo $query;


        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, "Program", true);
    }

    public function getProgramTypeCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $query = "select DISTINCT p.course_type,concat(p.course_type ,' ') from ggportal_tbl_program p  where 1=1 and p.course_type<>'' $cond order by p.program_id;";
        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, " Course Type", true);
    }
    //Codeaisys#Sithum#program-search-result
    public function getProgramTypeComboArrayByCourseID($criteria, $combo_name = 'program_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $query = "select DISTINCT p.course_type, concat(p.course_type, ' ') from ggportal_tbl_program p where course_id =$id_match   and p.course_type <> '' $cond order by p.program_id";
        $result = mysqli_query($this->_conn, $query);

        $program_combo = array(); // Initialize an array to store the data

        while ($row = mysqli_fetch_assoc($result)) {
            // Assuming the 'course_type' column holds the program type
            $program_combo[$row['course_type']] = $row['course_type'];
        }

        return $program_combo;
    }



    public function getProgramTypeComboArray($criteria, $combo_name = 'program_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $query = "select DISTINCT p.course_type, concat(p.course_type, ' ') from ggportal_tbl_program p where 1=1 and p.course_type <> '' $cond order by p.program_id;";
        $result = mysqli_query($this->_conn, $query);

        $program_combo = array(); // Initialize an array to store the data

        while ($row = mysqli_fetch_assoc($result)) {
            // Assuming the 'course_type' column holds the program type
            $program_combo[$row['course_type']] = $row['course_type'];
        }

        return $program_combo;
    }

    public function getStudyIntakeCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select  DISTINCT p.intake,concat(p.intake ,' ') from ggportal_tbl_program p  where 1=1 $cond order by p.intake ASC;";
        //echo $query;


        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, "  Intake", true);
    }
    //#Codeaisys#Sithum#filetr According to country
    public function getStudyInstituteComboBySelectedCountery($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];




        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select i.institute_id,concat( i.institute_name,' ') from ggportal_tbl_institute i  where institute_country_id = $id_match  order by i.institute_id;";
        //echo $query;


        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, "  University", true);
    }

    public function getStudyInstituteCombo($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];




        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select i.institute_id,concat( i.institute_name,' ') from ggportal_tbl_institute i  where 1=1 $cond order by i.institute_id;";
        //echo $query;


        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4_first_disabled($id_match, $result, $combo_name, $function_to_call, $required, "  University", true);
    }

    //Codeaisys#Sithum#Filter institute by country
    public function getStudyInstituteComboArrayCountryByFilter($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $query = "select i.institute_id, concat(i.institute_name, ' ') as institute_name from ggportal_tbl_institute i where institute_country_id = $id_match  order by i.institute_id;";

        $result = mysqli_query($this->_conn, $query);

        $institute_array = array();

        while ($row = mysqli_fetch_assoc($result)) {
            $institute_array[] = array(
                'id' => $row['institute_id'],
                'name' => $row['institute_name']
            );
        }

        return $institute_array;
    }


    public function getStudyInstituteComboArray($criteria, $combo_name = 'country_id', $function_to_call = '', $required = 'required')
    {
        $cond = "";

        $query = "select i.institute_id, concat(i.institute_name, ' ') as institute_name from ggportal_tbl_institute i where 1=1 $cond order by i.institute_id;";

        $result = mysqli_query($this->_conn, $query);

        $institute_array = array();

        while ($row = mysqli_fetch_assoc($result)) {
            $institute_array[] = array(
                'id' => $row['institute_id'],
                'name' => $row['institute_name']
            );
        }

        return $institute_array;
    }



    public function getProgramMultipleCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0) {
            $institute_id = intval($criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }

        $query = "select p.program_id ,concat(p.program_name ,' ') from ggportal_tbl_program p
        where 1=1 $cond group by p.program_id  order by p.program_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_select2_multiple($id_match, $result, $combo_name, $function_to_call, $required, " Program", true);
    }
    public function getProgramComboNew($criteria, $combo_name = 'program_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0) {
            $institute_id = intval($criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }

        $query = "select p.program_id ,concat(p.program_name ,' ') from ggportal_tbl_program p
        where 1=1 $cond group by p.program_id  order by p.program_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Program", true);
    }

    public function getProgramCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0 ) {
            $institute_id = intval( $criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }*/

        $query = "select p.program_id ,concat(p.program_name ,' ') from ggportal_tbl_program p
        where 1=1 $cond group by p.program_id  order by p.program_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Program", true);
    }

    public function getIntakeCombo($criteria, $combo_name = 'program_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['institute_id']) && strlen($criteria['institute_id']) > 0 ) {
            $institute_id = intval( $criteria['institute_id']);
            $cond .= " and p.institute_id  = $institute_id ";
        }*/

        $query = "select p.program_id ,concat(p.intake,' ') from ggportal_tbl_program p
        where 1=1 $cond group by p.program_id  order by p.program_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Program", true);
    }

    public function getCourseCombo($criteria, $combo_name = 'course_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select c.course_id,concat(c.course_name ,' ') from ggportal_tbl_course c  where 1=1 $cond order by c.course_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " course", true);
    }


    public function getInstituteCombo($criteria, $combo_name = 'institute_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        /*if (isset($criteria['is_active']) && strlen($criteria['is_active']) > 0 ) {
            $is_active = $criteria['is_active'];
            $cond .= " and r.is_active like '%$is_active%' ";
        }*/

        $query = "select i.institute_id,concat(i.institute_name ,' ') from ggportal_tbl_institute i  where 1=1 $cond order by i.institute_id;";
        //echo $query;

        $result = mysqli_query($this->_conn, $query);

        return db_to_combo_bs4($id_match, $result, $combo_name, $function_to_call, $required, " Institute", true);
    }





    public function getAllCountryCombo($criteria, $combo_name = 'country_id', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $all = '';
        if (isset($criteria['all']) && strlen($criteria['all']) > 0) {
            $all = $criteria['all'];
        }

        $json = '[
            {"name": "Albania", "code": "AL" },
            {"name": "Åland Islands", "code": "AX"},
            {"name": "Algeria", "code": "DZ"},
            {"name": "American Samoa", "code": "AS"},
            {"name": "Andorra", "code": "AD"},
            {"name": "Angola", "code": "AO"},
            {"name": "Anguilla", "code": "AI"},
            {"name": "Antarctica", "code": "AQ"},
            {"name": "Antigua and Barbuda", "code": "AG"},
            {"name": "Argentina", "code": "AR"},
            {"name": "Armenia", "code": "AM"},
            {"name": "Aruba", "code": "AW"},
            {"name": "Australia", "code": "AU"},
            {"name": "Austria", "code": "AT"},
            {"name": "Azerbaijan", "code": "AZ"},
            {"name": "Bahamas (the)", "code": "BS"},
            {"name": "Bahrain", "code": "BH"},
            {"name": "Bangladesh", "code": "BD"},
            {"name": "Barbados", "code": "BB"},
            {"name": "Belarus", "code": "BY"},
            {"name": "Belgium", "code": "BE"},
            {"name": "Belize", "code": "BZ"},
            {"name": "Benin", "code": "BJ"},
            {"name": "Bermuda", "code": "BM"},
            {"name": "Bhutan", "code": "BT"},
            {"name": "Bolivia (Plurinational State of)", "code": "BO"},
            {"name": "Bonaire, Sint Eustatius and Saba", "code": "BQ"},
            {"name": "Bosnia and Herzegovina", "code": "BA"},
            {"name": "Botswana", "code": "BW"},
            {"name": "Bouvet Island", "code": "BV"},
            {"name": "Brazil", "code": "BR"},
            {"name": "British Indian Ocean Territory (the)", "code": "IO"},
            {"name": "Brunei Darussalam", "code": "BN"},
            {"name": "Bulgaria", "code": "BG"},
            {"name": "Burkina Faso", "code": "BF"},
            {"name": "Burundi", "code": "BI"},
            {"name": "Cabo Verde", "code": "CV"},
            {"name": "Cambodia", "code": "KH"},
            {"name": "Cameroon", "code": "CM"},
            {"name": "Canada", "code": "CA"},
            {"name": "Cayman Islands (the)", "code": "KY"},
            {"name": "Central African Republic (the)", "code": "CF"},
            {"name": "Chad", "code": "TD"},
            {"name": "Chile", "code": "CL"},
            {"name": "China", "code": "CN"},
            {"name": "Christmas Island", "code": "CX"},
            {"name": "Cocos (Keeling) Islands (the)", "code": "CC"},
            {"name": "Colombia", "code": "CO"},
            {"name": "Comoros (the)", "code": "KM"},
            {"name": "Congo (the Democratic Republic of the)", "code": "CD"},
            {"name": "Congo (the)", "code": "CG"},
            {"name": "Cook Islands (the)", "code": "CK"},
            {"name": "Costa Rica", "code": "CR"},
            {"name": "Croatia", "code": "HR"},
            {"name": "Cuba", "code": "CU"},
            {"name": "Curaçao", "code": "CW"},
            {"name": "Cyprus", "code": "CY"},
            {"name": "Czechia", "code": "CZ"},
            {"name": "Côte d\'Ivoire", "code": "CI"},
            {"name": "Denmark", "code": "DK"},
            {"name": "Djibouti", "code": "DJ"},
            {"name": "Dominica", "code": "DM"},
            {"name": "Dominican Republic (the)", "code": "DO"},
            {"name": "Ecuador", "code": "EC"},
            {"name": "Egypt", "code": "EG"},
            {"name": "El Salvador", "code": "SV"},
            {"name": "Equatorial Guinea", "code": "GQ"},
            {"name": "Eritrea", "code": "ER"},
            {"name": "Estonia", "code": "EE"},
            {"name": "Eswatini", "code": "SZ"},
            {"name": "Ethiopia", "code": "ET"},
            {"name": "Falkland Islands (the) [Malvinas]", "code": "FK"},
            {"name": "Faroe Islands (the)", "code": "FO"},
            {"name": "Fiji", "code": "FJ"},
            {"name": "Finland", "code": "FI"},
            {"name": "France", "code": "FR"},
            {"name": "French Guiana", "code": "GF"},
            {"name": "French Polynesia", "code": "PF"},
            {"name": "French Southern Territories (the)", "code": "TF"},
            {"name": "Gabon", "code": "GA"},
            {"name": "Gambia (the)", "code": "GM"},
            {"name": "Georgia", "code": "GE"},
            {"name": "Germany", "code": "DE"},
            {"name": "Ghana", "code": "GH"},
            {"name": "Gibraltar", "code": "GI"},
            {"name": "Greece", "code": "GR"},
            {"name": "Greenland", "code": "GL"},
            {"name": "Grenada", "code": "GD"},
            {"name": "Guadeloupe", "code": "GP"},
            {"name": "Guam", "code": "GU"},
            {"name": "Guatemala", "code": "GT"},
            {"name": "Guernsey", "code": "GG"},
            {"name": "Guinea", "code": "GN"},
            {"name": "Guinea-Bissau", "code": "GW"},
            {"name": "Guyana", "code": "GY"},
            {"name": "Haiti", "code": "HT"},
            {"name": "Heard Island and McDonald Islands", "code": "HM"},
            {"name": "Holy See (the)", "code": "VA"},
            {"name": "Honduras", "code": "HN"},
            {"name": "Hong Kong", "code": "HK"},
            {"name": "Hungary", "code": "HU"},
            {"name": "Iceland", "code": "IS"},
            {"name": "India", "code": "IN"},
            {"name": "Indonesia", "code": "ID"},
            {"name": "Iran (Islamic Republic of)", "code": "IR"},
            {"name": "Iraq", "code": "IQ"},
            {"name": "Ireland", "code": "IE"},
            {"name": "Isle of Man", "code": "IM"},
            {"name": "Israel", "code": "IL"},
            {"name": "Italy", "code": "IT"},
            {"name": "Jamaica", "code": "JM"},
            {"name": "Japan", "code": "JP"},
            {"name": "Jersey", "code": "JE"},
            {"name": "Jordan", "code": "JO"},
            {"name": "Kazakhstan", "code": "KZ"},
            {"name": "Kenya", "code": "KE"},
            {"name": "Kiribati", "code": "KI"},
            {"name": "Korea (the Democratic People\'s Republic of)", "code": "KP"},
            {"name": "Korea (the Republic of)", "code": "KR"},
            {"name": "Kuwait", "code": "KW"},
            {"name": "Kyrgyzstan", "code": "KG"},
            {"name": "Lao People\'s Democratic Republic (the)", "code": "LA"},
            {"name": "Latvia", "code": "LV"},
            {"name": "Lebanon", "code": "LB"},
            {"name": "Lesotho", "code": "LS"},
            {"name": "Liberia", "code": "LR"},
            {"name": "Libya", "code": "LY"},
            {"name": "Liechtenstein", "code": "LI"},
            {"name": "Lithuania", "code": "LT"},
            {"name": "Luxembourg", "code": "LU"},
            {"name": "Macao", "code": "MO"},
            {"name": "Madagascar", "code": "MG"},
            {"name": "Malawi", "code": "MW"},
            {"name": "Malaysia", "code": "MY"},
            {"name": "Maldives", "code": "MV"},
            {"name": "Mali", "code": "ML"},
            {"name": "Malta", "code": "MT"},
            {"name": "Marshall Islands (the)", "code": "MH"},
            {"name": "Martinique", "code": "MQ"},
            {"name": "Mauritania", "code": "MR"},
            {"name": "Mauritius", "code": "MU"},
            {"name": "Mayotte", "code": "YT"},
            {"name": "Mexico", "code": "MX"},
            {"name": "Micronesia (Federated States of)", "code": "FM"},
            {"name": "Moldova (the Republic of)", "code": "MD"},
            {"name": "Monaco", "code": "MC"},
            {"name": "Mongolia", "code": "MN"},
            {"name": "Montenegro", "code": "ME"},
            {"name": "Montserrat", "code": "MS"},
            {"name": "Morocco", "code": "MA"},
            {"name": "Mozambique", "code": "MZ"},
            {"name": "Myanmar", "code": "MM"},
            {"name": "Namibia", "code": "NA"},
            {"name": "Nauru", "code": "NR"},
            {"name": "Nepal", "code": "NP"},
            {"name": "Netherlands (the)", "code": "NL"},
            {"name": "New Caledonia", "code": "NC"},
            {"name": "New Zealand", "code": "NZ"},
            {"name": "Nicaragua", "code": "NI"},
            {"name": "Niger (the)", "code": "NE"},
            {"name": "Nigeria", "code": "NG"},
            {"name": "Niue", "code": "NU"},
            {"name": "Norfolk Island", "code": "NF"},
            {"name": "Northern Mariana Islands (the)", "code": "MP"},
            {"name": "Norway", "code": "NO"},
            {"name": "Oman", "code": "OM"},
            {"name": "Pakistan", "code": "PK"},
            {"name": "Palau", "code": "PW"},
            {"name": "Palestine, State of", "code": "PS"},
            {"name": "Panama", "code": "PA"},
            {"name": "Papua New Guinea", "code": "PG"},
            {"name": "Paraguay", "code": "PY"},
            {"name": "Peru", "code": "PE"},
            {"name": "Philippines (the)", "code": "PH"},
            {"name": "Pitcairn", "code": "PN"},
            {"name": "Poland", "code": "PL"},
            {"name": "Portugal", "code": "PT"},
            {"name": "Puerto Rico", "code": "PR"},
            {"name": "Qatar", "code": "QA"},
            {"name": "Republic of North Macedonia", "code": "MK"},
            {"name": "Romania", "code": "RO"},
            {"name": "Russian Federation (the)", "code": "RU"},
            {"name": "Rwanda", "code": "RW"},
            {"name": "Réunion", "code": "RE"},
            {"name": "Saint Barthélemy", "code": "BL"},
            {"name": "Saint Helena, Ascension and Tristan da Cunha", "code": "SH"},
            {"name": "Saint Kitts and Nevis", "code": "KN"},
            {"name": "Saint Lucia", "code": "LC"},
            {"name": "Saint Martin (French part)", "code": "MF"},
            {"name": "Saint Pierre and Miquelon", "code": "PM"},
            {"name": "Saint Vincent and the Grenadines", "code": "VC"},
            {"name": "Samoa", "code": "WS"},
            {"name": "San Marino", "code": "SM"},
            {"name": "Sao Tome and Principe", "code": "ST"},
            {"name": "Saudi Arabia", "code": "SA"},
            {"name": "Senegal", "code": "SN"},
            {"name": "Serbia", "code": "RS"},
            {"name": "Seychelles", "code": "SC"},
            {"name": "Sierra Leone", "code": "SL"},
            {"name": "Singapore", "code": "SG"},
            {"name": "Sint Maarten (Dutch part)", "code": "SX"},
            {"name": "Slovakia", "code": "SK"},
            {"name": "Slovenia", "code": "SI"},
            {"name": "Solomon Islands", "code": "SB"},
            {"name": "Somalia", "code": "SO"},
            {"name": "South Africa", "code": "ZA"},
            {"name": "South Georgia and the South Sandwich Islands", "code": "GS"},
            {"name": "South Sudan", "code": "SS"},
            {"name": "Spain", "code": "ES"},
            {"name": "Sri Lanka", "code": "LK"},
            {"name": "Sudan (the)", "code": "SD"},
            {"name": "Suriname", "code": "SR"},
            {"name": "Svalbard and Jan Mayen", "code": "SJ"},
            {"name": "Sweden", "code": "SE"},
            {"name": "Switzerland", "code": "CH"},
            {"name": "Syrian Arab Republic", "code": "SY"},
            {"name": "Taiwan (Province of China)", "code": "TW"},
            {"name": "Tajikistan", "code": "TJ"},
            {"name": "Tanzania, United Republic of", "code": "TZ"},
            {"name": "Thailand", "code": "TH"},
            {"name": "Timor-Leste", "code": "TL"},
            {"name": "Togo", "code": "TG"},
            {"name": "Tokelau", "code": "TK"},
            {"name": "Tonga", "code": "TO"},
            {"name": "Trinidad and Tobago", "code": "TT"},
            {"name": "Tunisia", "code": "TN"},
            {"name": "Turkey", "code": "TR"},
            {"name": "Turkmenistan", "code": "TM"},
            {"name": "Turks and Caicos Islands (the)", "code": "TC"},
            {"name": "Tuvalu", "code": "TV"},
            {"name": "Uganda", "code": "UG"},
            {"name": "Ukraine", "code": "UA"},
            {"name": "United Arab Emirates (the)", "code": "AE"},
            {"name": "United Kingdom of Great Britain and Northern Ireland (the)", "code": "GB"},
            {"name": "United States Minor Outlying Islands (the)", "code": "UM"},
            {"name": "United States of America (the)", "code": "US"},
            {"name": "Uruguay", "code": "UY"},
            {"name": "Uzbekistan", "code": "UZ"},
            {"name": "Vanuatu", "code": "VU"},
            {"name": "Venezuela (Bolivarian Republic of)", "code": "VE"},
            {"name": "Viet Nam", "code": "VN"},
            {"name": "Virgin Islands (British)", "code": "VG"},
            {"name": "Virgin Islands (U.S.)", "code": "VI"},
            {"name": "Wallis and Futuna", "code": "WF"},
            {"name": "Western Sahara", "code": "EH"},
            {"name": "Yemen", "code": "YE"},
            {"name": "Zambia", "code": "ZM"},
            {"name": "Zimbabwe", "code": "ZW"}
          ]';



        $result = json_decode($json, true);



        return db_to_combo_bs4_json($all, $id_match, $result, $combo_name, $function_to_call, $required, " Country", true);
    }




    public function getAllCountryPhoneCombo($criteria, $combo_name = 'country_phone_code', $function_to_call = '', $required = '')
    {
        $cond = "";
        $id_match = $criteria['id_match'];

        $json = '[
            {
            "name": "Sri Lanka",
            "dial_code": "+94",
            "code": "LK"
            },
            {
            "name": "Afghanistan",
            "dial_code": "+93",
            "code": "AF"
            },
            {
            "name": "Aland Islands",
            "dial_code": "+358",
            "code": "AX"
            },
            {
            "name": "Albania",
            "dial_code": "+355",
            "code": "AL"
            },
            {
            "name": "Algeria",
            "dial_code": "+213",
            "code": "DZ"
            },
            {
            "name": "AmericanSamoa",
            "dial_code": "+1684",
            "code": "AS"
            },
            {
            "name": "Andorra",
            "dial_code": "+376",
            "code": "AD"
            },
            {
            "name": "Angola",
            "dial_code": "+244",
            "code": "AO"
            },
            {
            "name": "Anguilla",
            "dial_code": "+1264",
            "code": "AI"
            },
            {
            "name": "Antarctica",
            "dial_code": "+672",
            "code": "AQ"
            },
            {
            "name": "Antigua and Barbuda",
            "dial_code": "+1268",
            "code": "AG"
            },
            {
            "name": "Argentina",
            "dial_code": "+54",
            "code": "AR"
            },
            {
            "name": "Armenia",
            "dial_code": "+374",
            "code": "AM"
            },
            {
            "name": "Aruba",
            "dial_code": "+297",
            "code": "AW"
            },
            {
            "name": "Australia",
            "dial_code": "+61",
            "code": "AU"
            },
            {
            "name": "Austria",
            "dial_code": "+43",
            "code": "AT"
            },
            {
            "name": "Azerbaijan",
            "dial_code": "+994",
            "code": "AZ"
            },
            {
            "name": "Bahamas",
            "dial_code": "+1242",
            "code": "BS"
            },
            {
            "name": "Bahrain",
            "dial_code": "+973",
            "code": "BH"
            },
            {
            "name": "Bangladesh",
            "dial_code": "+880",
            "code": "BD"
            },
            {
            "name": "Barbados",
            "dial_code": "+1246",
            "code": "BB"
            },
            {
            "name": "Belarus",
            "dial_code": "+375",
            "code": "BY"
            },
            {
            "name": "Belgium",
            "dial_code": "+32",
            "code": "BE"
            },
            {
            "name": "Belize",
            "dial_code": "+501",
            "code": "BZ"
            },
            {
            "name": "Benin",
            "dial_code": "+229",
            "code": "BJ"
            },
            {
            "name": "Bermuda",
            "dial_code": "+1441",
            "code": "BM"
            },
            {
            "name": "Bhutan",
            "dial_code": "+975",
            "code": "BT"
            },
            {
            "name": "Bolivia, Plurinational State of",
            "dial_code": "+591",
            "code": "BO"
            },
            {
            "name": "Bosnia and Herzegovina",
            "dial_code": "+387",
            "code": "BA"
            },
            {
            "name": "Botswana",
            "dial_code": "+267",
            "code": "BW"
            },
            {
            "name": "Brazil",
            "dial_code": "+55",
            "code": "BR"
            },
            {
            "name": "British Indian Ocean Territory",
            "dial_code": "+246",
            "code": "IO"
            },
            {
            "name": "Brunei Darussalam",
            "dial_code": "+673",
            "code": "BN"
            },
            {
            "name": "Bulgaria",
            "dial_code": "+359",
            "code": "BG"
            },
            {
            "name": "Burkina Faso",
            "dial_code": "+226",
            "code": "BF"
            },
            {
            "name": "Burundi",
            "dial_code": "+257",
            "code": "BI"
            },
            {
            "name": "Cambodia",
            "dial_code": "+855",
            "code": "KH"
            },
            {
            "name": "Cameroon",
            "dial_code": "+237",
            "code": "CM"
            },
            {
            "name": "Canada",
            "dial_code": "+1",
            "code": "CA"
            },
            {
            "name": "Cape Verde",
            "dial_code": "+238",
            "code": "CV"
            },
            {
            "name": "Cayman Islands",
            "dial_code": "+ 345",
            "code": "KY"
            },
            {
            "name": "Central African Republic",
            "dial_code": "+236",
            "code": "CF"
            },
            {
            "name": "Chad",
            "dial_code": "+235",
            "code": "TD"
            },
            {
            "name": "Chile",
            "dial_code": "+56",
            "code": "CL"
            },
            {
            "name": "China",
            "dial_code": "+86",
            "code": "CN"
            },
            {
            "name": "Christmas Island",
            "dial_code": "+61",
            "code": "CX"
            },
            {
            "name": "Cocos (Keeling) Islands",
            "dial_code": "+61",
            "code": "CC"
            },
            {
            "name": "Colombia",
            "dial_code": "+57",
            "code": "CO"
            },
            {
            "name": "Comoros",
            "dial_code": "+269",
            "code": "KM"
            },
            {
            "name": "Congo",
            "dial_code": "+242",
            "code": "CG"
            },
            {
            "name": "Congo, The Democratic Republic of the Congo",
            "dial_code": "+243",
            "code": "CD"
            },
            {
            "name": "Cook Islands",
            "dial_code": "+682",
            "code": "CK"
            },
            {
            "name": "Costa Rica",
            "dial_code": "+506",
            "code": "CR"
            },
            {
            "name": "Cote dIvoire",
            "dial_code": "+225",
            "code": "CI"
            },
            {
            "name": "Croatia",
            "dial_code": "+385",
            "code": "HR"
            },
            {
            "name": "Cuba",
            "dial_code": "+53",
            "code": "CU"
            },
            {
            "name": "Cyprus",
            "dial_code": "+357",
            "code": "CY"
            },
            {
            "name": "Czech Republic",
            "dial_code": "+420",
            "code": "CZ"
            },
            {
            "name": "Denmark",
            "dial_code": "+45",
            "code": "DK"
            },
            {
            "name": "Djibouti",
            "dial_code": "+253",
            "code": "DJ"
            },
            {
            "name": "Dominica",
            "dial_code": "+1767",
            "code": "DM"
            },
            {
            "name": "Dominican Republic",
            "dial_code": "+1849",
            "code": "DO"
            },
            {
            "name": "Ecuador",
            "dial_code": "+593",
            "code": "EC"
            },
            {
            "name": "Egypt",
            "dial_code": "+20",
            "code": "EG"
            },
            {
            "name": "El Salvador",
            "dial_code": "+503",
            "code": "SV"
            },
            {
            "name": "Equatorial Guinea",
            "dial_code": "+240",
            "code": "GQ"
            },
            {
            "name": "Eritrea",
            "dial_code": "+291",
            "code": "ER"
            },
            {
            "name": "Estonia",
            "dial_code": "+372",
            "code": "EE"
            },
            {
            "name": "Ethiopia",
            "dial_code": "+251",
            "code": "ET"
            },
            {
            "name": "Falkland Islands (Malvinas)",
            "dial_code": "+500",
            "code": "FK"
            },
            {
            "name": "Faroe Islands",
            "dial_code": "+298",
            "code": "FO"
            },
            {
            "name": "Fiji",
            "dial_code": "+679",
            "code": "FJ"
            },
            {
            "name": "Finland",
            "dial_code": "+358",
            "code": "FI"
            },
            {
            "name": "France",
            "dial_code": "+33",
            "code": "FR"
            },
            {
            "name": "French Guiana",
            "dial_code": "+594",
            "code": "GF"
            },
            {
            "name": "French Polynesia",
            "dial_code": "+689",
            "code": "PF"
            },
            {
            "name": "Gabon",
            "dial_code": "+241",
            "code": "GA"
            },
            {
            "name": "Gambia",
            "dial_code": "+220",
            "code": "GM"
            },
            {
            "name": "Georgia",
            "dial_code": "+995",
            "code": "GE"
            },
            {
            "name": "Germany",
            "dial_code": "+49",
            "code": "DE"
            },
            {
            "name": "Ghana",
            "dial_code": "+233",
            "code": "GH"
            },
            {
            "name": "Gibraltar",
            "dial_code": "+350",
            "code": "GI"
            },
            {
            "name": "Greece",
            "dial_code": "+30",
            "code": "GR"
            },
            {
            "name": "Greenland",
            "dial_code": "+299",
            "code": "GL"
            },
            {
            "name": "Grenada",
            "dial_code": "+1473",
            "code": "GD"
            },
            {
            "name": "Guadeloupe",
            "dial_code": "+590",
            "code": "GP"
            },
            {
            "name": "Guam",
            "dial_code": "+1671",
            "code": "GU"
            },
            {
            "name": "Guatemala",
            "dial_code": "+502",
            "code": "GT"
            },
            {
            "name": "Guernsey",
            "dial_code": "+44",
            "code": "GG"
            },
            {
            "name": "Guinea",
            "dial_code": "+224",
            "code": "GN"
            },
            {
            "name": "Guinea-Bissau",
            "dial_code": "+245",
            "code": "GW"
            },
            {
            "name": "Guyana",
            "dial_code": "+595",
            "code": "GY"
            },
            {
            "name": "Haiti",
            "dial_code": "+509",
            "code": "HT"
            },
            {
            "name": "Holy See (Vatican City State)",
            "dial_code": "+379",
            "code": "VA"
            },
            {
            "name": "Honduras",
            "dial_code": "+504",
            "code": "HN"
            },
            {
            "name": "Hong Kong",
            "dial_code": "+852",
            "code": "HK"
            },
            {
            "name": "Hungary",
            "dial_code": "+36",
            "code": "HU"
            },
            {
            "name": "Iceland",
            "dial_code": "+354",
            "code": "IS"
            },
            {
            "name": "India",
            "dial_code": "+91",
            "code": "IN"
            },
            {
            "name": "Indonesia",
            "dial_code": "+62",
            "code": "ID"
            },
            {
            "name": "Iran, Islamic Republic of Persian Gulf",
            "dial_code": "+98",
            "code": "IR"
            },
            {
            "name": "Iraq",
            "dial_code": "+964",
            "code": "IQ"
            },
            {
            "name": "Ireland",
            "dial_code": "+353",
            "code": "IE"
            },
            {
            "name": "Isle of Man",
            "dial_code": "+44",
            "code": "IM"
            },
            {
            "name": "Israel",
            "dial_code": "+972",
            "code": "IL"
            },
            {
            "name": "Italy",
            "dial_code": "+39",
            "code": "IT"
            },
            {
            "name": "Jamaica",
            "dial_code": "+1876",
            "code": "JM"
            },
            {
            "name": "Japan",
            "dial_code": "+81",
            "code": "JP"
            },
            {
            "name": "Jersey",
            "dial_code": "+44",
            "code": "JE"
            },
            {
            "name": "Jordan",
            "dial_code": "+962",
            "code": "JO"
            },
            {
            "name": "Kazakhstan",
            "dial_code": "+77",
            "code": "KZ"
            },
            {
            "name": "Kenya",
            "dial_code": "+254",
            "code": "KE"
            },
            {
            "name": "Kiribati",
            "dial_code": "+686",
            "code": "KI"
            },
            {
            "name": "Korea, Democratic Peoples Republic of Korea",
            "dial_code": "+850",
            "code": "KP"
            },
            {
            "name": "Korea, Republic of South Korea",
            "dial_code": "+82",
            "code": "KR"
            },
            {
            "name": "Kuwait",
            "dial_code": "+965",
            "code": "KW"
            },
            {
            "name": "Kyrgyzstan",
            "dial_code": "+996",
            "code": "KG"
            },
            {
            "name": "Laos",
            "dial_code": "+856",
            "code": "LA"
            },
            {
            "name": "Latvia",
            "dial_code": "+371",
            "code": "LV"
            },
            {
            "name": "Lebanon",
            "dial_code": "+961",
            "code": "LB"
            },
            {
            "name": "Lesotho",
            "dial_code": "+266",
            "code": "LS"
            },
            {
            "name": "Liberia",
            "dial_code": "+231",
            "code": "LR"
            },
            {
            "name": "Libyan Arab Jamahiriya",
            "dial_code": "+218",
            "code": "LY"
            },
            {
            "name": "Liechtenstein",
            "dial_code": "+423",
            "code": "LI"
            },
            {
            "name": "Lithuania",
            "dial_code": "+370",
            "code": "LT"
            },
            {
            "name": "Luxembourg",
            "dial_code": "+352",
            "code": "LU"
            },
            {
            "name": "Macao",
            "dial_code": "+853",
            "code": "MO"
            },
            {
            "name": "Macedonia",
            "dial_code": "+389",
            "code": "MK"
            },
            {
            "name": "Madagascar",
            "dial_code": "+261",
            "code": "MG"
            },
            {
            "name": "Malawi",
            "dial_code": "+265",
            "code": "MW"
            },
            {
            "name": "Malaysia",
            "dial_code": "+60",
            "code": "MY"
            },
            {
            "name": "Maldives",
            "dial_code": "+960",
            "code": "MV"
            },
            {
            "name": "Mali",
            "dial_code": "+223",
            "code": "ML"
            },
            {
            "name": "Malta",
            "dial_code": "+356",
            "code": "MT"
            },
            {
            "name": "Marshall Islands",
            "dial_code": "+692",
            "code": "MH"
            },
            {
            "name": "Martinique",
            "dial_code": "+596",
            "code": "MQ"
            },
            {
            "name": "Mauritania",
            "dial_code": "+222",
            "code": "MR"
            },
            {
            "name": "Mauritius",
            "dial_code": "+230",
            "code": "MU"
            },
            {
            "name": "Mayotte",
            "dial_code": "+262",
            "code": "YT"
            },
            {
            "name": "Mexico",
            "dial_code": "+52",
            "code": "MX"
            },
            {
            "name": "Micronesia, Federated States of Micronesia",
            "dial_code": "+691",
            "code": "FM"
            },
            {
            "name": "Moldova",
            "dial_code": "+373",
            "code": "MD"
            },
            {
            "name": "Monaco",
            "dial_code": "+377",
            "code": "MC"
            },
            {
            "name": "Mongolia",
            "dial_code": "+976",
            "code": "MN"
            },
            {
            "name": "Montenegro",
            "dial_code": "+382",
            "code": "ME"
            },
            {
            "name": "Montserrat",
            "dial_code": "+1664",
            "code": "MS"
            },
            {
            "name": "Morocco",
            "dial_code": "+212",
            "code": "MA"
            },
            {
            "name": "Mozambique",
            "dial_code": "+258",
            "code": "MZ"
            },
            {
            "name": "Myanmar",
            "dial_code": "+95",
            "code": "MM"
            },
            {
            "name": "Namibia",
            "dial_code": "+264",
            "code": "NA"
            },
            {
            "name": "Nauru",
            "dial_code": "+674",
            "code": "NR"
            },
            {
            "name": "Nepal",
            "dial_code": "+977",
            "code": "NP"
            },
            {
            "name": "Netherlands",
            "dial_code": "+31",
            "code": "NL"
            },
            {
            "name": "Netherlands Antilles",
            "dial_code": "+599",
            "code": "AN"
            },
            {
            "name": "New Caledonia",
            "dial_code": "+687",
            "code": "NC"
            },
            {
            "name": "New Zealand",
            "dial_code": "+64",
            "code": "NZ"
            },
            {
            "name": "Nicaragua",
            "dial_code": "+505",
            "code": "NI"
            },
            {
            "name": "Niger",
            "dial_code": "+227",
            "code": "NE"
            },
            {
            "name": "Nigeria",
            "dial_code": "+234",
            "code": "NG"
            },
            {
            "name": "Niue",
            "dial_code": "+683",
            "code": "NU"
            },
            {
            "name": "Norfolk Island",
            "dial_code": "+672",
            "code": "NF"
            },
            {
            "name": "Northern Mariana Islands",
            "dial_code": "+1670",
            "code": "MP"
            },
            {
            "name": "Norway",
            "dial_code": "+47",
            "code": "NO"
            },
            {
            "name": "Oman",
            "dial_code": "+968",
            "code": "OM"
            },
            {
            "name": "Pakistan",
            "dial_code": "+92",
            "code": "PK"
            },
            {
            "name": "Palau",
            "dial_code": "+680",
            "code": "PW"
            },
            {
            "name": "Palestinian Territory, Occupied",
            "dial_code": "+970",
            "code": "PS"
            },
            {
            "name": "Panama",
            "dial_code": "+507",
            "code": "PA"
            },
            {
            "name": "Papua New Guinea",
            "dial_code": "+675",
            "code": "PG"
            },
            {
            "name": "Paraguay",
            "dial_code": "+595",
            "code": "PY"
            },
            {
            "name": "Peru",
            "dial_code": "+51",
            "code": "PE"
            },
            {
            "name": "Philippines",
            "dial_code": "+63",
            "code": "PH"
            },
            {
            "name": "Pitcairn",
            "dial_code": "+872",
            "code": "PN"
            },
            {
            "name": "Poland",
            "dial_code": "+48",
            "code": "PL"
            },
            {
            "name": "Portugal",
            "dial_code": "+351",
            "code": "PT"
            },
            {
            "name": "Puerto Rico",
            "dial_code": "+1939",
            "code": "PR"
            },
            {
            "name": "Qatar",
            "dial_code": "+974",
            "code": "QA"
            },
            {
            "name": "Romania",
            "dial_code": "+40",
            "code": "RO"
            },
            {
            "name": "Russia",
            "dial_code": "+7",
            "code": "RU"
            },
            {
            "name": "Rwanda",
            "dial_code": "+250",
            "code": "RW"
            },
            {
            "name": "Reunion",
            "dial_code": "+262",
            "code": "RE"
            },
            {
            "name": "Saint Barthelemy",
            "dial_code": "+590",
            "code": "BL"
            },
            {
            "name": "Saint Helena, Ascension and Tristan Da Cunha",
            "dial_code": "+290",
            "code": "SH"
            },
            {
            "name": "Saint Kitts and Nevis",
            "dial_code": "+1869",
            "code": "KN"
            },
            {
            "name": "Saint Lucia",
            "dial_code": "+1758",
            "code": "LC"
            },
            {
            "name": "Saint Martin",
            "dial_code": "+590",
            "code": "MF"
            },
            {
            "name": "Saint Pierre and Miquelon",
            "dial_code": "+508",
            "code": "PM"
            },
            {
            "name": "Saint Vincent and the Grenadines",
            "dial_code": "+1784",
            "code": "VC"
            },
            {
            "name": "Samoa",
            "dial_code": "+685",
            "code": "WS"
            },
            {
            "name": "San Marino",
            "dial_code": "+378",
            "code": "SM"
            },
            {
            "name": "Sao Tome and Principe",
            "dial_code": "+239",
            "code": "ST"
            },
            {
            "name": "Saudi Arabia",
            "dial_code": "+966",
            "code": "SA"
            },
            {
            "name": "Senegal",
            "dial_code": "+221",
            "code": "SN"
            },
            {
            "name": "Serbia",
            "dial_code": "+381",
            "code": "RS"
            },
            {
            "name": "Seychelles",
            "dial_code": "+248",
            "code": "SC"
            },
            {
            "name": "Sierra Leone",
            "dial_code": "+232",
            "code": "SL"
            },
            {
            "name": "Singapore",
            "dial_code": "+65",
            "code": "SG"
            },
            {
            "name": "Slovakia",
            "dial_code": "+421",
            "code": "SK"
            },
            {
            "name": "Slovenia",
            "dial_code": "+386",
            "code": "SI"
            },
            {
            "name": "Solomon Islands",
            "dial_code": "+677",
            "code": "SB"
            },
            {
            "name": "Somalia",
            "dial_code": "+252",
            "code": "SO"
            },
            {
            "name": "South Africa",
            "dial_code": "+27",
            "code": "ZA"
            },
            {
            "name": "South Sudan",
            "dial_code": "+211",
            "code": "SS"
            },
            {
            "name": "South Georgia and the South Sandwich Islands",
            "dial_code": "+500",
            "code": "GS"
            },
            {
            "name": "Spain",
            "dial_code": "+34",
            "code": "ES"
            },
            {
            "name": "Sudan",
            "dial_code": "+249",
            "code": "SD"
            },
            {
            "name": "Suriname",
            "dial_code": "+597",
            "code": "SR"
            },
            {
            "name": "Svalbard and Jan Mayen",
            "dial_code": "+47",
            "code": "SJ"
            },
            {
            "name": "Swaziland",
            "dial_code": "+268",
            "code": "SZ"
            },
            {
            "name": "Sweden",
            "dial_code": "+46",
            "code": "SE"
            },
            {
            "name": "Switzerland",
            "dial_code": "+41",
            "code": "CH"
            },
            {
            "name": "Syrian Arab Republic",
            "dial_code": "+963",
            "code": "SY"
            },
            {
            "name": "Taiwan",
            "dial_code": "+886",
            "code": "TW"
            },
            {
            "name": "Tajikistan",
            "dial_code": "+992",
            "code": "TJ"
            },
            {
            "name": "Tanzania, United Republic of Tanzania",
            "dial_code": "+255",
            "code": "TZ"
            },
            {
            "name": "Thailand",
            "dial_code": "+66",
            "code": "TH"
            },
            {
            "name": "Timor-Leste",
            "dial_code": "+670",
            "code": "TL"
            },
            {
            "name": "Togo",
            "dial_code": "+228",
            "code": "TG"
            },
            {
            "name": "Tokelau",
            "dial_code": "+690",
            "code": "TK"
            },
            {
            "name": "Tonga",
            "dial_code": "+676",
            "code": "TO"
            },
            {
            "name": "Trinidad and Tobago",
            "dial_code": "+1868",
            "code": "TT"
            },
            {
            "name": "Tunisia",
            "dial_code": "+216",
            "code": "TN"
            },
            {
            "name": "Turkey",
            "dial_code": "+90",
            "code": "TR"
            },
            {
            "name": "Turkmenistan",
            "dial_code": "+993",
            "code": "TM"
            },
            {
            "name": "Turks and Caicos Islands",
            "dial_code": "+1649",
            "code": "TC"
            },
            {
            "name": "Tuvalu",
            "dial_code": "+688",
            "code": "TV"
            },
            {
            "name": "Uganda",
            "dial_code": "+256",
            "code": "UG"
            },
            {
            "name": "Ukraine",
            "dial_code": "+380",
            "code": "UA"
            },
            {
            "name": "United Arab Emirates",
            "dial_code": "+971",
            "code": "AE"
            },
            {
            "name": "United Kingdom",
            "dial_code": "+44",
            "code": "GB"
            },
            {
            "name": "United States",
            "dial_code": "+1",
            "code": "US"
            },
            {
            "name": "Uruguay",
            "dial_code": "+598",
            "code": "UY"
            },
            {
            "name": "Uzbekistan",
            "dial_code": "+998",
            "code": "UZ"
            },
            {
            "name": "Vanuatu",
            "dial_code": "+678",
            "code": "VU"
            },
            {
            "name": "Venezuela, Bolivarian Republic of Venezuela",
            "dial_code": "+58",
            "code": "VE"
            },
            {
            "name": "Vietnam",
            "dial_code": "+84",
            "code": "VN"
            },
            {
            "name": "Virgin Islands, British",
            "dial_code": "+1284",
            "code": "VG"
            },
            {
            "name": "Virgin Islands, U.S.",
            "dial_code": "+1340",
            "code": "VI"
            },
            {
            "name": "Wallis and Futuna",
            "dial_code": "+681",
            "code": "WF"
            },
            {
            "name": "Yemen",
            "dial_code": "+967",
            "code": "YE"
            },
            {
            "name": "Zambia",
            "dial_code": "+260",
            "code": "ZM"
            },
            {
            "name": "Zimbabwe",
            "dial_code": "+263",
            "code": "ZW"
            }
            ]';



        $result = json_decode($json, true);



        return db_to_combo_bs4_json_code($id_match, $result, $combo_name, $function_to_call, $required, " Country", true);
    }


    public function getStudyCourseComboValuesById()
    {        
        $courseIds = [23, 24, 25,26,27,28];        
        $courseIdsString = implode(',', $courseIds);        
        
        $query = "SELECT c.course_id, CONCAT(c.course_name, ' ') AS course_name 
                FROM ggportal_tbl_course c 
                WHERE c.course_id IN ($courseIdsString) 
                ORDER BY c.course_id;";

        $result = mysqli_query($this->_conn, $query);

        $resultset = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $resultset[] = $row;
        }

        if (!empty($resultset)) {
            return $resultset;
        }
    }
}