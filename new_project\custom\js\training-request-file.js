$(document).ready(function () {
 
    $("#quickform").validate({
      rules: {
        meeting_link: {
          required: true,
      },
      prefer_date: {
        required: true,        
    }    
      },
      messages: {
        meeting_link: {
            required: "Please enter a valid link",
        },
        prefer_date: {
            required: "Please select one",
        }
    },
    errorElement: 'span',
    errorPlacement: function (error, element) {
        error.addClass('invalid-feedback');
        if (element.attr("name") == "prefer_date") {
          error.appendTo(element.closest('.row').find('.col-12').last()); // Adjust this line to place the error message 
          
      } else {
          element.closest('.form-group').append(error);
      }
    },
    highlight: function (element, errorClass, validClass) {
        $(element).addClass('is-invalid');
    },
    unhighlight: function (element, errorClass, validClass) {
        $(element).removeClass('is-invalid');
    },  
    
      submitHandler: function (form) {
        var formData = new FormData(form);
        formData.append("trainingUpdate", "1");
        console.log(formData);
        $.ajax({
          type: "POST",
          url: "controller/training-controller.php",
          data: formData,
          dataType: "json",
          mimeType: "multipart/form-data",
          contentType: false,
          processData: false,
          cache: false,
          success: function (data) {
            if (data.data > 0) {
              toastr.success("Saved Successfully");
              window.location.href = "training-request-list.php";
            } else {
              toastr.error(data.status);
            }
          },
        });
      },
    });
  });
  