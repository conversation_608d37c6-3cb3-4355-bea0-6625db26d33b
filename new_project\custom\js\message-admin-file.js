$(document).ready(function () {
  bsCustomFileInput.init();
  //Initialize Select2 Elements
  $(".select2bs4").select2({
    theme: "bootstrap4",
  });

  $("#quickform").validate({
    rules: {
      message: {
        required: false,
    },
    _files: {
        required: function () {
            return $('input[name="message"]').val().trim() === "";
        }
    }
  },
  messages: {
      _files: {
          required: "Please attach a document or type a message",
      }
  },
    errorElement: "span",
    errorPlacement: function (error, element) {
      error.addClass("invalid-feedback");
      element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
      $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) {
      // console.log($(form).serialize()+'&messagesSave=1');
      //message save
  //     $.ajax({
  //       type: "POST",
  //       url: "controller/message-controller.php",
  //       data: $(form).serialize() + "&messagesSave=1",
  //       dataType: "json",
  //       success: function (data) {
  //          console.log(data);
  //         if (data) {
  //           $('input[name="message"]').val("");
  //           toastr.success("Saved Successfully");
  //           $(form).trigger("reset");
  //         } else {
  //           toastr.error("There is an error while saving.");
  //         }
  //       },
  //     });
  //   },
  // });
  var formData = new FormData(form);
          formData.append('messagesSave', '1');

          $.ajax({
              type: "POST",
              url: "controller/message-controller.php",
              data: formData,
              contentType: false,
              processData: false,
              dataType: 'json',
              success: function (data) {
                  console.log('Response from server:', data);
                  if (data.status === 'success') {
                    $('input[name="message"]').val('');
                    $('input[name="_files"]').val('');
                    console.log('Saved Successfully');
                    $(form).trigger("reset");
                    getchatmessages();
                } else {
                    console.log('Error:', data.message);
                }
              },
              error: function (xhr, status, error) {
                  console.log('Error while saving:', error);
                  console.log('Server response:', xhr.responseText);
                  $(form).trigger("reset");
                  $('input[name="message"]').val('');
              }
          });
      }
  });


  getchatmessages2();

  setInterval(function () {
    getchatmessages2();
  }, 15000);


  $("body").on("click", "#show2", function () {
    //console.log("this is working");
    setTimeout(function () {
      getchatmessages2();
    }, 500);
  });

  function getchatmessages2() {
    let student_id = $("#student_id").val();
    $.ajax({
      method: "post",
      url: "get-admin-messages.php",
      data: $("#displaydata2").serialize() + "&student_id=" + student_id,
      dataType: "html",
      success: function (response) {
        // Uncomment the line below to log the response
        // console.log({response});
  
        // Update the display with the retrieved messages
        $("#displaydata2").html(response);
      },
    });
  }
  

});
