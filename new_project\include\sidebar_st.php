<?php
$student_id = $_SESSION['user']['user_id'];

?>
<!-- Main Sidebar Container -->
<aside class="main-sidebar elevation-4 sidebar-primary test">
    <!-- Brand Logo -->
    <a href="dashboard.php" class="brand-link ctm-brand-link">
        <img src="dist/img/Icon.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">Edvios</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-child-indent  nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                <!-- Add icons to the links using the .nav-icon class
                     with font-awesome or any other icon font library -->
                <li class="nav-item">
                    <a href="dash-student.php" class="nav-link">
                        <i class="nav-icon fas fa-th"></i>
                        <p>
                            Dashboard
                        </p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="program.php" class="nav-link">
                        <i class="nav-icon fas fa-book-open"></i>
                        <p>
                            Program Finder
                        </p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="student-favorite.php" class="nav-link">
                        <i class="nav-icon fas fa-heart"></i>
                        <p>
                            Favorite
                        </p>
                    </a>
                </li>

                <!-- <li class="nav-item">
                    <a href="training-request.php" class="nav-link">
                        <i class="nav-icon fas fa-flag"></i>
                        <p>
                            Training Request
                            <span class="right badge badge-danger">New</span>
                        </p>
                    </a>
                </li> -->
                <li class="nav-item">
                    <a href="student-application-view-all.php?$student_id=<?= $student_id ?>" class="nav-link">
                        <i class="nav-icon fas fa-book"></i>
                        <p>
                            My Applications
                        </p>
                    </a>
                </li>
                <!-- <li class="nav-item">
                    <a href="message-student.php" class="nav-link">
                        <i class="nav-icon fas fa-sms"></i>
                        <p>
                        Live Chat
                        </p>
                    </a>
                </li> -->
                <li class="nav-item">
                    <a href="student-profile.php" class="nav-link">
                        <i class="nav-icon fas fa-book-open"></i>
                        <p>
                            Profile
                        </p>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
<script>
    $(document).ready(function() {

        var url = window.location;
        // for single sidebar menu
        $('ul.nav-sidebar a').filter(function() {
            return this.href == url;
        }).addClass('active');

        // for sidebar menu and treeview
        $('ul.nav-treeview a').filter(function() {
                return this.href == url;
            }).parentsUntil(".nav-sidebar > .nav-treeview")
            .css({
                'display': 'block'
            })
            .addClass('menu-open').prev('a')
            .addClass('active');

    });
</script>