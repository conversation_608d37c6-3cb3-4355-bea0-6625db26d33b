$(document).ready(function () {
  bsCustomFileInput.init();
  $('.select2bs4').select2({
      theme: 'bootstrap4'
  });

  $('#quickform').validate({
      rules: {
          // Remove message requirement, it can be optional
          message: {
              required: false,
          },
          _files: {
              required: function () {
                  return $('input[name="message"]').val().trim() === "";
              }
          }
      },
      messages: {
          _files: {
              required: "Please attach a document or type a message",
          }
      },
      errorElement: 'span',
      errorPlacement: function (error, element) {
          error.addClass('invalid-feedback');
          element.closest('.form-group').append(error);
      },
      highlight: function (element, errorClass, validClass) {
          $(element).addClass('is-invalid');
      },
      unhighlight: function (element, errorClass, validClass) {
          $(element).removeClass('is-invalid');
      },
      submitHandler: function (form) {
          var formData = new FormData(form);
          formData.append('messagesSave', '1');

          $.ajax({
              type: "POST",
              url: "controller/message-controller.php",
              data: formData,
              contentType: false,
              processData: false,
              dataType: 'json',
              success: function (data) {
                //   console.log('Response from server:', data);
                  if (data.status === 'success') {
                    $('input[name="message"]').val('');
                    $('input[name="_files"]').val('');
                    console.log('Saved Successfully');
                    $(form).trigger("reset");
                    getchatmessages();
                } else {
                    console.log('Error:', data.message);
                }
              },
              error: function (xhr, status, error) {
                  console.log('Error while saving:', error);
                  console.log('Server response:', xhr.responseText);
                  $(form).trigger("reset");
                  $('input[name="message"]').val('');
              }
          });
      }
  });

  function getchatmessages() {
      $.ajax({
          method: "post",
          url: "get-customer-messages.php",
          data: $('#displaydata').serialize(),
          dataType: "html",
          success: function (response) {
              $('#displaydata').html(response)
          }
      })
  }

  setInterval(function () {
      getchatmessages();
  }, 15000);

  $('body').on('click', '#show', function () {
      setTimeout(function () {
          getchatmessages();
      }, 500);
  });
});
