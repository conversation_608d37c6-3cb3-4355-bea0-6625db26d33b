
$(document).ready(function () {
  var activeIndexm;
  $("a.custom-width2").click(function () {
    //first execute this
    // Create a new form element with the same structure as the original form
    var newForm = $("#degree-form2").clone();

    // Clear input fields in the new form
    newForm.find('input[type="text"]').val("");
    newForm.find('input[type="radio"]').prop("checked", false);

    // Show the new form
    newForm.show();

    // Append the new form to an element with the class "content3"
    newForm.appendTo(".content4");
  });

  $(document).on("click", ".delete2", function () {
    // Remove the parent element (the whole form)
    $(this).closest(".add-form2").remove();
  });

  //check email
//   $(document).on("click", ".next-button", function () {
//     const email = $('#student-email').val();
//     const dataToSend = { email: email };

//   //   $.ajax({
//   //     url: 'controller/student-controller.php', 
//   //     type: 'POST', 
//   //     data: dataToSend, 
//   //     success: function(response) {
          
          
//   //     },
//   //     error: function(error) {
//   //         // Handle any errors
//   //         console.error('Error:', error);
          
//   //     }
//   // });

//   });
});



$(document).ready(function () {
  let validate_form_1 =
    (validate_form_2 =
    validate_form_3 =
    validate_form_4 =
    validate_form_5 =
    validate_form_6 =
      false);

  var education_files = [];
  // var english_files = [];
  var englishtest_files = [];
  var travel_files = [];  
  var work_files = [];
  var service_files = [];
  var cv = [];
  var sop = [];
  var lor = [];
  var moi = [];
  var refusal = [];
  var medical = [];
  var offer = [];
  var cas = [];
  var loa = [];
  var receipt = [];

  bsCustomFileInput.init();
  //Initialize Select2 Elements
  $(".select2bs4").select2({
    theme: "bootstrap4",
  });

  //console.log($('.country_phone_code :selected').text());
  // var phone = $("#mobile").val();
  // var phone = $("#mobile_no").val(
  //   phone.replace($(".country_phone_code :selected").text())
  // );

  // var phone = $("#emergency_contact_mobile").val();
  // var phone = $("#emergency_contact_mobile_no").val(
  //   phone.replace($(".emergency_country_phone :selected").text())
  // );

  $("#mobile_no").on("keyup", function () {
    var mobile = $(this).val();
    $("#mobile").val("("+$(".country_phone_code :selected").text()+")"+mobile);
  });

  $('select[name="country_phone_code"]').on("change", function() {
    var mobile = $('#mobile_no').val();
    $("#mobile").val("("+$(".country_phone_code :selected").text()+")"+mobile);
  });

  $("#emergency_contact_mobile_no").on("keyup", function () {
    var emergency_mobile = $(this).val();
    $("#emergency_contact_mobile").val("("+$(".emergency_country_phone :selected").text()+")" + emergency_mobile);
  });

  $('select[name="emergency_country_phone"]').on("change", function() {
    var emergency_mobile = $('#emergency_contact_mobile_no').val();
    $("#emergency_contact_mobile").val("("+$(".emergency_country_phone :selected").text()+")" + emergency_mobile);  
  });

  $("#reservation").daterangepicker({
    locale: {
      format: "YYYY-MM-DD",
    },
    singleDatePicker: true,
    showDropdowns: true,
  });

  $(".add_travel").on("click", function () {
    $.ajax({
      type: "POST",
      url: "controller/country-controller.php",
      data: {
        name: "travel_country_id[]",
        country_id: "",
        getAllCountryCombo: 1,
      },
      dataType: "json",
      success: function (data) {
        $(".div_travel").append(
          '<div class="col-md-6 shadow-sm mt-2">' +
            '<div class="col-md-12" style="display: inline-block;">' +
            '<a class="btn btn-sm btn-danger float-right font-weight-bolder remove_travel"> X </a>' +
            "</div>" +
            '<div class="col-sm-10 row ">' +
            '<input type="hidden" class="form-control" name="travel_history_id[]" value="0">' +
            '<div class="col-sm-6">' +
            '<div class="form-group">' +
            '<label>Country<span style="color:red;">*</span></label>' +
            "" +
            data.data +
            "" +
            "</div>" +
            "</div>" +
            '<div class="col-sm-6">' +
            '<div class="form-group">' +
            '<label>Departure Date<span style="color:red;">*</span></label>' +
            '<input type="date" class="form-control" name="travel_departure[]" value="" required>' +
            "</div>" +
            "</div>" +
            "</div>" +
            '<div class="col-sm-10 row mt-2">' +
            '<div class="col-sm-6">' +
            '<div class="form-group">' +
            '<label>Arrival Date<span style="color:red;">*</span></label>' +
            '<input type="date" class="form-control" name="travel_arrival[]" value="" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-6">' +
            '<div class="form-group">' +
            '<label>Reason<span style="color:red;">*</span></label>' +
            '<select class="form-control" name="travel_reason[]" required>' +
            '<option value="select">----Select One----</option>' +
            '<option value="Studies">Studies</option>' +
            '<option value="Travel">Travel</option>' +
            '<option value="Business">Business</option>' +
            '<option value="Conference">Conference</option>' +
            '<option value="Other">Other</option>' +
            "</select>" +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>"
        );
      },
    });
  });

  $(document).on("click", ".remove_travel", function () {
    $(this).parent().parent().remove();
  });

  $(document).on("click", ".remove_visa", function () {
    $(this).parent().parent().remove();
  });

  $(document).on("click", ".remove_dependence", function () {
    $(this).parent().parent().remove();
  });

  //remove education
  $(document).on("click", ".remove_education", function () {
    $(this).parent().remove();
  });
  $(document).on("click", ".remove_work", function () {
    $(this).parent().remove();
  });
  $(document).on("click", ".remove_english", function () {
    $(this).parent().remove();
  });

  $(".add_visa").on("click", function () {
    $.ajax({
      type: "POST",
      url: "controller/country-controller.php",
      data: {
        name: "visa_country_id[]",
        country_id: "",
        getAllCountryCombo: 1,
      },
      dataType: "json",
      success: function (data) {
        $(".div_visa").append(
          '<div class="col-md-12 row mt-4">' +
            '<input type="hidden" class="form-control" name="visa_refusal_id[]" value="0">' +
            '<div class="col-md-3">' +
            '<div class="form-group">' +
            '<label>Country<span style="color:red;">*</span></label>' +
            "" +
            data.data +
            "" +
            "</div>" +
            "</div>" +
            '<div class="col-md-3">' +
            '<div class="form-group">' +
            '<label>date<span style="color:red;">*</span></label>' +
            '<input type="date" class="form-control" name="visa_date[]" value="" required>' +
            "</div>" +
            "</div>" +
            '<div class="" style="display: inline-block;">' +
            '<a class="btn btn-sm btn-danger float-right font-weight-bolder remove_visa" ><i class="fa fa-times" aria-hidden="true"></i></a>' +
            "</div>" +
            "</div>"
        );
      },
    });
  });

  $(".add_dependence").on("click", function () {   
    $.ajax({
      success: function (response) {
          $(".div_dependence").append(
            '<div class="row mt-2 mb-4">' +
            '<input type="hidden" class="form-control" name="dependence_id[]" value="0">' +
            '<div class="col-sm-4">' +
            '<div class="form-group">' +
            '<label>Dependence Type<span style="color:red;">*</span></label>' +
            '<select class="form-control" name="dependence_type[]" required>' +
            '<option value="select">----Select One----</option>' +
            '<option value="Spouse">Spouse</option>' +
            '<option value="Child">Child</option>' +
            "</select>" +
            "</div>" +
            "</div>" +
            '<div class="col-sm-4">' +
            '<div class="form-group">' +
            '<label>Age (years) <span style="color:red;">*</span></label>' +
            '<input type="number" class="form-control" name="dependence_age[]" value="" required>' +
            "</div>" +
            "</div>" +
            '<div class="" style="display: inline-block;">' +
            '<a class="btn btn-sm btn-danger float-right font-weight-bolder remove_dependence"><i class="fa fa-times" aria-hidden="true"></i></a>' +
            "</div>" +
            "</div>"
        );      
      },
    });
  });

  //add education
  $(".add_education").on("click", function () {
    $.ajax({
      type: "POST",
      url: "controller/country-controller.php",
      data: {
        name: "education_country_id[]",
        country_id: "",
        getAllCountryCombo: 1,
      },
      dataType: "json",
      success: function (data) {
        $(".div_education").append(
          ' <div class="add-form" style="width: unset; border-radius: 8px; position: relative;">' +
            '<a class="btn btn-sm btn-danger font-weight-bolder remove_education" style="position: absolute; right: 20px; top: 12px;"><i class="fa fa-times" aria-hidden="true"></i></a>' +
            '<br>' +
            '<div class="form-row">' +
            '<input type="hidden" class="form-control row" name="education_id[]" value="0">' +
            '<div class="form-group col-md-6">' +
            '<label for="education_level">Education Level Name<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="education_level" name="education_level[]" placeholder="O/L" value="" required>' +
            "</div>" +
            '<div class="form-group col-md-6">' +
            '<label for="institute">Institute<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="institute" name="institute[]" placeholder="Open University of Sri Lanaka" value="" required>' +
            "</div>" +
            '<div class="form-group col-md-6">' +
            '<label for="program">Program<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="program" name="program[]" placeholder="Computer Literacy" value="" required>' +
            "</div>" +
            '<div class="form-group col-md-6">' +
            '<label for="language">Study Language<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="language" name="language[]" placeholder="English" value="" required>' +
            "</div>" +
            '<div class="form-group col-md-6">' +
            '<label>Degree Type<span style="color:red;">*</span></label><br>' +
            '<select class="form-control" name="degree_type[]">' +
            '<option value="certificate">Certificate</option>' +
            '<option value="diploma">Diploma</option>' +
            '<option value="degree" >Degree</option>' +
            "</select>" +
            "</div>" +
            '<div class="col-md-6">' +
            '<div class="form-group">' +
            '<label>Country<span style="color:red;">*</span></label><br>' +
            "" +
            data.data +
            "" +
            "</div>" +
            "</div>" +
            '<div class="col-md-6">' +
            '<label>Start Date<span style="color:red;">*</span></label><br>' +
            '<div class="input-group">' +
            '<input type="date" class="form-control" name="starting_date[]" id="starting_date" value="" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-sm-6">' +
            '<label>End Date<span style="color:red;">*</span></label>' +
            '<div class="input-group">' +
            '<input type="date" class="form-control" name="end_date[]" value="" id="end_date" required>' +
            "</div>" +
            "</div>" +
            
            '<div class="col-sm-6">' +
            "</div>" +
            "</div>" +
            "</div>"
        );
      },
    });
  });

  $(".add_english").on("click", function () {
    $.ajax({
      success: function () {
        $(".div_english").append(
          '<div class="add-form">' +
            '<a class="btn btn-sm btn-danger font-weight-bolder float-right remove_education">  X   </a>' +
            '<div class="form-row">' +
            '<div class="form-group col-md-6">' +
            "<label>Tests Type     :</span></label><br>" +
            "<hr>" +
            '<div class="form-check form-check-inline">' +
            '<input class="form-check-input" type="radio" name="tests_type" id="ielts" value="option1">' +
            '<label class="form-check-label" for="ielts">IELTS</label>' +
            "</div>" +
            '<div class="form-check form-check-inline">' +
            '<input class="form-check-input" type="radio" name="tests_type" id="pte" value="option2">' +
            '<label class="form-check-label" for="pte">PTE</label>' +
            "</div>" +
            '<div class="form-check form-check-inline">' +
            '<input class="form-check-input" type="radio" name="tests_type" id="duolingo" value="option3">' +
            '<label class="form-check-label" for="duolingo">DUOLINGO</label>' +
            "</div>" +
            '<div class="form-check form-check-inline">' +
            '<input class="form-check-input" type="radio" name="tests_type" id="other" value="option3">' +
            '<label class="form-check-label" for="other">OTHER</label>' +
            "</div><br>" +
            '<div id="notepad">' +
            "<!-- Your notepad content goes here -->" +
            '<textarea rows="4" cols="50">------Note--------</textarea>' +
            "</div>" +
            "</div>" +
            "</div>" +
            '<div class="form-row">' +
            '<div class="form-group col-md-12">' +
            "<label>Test Details     :</label>" +
            "</div>" +
            '<div class="form-row">' +
            '<div class="form-group col-md-12">' +
            '<div class="form-check"><br>' +
            '<input class="form-check-input" type="checkbox" value="" id="taking-checkbox">' +
            '<label class="form-check-label" for="taking-checkbox">' +
            "<h6> <b> Taken</b></h6>" +
            "</label>" +
            "</div>" +
            "</div>" +
            "</div>" +
            '<div class="form-row" id="taking-form">' +
            '<div class="form-group col-md-12">' +
            '<label for="reading">Reading score<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="reading" placeholder="Reading score">' +
            "</div>" +
            '<div class="form-group col-md-12">' +
            '<label for="writing">writing score<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="writing" placeholder="writing score">' +
            "</div>" +
            '<div class="form-group col-md-12">' +
            '<label for="listening">listening score<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="listening" placeholder="listening score">' +
            "</div>" +
            '<div class="form-group col-md-12">' +
            '<label for="speaking">speaking score<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="speaking" placeholder="speaking score">' +
            "</div>" +
            '<div class="col-sm-12">' +
            "</div>" +
            "</div>" +
            "</div>" +
            '<div class="form-row">' +
            '<div class="form-group col-md-12">' +
            '<div class="form-check"><br>' +
            '<input class="form-check-input" type="checkbox" value="" id="flexCheckIndeterminate">' +
            '<label class="form-check-label" for="flexCheckIndeterminate">' +
            "<h6> <b>  Will be Taking</b></h6>" +
            "</label>" +
            '<div id="calendar">' +
            '<div class="input-group">' +
            '<input type="date" id="start" name="trip-start" value="2018-07-22" /><br>' +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>" +
            '<div class="form-row">' +
            '<div class="form-group col-md-12">' +
            '<div class="form-check"><br>' +
            '<input class="form-check-input" type="checkbox" value="" id="flexCheckIndeterminate">' +
            '<label class="form-check-label" for="flexCheckIndeterminate">' +
            "<h6> <b> Not taking</b></h6>" +
            "</label>" +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>"
        );
      },
    });
  });
  $(".add_work").on("click", function () {
    $.ajax({
      success: function () {
        $(".div_work").append(
          '<div class="add-form" style="width: unset; border-radius: 8px; position: relative;">' +
            '<a class="btn btn-sm btn-danger font-weight-bolder float-right remove_education" style="position: absolute; right: 20px; top: 12px;"><i class="fa fa-times" aria-hidden="true"></i></a>' +
            '<br>' +
            '<div class="form-row">' +
            '<input type="hidden" class="form-control row" name="work_id[]" value="0">' +
            '<div class="form-group col-md-6">' +
            '<label for="companyname">Company Name<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="companyname" name="companyname[]" placeholder="Company Name" value="">' +
            "</div>" +
            '<div class="form-group col-md-6">' +
            '<label for="position">Position<span style="color:red;">*</span></label>' +
            '<input type="text" class="form-control" id="position" name="position[]" placeholder="Position" value="">' +
            "</div>" +
            '<div class="col-md-6">' +
            '<label>Start Date<span style="color:red;">*</span></label><br>' +
            '<div class="input-group">' +
            '<input type="date" class="form-control" name="work_starting_date[]" id="work_starting_date" value="" required>' +
            "</div>" +
            "</div>" +
            '<div class="col-md-6 form-group">' +
            '<label>End Date<span style="color:red;">*</span></label>' +
            '<div class="input-group">' +
            '<input type="date" class="form-control" name="work_end_date[]" value="" id="work_end_date" required>' +
            "</div>" +
            "</div>" +
            "</div>" +
            "</div>"
        );
      },
    });
  });
  $(".delete_files_btn").on("click", function () {
    var student_document_id = $(this).data("student_document_id");
    var doc_url = $(this).data("doc_url");
    console.log({
      student_document_id: student_document_id,
      doc_url: doc_url,
    });

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/files-controller.php",
          data: {
            student_document_id: student_document_id,
            doc_url: doc_url,
            deleteStudentDocument: 1,
          },
          dataType: "json",
          success: function (data) {
            console.log(data);
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your file has been deleted.", "success");

              //find this parant tr and remove it
              $("#" + student_document_id).remove();
              //after 1 second reload the page
              // setTimeout(function() {
              //     window.location.reload();
              // }, 1000);
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //Delete button for visa refusal
  $(".delete_visa").on("click", function () {
    var visa_refusal_id = $(this)
      .closest(".row")
      .find('input[name="visa_refusal_id[]"]')
      .val();
    var rowElement = $(this).closest(".row");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            visa_refusal_id: visa_refusal_id,
            deleteVisa: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + visa_refusal_id).remove();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //Delete button for dependents
  $(".delete_dependent").on("click", function () {
    var dependence_id = $(this)
      .closest(".row")
      .find('input[name="dependence_id[]"]')
      .val();
    var rowElement = $(this).closest(".row");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            dependence_id: dependence_id,
            deleteDependence: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + dependence_id).remove();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //Delete button for disabilities
  $(".delete_disability").on("click", function () {
    var disability_id = $(this)
      .closest(".row")
      .find('input[name="disability_id[]"]')
      .val();
    var rowElement = $(this).closest(".row");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            disability_id: disability_id,
            deleteDisability: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + disability_id).remove();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //Delete button for travel history
  $(".delete_travel").on("click", function () {
    var travel_history_id = $(this)
      .closest(".row")
      .find('input[name="travel_history_id[]"]')
      .val();
    var rowElement = $(this).closest(".row");

    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      /* Read more about isConfirmed, isDenied below */
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            travel_history_id: travel_history_id,
            deleteTravel: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + travel_history_id).remove();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //Delete button for education
  $(".delete_education").on("click", function () {
    var education_id = $(this)
      .closest(".need-remove")
      .find('input[name="education_id[]"]')
      .val();
    var rowElement = $(this).closest(".need-remove");       
    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            education_id: education_id,
            deleteEducation: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + education_id).remove();
             
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  //delete work
  $(".delete_work").on("click", function () {
    var work_id = $(this).closest(".remove-work").find('input[name="work_id[]"]').val();
    var rowElement = $(this).closest(".remove-work");
    Swal.fire({
      title: "Are you sure want to delete?",
      showCancelButton: true,
      confirmButtonText: `Delete`,
      confirmButtonColor: "#d33",
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          type: "POST",
          url: "controller/student-controller.php",
          data: {
            work_id: work_id,
            deleteWork: 1,
          },
          dataType: "json",
          success: function (data) {
            if (data.data > 0) {
              Swal.fire("Deleted!", "Your data has been deleted.", "success");
              rowElement.remove();
              $("#" + work_id).remove();
            } else {
              toastr.error(data.status);
            }
          },
        });
      }
    });
  });

  $("#education_files").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#educationform").find('input[name="_files"]').first().val(),
      file_name: $("#educationform")
        .find('input[name="file_name"]')
        .first()
        .val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result.data);
      //data.ff_info.RemoveFile();
      education_files.push(data._response.result.data);
      $('input[name="education_files_input"]').val(education_files);
      validate_form_2 = true;
    },
  });

  $("#englishtest_files").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#englishtestform").find('input[name="_files"]').first().val(),
      file_name: $("#englishtestform")
        .find('input[name="file_name"]')
        .first()
        .val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result);
      //data.ff_info.RemoveFile();
      englishtest_files.push(data._response.result.data);
      $('input[name="englishtest_files_input"]').val(englishtest_files);
      validate_form_3 = true;
    },
  });

  $("#work_files").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#workform").find('input[name="_files"]').first().val(),
      file_name: $("#workform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result);
      //data.ff_info.RemoveFile();
      work_files.push(data._response.result.data);
      $('input[name="work_files_input"]').val(work_files);
      validate_form_4 = true;
    },
  });

  $("#service_files").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#serviceform").find('input[name="_files"]').first().val(),
      file_name: $("#serviceform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result);
      //data.ff_info.RemoveFile();
      service_files.push(data._response.result.data);
      $('input[name="service_files_input"]').val(service_files);
      validate_form_4 = true;
    },
  });

  $("#travel_files").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#travelform").find('input[name="_files"]').first().val(),
      file_name: $("#travelform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result);
      //data.ff_info.RemoveFile();
      travel_files.push(data._response.result.data);
      $('input[name="travel_files_input"]').val(travel_files);
      validate_form_5 = true;
    },
  });

  $("#cv").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      cv.push(data._response.result.data);
      $('input[name="cv_input"]').val(cv);
      validate_form_6 = true;
    },
  });

  $("#sop").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files2"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      //console.log(data._response.result);
      //data.ff_info.RemoveFile();
      sop.push(data._response.result.data);
      $('input[name="sop_input"]').val(sop);
      validate_form_6 = true;
    },
  });

  $("#lor").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files3"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      lor.push(data._response.result.data);
      $('input[name="lor_input"]').val(lor);
      validate_form_6 = true;
    },
  });

  $("#moi").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files4"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      moi.push(data._response.result.data);
      $('input[name="moi_input"]').val(moi);
      validate_form_6 = true;
    },
  });

  $("#refusal").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files5"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      refusal.push(data._response.result.data);
      $('input[name="refusal_input"]').val(refusal);
      validate_form_6 = true;
    },
  });

  $("#medical").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files6"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      medical.push(data._response.result.data);
      $('input[name="medical_input"]').val(medical);
      validate_form_6 = true;
    },
  });

  $("#offer").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files7"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      offer.push(data._response.result.data);
      $('input[name="offer_input"]').val(offer);
      validate_form_6 = true;
    },
  });

  $("#cas").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files8"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      cas.push(data._response.result.data);
      $('input[name="cas_input"]').val(cas);
      validate_form_6 = true;
    },
  });

  $("#loa").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files9"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      loa.push(data._response.result.data);
      $('input[name="loa_input"]').val(loa);
      validate_form_6 = true;
    },
  });

  $("#receipt").FancyFileUpload({
    url: "controller/files-controller.php",
    params: {
      action: "FileUploader",
      student_id: $("#quickform")
        .find('input[name="student_id"]')
        .first()
        .val(),
      data: $("#otherform").find('input[name="_files10"]').first().val(),
      file_name: $("#otherform").find('input[name="file_name"]').first().val(),
    },
    maxfilesize: 4194304,
    uploadcompleted: function (e, data) {
      receipt.push(data._response.result.data);
      $('input[name="receipt_input"]').val(receipt);
      validate_form_6 = true;
    },
  });

  var current_fs, next_fs, previous_fs;

  //Sithum
  // No BACK button on first screen
  if ($(".show").hasClass("first-screen")) {
    $(".prev").css({
      display: "none",
    });
  }

  //Sithum
  // Next button
  $(".next-button").click(function () {
    //validate form 1
    // console.log($('#quickform').valid());
    if ($("#quickform").valid()) {
      validate_form_1 = true;
    } else {
      validate_form_1 = false;
    }

    // console.log({
    //     validate_form_1: validate_form_1,
    //     validate_form_2: validate_form_2,
    //     validate_form_3: validate_form_3,
    //     validate_form_4: validate_form_4,
    //     validate_form_5: validate_form_5,
    //     validate_form_6: validate_form_6
    // });

    if (
      validate_form_1 ||
      validate_form_2 ||
      validate_form_3 ||
      validate_form_4 ||
      validate_form_5 ||
      validate_form_6
    ) {
      current_fs = $(this).parent().parent();
      next_fs = $(this).parent().parent().next();

      $(".prev").css({
        display: "inline-block",
      });

      $(current_fs).removeClass("show");
      $(next_fs).addClass("show");

      $("#progressbar li").eq($(".card2").index(next_fs)).addClass("active");
      $("#progressbar li")
        .eq($(".card2").index(current_fs))
        .addClass("complete_form");

      current_fs.animate(
        {},
        {
          step: function () {
            current_fs.css({
              display: "none",
              position: "relative",
            });

            next_fs.css({
              display: "block",
            });
          },
        }
      );
    } else {
      //toastr.info('Please complete the form first!');
    }

    validate_form_1 =
      validate_form_2 =
      validate_form_3 =
      validate_form_4 =
      validate_form_5 =
        false;
  });

  //Sithum
  // Previous button
  $(".prev").click(function () {
    current_fs = $(".show");
    previous_fs = $(".show").prev();

    $(current_fs).removeClass("show");
    $(previous_fs).addClass("show");

    $(".prev").css({
      display: "inline-block",
    });

    if ($(".show").hasClass("first-screen")) {
      $(".prev").css({
        display: "none",
      });
    }

    $("#progressbar li")
      .eq($(".card2").index(current_fs))
      .removeClass("active");
    $("#progressbar li")
      .eq($(".card2").index(previous_fs))
      .removeClass("complete_form");

    current_fs.animate(
      {},
      {
        step: function () {
          current_fs.css({
            display: "none",
            position: "relative",
          });

          previous_fs.css({
            display: "block",
          });
        },
      }
    );
  });

  //this function for the next and preview
  /*  function nextpreview(id){
  alert('hgsafsjah'+id);
 } */

  /*Save Student*/
  $("#quickform").validate({
    rules: {
      first_name: {
        required: true,
      },
      last_name: {
        required: true,
      },
      mobile: {
        required: true,
        digits: true,
      },
      gender: {
        required: true,
      },
      date_of_birth: {
        required: true,
      },
      marital_status: {
        required: true,
      },
      email: {
        email: true,
        required: true,
      },
      country: {
        required: true,
      },
      state: {
        required: true,
      },
      emergency_contact_name: {
        required: true,
      },
      emergency_contact_mobile: {
        required: true,
        digits: true,
      },
      emergency_contact_relation: {
        required: true,
      },
      passport_number: {
        required: true,
      },
      expiry_date: {
        required: true,
      },
      nic: {
        required: true,
      },
      date_of_birth: {
        required: true,
      },
      address: {
        required: true,
      },
      province: {
        required: true,
      },
      city: {
        required: true,
      },
      postal_code: {
        required: true,
      },
      mobile_no: {
        required: true,
        digits: true,
      },
      // profile_picture: {
      //   required: true,
      // },
      // issued_date: {
      //   required: true,
      // },
      passport_country_id: {
        required: true,
      },
      _files:{
        required: true,
      },
      _files1:  {
        required: true,
      },
      _files2:  {
        required: true,
      },
      _files3:  {
        required: true,
      },
      _files4:  {
        required: true,
      },
      _files5:  {
        required: true,
      },
      _files6:  {
        required: true,
      },
      _files7:  {
        required: true,
      },
      _files8:  {
        required: true,
      },
      _files9:  {
        required: true,
      },
      _files10:  {
           required: true,
         },
    },
    messages: {
      first_name: {
        required: "Please enter a value",
      },
      last_name: {
        required: "Please enter a value",
      },
      nic: {
        required: "Please enter a value",
      },
      date_of_birth: {
        required: "Please enter a value",
      },
      address: {
        required: "Please enter a value",
      },
      province: {
        required: "Please select a value",
      },
      city: {
        required: "Please enter a value",
      },
      profile_picture: {
        required: "Please enter a value",
      },
      mobile_no: {
        required: "Please enter a value",
        minlength: "Please specify a valid phone number",
        maxlength: "Please specify a valid phone number",
      },
      profile_picture: {
        required: "Please enter a value",
      },
      mobile: {
        required: "Please enter a value",
        minlength: "Please specify a valid phone number",
        maxlength: "Please specify a valid phone number",
      },
      gender: {
        required: "Please select a value",
      },
      marital_status: {
        required: "Please select a value",
      },
      date_of_birth: {
        required: "Please enter a value",
      },
      email: {
        email: "Please enter a vaild email address",
        required: "Please enter a value",
      },
      country: {
        required: "Please select a value",
      },
      state: {
        required: "Please select one",
      },
      emergency_contact_name: {
        required: "Please enter a value",
      },
      emergency_contact_mobile: {
        required: "Please enter a value",
        minlength: "Please specify a valid phone number",
        maxlength: "Please specify a valid phone number",
      },
      emergency_contact_relation: {
        required: "Please enter a value",
      },
      passport_number: {
        required: "Please enter a value",
      },
      expiry_date: {
        required: "Please enter a value",
      },
      postal_code: {
        required: "Please enter a value",
      },
      issued_date: {
        required: "Please enter a value",
      },
      passport_country_id: {
        required: "Please enter a value",
      },
      _files:{
        required: "Please select one",
      },
      _files1:  {
        required: "Please select one",
      },
      _files2:  {
        required: "Please select one",
      },
      _files3:  {
        required: "Please select one",
      },
      _files4:  {
        required: "Please select one",
      },
      _files5:  {
        required: "Please select one",
      },
      _files6:  {
        required: "Please select one",
      },
      _files7:  {
        required: "Please select one",
      },
      _files8:  {
        required: "Please select one",
      },
      _files9:  {
        required: "Please select one",
      },
      _files10:  {
        required: "Please select one",
      },
    },
    errorElement: "span",
    errorPlacement: function (error, element) {
      error.addClass("invalid-feedback");
      element.closest(".form-group").append(error);
    },
    highlight: function (element, errorClass, validClass) {
      $(element).addClass("is-invalid");
    },
    unhighlight: function (element, errorClass, validClass) {
      $(element).removeClass("is-invalid");
    },
    submitHandler: function (form) { 

      // Disable all buttons
      var $buttons = $(this).find('button');
      $buttons.prop('disabled', true); 

      // Sithum
      var formData = new FormData(form);
      formData.append("studentSave", "1");
      $.ajax({
        type: "POST",
        url: "controller/student-controller.php",
        data: formData,
        dataType: "json",
        mimeType: "multipart/form-data",
        contentType: false,
        processData: false,
        cache: false,
        success: function (data) {              
          if (data.data > 0) {          
            toastr.success("Saved Successfully");           
            //validate_form_1 = true;
            //$('input[name="student_id"]').val(data.data);
            //simulate a next button click
            var user_type = $('input[name="user_type"]').val();
            if (user_type == "ST") {
              window.location.href = "student-profile.php"+ '?student_id=' + data.data ;
            } else {
              localStorage.setItem("is_saveUser", true);
              window.location.href = "student-profile.php"+ '?student_id=' + data.data ;
            }
            // $('#form').trigger("reset");
            //   window.location.href = window.location.pathname + '?savedSuccess=1';
          } else {
            setTimeout(function() {
              document.getElementById('loadingIndicator').classList.add('hidden'); 
            }, 1000);
                 
            toastr.error(data.status);
          }

          $buttons.prop('disabled', false); // Re-enable all buttons
        
        },        
      });
    },
  });

  document.getElementById('quickform').addEventListener('submit', function(event) {
    event.preventDefault();    
    // Show the loading indicator
    document.getElementById('loadingIndicator').classList.remove('hidden');  
  });
    
  
  //prevent enter keypress submit
  $(document).on("keypress", "form", function(event) {
    // return event.keyCode != 13;
   //form sumbit on enter keypress
    if (event.keyCode == 13) {
      // prevent default form action
      // event.preventDefault();

      // Rum form validation
      event.preventDefault();

        // Run form validation
        if ($("#quickform").validate().form()) {
            // Trigger the form submission programmatically
            $("#quickform").submit();
        }    
      // Trigger the button element with a click
      
      return false;
    }
  });


});
