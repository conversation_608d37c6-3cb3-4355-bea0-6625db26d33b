$(document).ready(function() {
    $('#example2').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "pageLength": 50,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
    });


    $('.delete_btn').on('click', function() {
        var staff_id = $(this).data('staff_id');

        Swal.fire({
            title: 'Are you sure want to delete?',
            showCancelButton: true,
            confirmButtonText: `Delete`,
            confirmButtonColor: '#d33',
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {

                $.ajax({
                    type: "POST",
                    url: "controller/staff-controller.php",
                    data: {
                        "staff_id": staff_id,
                        "deleteStaff": 1
                    },
                    dataType: 'json',
                    success: function(data) {
                        //console.log(data);
                        if (data.data > 0) {
                            Swal.fire(
                                'Deleted!',
                                'Your file has been deleted.',
                                'success'
                            )
                            //after 1 second reload the page
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(data.status);
                        }

                    }
                });


            }
        })
    });
    // })

});