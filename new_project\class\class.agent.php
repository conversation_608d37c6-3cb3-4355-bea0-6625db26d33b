<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of user
 *
 * <AUTHOR>
 */

include_once 'connection.php';

class Agent extends connection
{
    public function __construct()
    {
        $cconn = new connection();
        $this->_conn = $cconn->makeConnection();
    }

    public function Agent()
    {

        self::__construct();
    }

    private $_conn;
    
    function getLogin($email)
    {
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $email) . "' ";
        $query = "select u.*, password as password1 from ggportal_tbl_agent u where 1=1 $cond;";
        //        echo $query;

        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function checkEmail($email, $agent_id)
    {
        // print_r($email);
        // die();
        $conn = $this->_conn;

        $cond = " and u.email = '" . mysqli_real_escape_string($conn, $email) . "' ";
        $cond = $cond ."and u.agent_id not in( '" . mysqli_real_escape_string($conn, $agent_id) . "') ";
        $query = "SELECT u.* FROM ggportal_tbl_agent u WHERE 1=1 $cond;";
        // echo $query;
        // die();
        $res = mysqli_query($conn, $query);
        // echo $res;
        // die();
        return db_to_array($res);
    }

    function getAgentByID($agent_id)
    {
        $conn = $this->_conn;
        $agent_id = intval($agent_id);
        if ($agent_id > 0) {
            $cond = "and s.agent_id=$agent_id ";
        } else {
            $cond = " and 1=2 ";
        }
        $query = "select s.*,
        b.account_name,
        b.account_no,
        b.bank_id,
        b.bank_name,
        b.branch_name,
        b.branch_code,
        b.swift_code,
        b.bank_code,
        b.iban,
        b.is_active as bank_active_yn
         from ggportal_tbl_agent s
                 left join ggportal_tbl_bank b on b.user_id = s.agent_id
                    WHERE 1=1 $cond;";
        //echo $query;
        //die();
        $res = mysqli_query($conn, $query);
        return db_to_array($res);
    }

    function getAssignedStaff($user_id){
        $conn = $this->_conn;
        $user_id = intval($user_id);
        $cond = "and s.agent_id=$user_id ";

        $query = "select s.assigned_staff from ggportal_tbl_agent s
        WHERE 1=1 $cond;";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['assigned_staff'];

    }


    function getAgents($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['parent_user_id']) && strlen($criteria['parent_user_id']) > 0) {
            $parent_user_id = intval($criteria['parent_user_id']);
            $cond .= " and s.parent_user_id  = $parent_user_id ";
        }

        if (isset($criteria['parent_user_type']) && strlen($criteria['parent_user_type']) > 0) {
            $parent_user_type = mysqli_real_escape_string($conn, $criteria['parent_user_type']);
            $cond .= " and s.parent_user_type = '$parent_user_type' ";
        }

        $query = "select s.* from ggportal_tbl_agent s
         where 1=1  $cond order by s.agent_id DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function getChannelPartnerCount($criteria)
    {
        $conn = $this->_conn;
        $user_id = intval($criteria['user_id']);
        $query = "select count(*) as total from ggportal_tbl_agent;";
        $result = mysqli_query($conn, $query);
        $result = db_to_array($result);
        return $result[0]['total'];
    }

    function getSubAgent($criteria)
    {
        $conn = $this->_conn;
        $cond = "";

        if (isset($criteria['parent_user_id']) && strlen($criteria['parent_user_id']) > 0) {
            $parent_user_id = intval($criteria['parent_user_id']);
            $cond .= " and s.parent_agent_id  = $parent_user_id ";
        }

        // if (isset($criteria['parent_user_type']) && strlen($criteria['parent_user_type']) > 0) {
        //     $parent_user_type = mysqli_real_escape_string($conn, $criteria['parent_user_type']);
        //     $cond .= " and s.parent_user_type = '$parent_user_type' ";
        // }

        $query = "select s.* from ggportal_tbl_agent s
        left join ggportal_tbl_agent s1 on s1.agent_id = s.parent_agent_id
         where 1=1  $cond order by s.agent_id DESC;";
        //    echo $query;
        //        die();

        $result = mysqli_query($conn, $query);
        return db_to_array($result);
    }

    function saveAgent($_details)
    {
        $conn = $this->_conn;
        $agent_id = intval($_details['agent_id']);
        // return $_details;
        if ($agent_id == 0) {
            $query = "INSERT INTO ggportal_tbl_agent SET   
                parent_agent_id = '" . mysqli_real_escape_string($conn, $_details['parent_agent_id']) . "' 
                ,ag_company_name = '" . mysqli_real_escape_string($conn, $_details['ag_company_name']) . "'             
                ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'                
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'
                ,br_document = '" . mysqli_real_escape_string($conn, $_details['br_document']) . "'                  
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,user_access_level = 100
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'              
                ,agreement_signed_yn = '" . mysqli_real_escape_string($conn, $_details['agreement_signed_yn']) . "'              
                ,commission_rate = " . mysqli_real_escape_string($conn, $_details['commission_rate']) . " 
                ,ag_document = '" . mysqli_real_escape_string($conn, $_details['ag_document']) . "'             
                                 
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_agent SET            
            parent_agent_id = '" . mysqli_real_escape_string($conn, $_details['parent_agent_id']) . "'  
            ,ag_company_name = '" . mysqli_real_escape_string($conn, $_details['ag_company_name']) . "'            
            ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
            ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
            ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
            ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
            ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'                
            ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                
            ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "' 
            ,br_document = '" . mysqli_real_escape_string($conn, $_details['br_document']) . "'                
            ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
            ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
            ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
            ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
            ,user_access_level = 100
            ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
            ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'              
            ,agreement_signed_yn = '" . mysqli_real_escape_string($conn, $_details['agreement_signed_yn']) . "'              
            ,commission_rate = " . mysqli_real_escape_string($conn, $_details['commission_rate']) . "  
            ,ag_document = '" . mysqli_real_escape_string($conn, $_details['ag_document']) . "'                          
            WHERE agent_id = $agent_id";
        }
        // return $query;
        //    die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($agent_id == 0) {
                $agent_id = mysqli_insert_id($conn);
            }
            return $agent_id;
        } else {
            return mysqli_error($conn);
        }
    }

    //update agreement
    function updateAgreement($_details)
    {
        $conn = $this->_conn;
        $agent_id = intval($_details['agent_id']);
        $query = "update ggportal_tbl_agent SET            
        agreement_signed_yn = '" . mysqli_real_escape_string($conn, $_details['agreement_signed_yn']) . "' 
       ,ag_document = '" . mysqli_real_escape_string($conn, $_details['ag_document']) . "'             
        WHERE agent_id = $agent_id";

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($agent_id == 0) {
                $agent_id = mysqli_insert_id($conn);
            }
            return $agent_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function assignStaff($agent_id, $staff_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_agent SET            
        assigned_staff = '" . mysqli_real_escape_string($conn, $staff_id) . "'           
        WHERE agent_id = $agent_id";

        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($agent_id == 0) {
                $agent_id = mysqli_insert_id($conn);
            }
            return $agent_id;
        } else {
            return mysqli_error($conn);
        }
    }


    function registerAgent($_details)
    {
        $conn = $this->_conn;
        $agent_id = intval($_details['agent_id']);


        $query = "INSERT INTO ggportal_tbl_agent SET  
                parent_agent_id = '" . mysqli_real_escape_string($conn, $_details['parent_agent_id']) . "'
                ,ag_company_name = '" . mysqli_real_escape_string($conn, $_details['ag_company_name']) . "'              
                ,first_name = '" . mysqli_real_escape_string($conn, $_details['first_name']) . "'                         
                ,last_name = '" . mysqli_real_escape_string($conn, $_details['last_name']) . "'                         
                ,username = '" . mysqli_real_escape_string($conn, $_details['username']) . "'               
                ,email = '" . mysqli_real_escape_string($conn, $_details['email']) . "'                    
                ,mobile = '" . mysqli_real_escape_string($conn, $_details['mobile']) . "'                
                ,country = '" . mysqli_real_escape_string($conn, $_details['country']) . "'                
                ,city = '" . mysqli_real_escape_string($conn, $_details['city']) . "'                
                ,password = '" . mysqli_real_escape_string($conn, $_details['password']) . "'              
                ,password_salt = '" . mysqli_real_escape_string($conn, $_details['password_salt']) . "'              
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'              
                ,profile_picture = '" . mysqli_real_escape_string($conn, $_details['profile_picture']) . "'              
                ,user_access_level = 100
                ,email_validate_yn = '" . mysqli_real_escape_string($conn, $_details['email_validate_yn']) . "'              
                ,active_yn = '" . mysqli_real_escape_string($conn, $_details['active_yn']) . "'              
                ,agreement_signed_yn = '" . mysqli_real_escape_string($conn, $_details['agreement_signed_yn']) . "'              
                ,commission_rate = " . mysqli_real_escape_string($conn, $_details['commission_rate']) . "             
               ;";

        // echo $query;

        $res = mysqli_query($conn, $query);


        if ($res) {
            if ($agent_id == 0) {
                $agent_id = mysqli_insert_id($conn);
            }
            return $agent_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function saveAgentBank($_details)
    {
        $conn = $this->_conn;
        $bank_id = intval($_details['bank_id']);

        if ($bank_id == 0) {
            $query = "INSERT INTO ggportal_tbl_bank SET   
                user_id = '" . mysqli_real_escape_string($conn, $_details['user_id']) . "'                         
                ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'                         
                ,bank_name = '" . mysqli_real_escape_string($conn, $_details['bank_name']) . "'                         
                ,branch_code = '" . mysqli_real_escape_string($conn, $_details['branch_code']) . "'               
                ,branch_name = '" . mysqli_real_escape_string($conn, $_details['branch_name']) . "'                    
                ,account_name = '" . mysqli_real_escape_string($conn, $_details['account_name']) . "'                
                ,account_no = '" . mysqli_real_escape_string($conn, $_details['account_no']) . "'                
                ,swift_code = '" . mysqli_real_escape_string($conn, $_details['swift_code']) . "'                
                ,is_active = '" . mysqli_real_escape_string($conn, $_details['is_active']) . "'              
                ,iban = '" . mysqli_real_escape_string($conn, $_details['iban']) . "'              
                ,bank_code = '" . mysqli_real_escape_string($conn, $_details['bank_code']) . "'                                                         
               ;";
        } else {
            //update
            $query = "update ggportal_tbl_bank SET            
            user_id = '" . mysqli_real_escape_string($conn, $_details['user_id']) . "'                         
            ,user_type = '" . mysqli_real_escape_string($conn, $_details['user_type']) . "'                         
            ,bank_name = '" . mysqli_real_escape_string($conn, $_details['bank_name']) . "'                         
            ,branch_code = '" . mysqli_real_escape_string($conn, $_details['branch_code']) . "'               
            ,branch_name = '" . mysqli_real_escape_string($conn, $_details['branch_name']) . "'                    
            ,account_name = '" . mysqli_real_escape_string($conn, $_details['account_name']) . "'                
            ,account_no = '" . mysqli_real_escape_string($conn, $_details['account_no']) . "'                
            ,swift_code = '" . mysqli_real_escape_string($conn, $_details['swift_code']) . "'                
            ,is_active = '" . mysqli_real_escape_string($conn, $_details['is_active']) . "'              
            ,iban = '" . mysqli_real_escape_string($conn, $_details['iban']) . "'              
            ,bank_code = '" . mysqli_real_escape_string($conn, $_details['bank_code']) . "'                      
            WHERE bank_id = $bank_id";
        }
        // return $query;
        //    die();
        $res = mysqli_query($conn, $query);
        if ($res) {
            if ($bank_id == 0) {
                $bank_id = mysqli_insert_id($conn);
            }
            return $bank_id;
        } else {
            return mysqli_error($conn);
        }
    }

    function deleteAgent($agent_id)
    {
        $conn = $this->_conn;

        $query = "DELETE FROM `ggportal_tbl_agent` WHERE `agent_id` = $agent_id;";

        $res = mysqli_query($conn, $query);
        // return $query;
        // die();


        if ($res) {
            return $agent_id;
        } else {
            return mysqli_error($conn);
        }
    }

    //update agent status
    function enableAgent($agent_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_agent SET active_yn = 'Y' WHERE agent_id = $agent_id;";
        $res = mysqli_query($conn, $query);

        if ($res) {
            return true;
        } else {
            return mysqli_error($conn);
        }
    }

    function disableAgent($agent_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_agent SET active_yn = 'N' WHERE agent_id = $agent_id;";
        $res = mysqli_query($conn, $query);

        if ($res) {
            return true;
        } else {
            return mysqli_error($conn);
        }
    }

    function activateAccount($agent_id, $token_id)
    {
        $conn = $this->_conn;

        $query = "update ggportal_tbl_agent SET active_yn = 'Y', email_validate_yn = 'Y' WHERE agent_id = $agent_id";
        $res = mysqli_query($conn, $query);
        // echo $query;
        // die();

        if ($res) {
            return true;
        } else {
            return mysqli_error($conn);
        }
    }

    function saveUserLogin($user_id, $login_id = 0)
    {
        global $_conn;
        $user_id = intval($user_id);
        $login_id = intval($login_id);
        if ($login_id > 0) {
            $query = "update ggportal_tbl_agent_login set last_activity=now() where user_login_id=$login_id;";
        } else {
            //update tbl_user and reset the login try
            $query = "update ggportal_tbl_agent SET last_login_date=now()
            ,login_failure_try=0
            ,login_failure_ip=''
            ,login_failure_date=null
            where user_id=$user_id;";
            //            $res=mysqli_query($_conn,$query);

            //insert to tbl_user_login
            $query = "Insert into ggportal_tbl_user_login SET
                user_id=$user_id
                ,date_login=now()
                ,ip_address='" . getIP() . "'
                ,last_activity=now(); ";
        }

        $res = mysqli_query($_conn, $query);
        //echo $query;
        //die();
        if ($res) {
            if ($login_id == 0) {
                $login_id = mysqli_insert_id($_conn);
            }
        } else {
            $login_id = 0;
        }
        return $login_id;
    }

    public function calAgentOnlineStatus($agent_id)
    {
        $conn = $this->_conn;

        $query = "SELECT first_name, last_seen FROM ggportal_tbl_agent WHERE agent_id = $agent_id";
        $res = mysqli_query($conn, $query);
        if ($res === false) {
            error_log("Query failed: " . mysqli_error($conn));
            return null;
        }
        $resultData = mysqli_fetch_assoc($res);

        return $resultData;
    }

    public function savePassDetails($passdetails)
    {
        $conn = $this->_conn;

        // Sanitize input
        $agent_id = mysqli_real_escape_string($conn, $passdetails['agent_id']);
        $password1 = $passdetails['password'];
        $password = convert_string('encrypt', $password1); // Encrypt the password

        // Construct the SQL query to update the password using agent_id
        $query = "UPDATE `ggportal_tbl_agent` SET `password` = '$password' WHERE agent_id = '$agent_id'";

        // Execute the query
        $res = mysqli_query($conn, $query);

        if ($res) {
            // Password updated successfully
            return true;
        } else {
            // Error updating password
            echo "Error: " . mysqli_error($conn);
            return false;
        }
    }

    //get agent name by id 
    public function getAgentNameById($agent_id){
        $conn = $this->_conn;
        $agent_id = intval($agent_id);    
             
        $query = "SELECT first_name, last_name  FROM ggportal_tbl_agent WHERE agent_id = $agent_id";
        $res = mysqli_query($conn, $query);
    
        if ($res && mysqli_num_rows($res) > 0) {
            $row = mysqli_fetch_assoc($res);
            return $row['first_name'] . ' ' . $row['last_name']; 
        } else {
            return null; 
        }
    }

    
}
