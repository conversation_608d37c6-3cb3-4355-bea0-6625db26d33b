<?php
session_start();

require_once $_SERVER['DOCUMENT_ROOT'].'/config-nbcourier.php';
include_once $include_path . 'common_functions.php';
require_once $include_path . "email_functions.php";
include_once $class_path . 'class.user.php';
include '../form-submit-email.php';

$redirect_url = filter_input(INPUT_POST, "redirect", FILTER_SANITIZE_URL); //after loggedin, redirect.
if($redirect_url==""){
    $redirect_url="../dashboard.php";
}

//echo "redirect=".$redirect_url.";";
$login_session = (isset($_SESSION['user']['login_session'])?$_SESSION['user']['login_session']:"");
unset($_SESSION['user']['login_session']); //regenerate in login.php
unset($_SESSION['user']);

$start = "http://localhost:8080/";

if(isset($_POST['email'])){
    $email = $_POST['email'];
    $cuser = new user();
    $user_details = $cuser->getCusDetails($email); 
    // print_r($user_details);
    // die();
    if(isset($user_details[0]['email'])){
        if($email == $user_details[0]['email']){
            if($user_details[0]['user_active_yn'] == "Y"){
                $user_id = $user_details[0]['user_id'];
                $name = "Admin";
                $token_id = uniqid();
                $token_created_time = date("Y-m-d H:i:s");

                    $tokendetails=array(
                        'user_id' => $user_id
                        ,'token_id' => $token_id
                        ,'token_created_time' => $token_created_time
                    );
            
                    $cuser->saveTokenDetails($tokendetails);

                $address = $email;
                $subject = 'Change Password';
                
                $body = '<html>
                            <body>
                                <h2>Please click the following link to change the password</h2>
                                <hr>
                                <p>link : </p>
                                <a href="'.$start.'recover-password.php?token_id='.$token_id.'">Click here to change the password</a>
                                <p>User:<br>'.$email.'</p>
                            </body>
                        </html>';
                
                $status = send_mail($subject,$address,$body);
                echo '<script>alert("Please check your email and click the link to change the password");
                        location="../admin/admin-login.php";
                        </script>';
            }else{
                errorRedirect("User is not active.Please contact the techinical team to activate the account first!","../forgot-password.php?redirect=$redirect_url");
            }
        }else{
            errorRedirect("User not found!","../forgot-password.php?redirect=$redirect_url");
            //   print_r("User account not found!");
        }
    }else{
        errorRedirect("User not found!","../forgot-password.php?redirect=$redirect_url");
        // $_SESSION['error']="User account not found!";
        // echo '<script>location="../forgot-password.php";</script>';
        //   print_r("User account not found!");
    }
}
function clearUser(){
    $_SESSION['user'] = Array (
        'user_id' => 0
       ,'user_login_id' => 0
       ,'user_name' => ""
       ,'email' => ""
       ,'first_name' => ""
       ,"last_name" => ""
       ,"last_login" => date("Y-m-d H:i")
       ,"user_access_level" => ""
       ,"user_type" => ""
       ,"user_type_label" => ""
       ,"user_photo" => ""
       ,"organisation_id" => 0
       ,"organisation_included" => ""
    );
}
?>